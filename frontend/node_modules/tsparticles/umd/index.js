(function (factory) {
    if (typeof module === "object" && typeof module.exports === "object") {
        var v = factory(require, exports);
        if (v !== undefined) module.exports = v;
    }
    else if (typeof define === "function" && define.amd) {
        define(["require", "exports", "@tsparticles/plugin-absorbers", "@tsparticles/updater-destroy", "@tsparticles/plugin-emitters", "@tsparticles/plugin-emitters-shape-circle", "@tsparticles/plugin-emitters-shape-square", "@tsparticles/interaction-external-trail", "@tsparticles/updater-roll", "@tsparticles/slim", "@tsparticles/shape-text", "@tsparticles/updater-tilt", "@tsparticles/updater-twinkle", "@tsparticles/updater-wobble"], factory);
    }
})(function (require, exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.loadFull = loadFull;
    const plugin_absorbers_1 = require("@tsparticles/plugin-absorbers");
    const updater_destroy_1 = require("@tsparticles/updater-destroy");
    const plugin_emitters_1 = require("@tsparticles/plugin-emitters");
    const plugin_emitters_shape_circle_1 = require("@tsparticles/plugin-emitters-shape-circle");
    const plugin_emitters_shape_square_1 = require("@tsparticles/plugin-emitters-shape-square");
    const interaction_external_trail_1 = require("@tsparticles/interaction-external-trail");
    const updater_roll_1 = require("@tsparticles/updater-roll");
    const slim_1 = require("@tsparticles/slim");
    const shape_text_1 = require("@tsparticles/shape-text");
    const updater_tilt_1 = require("@tsparticles/updater-tilt");
    const updater_twinkle_1 = require("@tsparticles/updater-twinkle");
    const updater_wobble_1 = require("@tsparticles/updater-wobble");
    async function loadFull(engine, refresh = true) {
        engine.checkVersion("3.8.1");
        await (0, updater_destroy_1.loadDestroyUpdater)(engine, false);
        await (0, updater_roll_1.loadRollUpdater)(engine, false);
        await (0, updater_tilt_1.loadTiltUpdater)(engine, false);
        await (0, updater_twinkle_1.loadTwinkleUpdater)(engine, false);
        await (0, updater_wobble_1.loadWobbleUpdater)(engine, false);
        await (0, shape_text_1.loadTextShape)(engine, false);
        await (0, interaction_external_trail_1.loadExternalTrailInteraction)(engine, false);
        await (0, plugin_absorbers_1.loadAbsorbersPlugin)(engine, false);
        await (0, plugin_emitters_1.loadEmittersPlugin)(engine, false);
        await (0, plugin_emitters_shape_circle_1.loadEmittersShapeCircle)(engine, false);
        await (0, plugin_emitters_shape_square_1.loadEmittersShapeSquare)(engine, false);
        await (0, slim_1.loadSlim)(engine, refresh);
    }
});
