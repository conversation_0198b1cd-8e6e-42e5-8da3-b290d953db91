(function (factory) {
    if (typeof module === "object" && typeof module.exports === "object") {
        var v = factory(require, exports);
        if (v !== undefined) module.exports = v;
    }
    else if (typeof define === "function" && define.amd) {
        define(["require", "exports", "./StrokeColorUpdater.js"], factory);
    }
})(function (require, exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.loadStrokeColorUpdater = loadStrokeColorUpdater;
    const StrokeColorUpdater_js_1 = require("./StrokeColorUpdater.js");
    async function loadStrokeColorUpdater(engine, refresh = true) {
        engine.checkVersion("3.8.1");
        await engine.addParticleUpdater("strokeColor", container => {
            return Promise.resolve(new StrokeColorUpdater_js_1.StrokeColorUpdater(container, engine));
        }, refresh);
    }
});
