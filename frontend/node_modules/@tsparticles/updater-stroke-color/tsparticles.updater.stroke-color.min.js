/*! For license information please see tsparticles.updater.stroke-color.min.js.LICENSE.txt */
!function(e,o){if("object"==typeof exports&&"object"==typeof module)module.exports=o(require("@tsparticles/engine"));else if("function"==typeof define&&define.amd)define(["@tsparticles/engine"],o);else{var t="object"==typeof exports?o(require("@tsparticles/engine")):o(e.window);for(var r in t)("object"==typeof exports?exports:e)[r]=t[r]}}(this,(e=>(()=>{var o={303:o=>{o.exports=e}},t={};function r(e){var i=t[e];if(void 0!==i)return i.exports;var n=t[e]={exports:{}};return o[e](n,n.exports,r),n.exports}r.d=(e,o)=>{for(var t in o)r.o(o,t)&&!r.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:o[t]})},r.o=(e,o)=>Object.prototype.hasOwnProperty.call(e,o),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var i={};r.r(i),r.d(i,{loadStrokeColorUpdater:()=>s});var n=r(303);class a{constructor(e,o){this._container=e,this._engine=o}init(e){const o=this._container,t=e.options,r=(0,n.itemFromSingleOrMultiple)(t.stroke,e.id,t.reduceDuplicates);e.strokeWidth=(0,n.getRangeValue)(r.width)*o.retina.pixelRatio,e.strokeOpacity=(0,n.getRangeValue)(r.opacity??1),e.strokeAnimation=r.color?.animation;const i=(0,n.rangeColorToHsl)(this._engine,r.color)??e.getFillColor();i&&(e.strokeColor=(0,n.getHslAnimationFromHsl)(i,e.strokeAnimation,o.retina.reduceFactor))}isEnabled(e){const o=e.strokeAnimation,{strokeColor:t}=e;return!e.destroyed&&!e.spawning&&!!o&&(void 0!==t?.h.value&&t.h.enable||void 0!==t?.s.value&&t.s.enable||void 0!==t?.l.value&&t.l.enable)}update(e,o){this.isEnabled(e)&&(0,n.updateColor)(e.strokeColor,o)}}async function s(e,o=!0){e.checkVersion("3.8.1"),await e.addParticleUpdater("strokeColor",(o=>Promise.resolve(new a(o,e))),o)}return i})()));