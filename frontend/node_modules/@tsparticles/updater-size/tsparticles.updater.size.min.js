/*! For license information please see tsparticles.updater.size.min.js.LICENSE.txt */
!function(e,o){if("object"==typeof exports&&"object"==typeof module)module.exports=o(require("@tsparticles/engine"));else if("function"==typeof define&&define.amd)define(["@tsparticles/engine"],o);else{var t="object"==typeof exports?o(require("@tsparticles/engine")):o(e.window);for(var i in t)("object"==typeof exports?exports:e)[i]=t[i]}}(this,(e=>(()=>{var o={303:o=>{o.exports=e}},t={};function i(e){var n=t[e];if(void 0!==n)return n.exports;var r=t[e]={exports:{}};return o[e](r,r.exports,i),r.exports}i.d=(e,o)=>{for(var t in o)i.o(o,t)&&!i.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:o[t]})},i.o=(e,o)=>Object.prototype.hasOwnProperty.call(e,o),i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};i.r(n),i.d(n,{loadSizeUpdater:()=>a});var r=i(303);class s{init(e){const o=e.container,t=e.options.size.animation;t.enable&&(e.size.velocity=(e.retina.sizeAnimationSpeed??o.retina.sizeAnimationSpeed)/r.percentDenominator*o.retina.reduceFactor,t.sync||(e.size.velocity*=(0,r.getRandom)()))}isEnabled(e){return!e.destroyed&&!e.spawning&&e.size.enable&&((e.size.maxLoops??0)<=0||(e.size.maxLoops??0)>0&&(e.size.loops??0)<(e.size.maxLoops??0))}reset(e){e.size.loops=0}update(e,o){this.isEnabled(e)&&(0,r.updateAnimation)(e,e.size,!0,e.options.size.animation.destroy,o)}}async function a(e,o=!0){e.checkVersion("3.8.1"),await e.addParticleUpdater("size",(()=>Promise.resolve(new s)),o)}return n})()));