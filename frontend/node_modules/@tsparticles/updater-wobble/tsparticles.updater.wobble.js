/*!
 * Author : <PERSON>
 * MIT license: https://opensource.org/licenses/MIT
 * Demo / Generator : https://particles.js.org/
 * GitHub : https://www.github.com/matteobruni/tsparticles
 * How to use? : Check the GitHub README
 * v3.8.1
 */
/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory(require("@tsparticles/engine"));
	else if(typeof define === 'function' && define.amd)
		define(["@tsparticles/engine"], factory);
	else {
		var a = typeof exports === 'object' ? factory(require("@tsparticles/engine")) : factory(root["window"]);
		for(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];
	}
})(this, (__WEBPACK_EXTERNAL_MODULE__tsparticles_engine__) => {
return /******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./dist/browser/Options/Classes/Wobble.js":
/*!************************************************!*\
  !*** ./dist/browser/Options/Classes/Wobble.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Wobble: () => (/* binding */ Wobble)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _WobbleSpeed_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./WobbleSpeed.js */ \"./dist/browser/Options/Classes/WobbleSpeed.js\");\n\n\nclass Wobble {\n  constructor() {\n    this.distance = 5;\n    this.enable = false;\n    this.speed = new _WobbleSpeed_js__WEBPACK_IMPORTED_MODULE_1__.WobbleSpeed();\n  }\n  load(data) {\n    if ((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    if (data.distance !== undefined) {\n      this.distance = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.setRangeValue)(data.distance);\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    if (data.speed !== undefined) {\n      if ((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isNumber)(data.speed)) {\n        this.speed.load({\n          angle: data.speed\n        });\n      } else {\n        const rangeSpeed = data.speed;\n        if (rangeSpeed.min !== undefined) {\n          this.speed.load({\n            angle: rangeSpeed\n          });\n        } else {\n          this.speed.load(data.speed);\n        }\n      }\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/updater-wobble/./dist/browser/Options/Classes/Wobble.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/WobbleSpeed.js":
/*!*****************************************************!*\
  !*** ./dist/browser/Options/Classes/WobbleSpeed.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WobbleSpeed: () => (/* binding */ WobbleSpeed)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n\nclass WobbleSpeed {\n  constructor() {\n    this.angle = 50;\n    this.move = 10;\n  }\n  load(data) {\n    if ((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    if (data.angle !== undefined) {\n      this.angle = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.setRangeValue)(data.angle);\n    }\n    if (data.move !== undefined) {\n      this.move = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.setRangeValue)(data.move);\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/updater-wobble/./dist/browser/Options/Classes/WobbleSpeed.js?");

/***/ }),

/***/ "./dist/browser/Utils.js":
/*!*******************************!*\
  !*** ./dist/browser/Utils.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   updateWobble: () => (/* binding */ updateWobble)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n\nconst defaultDistance = 0,\n  double = 2,\n  doublePI = Math.PI * double,\n  distanceFactor = 60;\nfunction updateWobble(particle, delta) {\n  const {\n      wobble: wobbleOptions\n    } = particle.options,\n    {\n      wobble\n    } = particle;\n  if (!wobbleOptions?.enable || !wobble) {\n    return;\n  }\n  const angleSpeed = wobble.angleSpeed * delta.factor,\n    moveSpeed = wobble.moveSpeed * delta.factor,\n    distance = moveSpeed * ((particle.retina.wobbleDistance ?? defaultDistance) * delta.factor) / (_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.millisecondsToSeconds / distanceFactor),\n    max = doublePI,\n    {\n      position\n    } = particle;\n  wobble.angle += angleSpeed;\n  if (wobble.angle > max) {\n    wobble.angle -= max;\n  }\n  position.x += distance * Math.cos(wobble.angle);\n  position.y += distance * Math.abs(Math.sin(wobble.angle));\n}\n\n//# sourceURL=webpack://@tsparticles/updater-wobble/./dist/browser/Utils.js?");

/***/ }),

/***/ "./dist/browser/WobbleUpdater.js":
/*!***************************************!*\
  !*** ./dist/browser/WobbleUpdater.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WobbleUpdater: () => (/* binding */ WobbleUpdater)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Options_Classes_Wobble_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Options/Classes/Wobble.js */ \"./dist/browser/Options/Classes/Wobble.js\");\n/* harmony import */ var _Utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Utils.js */ \"./dist/browser/Utils.js\");\n\n\n\nconst double = 2,\n  doublePI = Math.PI * double,\n  maxAngle = 360,\n  moveSpeedFactor = 10,\n  defaultDistance = 0;\nclass WobbleUpdater {\n  constructor(container) {\n    this.container = container;\n  }\n  init(particle) {\n    const wobbleOpt = particle.options.wobble;\n    if (wobbleOpt?.enable) {\n      particle.wobble = {\n        angle: (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRandom)() * doublePI,\n        angleSpeed: (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(wobbleOpt.speed.angle) / maxAngle,\n        moveSpeed: (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(wobbleOpt.speed.move) / moveSpeedFactor\n      };\n    } else {\n      particle.wobble = {\n        angle: 0,\n        angleSpeed: 0,\n        moveSpeed: 0\n      };\n    }\n    particle.retina.wobbleDistance = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(wobbleOpt?.distance ?? defaultDistance) * this.container.retina.pixelRatio;\n  }\n  isEnabled(particle) {\n    return !particle.destroyed && !particle.spawning && !!particle.options.wobble?.enable;\n  }\n  loadOptions(options, ...sources) {\n    if (!options.wobble) {\n      options.wobble = new _Options_Classes_Wobble_js__WEBPACK_IMPORTED_MODULE_1__.Wobble();\n    }\n    for (const source of sources) {\n      options.wobble.load(source?.wobble);\n    }\n  }\n  update(particle, delta) {\n    if (!this.isEnabled(particle)) {\n      return;\n    }\n    (0,_Utils_js__WEBPACK_IMPORTED_MODULE_2__.updateWobble)(particle, delta);\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/updater-wobble/./dist/browser/WobbleUpdater.js?");

/***/ }),

/***/ "./dist/browser/index.js":
/*!*******************************!*\
  !*** ./dist/browser/index.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadWobbleUpdater: () => (/* binding */ loadWobbleUpdater)\n/* harmony export */ });\n/* harmony import */ var _WobbleUpdater_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./WobbleUpdater.js */ \"./dist/browser/WobbleUpdater.js\");\n\nasync function loadWobbleUpdater(engine, refresh = true) {\n  engine.checkVersion(\"3.8.1\");\n  await engine.addParticleUpdater(\"wobble\", container => {\n    return Promise.resolve(new _WobbleUpdater_js__WEBPACK_IMPORTED_MODULE_0__.WobbleUpdater(container));\n  }, refresh);\n}\n\n//# sourceURL=webpack://@tsparticles/updater-wobble/./dist/browser/index.js?");

/***/ }),

/***/ "@tsparticles/engine":
/*!*********************************************************************************************************************************!*\
  !*** external {"commonjs":"@tsparticles/engine","commonjs2":"@tsparticles/engine","amd":"@tsparticles/engine","root":"window"} ***!
  \*********************************************************************************************************************************/
/***/ ((module) => {

module.exports = __WEBPACK_EXTERNAL_MODULE__tsparticles_engine__;

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__("./dist/browser/index.js");
/******/ 	
/******/ 	return __webpack_exports__;
/******/ })()
;
});