/*! For license information please see tsparticles.updater.wobble.min.js.LICENSE.txt */
!function(e,o){if("object"==typeof exports&&"object"==typeof module)module.exports=o(require("@tsparticles/engine"));else if("function"==typeof define&&define.amd)define(["@tsparticles/engine"],o);else{var t="object"==typeof exports?o(require("@tsparticles/engine")):o(e.window);for(var n in t)("object"==typeof exports?exports:e)[n]=t[n]}}(this,(e=>(()=>{var o={303:o=>{o.exports=e}},t={};function n(e){var a=t[e];if(void 0!==a)return a.exports;var s=t[e]={exports:{}};return o[e](s,s.exports,n),s.exports}n.d=(e,o)=>{for(var t in o)n.o(o,t)&&!n.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:o[t]})},n.o=(e,o)=>Object.prototype.hasOwnProperty.call(e,o),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var a={};n.r(a),n.d(a,{loadWobbleUpdater:()=>p});var s=n(303);class i{constructor(){this.angle=50,this.move=10}load(e){(0,s.isNull)(e)||(void 0!==e.angle&&(this.angle=(0,s.setRangeValue)(e.angle)),void 0!==e.move&&(this.move=(0,s.setRangeValue)(e.move)))}}class l{constructor(){this.distance=5,this.enable=!1,this.speed=new i}load(e){if(!(0,s.isNull)(e)&&(void 0!==e.distance&&(this.distance=(0,s.setRangeValue)(e.distance)),void 0!==e.enable&&(this.enable=e.enable),void 0!==e.speed))if((0,s.isNumber)(e.speed))this.speed.load({angle:e.speed});else{const o=e.speed;void 0!==o.min?this.speed.load({angle:o}):this.speed.load(e.speed)}}}const r=2*Math.PI;const d=2*Math.PI;class c{constructor(e){this.container=e}init(e){const o=e.options.wobble;e.wobble=o?.enable?{angle:(0,s.getRandom)()*d,angleSpeed:(0,s.getRangeValue)(o.speed.angle)/360,moveSpeed:(0,s.getRangeValue)(o.speed.move)/10}:{angle:0,angleSpeed:0,moveSpeed:0},e.retina.wobbleDistance=(0,s.getRangeValue)(o?.distance??0)*this.container.retina.pixelRatio}isEnabled(e){return!e.destroyed&&!e.spawning&&!!e.options.wobble?.enable}loadOptions(e,...o){e.wobble||(e.wobble=new l);for(const t of o)e.wobble.load(t?.wobble)}update(e,o){this.isEnabled(e)&&function(e,o){const{wobble:t}=e.options,{wobble:n}=e;if(!t?.enable||!n)return;const a=n.angleSpeed*o.factor,i=n.moveSpeed*o.factor*((e.retina.wobbleDistance??0)*o.factor)/(s.millisecondsToSeconds/60),l=r,{position:d}=e;n.angle+=a,n.angle>l&&(n.angle-=l),d.x+=i*Math.cos(n.angle),d.y+=i*Math.abs(Math.sin(n.angle))}(e,o)}}async function p(e,o=!0){e.checkVersion("3.8.1"),await e.addParticleUpdater("wobble",(e=>Promise.resolve(new c(e))),o)}return a})()));