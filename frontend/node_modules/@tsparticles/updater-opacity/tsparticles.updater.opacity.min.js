/*! For license information please see tsparticles.updater.opacity.min.js.LICENSE.txt */
!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t(require("@tsparticles/engine"));else if("function"==typeof define&&define.amd)define(["@tsparticles/engine"],t);else{var o="object"==typeof exports?t(require("@tsparticles/engine")):t(e.window);for(var i in o)("object"==typeof exports?exports:e)[i]=o[i]}}(this,(e=>(()=>{var t={303:t=>{t.exports=e}},o={};function i(e){var a=o[e];if(void 0!==a)return a.exports;var r=o[e]={exports:{}};return t[e](r,r.exports,i),r.exports}i.d=(e,t)=>{for(var o in t)i.o(t,o)&&!i.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var a={};i.r(a),i.d(a,{loadOpacityUpdater:()=>p});var r=i(303);class n{constructor(e){this.container=e}init(e){const t=e.options.opacity;e.opacity=(0,r.initParticleNumericAnimationValue)(t,1);const o=t.animation;o.enable&&(e.opacity.velocity=(0,r.getRangeValue)(o.speed)/r.percentDenominator*this.container.retina.reduceFactor,o.sync||(e.opacity.velocity*=(0,r.getRandom)()))}isEnabled(e){return!e.destroyed&&!e.spawning&&!!e.opacity&&e.opacity.enable&&((e.opacity.maxLoops??0)<=0||(e.opacity.maxLoops??0)>0&&(e.opacity.loops??0)<(e.opacity.maxLoops??0))}reset(e){e.opacity&&(e.opacity.time=0,e.opacity.loops=0)}update(e,t){this.isEnabled(e)&&e.opacity&&(0,r.updateAnimation)(e,e.opacity,!0,e.options.opacity.animation.destroy,t)}}async function p(e,t=!0){e.checkVersion("3.8.1"),await e.addParticleUpdater("opacity",(e=>Promise.resolve(new n(e))),t)}return a})()));