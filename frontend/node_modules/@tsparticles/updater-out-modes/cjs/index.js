"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.loadOutModesUpdater = loadOutModesUpdater;
const OutOfCanvasUpdater_js_1 = require("./OutOfCanvasUpdater.js");
async function loadOutModesUpdater(engine, refresh = true) {
    engine.checkVersion("3.8.1");
    await engine.addParticleUpdater("outModes", container => {
        return Promise.resolve(new OutOfCanvasUpdater_js_1.OutOfCanvasUpdater(container));
    }, refresh);
}
