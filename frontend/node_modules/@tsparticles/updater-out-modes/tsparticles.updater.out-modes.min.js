/*! For license information please see tsparticles.updater.out-modes.min.js.LICENSE.txt */
!function(t,e){if("object"==typeof exports&&"object"==typeof module)module.exports=e(require("@tsparticles/engine"));else if("function"==typeof define&&define.amd)define(["@tsparticles/engine"],e);else{var i="object"==typeof exports?e(require("@tsparticles/engine")):e(t.window);for(var o in i)("object"==typeof exports?exports:t)[o]=i[o]}}(this,(t=>(()=>{var e={303:e=>{e.exports=t}},i={};function o(t){var n=i[t];if(void 0!==n)return n.exports;var s=i[t]={exports:{}};return e[t](s,s.exports,o),s.exports}o.d=(t,e)=>{for(var i in e)o.o(e,i)&&!o.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},o.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),o.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var n={};o.r(n),o.d(n,{loadOutModesUpdater:()=>p});var s=o(303);class r{constructor(t){this.container=t,this.modes=[s.OutMode.bounce,s.OutMode.split]}update(t,e,i,o){if(!this.modes.includes(o))return;const n=this.container;let r=!1;for(const o of n.plugins.values())if(void 0!==o.particleBounce&&(r=o.particleBounce(t,i,e)),r)break;if(r)return;const a=t.getPosition(),d=t.offset,c=t.getRadius(),u=(0,s.calculateBounds)(a,c),p=n.canvas.size;!function(t){if(t.outMode!==s.OutMode.bounce&&t.outMode!==s.OutMode.split||t.direction!==s.OutModeDirection.left&&t.direction!==s.OutModeDirection.right)return;t.bounds.right<0&&t.direction===s.OutModeDirection.left?t.particle.position.x=t.size+t.offset.x:t.bounds.left>t.canvasSize.width&&t.direction===s.OutModeDirection.right&&(t.particle.position.x=t.canvasSize.width-t.size-t.offset.x);const e=t.particle.velocity.x;let i=!1;if(t.direction===s.OutModeDirection.right&&t.bounds.right>=t.canvasSize.width&&e>0||t.direction===s.OutModeDirection.left&&t.bounds.left<=0&&e<0){const e=(0,s.getRangeValue)(t.particle.options.bounce.horizontal.value);t.particle.velocity.x*=-e,i=!0}if(!i)return;const o=t.offset.x+t.size;t.bounds.right>=t.canvasSize.width&&t.direction===s.OutModeDirection.right?t.particle.position.x=t.canvasSize.width-o:t.bounds.left<=0&&t.direction===s.OutModeDirection.left&&(t.particle.position.x=o),t.outMode===s.OutMode.split&&t.particle.destroy()}({particle:t,outMode:o,direction:e,bounds:u,canvasSize:p,offset:d,size:c}),function(t){if(t.outMode!==s.OutMode.bounce&&t.outMode!==s.OutMode.split||t.direction!==s.OutModeDirection.bottom&&t.direction!==s.OutModeDirection.top)return;t.bounds.bottom<0&&t.direction===s.OutModeDirection.top?t.particle.position.y=t.size+t.offset.y:t.bounds.top>t.canvasSize.height&&t.direction===s.OutModeDirection.bottom&&(t.particle.position.y=t.canvasSize.height-t.size-t.offset.y);const e=t.particle.velocity.y;let i=!1;if(t.direction===s.OutModeDirection.bottom&&t.bounds.bottom>=t.canvasSize.height&&e>0||t.direction===s.OutModeDirection.top&&t.bounds.top<=0&&e<0){const e=(0,s.getRangeValue)(t.particle.options.bounce.vertical.value);t.particle.velocity.y*=-e,i=!0}if(!i)return;const o=t.offset.y+t.size;t.bounds.bottom>=t.canvasSize.height&&t.direction===s.OutModeDirection.bottom?t.particle.position.y=t.canvasSize.height-o:t.bounds.top<=0&&t.direction===s.OutModeDirection.top&&(t.particle.position.y=o),t.outMode===s.OutMode.split&&t.particle.destroy()}({particle:t,outMode:o,direction:e,bounds:u,canvasSize:p,offset:d,size:c})}}class a{constructor(t){this.container=t,this.modes=[s.OutMode.destroy]}update(t,e,i,o){if(!this.modes.includes(o))return;const n=this.container;switch(t.outType){case s.ParticleOutType.normal:case s.ParticleOutType.outside:if((0,s.isPointInside)(t.position,n.canvas.size,s.Vector.origin,t.getRadius(),e))return;break;case s.ParticleOutType.inside:{const{dx:e,dy:i}=(0,s.getDistances)(t.position,t.moveCenter),{x:o,y:n}=t.velocity;if(o<0&&e>t.moveCenter.radius||n<0&&i>t.moveCenter.radius||o>=0&&e<-t.moveCenter.radius||n>=0&&i<-t.moveCenter.radius)return;break}}n.particles.remove(t,t.group,!0)}}class d{constructor(t){this.container=t,this.modes=[s.OutMode.none]}update(t,e,i,o){if(!this.modes.includes(o))return;if((t.options.move.distance.horizontal&&(e===s.OutModeDirection.left||e===s.OutModeDirection.right))??(t.options.move.distance.vertical&&(e===s.OutModeDirection.top||e===s.OutModeDirection.bottom)))return;const n=t.options.move.gravity,r=this.container,a=r.canvas.size,d=t.getRadius();if(n.enable){const i=t.position;(!n.inverse&&i.y>a.height+d&&e===s.OutModeDirection.bottom||n.inverse&&i.y<-d&&e===s.OutModeDirection.top)&&r.particles.remove(t)}else{if(t.velocity.y>0&&t.position.y<=a.height+d||t.velocity.y<0&&t.position.y>=-d||t.velocity.x>0&&t.position.x<=a.width+d||t.velocity.x<0&&t.position.x>=-d)return;(0,s.isPointInside)(t.position,r.canvas.size,s.Vector.origin,d,e)||r.particles.remove(t)}}}class c{constructor(t){this.container=t,this.modes=[s.OutMode.out]}update(t,e,i,o){if(!this.modes.includes(o))return;const n=this.container;switch(t.outType){case s.ParticleOutType.inside:{const{x:e,y:i}=t.velocity,o=s.Vector.origin;o.length=t.moveCenter.radius,o.angle=t.velocity.angle+Math.PI,o.addTo(s.Vector.create(t.moveCenter));const{dx:r,dy:a}=(0,s.getDistances)(t.position,o);if(e<=0&&r>=0||i<=0&&a>=0||e>=0&&r<=0||i>=0&&a<=0)return;t.position.x=Math.floor((0,s.randomInRange)({min:0,max:n.canvas.size.width})),t.position.y=Math.floor((0,s.randomInRange)({min:0,max:n.canvas.size.height}));const{dx:d,dy:c}=(0,s.getDistances)(t.position,t.moveCenter);t.direction=Math.atan2(-c,-d),t.velocity.angle=t.direction;break}default:if((0,s.isPointInside)(t.position,n.canvas.size,s.Vector.origin,t.getRadius(),e))return;switch(t.outType){case s.ParticleOutType.outside:{t.position.x=Math.floor((0,s.randomInRange)({min:-t.moveCenter.radius,max:t.moveCenter.radius}))+t.moveCenter.x,t.position.y=Math.floor((0,s.randomInRange)({min:-t.moveCenter.radius,max:t.moveCenter.radius}))+t.moveCenter.y;const{dx:e,dy:i}=(0,s.getDistances)(t.position,t.moveCenter);t.moveCenter.radius&&(t.direction=Math.atan2(i,e),t.velocity.angle=t.direction);break}case s.ParticleOutType.normal:{const i=t.options.move.warp,o=n.canvas.size,r={bottom:o.height+t.getRadius()+t.offset.y,left:-t.getRadius()-t.offset.x,right:o.width+t.getRadius()+t.offset.x,top:-t.getRadius()-t.offset.y},a=t.getRadius(),d=(0,s.calculateBounds)(t.position,a);e===s.OutModeDirection.right&&d.left>o.width+t.offset.x?(t.position.x=r.left,t.initialPosition.x=t.position.x,i||(t.position.y=(0,s.getRandom)()*o.height,t.initialPosition.y=t.position.y)):e===s.OutModeDirection.left&&d.right<-t.offset.x&&(t.position.x=r.right,t.initialPosition.x=t.position.x,i||(t.position.y=(0,s.getRandom)()*o.height,t.initialPosition.y=t.position.y)),e===s.OutModeDirection.bottom&&d.top>o.height+t.offset.y?(i||(t.position.x=(0,s.getRandom)()*o.width,t.initialPosition.x=t.position.x),t.position.y=r.top,t.initialPosition.y=t.position.y):e===s.OutModeDirection.top&&d.bottom<-t.offset.y&&(i||(t.position.x=(0,s.getRandom)()*o.width,t.initialPosition.x=t.position.x),t.position.y=r.bottom,t.initialPosition.y=t.position.y);break}}}}}class u{constructor(t){this._addUpdaterIfMissing=(t,e,i)=>{const o=t.options.move.outModes;!this.updaters.has(e)&&((t,e)=>t.default===e||t.bottom===e||t.left===e||t.right===e||t.top===e)(o,e)&&this.updaters.set(e,i(this.container))},this._updateOutMode=(t,e,i,o)=>{for(const n of this.updaters.values())n.update(t,o,e,i)},this.container=t,this.updaters=new Map}init(t){this._addUpdaterIfMissing(t,s.OutMode.bounce,(t=>new r(t))),this._addUpdaterIfMissing(t,s.OutMode.out,(t=>new c(t))),this._addUpdaterIfMissing(t,s.OutMode.destroy,(t=>new a(t))),this._addUpdaterIfMissing(t,s.OutMode.none,(t=>new d(t)))}isEnabled(t){return!t.destroyed&&!t.spawning}update(t,e){const i=t.options.move.outModes;this._updateOutMode(t,e,i.bottom??i.default,s.OutModeDirection.bottom),this._updateOutMode(t,e,i.left??i.default,s.OutModeDirection.left),this._updateOutMode(t,e,i.right??i.default,s.OutModeDirection.right),this._updateOutMode(t,e,i.top??i.default,s.OutModeDirection.top)}}async function p(t,e=!0){t.checkVersion("3.8.1"),await t.addParticleUpdater("outModes",(t=>Promise.resolve(new u(t))),e)}return n})()));