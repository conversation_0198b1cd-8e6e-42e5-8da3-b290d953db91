/*!
 * Author : <PERSON>
 * MIT license: https://opensource.org/licenses/MIT
 * Demo / Generator : https://particles.js.org/
 * GitHub : https://www.github.com/matteobruni/tsparticles
 * How to use? : Check the GitHub README
 * v3.8.1
 */
/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory(require("@tsparticles/engine"));
	else if(typeof define === 'function' && define.amd)
		define(["@tsparticles/engine"], factory);
	else {
		var a = typeof exports === 'object' ? factory(require("@tsparticles/engine")) : factory(root["window"]);
		for(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];
	}
})(this, (__WEBPACK_EXTERNAL_MODULE__tsparticles_engine__) => {
return /******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./dist/browser/BounceOutMode.js":
/*!***************************************!*\
  !*** ./dist/browser/BounceOutMode.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BounceOutMode: () => (/* binding */ BounceOutMode)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Utils.js */ \"./dist/browser/Utils.js\");\n\n\nclass BounceOutMode {\n  constructor(container) {\n    this.container = container;\n    this.modes = [_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutMode.bounce, _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutMode.split];\n  }\n  update(particle, direction, delta, outMode) {\n    if (!this.modes.includes(outMode)) {\n      return;\n    }\n    const container = this.container;\n    let handled = false;\n    for (const plugin of container.plugins.values()) {\n      if (plugin.particleBounce !== undefined) {\n        handled = plugin.particleBounce(particle, delta, direction);\n      }\n      if (handled) {\n        break;\n      }\n    }\n    if (handled) {\n      return;\n    }\n    const pos = particle.getPosition(),\n      offset = particle.offset,\n      size = particle.getRadius(),\n      bounds = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.calculateBounds)(pos, size),\n      canvasSize = container.canvas.size;\n    (0,_Utils_js__WEBPACK_IMPORTED_MODULE_1__.bounceHorizontal)({\n      particle,\n      outMode,\n      direction,\n      bounds,\n      canvasSize,\n      offset,\n      size\n    });\n    (0,_Utils_js__WEBPACK_IMPORTED_MODULE_1__.bounceVertical)({\n      particle,\n      outMode,\n      direction,\n      bounds,\n      canvasSize,\n      offset,\n      size\n    });\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/updater-out-modes/./dist/browser/BounceOutMode.js?");

/***/ }),

/***/ "./dist/browser/DestroyOutMode.js":
/*!****************************************!*\
  !*** ./dist/browser/DestroyOutMode.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DestroyOutMode: () => (/* binding */ DestroyOutMode)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n\nconst minVelocity = 0;\nclass DestroyOutMode {\n  constructor(container) {\n    this.container = container;\n    this.modes = [_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutMode.destroy];\n  }\n  update(particle, direction, _delta, outMode) {\n    if (!this.modes.includes(outMode)) {\n      return;\n    }\n    const container = this.container;\n    switch (particle.outType) {\n      case _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.ParticleOutType.normal:\n      case _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.ParticleOutType.outside:\n        if ((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isPointInside)(particle.position, container.canvas.size, _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.Vector.origin, particle.getRadius(), direction)) {\n          return;\n        }\n        break;\n      case _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.ParticleOutType.inside:\n        {\n          const {\n              dx,\n              dy\n            } = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getDistances)(particle.position, particle.moveCenter),\n            {\n              x: vx,\n              y: vy\n            } = particle.velocity;\n          if (vx < minVelocity && dx > particle.moveCenter.radius || vy < minVelocity && dy > particle.moveCenter.radius || vx >= minVelocity && dx < -particle.moveCenter.radius || vy >= minVelocity && dy < -particle.moveCenter.radius) {\n            return;\n          }\n          break;\n        }\n    }\n    container.particles.remove(particle, particle.group, true);\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/updater-out-modes/./dist/browser/DestroyOutMode.js?");

/***/ }),

/***/ "./dist/browser/NoneOutMode.js":
/*!*************************************!*\
  !*** ./dist/browser/NoneOutMode.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NoneOutMode: () => (/* binding */ NoneOutMode)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n\nconst minVelocity = 0;\nclass NoneOutMode {\n  constructor(container) {\n    this.container = container;\n    this.modes = [_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutMode.none];\n  }\n  update(particle, direction, delta, outMode) {\n    if (!this.modes.includes(outMode)) {\n      return;\n    }\n    if ((particle.options.move.distance.horizontal && (direction === _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutModeDirection.left || direction === _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutModeDirection.right)) ?? (particle.options.move.distance.vertical && (direction === _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutModeDirection.top || direction === _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutModeDirection.bottom))) {\n      return;\n    }\n    const gravityOptions = particle.options.move.gravity,\n      container = this.container,\n      canvasSize = container.canvas.size,\n      pRadius = particle.getRadius();\n    if (!gravityOptions.enable) {\n      if (particle.velocity.y > minVelocity && particle.position.y <= canvasSize.height + pRadius || particle.velocity.y < minVelocity && particle.position.y >= -pRadius || particle.velocity.x > minVelocity && particle.position.x <= canvasSize.width + pRadius || particle.velocity.x < minVelocity && particle.position.x >= -pRadius) {\n        return;\n      }\n      if (!(0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isPointInside)(particle.position, container.canvas.size, _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.Vector.origin, pRadius, direction)) {\n        container.particles.remove(particle);\n      }\n    } else {\n      const position = particle.position;\n      if (!gravityOptions.inverse && position.y > canvasSize.height + pRadius && direction === _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutModeDirection.bottom || gravityOptions.inverse && position.y < -pRadius && direction === _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutModeDirection.top) {\n        container.particles.remove(particle);\n      }\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/updater-out-modes/./dist/browser/NoneOutMode.js?");

/***/ }),

/***/ "./dist/browser/OutOfCanvasUpdater.js":
/*!********************************************!*\
  !*** ./dist/browser/OutOfCanvasUpdater.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OutOfCanvasUpdater: () => (/* binding */ OutOfCanvasUpdater)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _BounceOutMode_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./BounceOutMode.js */ \"./dist/browser/BounceOutMode.js\");\n/* harmony import */ var _DestroyOutMode_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./DestroyOutMode.js */ \"./dist/browser/DestroyOutMode.js\");\n/* harmony import */ var _NoneOutMode_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./NoneOutMode.js */ \"./dist/browser/NoneOutMode.js\");\n/* harmony import */ var _OutOutMode_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./OutOutMode.js */ \"./dist/browser/OutOutMode.js\");\n\n\n\n\n\nconst checkOutMode = (outModes, outMode) => {\n  return outModes.default === outMode || outModes.bottom === outMode || outModes.left === outMode || outModes.right === outMode || outModes.top === outMode;\n};\nclass OutOfCanvasUpdater {\n  constructor(container) {\n    this._addUpdaterIfMissing = (particle, outMode, getUpdater) => {\n      const outModes = particle.options.move.outModes;\n      if (!this.updaters.has(outMode) && checkOutMode(outModes, outMode)) {\n        this.updaters.set(outMode, getUpdater(this.container));\n      }\n    };\n    this._updateOutMode = (particle, delta, outMode, direction) => {\n      for (const updater of this.updaters.values()) {\n        updater.update(particle, direction, delta, outMode);\n      }\n    };\n    this.container = container;\n    this.updaters = new Map();\n  }\n  init(particle) {\n    this._addUpdaterIfMissing(particle, _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutMode.bounce, container => new _BounceOutMode_js__WEBPACK_IMPORTED_MODULE_1__.BounceOutMode(container));\n    this._addUpdaterIfMissing(particle, _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutMode.out, container => new _OutOutMode_js__WEBPACK_IMPORTED_MODULE_2__.OutOutMode(container));\n    this._addUpdaterIfMissing(particle, _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutMode.destroy, container => new _DestroyOutMode_js__WEBPACK_IMPORTED_MODULE_3__.DestroyOutMode(container));\n    this._addUpdaterIfMissing(particle, _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutMode.none, container => new _NoneOutMode_js__WEBPACK_IMPORTED_MODULE_4__.NoneOutMode(container));\n  }\n  isEnabled(particle) {\n    return !particle.destroyed && !particle.spawning;\n  }\n  update(particle, delta) {\n    const outModes = particle.options.move.outModes;\n    this._updateOutMode(particle, delta, outModes.bottom ?? outModes.default, _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutModeDirection.bottom);\n    this._updateOutMode(particle, delta, outModes.left ?? outModes.default, _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutModeDirection.left);\n    this._updateOutMode(particle, delta, outModes.right ?? outModes.default, _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutModeDirection.right);\n    this._updateOutMode(particle, delta, outModes.top ?? outModes.default, _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutModeDirection.top);\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/updater-out-modes/./dist/browser/OutOfCanvasUpdater.js?");

/***/ }),

/***/ "./dist/browser/OutOutMode.js":
/*!************************************!*\
  !*** ./dist/browser/OutOutMode.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OutOutMode: () => (/* binding */ OutOutMode)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n\nconst minVelocity = 0,\n  minDistance = 0;\nclass OutOutMode {\n  constructor(container) {\n    this.container = container;\n    this.modes = [_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutMode.out];\n  }\n  update(particle, direction, delta, outMode) {\n    if (!this.modes.includes(outMode)) {\n      return;\n    }\n    const container = this.container;\n    switch (particle.outType) {\n      case _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.ParticleOutType.inside:\n        {\n          const {\n            x: vx,\n            y: vy\n          } = particle.velocity;\n          const circVec = _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.Vector.origin;\n          circVec.length = particle.moveCenter.radius;\n          circVec.angle = particle.velocity.angle + Math.PI;\n          circVec.addTo(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.Vector.create(particle.moveCenter));\n          const {\n            dx,\n            dy\n          } = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getDistances)(particle.position, circVec);\n          if (vx <= minVelocity && dx >= minDistance || vy <= minVelocity && dy >= minDistance || vx >= minVelocity && dx <= minDistance || vy >= minVelocity && dy <= minDistance) {\n            return;\n          }\n          particle.position.x = Math.floor((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.randomInRange)({\n            min: 0,\n            max: container.canvas.size.width\n          }));\n          particle.position.y = Math.floor((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.randomInRange)({\n            min: 0,\n            max: container.canvas.size.height\n          }));\n          const {\n            dx: newDx,\n            dy: newDy\n          } = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getDistances)(particle.position, particle.moveCenter);\n          particle.direction = Math.atan2(-newDy, -newDx);\n          particle.velocity.angle = particle.direction;\n          break;\n        }\n      default:\n        {\n          if ((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isPointInside)(particle.position, container.canvas.size, _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.Vector.origin, particle.getRadius(), direction)) {\n            return;\n          }\n          switch (particle.outType) {\n            case _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.ParticleOutType.outside:\n              {\n                particle.position.x = Math.floor((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.randomInRange)({\n                  min: -particle.moveCenter.radius,\n                  max: particle.moveCenter.radius\n                })) + particle.moveCenter.x;\n                particle.position.y = Math.floor((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.randomInRange)({\n                  min: -particle.moveCenter.radius,\n                  max: particle.moveCenter.radius\n                })) + particle.moveCenter.y;\n                const {\n                  dx,\n                  dy\n                } = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getDistances)(particle.position, particle.moveCenter);\n                if (particle.moveCenter.radius) {\n                  particle.direction = Math.atan2(dy, dx);\n                  particle.velocity.angle = particle.direction;\n                }\n                break;\n              }\n            case _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.ParticleOutType.normal:\n              {\n                const warp = particle.options.move.warp,\n                  canvasSize = container.canvas.size,\n                  newPos = {\n                    bottom: canvasSize.height + particle.getRadius() + particle.offset.y,\n                    left: -particle.getRadius() - particle.offset.x,\n                    right: canvasSize.width + particle.getRadius() + particle.offset.x,\n                    top: -particle.getRadius() - particle.offset.y\n                  },\n                  sizeValue = particle.getRadius(),\n                  nextBounds = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.calculateBounds)(particle.position, sizeValue);\n                if (direction === _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutModeDirection.right && nextBounds.left > canvasSize.width + particle.offset.x) {\n                  particle.position.x = newPos.left;\n                  particle.initialPosition.x = particle.position.x;\n                  if (!warp) {\n                    particle.position.y = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRandom)() * canvasSize.height;\n                    particle.initialPosition.y = particle.position.y;\n                  }\n                } else if (direction === _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutModeDirection.left && nextBounds.right < -particle.offset.x) {\n                  particle.position.x = newPos.right;\n                  particle.initialPosition.x = particle.position.x;\n                  if (!warp) {\n                    particle.position.y = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRandom)() * canvasSize.height;\n                    particle.initialPosition.y = particle.position.y;\n                  }\n                }\n                if (direction === _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutModeDirection.bottom && nextBounds.top > canvasSize.height + particle.offset.y) {\n                  if (!warp) {\n                    particle.position.x = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRandom)() * canvasSize.width;\n                    particle.initialPosition.x = particle.position.x;\n                  }\n                  particle.position.y = newPos.top;\n                  particle.initialPosition.y = particle.position.y;\n                } else if (direction === _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutModeDirection.top && nextBounds.bottom < -particle.offset.y) {\n                  if (!warp) {\n                    particle.position.x = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRandom)() * canvasSize.width;\n                    particle.initialPosition.x = particle.position.x;\n                  }\n                  particle.position.y = newPos.bottom;\n                  particle.initialPosition.y = particle.position.y;\n                }\n                break;\n              }\n          }\n          break;\n        }\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/updater-out-modes/./dist/browser/OutOutMode.js?");

/***/ }),

/***/ "./dist/browser/Utils.js":
/*!*******************************!*\
  !*** ./dist/browser/Utils.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bounceHorizontal: () => (/* binding */ bounceHorizontal),\n/* harmony export */   bounceVertical: () => (/* binding */ bounceVertical)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n\nconst minVelocity = 0,\n  boundsMin = 0;\nfunction bounceHorizontal(data) {\n  if (data.outMode !== _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutMode.bounce && data.outMode !== _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutMode.split || data.direction !== _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutModeDirection.left && data.direction !== _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutModeDirection.right) {\n    return;\n  }\n  if (data.bounds.right < boundsMin && data.direction === _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutModeDirection.left) {\n    data.particle.position.x = data.size + data.offset.x;\n  } else if (data.bounds.left > data.canvasSize.width && data.direction === _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutModeDirection.right) {\n    data.particle.position.x = data.canvasSize.width - data.size - data.offset.x;\n  }\n  const velocity = data.particle.velocity.x;\n  let bounced = false;\n  if (data.direction === _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutModeDirection.right && data.bounds.right >= data.canvasSize.width && velocity > minVelocity || data.direction === _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutModeDirection.left && data.bounds.left <= boundsMin && velocity < minVelocity) {\n    const newVelocity = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(data.particle.options.bounce.horizontal.value);\n    data.particle.velocity.x *= -newVelocity;\n    bounced = true;\n  }\n  if (!bounced) {\n    return;\n  }\n  const minPos = data.offset.x + data.size;\n  if (data.bounds.right >= data.canvasSize.width && data.direction === _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutModeDirection.right) {\n    data.particle.position.x = data.canvasSize.width - minPos;\n  } else if (data.bounds.left <= boundsMin && data.direction === _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutModeDirection.left) {\n    data.particle.position.x = minPos;\n  }\n  if (data.outMode === _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutMode.split) {\n    data.particle.destroy();\n  }\n}\nfunction bounceVertical(data) {\n  if (data.outMode !== _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutMode.bounce && data.outMode !== _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutMode.split || data.direction !== _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutModeDirection.bottom && data.direction !== _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutModeDirection.top) {\n    return;\n  }\n  if (data.bounds.bottom < boundsMin && data.direction === _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutModeDirection.top) {\n    data.particle.position.y = data.size + data.offset.y;\n  } else if (data.bounds.top > data.canvasSize.height && data.direction === _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutModeDirection.bottom) {\n    data.particle.position.y = data.canvasSize.height - data.size - data.offset.y;\n  }\n  const velocity = data.particle.velocity.y;\n  let bounced = false;\n  if (data.direction === _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutModeDirection.bottom && data.bounds.bottom >= data.canvasSize.height && velocity > minVelocity || data.direction === _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutModeDirection.top && data.bounds.top <= boundsMin && velocity < minVelocity) {\n    const newVelocity = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(data.particle.options.bounce.vertical.value);\n    data.particle.velocity.y *= -newVelocity;\n    bounced = true;\n  }\n  if (!bounced) {\n    return;\n  }\n  const minPos = data.offset.y + data.size;\n  if (data.bounds.bottom >= data.canvasSize.height && data.direction === _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutModeDirection.bottom) {\n    data.particle.position.y = data.canvasSize.height - minPos;\n  } else if (data.bounds.top <= boundsMin && data.direction === _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutModeDirection.top) {\n    data.particle.position.y = minPos;\n  }\n  if (data.outMode === _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OutMode.split) {\n    data.particle.destroy();\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/updater-out-modes/./dist/browser/Utils.js?");

/***/ }),

/***/ "./dist/browser/index.js":
/*!*******************************!*\
  !*** ./dist/browser/index.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadOutModesUpdater: () => (/* binding */ loadOutModesUpdater)\n/* harmony export */ });\n/* harmony import */ var _OutOfCanvasUpdater_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./OutOfCanvasUpdater.js */ \"./dist/browser/OutOfCanvasUpdater.js\");\n\nasync function loadOutModesUpdater(engine, refresh = true) {\n  engine.checkVersion(\"3.8.1\");\n  await engine.addParticleUpdater(\"outModes\", container => {\n    return Promise.resolve(new _OutOfCanvasUpdater_js__WEBPACK_IMPORTED_MODULE_0__.OutOfCanvasUpdater(container));\n  }, refresh);\n}\n\n//# sourceURL=webpack://@tsparticles/updater-out-modes/./dist/browser/index.js?");

/***/ }),

/***/ "@tsparticles/engine":
/*!*********************************************************************************************************************************!*\
  !*** external {"commonjs":"@tsparticles/engine","commonjs2":"@tsparticles/engine","amd":"@tsparticles/engine","root":"window"} ***!
  \*********************************************************************************************************************************/
/***/ ((module) => {

module.exports = __WEBPACK_EXTERNAL_MODULE__tsparticles_engine__;

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__("./dist/browser/index.js");
/******/ 	
/******/ 	return __webpack_exports__;
/******/ })()
;
});