/*!
 * Author : <PERSON>
 * MIT license: https://opensource.org/licenses/MIT
 * Demo / Generator : https://particles.js.org/
 * GitHub : https://www.github.com/matteobruni/tsparticles
 * How to use? : Check the GitHub README
 * v3.8.1
 */
/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory(require("@tsparticles/engine"));
	else if(typeof define === 'function' && define.amd)
		define(["@tsparticles/engine"], factory);
	else {
		var a = typeof exports === 'object' ? factory(require("@tsparticles/engine")) : factory(root["window"]);
		for(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];
	}
})(this, (__WEBPACK_EXTERNAL_MODULE__tsparticles_engine__) => {
return /******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./dist/browser/Options/Classes/Tilt.js":
/*!**********************************************!*\
  !*** ./dist/browser/Options/Classes/Tilt.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tilt: () => (/* binding */ Tilt)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _TiltDirection_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../TiltDirection.js */ \"./dist/browser/TiltDirection.js\");\n/* harmony import */ var _TiltAnimation_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./TiltAnimation.js */ \"./dist/browser/Options/Classes/TiltAnimation.js\");\n\n\n\nclass Tilt extends _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.ValueWithRandom {\n  constructor() {\n    super();\n    this.animation = new _TiltAnimation_js__WEBPACK_IMPORTED_MODULE_1__.TiltAnimation();\n    this.direction = _TiltDirection_js__WEBPACK_IMPORTED_MODULE_2__.TiltDirection.clockwise;\n    this.enable = false;\n    this.value = 0;\n  }\n  load(data) {\n    super.load(data);\n    if ((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    this.animation.load(data.animation);\n    if (data.direction !== undefined) {\n      this.direction = data.direction;\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/updater-tilt/./dist/browser/Options/Classes/Tilt.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/TiltAnimation.js":
/*!*******************************************************!*\
  !*** ./dist/browser/Options/Classes/TiltAnimation.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TiltAnimation: () => (/* binding */ TiltAnimation)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n\nclass TiltAnimation {\n  constructor() {\n    this.enable = false;\n    this.speed = 0;\n    this.decay = 0;\n    this.sync = false;\n  }\n  load(data) {\n    if ((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    if (data.speed !== undefined) {\n      this.speed = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.setRangeValue)(data.speed);\n    }\n    if (data.decay !== undefined) {\n      this.decay = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.setRangeValue)(data.decay);\n    }\n    if (data.sync !== undefined) {\n      this.sync = data.sync;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/updater-tilt/./dist/browser/Options/Classes/TiltAnimation.js?");

/***/ }),

/***/ "./dist/browser/TiltDirection.js":
/*!***************************************!*\
  !*** ./dist/browser/TiltDirection.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TiltDirection: () => (/* binding */ TiltDirection)\n/* harmony export */ });\nvar TiltDirection;\n(function (TiltDirection) {\n  TiltDirection[\"clockwise\"] = \"clockwise\";\n  TiltDirection[\"counterClockwise\"] = \"counter-clockwise\";\n  TiltDirection[\"random\"] = \"random\";\n})(TiltDirection || (TiltDirection = {}));\n\n//# sourceURL=webpack://@tsparticles/updater-tilt/./dist/browser/TiltDirection.js?");

/***/ }),

/***/ "./dist/browser/TiltUpdater.js":
/*!*************************************!*\
  !*** ./dist/browser/TiltUpdater.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TiltUpdater: () => (/* binding */ TiltUpdater)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Options_Classes_Tilt_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Options/Classes/Tilt.js */ \"./dist/browser/Options/Classes/Tilt.js\");\n/* harmony import */ var _TiltDirection_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./TiltDirection.js */ \"./dist/browser/TiltDirection.js\");\n\n\n\nconst identity = 1,\n  double = 2,\n  doublePI = Math.PI * double,\n  maxAngle = 360;\nclass TiltUpdater {\n  constructor(container) {\n    this.container = container;\n  }\n  getTransformValues(particle) {\n    const tilt = particle.tilt?.enable && particle.tilt;\n    return {\n      b: tilt ? Math.cos(tilt.value) * tilt.cosDirection : undefined,\n      c: tilt ? Math.sin(tilt.value) * tilt.sinDirection : undefined\n    };\n  }\n  init(particle) {\n    const tiltOptions = particle.options.tilt;\n    if (!tiltOptions) {\n      return;\n    }\n    particle.tilt = {\n      enable: tiltOptions.enable,\n      value: (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.degToRad)((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(tiltOptions.value)),\n      sinDirection: (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRandom)() >= _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.half ? identity : -identity,\n      cosDirection: (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRandom)() >= _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.half ? identity : -identity,\n      min: 0,\n      max: doublePI\n    };\n    let tiltDirection = tiltOptions.direction;\n    if (tiltDirection === _TiltDirection_js__WEBPACK_IMPORTED_MODULE_1__.TiltDirection.random) {\n      const index = Math.floor((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRandom)() * double),\n        minIndex = 0;\n      tiltDirection = index > minIndex ? _TiltDirection_js__WEBPACK_IMPORTED_MODULE_1__.TiltDirection.counterClockwise : _TiltDirection_js__WEBPACK_IMPORTED_MODULE_1__.TiltDirection.clockwise;\n    }\n    switch (tiltDirection) {\n      case _TiltDirection_js__WEBPACK_IMPORTED_MODULE_1__.TiltDirection.counterClockwise:\n      case \"counterClockwise\":\n        particle.tilt.status = _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.AnimationStatus.decreasing;\n        break;\n      case _TiltDirection_js__WEBPACK_IMPORTED_MODULE_1__.TiltDirection.clockwise:\n        particle.tilt.status = _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.AnimationStatus.increasing;\n        break;\n    }\n    const tiltAnimation = particle.options.tilt?.animation;\n    if (tiltAnimation?.enable) {\n      particle.tilt.decay = identity - (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(tiltAnimation.decay);\n      particle.tilt.velocity = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(tiltAnimation.speed) / maxAngle * this.container.retina.reduceFactor;\n      if (!tiltAnimation.sync) {\n        particle.tilt.velocity *= (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRandom)();\n      }\n    }\n  }\n  isEnabled(particle) {\n    const tiltAnimation = particle.options.tilt?.animation;\n    return !particle.destroyed && !particle.spawning && !!tiltAnimation?.enable;\n  }\n  loadOptions(options, ...sources) {\n    if (!options.tilt) {\n      options.tilt = new _Options_Classes_Tilt_js__WEBPACK_IMPORTED_MODULE_2__.Tilt();\n    }\n    for (const source of sources) {\n      options.tilt.load(source?.tilt);\n    }\n  }\n  async update(particle, delta) {\n    if (!this.isEnabled(particle) || !particle.tilt) {\n      return;\n    }\n    (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.updateAnimation)(particle, particle.tilt, false, _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.DestroyType.none, delta);\n    await Promise.resolve();\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/updater-tilt/./dist/browser/TiltUpdater.js?");

/***/ }),

/***/ "./dist/browser/index.js":
/*!*******************************!*\
  !*** ./dist/browser/index.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadTiltUpdater: () => (/* binding */ loadTiltUpdater)\n/* harmony export */ });\n/* harmony import */ var _TiltUpdater_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./TiltUpdater.js */ \"./dist/browser/TiltUpdater.js\");\n\nasync function loadTiltUpdater(engine, refresh = true) {\n  engine.checkVersion(\"3.8.1\");\n  await engine.addParticleUpdater(\"tilt\", container => {\n    return Promise.resolve(new _TiltUpdater_js__WEBPACK_IMPORTED_MODULE_0__.TiltUpdater(container));\n  }, refresh);\n}\n\n//# sourceURL=webpack://@tsparticles/updater-tilt/./dist/browser/index.js?");

/***/ }),

/***/ "@tsparticles/engine":
/*!*********************************************************************************************************************************!*\
  !*** external {"commonjs":"@tsparticles/engine","commonjs2":"@tsparticles/engine","amd":"@tsparticles/engine","root":"window"} ***!
  \*********************************************************************************************************************************/
/***/ ((module) => {

module.exports = __WEBPACK_EXTERNAL_MODULE__tsparticles_engine__;

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__("./dist/browser/index.js");
/******/ 	
/******/ 	return __webpack_exports__;
/******/ })()
;
});