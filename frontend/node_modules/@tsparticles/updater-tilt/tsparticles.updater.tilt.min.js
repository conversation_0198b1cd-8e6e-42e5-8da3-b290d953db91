/*! For license information please see tsparticles.updater.tilt.min.js.LICENSE.txt */
!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t(require("@tsparticles/engine"));else if("function"==typeof define&&define.amd)define(["@tsparticles/engine"],t);else{var i="object"==typeof exports?t(require("@tsparticles/engine")):t(e.window);for(var o in i)("object"==typeof exports?exports:e)[o]=i[o]}}(this,(e=>(()=>{var t={303:t=>{t.exports=e}},i={};function o(e){var n=i[e];if(void 0!==n)return n.exports;var a=i[e]={exports:{}};return t[e](a,a.exports,o),a.exports}o.d=(e,t)=>{for(var i in t)o.o(t,i)&&!o.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};o.r(n),o.d(n,{loadTiltUpdater:()=>u});var a,s=o(303);!function(e){e.clockwise="clockwise",e.counterClockwise="counter-clockwise",e.random="random"}(a||(a={}));class r{constructor(){this.enable=!1,this.speed=0,this.decay=0,this.sync=!1}load(e){(0,s.isNull)(e)||(void 0!==e.enable&&(this.enable=e.enable),void 0!==e.speed&&(this.speed=(0,s.setRangeValue)(e.speed)),void 0!==e.decay&&(this.decay=(0,s.setRangeValue)(e.decay)),void 0!==e.sync&&(this.sync=e.sync))}}class l extends s.ValueWithRandom{constructor(){super(),this.animation=new r,this.direction=a.clockwise,this.enable=!1,this.value=0}load(e){super.load(e),(0,s.isNull)(e)||(this.animation.load(e.animation),void 0!==e.direction&&(this.direction=e.direction),void 0!==e.enable&&(this.enable=e.enable))}}const c=2*Math.PI;class d{constructor(e){this.container=e}getTransformValues(e){const t=e.tilt?.enable&&e.tilt;return{b:t?Math.cos(t.value)*t.cosDirection:void 0,c:t?Math.sin(t.value)*t.sinDirection:void 0}}init(e){const t=e.options.tilt;if(!t)return;e.tilt={enable:t.enable,value:(0,s.degToRad)((0,s.getRangeValue)(t.value)),sinDirection:(0,s.getRandom)()>=s.half?1:-1,cosDirection:(0,s.getRandom)()>=s.half?1:-1,min:0,max:c};let i=t.direction;if(i===a.random){i=Math.floor(2*(0,s.getRandom)())>0?a.counterClockwise:a.clockwise}switch(i){case a.counterClockwise:case"counterClockwise":e.tilt.status=s.AnimationStatus.decreasing;break;case a.clockwise:e.tilt.status=s.AnimationStatus.increasing}const o=e.options.tilt?.animation;o?.enable&&(e.tilt.decay=1-(0,s.getRangeValue)(o.decay),e.tilt.velocity=(0,s.getRangeValue)(o.speed)/360*this.container.retina.reduceFactor,o.sync||(e.tilt.velocity*=(0,s.getRandom)()))}isEnabled(e){const t=e.options.tilt?.animation;return!e.destroyed&&!e.spawning&&!!t?.enable}loadOptions(e,...t){e.tilt||(e.tilt=new l);for(const i of t)e.tilt.load(i?.tilt)}async update(e,t){this.isEnabled(e)&&e.tilt&&((0,s.updateAnimation)(e,e.tilt,!1,s.DestroyType.none,t),await Promise.resolve())}}async function u(e,t=!0){e.checkVersion("3.8.1"),await e.addParticleUpdater("tilt",(e=>Promise.resolve(new d(e))),t)}return n})()));