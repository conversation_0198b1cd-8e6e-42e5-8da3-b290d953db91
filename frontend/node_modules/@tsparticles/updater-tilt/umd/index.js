(function (factory) {
    if (typeof module === "object" && typeof module.exports === "object") {
        var v = factory(require, exports);
        if (v !== undefined) module.exports = v;
    }
    else if (typeof define === "function" && define.amd) {
        define(["require", "exports", "./TiltUpdater.js"], factory);
    }
})(function (require, exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.loadTiltUpdater = loadTiltUpdater;
    const TiltUpdater_js_1 = require("./TiltUpdater.js");
    async function loadTiltUpdater(engine, refresh = true) {
        engine.checkVersion("3.8.1");
        await engine.addParticleUpdater("tilt", container => {
            return Promise.resolve(new TiltUpdater_js_1.TiltUpdater(container));
        }, refresh);
    }
});
