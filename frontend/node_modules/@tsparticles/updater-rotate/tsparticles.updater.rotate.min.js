/*! For license information please see tsparticles.updater.rotate.min.js.LICENSE.txt */
!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t(require("@tsparticles/engine"));else if("function"==typeof define&&define.amd)define(["@tsparticles/engine"],t);else{var o="object"==typeof exports?t(require("@tsparticles/engine")):t(e.window);for(var a in o)("object"==typeof exports?exports:e)[a]=o[a]}}(this,(e=>(()=>{var t={303:t=>{t.exports=e}},o={};function a(e){var i=o[e];if(void 0!==i)return i.exports;var n=o[e]={exports:{}};return t[e](n,n.exports,a),n.exports}a.d=(e,t)=>{for(var o in t)a.o(t,o)&&!a.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),a.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var i={};a.r(i),a.d(i,{loadRotateUpdater:()=>l});var n=a(303);class r{constructor(){this.enable=!1,this.speed=0,this.decay=0,this.sync=!1}load(e){(0,n.isNull)(e)||(void 0!==e.enable&&(this.enable=e.enable),void 0!==e.speed&&(this.speed=(0,n.setRangeValue)(e.speed)),void 0!==e.decay&&(this.decay=(0,n.setRangeValue)(e.decay)),void 0!==e.sync&&(this.sync=e.sync))}}class s extends n.ValueWithRandom{constructor(){super(),this.animation=new r,this.direction=n.RotateDirection.clockwise,this.path=!1,this.value=0}load(e){(0,n.isNull)(e)||(super.load(e),void 0!==e.direction&&(this.direction=e.direction),this.animation.load(e.animation),void 0!==e.path&&(this.path=e.path))}}const c=2*Math.PI;class d{constructor(e){this.container=e}init(e){const t=e.options.rotate;if(!t)return;e.rotate={enable:t.animation.enable,value:(0,n.degToRad)((0,n.getRangeValue)(t.value)),min:0,max:c},e.pathRotation=t.path;let o=t.direction;if(o===n.RotateDirection.random){o=Math.floor(2*(0,n.getRandom)())>0?n.RotateDirection.counterClockwise:n.RotateDirection.clockwise}switch(o){case n.RotateDirection.counterClockwise:case"counterClockwise":e.rotate.status=n.AnimationStatus.decreasing;break;case n.RotateDirection.clockwise:e.rotate.status=n.AnimationStatus.increasing}const a=t.animation;a.enable&&(e.rotate.decay=1-(0,n.getRangeValue)(a.decay),e.rotate.velocity=(0,n.getRangeValue)(a.speed)/360*this.container.retina.reduceFactor,a.sync||(e.rotate.velocity*=(0,n.getRandom)())),e.rotation=e.rotate.value}isEnabled(e){const t=e.options.rotate;return!!t&&(!e.destroyed&&!e.spawning&&(!!t.value||t.animation.enable||t.path))}loadOptions(e,...t){e.rotate||(e.rotate=new s);for(const o of t)e.rotate.load(o?.rotate)}update(e,t){this.isEnabled(e)&&(e.isRotating=!!e.rotate,e.rotate&&((0,n.updateAnimation)(e,e.rotate,!1,n.DestroyType.none,t),e.rotation=e.rotate.value))}}async function l(e,t=!0){e.checkVersion("3.8.1"),await e.addParticleUpdater("rotate",(e=>Promise.resolve(new d(e))),t)}return i})()));