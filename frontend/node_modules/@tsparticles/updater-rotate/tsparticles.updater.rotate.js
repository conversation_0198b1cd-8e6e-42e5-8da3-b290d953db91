/*!
 * Author : <PERSON>
 * MIT license: https://opensource.org/licenses/MIT
 * Demo / Generator : https://particles.js.org/
 * GitHub : https://www.github.com/matteobruni/tsparticles
 * How to use? : Check the GitHub README
 * v3.8.1
 */
/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory(require("@tsparticles/engine"));
	else if(typeof define === 'function' && define.amd)
		define(["@tsparticles/engine"], factory);
	else {
		var a = typeof exports === 'object' ? factory(require("@tsparticles/engine")) : factory(root["window"]);
		for(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];
	}
})(this, (__WEBPACK_EXTERNAL_MODULE__tsparticles_engine__) => {
return /******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./dist/browser/Options/Classes/Rotate.js":
/*!************************************************!*\
  !*** ./dist/browser/Options/Classes/Rotate.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Rotate: () => (/* binding */ Rotate)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _RotateAnimation_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./RotateAnimation.js */ \"./dist/browser/Options/Classes/RotateAnimation.js\");\n\n\nclass Rotate extends _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.ValueWithRandom {\n  constructor() {\n    super();\n    this.animation = new _RotateAnimation_js__WEBPACK_IMPORTED_MODULE_1__.RotateAnimation();\n    this.direction = _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.RotateDirection.clockwise;\n    this.path = false;\n    this.value = 0;\n  }\n  load(data) {\n    if ((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    super.load(data);\n    if (data.direction !== undefined) {\n      this.direction = data.direction;\n    }\n    this.animation.load(data.animation);\n    if (data.path !== undefined) {\n      this.path = data.path;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/updater-rotate/./dist/browser/Options/Classes/Rotate.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/RotateAnimation.js":
/*!*********************************************************!*\
  !*** ./dist/browser/Options/Classes/RotateAnimation.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RotateAnimation: () => (/* binding */ RotateAnimation)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n\nclass RotateAnimation {\n  constructor() {\n    this.enable = false;\n    this.speed = 0;\n    this.decay = 0;\n    this.sync = false;\n  }\n  load(data) {\n    if ((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    if (data.speed !== undefined) {\n      this.speed = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.setRangeValue)(data.speed);\n    }\n    if (data.decay !== undefined) {\n      this.decay = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.setRangeValue)(data.decay);\n    }\n    if (data.sync !== undefined) {\n      this.sync = data.sync;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/updater-rotate/./dist/browser/Options/Classes/RotateAnimation.js?");

/***/ }),

/***/ "./dist/browser/RotateUpdater.js":
/*!***************************************!*\
  !*** ./dist/browser/RotateUpdater.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RotateUpdater: () => (/* binding */ RotateUpdater)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Options_Classes_Rotate_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Options/Classes/Rotate.js */ \"./dist/browser/Options/Classes/Rotate.js\");\n\n\nconst double = 2,\n  doublePI = Math.PI * double,\n  identity = 1,\n  doublePIDeg = 360;\nclass RotateUpdater {\n  constructor(container) {\n    this.container = container;\n  }\n  init(particle) {\n    const rotateOptions = particle.options.rotate;\n    if (!rotateOptions) {\n      return;\n    }\n    particle.rotate = {\n      enable: rotateOptions.animation.enable,\n      value: (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.degToRad)((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(rotateOptions.value)),\n      min: 0,\n      max: doublePI\n    };\n    particle.pathRotation = rotateOptions.path;\n    let rotateDirection = rotateOptions.direction;\n    if (rotateDirection === _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.RotateDirection.random) {\n      const index = Math.floor((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRandom)() * double),\n        minIndex = 0;\n      rotateDirection = index > minIndex ? _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.RotateDirection.counterClockwise : _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.RotateDirection.clockwise;\n    }\n    switch (rotateDirection) {\n      case _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.RotateDirection.counterClockwise:\n      case \"counterClockwise\":\n        particle.rotate.status = _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.AnimationStatus.decreasing;\n        break;\n      case _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.RotateDirection.clockwise:\n        particle.rotate.status = _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.AnimationStatus.increasing;\n        break;\n    }\n    const rotateAnimation = rotateOptions.animation;\n    if (rotateAnimation.enable) {\n      particle.rotate.decay = identity - (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(rotateAnimation.decay);\n      particle.rotate.velocity = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(rotateAnimation.speed) / doublePIDeg * this.container.retina.reduceFactor;\n      if (!rotateAnimation.sync) {\n        particle.rotate.velocity *= (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRandom)();\n      }\n    }\n    particle.rotation = particle.rotate.value;\n  }\n  isEnabled(particle) {\n    const rotate = particle.options.rotate;\n    if (!rotate) {\n      return false;\n    }\n    return !particle.destroyed && !particle.spawning && (!!rotate.value || rotate.animation.enable || rotate.path);\n  }\n  loadOptions(options, ...sources) {\n    if (!options.rotate) {\n      options.rotate = new _Options_Classes_Rotate_js__WEBPACK_IMPORTED_MODULE_1__.Rotate();\n    }\n    for (const source of sources) {\n      options.rotate.load(source?.rotate);\n    }\n  }\n  update(particle, delta) {\n    if (!this.isEnabled(particle)) {\n      return;\n    }\n    particle.isRotating = !!particle.rotate;\n    if (!particle.rotate) {\n      return;\n    }\n    (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.updateAnimation)(particle, particle.rotate, false, _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.DestroyType.none, delta);\n    particle.rotation = particle.rotate.value;\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/updater-rotate/./dist/browser/RotateUpdater.js?");

/***/ }),

/***/ "./dist/browser/index.js":
/*!*******************************!*\
  !*** ./dist/browser/index.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadRotateUpdater: () => (/* binding */ loadRotateUpdater)\n/* harmony export */ });\n/* harmony import */ var _RotateUpdater_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./RotateUpdater.js */ \"./dist/browser/RotateUpdater.js\");\n\nasync function loadRotateUpdater(engine, refresh = true) {\n  engine.checkVersion(\"3.8.1\");\n  await engine.addParticleUpdater(\"rotate\", container => {\n    return Promise.resolve(new _RotateUpdater_js__WEBPACK_IMPORTED_MODULE_0__.RotateUpdater(container));\n  }, refresh);\n}\n\n//# sourceURL=webpack://@tsparticles/updater-rotate/./dist/browser/index.js?");

/***/ }),

/***/ "@tsparticles/engine":
/*!*********************************************************************************************************************************!*\
  !*** external {"commonjs":"@tsparticles/engine","commonjs2":"@tsparticles/engine","amd":"@tsparticles/engine","root":"window"} ***!
  \*********************************************************************************************************************************/
/***/ ((module) => {

module.exports = __WEBPACK_EXTERNAL_MODULE__tsparticles_engine__;

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__("./dist/browser/index.js");
/******/ 	
/******/ 	return __webpack_exports__;
/******/ })()
;
});