/*!
 * Author : <PERSON>
 * MIT license: https://opensource.org/licenses/MIT
 * Demo / Generator : https://particles.js.org/
 * GitHub : https://www.github.com/matteobruni/tsparticles
 * How to use? : Check the GitHub README
 * v3.8.1
 */
/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory(require("@tsparticles/engine"));
	else if(typeof define === 'function' && define.amd)
		define(["@tsparticles/engine"], factory);
	else {
		var a = typeof exports === 'object' ? factory(require("@tsparticles/engine")) : factory(root["window"]);
		for(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];
	}
})(this, (__WEBPACK_EXTERNAL_MODULE__tsparticles_engine__) => {
return /******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./dist/browser/Options/Classes/Roll.js":
/*!**********************************************!*\
  !*** ./dist/browser/Options/Classes/Roll.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Roll: () => (/* binding */ Roll)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _RollLight_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./RollLight.js */ \"./dist/browser/Options/Classes/RollLight.js\");\n/* harmony import */ var _RollMode_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../RollMode.js */ \"./dist/browser/RollMode.js\");\n\n\n\nclass Roll {\n  constructor() {\n    this.darken = new _RollLight_js__WEBPACK_IMPORTED_MODULE_1__.RollLight();\n    this.enable = false;\n    this.enlighten = new _RollLight_js__WEBPACK_IMPORTED_MODULE_1__.RollLight();\n    this.mode = _RollMode_js__WEBPACK_IMPORTED_MODULE_2__.RollMode.vertical;\n    this.speed = 25;\n  }\n  load(data) {\n    if ((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    if (data.backColor !== undefined) {\n      this.backColor = _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OptionsColor.create(this.backColor, data.backColor);\n    }\n    this.darken.load(data.darken);\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    this.enlighten.load(data.enlighten);\n    if (data.mode !== undefined) {\n      this.mode = data.mode;\n    }\n    if (data.speed !== undefined) {\n      this.speed = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.setRangeValue)(data.speed);\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/updater-roll/./dist/browser/Options/Classes/Roll.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/RollLight.js":
/*!***************************************************!*\
  !*** ./dist/browser/Options/Classes/RollLight.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RollLight: () => (/* binding */ RollLight)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n\nclass RollLight {\n  constructor() {\n    this.enable = false;\n    this.value = 0;\n  }\n  load(data) {\n    if ((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    if (data.value !== undefined) {\n      this.value = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.setRangeValue)(data.value);\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/updater-roll/./dist/browser/Options/Classes/RollLight.js?");

/***/ }),

/***/ "./dist/browser/RollMode.js":
/*!**********************************!*\
  !*** ./dist/browser/RollMode.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RollMode: () => (/* binding */ RollMode)\n/* harmony export */ });\nvar RollMode;\n(function (RollMode) {\n  RollMode[\"both\"] = \"both\";\n  RollMode[\"horizontal\"] = \"horizontal\";\n  RollMode[\"vertical\"] = \"vertical\";\n})(RollMode || (RollMode = {}));\n\n//# sourceURL=webpack://@tsparticles/updater-roll/./dist/browser/RollMode.js?");

/***/ }),

/***/ "./dist/browser/RollUpdater.js":
/*!*************************************!*\
  !*** ./dist/browser/RollUpdater.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RollUpdater: () => (/* binding */ RollUpdater)\n/* harmony export */ });\n/* harmony import */ var _Utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Utils.js */ \"./dist/browser/Utils.js\");\n/* harmony import */ var _Options_Classes_Roll_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Options/Classes/Roll.js */ \"./dist/browser/Options/Classes/Roll.js\");\n\n\nclass RollUpdater {\n  constructor(engine) {\n    this._engine = engine;\n  }\n  getTransformValues(particle) {\n    const roll = particle.roll?.enable && particle.roll,\n      rollHorizontal = roll && roll.horizontal,\n      rollVertical = roll && roll.vertical;\n    return {\n      a: rollHorizontal ? Math.cos(roll.angle) : undefined,\n      d: rollVertical ? Math.sin(roll.angle) : undefined\n    };\n  }\n  init(particle) {\n    (0,_Utils_js__WEBPACK_IMPORTED_MODULE_0__.initParticle)(this._engine, particle);\n  }\n  isEnabled(particle) {\n    const roll = particle.options.roll;\n    return !particle.destroyed && !particle.spawning && !!roll?.enable;\n  }\n  loadOptions(options, ...sources) {\n    if (!options.roll) {\n      options.roll = new _Options_Classes_Roll_js__WEBPACK_IMPORTED_MODULE_1__.Roll();\n    }\n    for (const source of sources) {\n      options.roll.load(source?.roll);\n    }\n  }\n  update(particle, delta) {\n    if (!this.isEnabled(particle)) {\n      return;\n    }\n    (0,_Utils_js__WEBPACK_IMPORTED_MODULE_0__.updateRoll)(particle, delta);\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/updater-roll/./dist/browser/RollUpdater.js?");

/***/ }),

/***/ "./dist/browser/Utils.js":
/*!*******************************!*\
  !*** ./dist/browser/Utils.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initParticle: () => (/* binding */ initParticle),\n/* harmony export */   updateRoll: () => (/* binding */ updateRoll)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _RollMode_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./RollMode.js */ \"./dist/browser/RollMode.js\");\n\n\nconst double = 2,\n  doublePI = Math.PI * double,\n  maxAngle = 360;\nfunction initParticle(engine, particle) {\n  const rollOpt = particle.options.roll;\n  if (!rollOpt?.enable) {\n    particle.roll = {\n      enable: false,\n      horizontal: false,\n      vertical: false,\n      angle: 0,\n      speed: 0\n    };\n    return;\n  }\n  particle.roll = {\n    enable: rollOpt.enable,\n    horizontal: rollOpt.mode === _RollMode_js__WEBPACK_IMPORTED_MODULE_1__.RollMode.horizontal || rollOpt.mode === _RollMode_js__WEBPACK_IMPORTED_MODULE_1__.RollMode.both,\n    vertical: rollOpt.mode === _RollMode_js__WEBPACK_IMPORTED_MODULE_1__.RollMode.vertical || rollOpt.mode === _RollMode_js__WEBPACK_IMPORTED_MODULE_1__.RollMode.both,\n    angle: (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRandom)() * doublePI,\n    speed: (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(rollOpt.speed) / maxAngle\n  };\n  if (rollOpt.backColor) {\n    particle.backColor = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.rangeColorToHsl)(engine, rollOpt.backColor);\n  } else if (rollOpt.darken.enable && rollOpt.enlighten.enable) {\n    const alterType = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRandom)() >= _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.half ? _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.AlterType.darken : _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.AlterType.enlighten;\n    particle.roll.alter = {\n      type: alterType,\n      value: (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(alterType === _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.AlterType.darken ? rollOpt.darken.value : rollOpt.enlighten.value)\n    };\n  } else if (rollOpt.darken.enable) {\n    particle.roll.alter = {\n      type: _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.AlterType.darken,\n      value: (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(rollOpt.darken.value)\n    };\n  } else if (rollOpt.enlighten.enable) {\n    particle.roll.alter = {\n      type: _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.AlterType.enlighten,\n      value: (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(rollOpt.enlighten.value)\n    };\n  }\n}\nfunction updateRoll(particle, delta) {\n  const roll = particle.options.roll,\n    data = particle.roll;\n  if (!data || !roll?.enable) {\n    return;\n  }\n  const speed = data.speed * delta.factor,\n    max = doublePI;\n  data.angle += speed;\n  if (data.angle > max) {\n    data.angle -= max;\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/updater-roll/./dist/browser/Utils.js?");

/***/ }),

/***/ "./dist/browser/index.js":
/*!*******************************!*\
  !*** ./dist/browser/index.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadRollUpdater: () => (/* binding */ loadRollUpdater)\n/* harmony export */ });\n/* harmony import */ var _RollUpdater_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./RollUpdater.js */ \"./dist/browser/RollUpdater.js\");\n\nasync function loadRollUpdater(engine, refresh = true) {\n  engine.checkVersion(\"3.8.1\");\n  await engine.addParticleUpdater(\"roll\", () => {\n    return Promise.resolve(new _RollUpdater_js__WEBPACK_IMPORTED_MODULE_0__.RollUpdater(engine));\n  }, refresh);\n}\n\n//# sourceURL=webpack://@tsparticles/updater-roll/./dist/browser/index.js?");

/***/ }),

/***/ "@tsparticles/engine":
/*!*********************************************************************************************************************************!*\
  !*** external {"commonjs":"@tsparticles/engine","commonjs2":"@tsparticles/engine","amd":"@tsparticles/engine","root":"window"} ***!
  \*********************************************************************************************************************************/
/***/ ((module) => {

module.exports = __WEBPACK_EXTERNAL_MODULE__tsparticles_engine__;

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__("./dist/browser/index.js");
/******/ 	
/******/ 	return __webpack_exports__;
/******/ })()
;
});