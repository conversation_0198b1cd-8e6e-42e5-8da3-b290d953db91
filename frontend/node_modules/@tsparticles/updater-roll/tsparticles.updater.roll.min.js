/*! For license information please see tsparticles.updater.roll.min.js.LICENSE.txt */
!function(e,l){if("object"==typeof exports&&"object"==typeof module)module.exports=l(require("@tsparticles/engine"));else if("function"==typeof define&&define.amd)define(["@tsparticles/engine"],l);else{var o="object"==typeof exports?l(require("@tsparticles/engine")):l(e.window);for(var t in o)("object"==typeof exports?exports:e)[t]=o[t]}}(this,(e=>(()=>{var l={303:l=>{l.exports=e}},o={};function t(e){var n=o[e];if(void 0!==n)return n.exports;var a=o[e]={exports:{}};return l[e](a,a.exports,t),a.exports}t.d=(e,l)=>{for(var o in l)t.o(l,o)&&!t.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:l[o]})},t.o=(e,l)=>Object.prototype.hasOwnProperty.call(e,l),t.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};t.r(n),t.d(n,{loadRollUpdater:()=>p});var a,r=t(303);!function(e){e.both="both",e.horizontal="horizontal",e.vertical="vertical"}(a||(a={}));const i=2*Math.PI;class s{constructor(){this.enable=!1,this.value=0}load(e){(0,r.isNull)(e)||(void 0!==e.enable&&(this.enable=e.enable),void 0!==e.value&&(this.value=(0,r.setRangeValue)(e.value)))}}class d{constructor(){this.darken=new s,this.enable=!1,this.enlighten=new s,this.mode=a.vertical,this.speed=25}load(e){(0,r.isNull)(e)||(void 0!==e.backColor&&(this.backColor=r.OptionsColor.create(this.backColor,e.backColor)),this.darken.load(e.darken),void 0!==e.enable&&(this.enable=e.enable),this.enlighten.load(e.enlighten),void 0!==e.mode&&(this.mode=e.mode),void 0!==e.speed&&(this.speed=(0,r.setRangeValue)(e.speed)))}}class c{constructor(e){this._engine=e}getTransformValues(e){const l=e.roll?.enable&&e.roll,o=l&&l.horizontal,t=l&&l.vertical;return{a:o?Math.cos(l.angle):void 0,d:t?Math.sin(l.angle):void 0}}init(e){!function(e,l){const o=l.options.roll;if(o?.enable)if(l.roll={enable:o.enable,horizontal:o.mode===a.horizontal||o.mode===a.both,vertical:o.mode===a.vertical||o.mode===a.both,angle:(0,r.getRandom)()*i,speed:(0,r.getRangeValue)(o.speed)/360},o.backColor)l.backColor=(0,r.rangeColorToHsl)(e,o.backColor);else if(o.darken.enable&&o.enlighten.enable){const e=(0,r.getRandom)()>=r.half?r.AlterType.darken:r.AlterType.enlighten;l.roll.alter={type:e,value:(0,r.getRangeValue)(e===r.AlterType.darken?o.darken.value:o.enlighten.value)}}else o.darken.enable?l.roll.alter={type:r.AlterType.darken,value:(0,r.getRangeValue)(o.darken.value)}:o.enlighten.enable&&(l.roll.alter={type:r.AlterType.enlighten,value:(0,r.getRangeValue)(o.enlighten.value)});else l.roll={enable:!1,horizontal:!1,vertical:!1,angle:0,speed:0}}(this._engine,e)}isEnabled(e){const l=e.options.roll;return!e.destroyed&&!e.spawning&&!!l?.enable}loadOptions(e,...l){e.roll||(e.roll=new d);for(const o of l)e.roll.load(o?.roll)}update(e,l){this.isEnabled(e)&&function(e,l){const o=e.options.roll,t=e.roll;if(!t||!o?.enable)return;const n=t.speed*l.factor,a=i;t.angle+=n,t.angle>a&&(t.angle-=a)}(e,l)}}async function p(e,l=!0){e.checkVersion("3.8.1"),await e.addParticleUpdater("roll",(()=>Promise.resolve(new c(e))),l)}return n})()));