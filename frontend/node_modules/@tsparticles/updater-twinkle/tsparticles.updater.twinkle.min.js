/*! For license information please see tsparticles.updater.twinkle.min.js.LICENSE.txt */
!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t(require("@tsparticles/engine"));else if("function"==typeof define&&define.amd)define(["@tsparticles/engine"],t);else{var o="object"==typeof exports?t(require("@tsparticles/engine")):t(e.window);for(var i in o)("object"==typeof exports?exports:e)[i]=o[i]}}(this,(e=>(()=>{var t={303:t=>{t.exports=e}},o={};function i(e){var r=o[e];if(void 0!==r)return r.exports;var n=o[e]={exports:{}};return t[e](n,n.exports,i),n.exports}i.d=(e,t)=>{for(var o in t)i.o(t,o)&&!i.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};i.r(r),i.d(r,{loadTwinkleUpdater:()=>c});var n=i(303);class s{constructor(){this.enable=!1,this.frequency=.05,this.opacity=1}load(e){(0,n.isNull)(e)||(void 0!==e.color&&(this.color=n.OptionsColor.create(this.color,e.color)),void 0!==e.enable&&(this.enable=e.enable),void 0!==e.frequency&&(this.frequency=e.frequency),void 0!==e.opacity&&(this.opacity=(0,n.setRangeValue)(e.opacity)))}}class l{constructor(){this.lines=new s,this.particles=new s}load(e){(0,n.isNull)(e)||(this.lines.load(e.lines),this.particles.load(e.particles))}}class a{constructor(e){this._engine=e}getColorStyles(e,t,o,i){const r=e.options.twinkle;if(!r)return{};const s=r.particles,l=s.enable&&(0,n.getRandom)()<s.frequency,a=e.options.zIndex,c=(1-e.zIndexFactor)**a.opacityRate,p=l?(0,n.getRangeValue)(s.opacity)*c:i,d=(0,n.rangeColorToHsl)(this._engine,s.color),u=d?(0,n.getStyleFromHsl)(d,p):void 0,f={},y=l&&u;return f.fill=y?u:void 0,f.stroke=y?u:void 0,f}async init(){await Promise.resolve()}isEnabled(e){const t=e.options.twinkle;return!!t&&t.particles.enable}loadOptions(e,...t){e.twinkle||(e.twinkle=new l);for(const o of t)e.twinkle.load(o?.twinkle)}async update(){await Promise.resolve()}}async function c(e,t=!0){e.checkVersion("3.8.1"),await e.addParticleUpdater("twinkle",(()=>Promise.resolve(new a(e))),t)}return r})()));