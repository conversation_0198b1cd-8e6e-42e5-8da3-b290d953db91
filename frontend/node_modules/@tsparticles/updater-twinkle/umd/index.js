(function (factory) {
    if (typeof module === "object" && typeof module.exports === "object") {
        var v = factory(require, exports);
        if (v !== undefined) module.exports = v;
    }
    else if (typeof define === "function" && define.amd) {
        define(["require", "exports", "./TwinkleUpdater.js"], factory);
    }
})(function (require, exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.loadTwinkleUpdater = loadTwinkleUpdater;
    const TwinkleUpdater_js_1 = require("./TwinkleUpdater.js");
    async function loadTwinkleUpdater(engine, refresh = true) {
        engine.checkVersion("3.8.1");
        await engine.addParticleUpdater("twinkle", () => {
            return Promise.resolve(new TwinkleUpdater_js_1.TwinkleUpdater(engine));
        }, refresh);
    }
});
