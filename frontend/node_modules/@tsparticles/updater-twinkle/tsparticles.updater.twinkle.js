/*!
 * Author : <PERSON>
 * MIT license: https://opensource.org/licenses/MIT
 * Demo / Generator : https://particles.js.org/
 * GitHub : https://www.github.com/matteobruni/tsparticles
 * How to use? : Check the GitHub README
 * v3.8.1
 */
/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory(require("@tsparticles/engine"));
	else if(typeof define === 'function' && define.amd)
		define(["@tsparticles/engine"], factory);
	else {
		var a = typeof exports === 'object' ? factory(require("@tsparticles/engine")) : factory(root["window"]);
		for(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];
	}
})(this, (__WEBPACK_EXTERNAL_MODULE__tsparticles_engine__) => {
return /******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./dist/browser/Options/Classes/Twinkle.js":
/*!*************************************************!*\
  !*** ./dist/browser/Options/Classes/Twinkle.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Twinkle: () => (/* binding */ Twinkle)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _TwinkleValues_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./TwinkleValues.js */ \"./dist/browser/Options/Classes/TwinkleValues.js\");\n\n\nclass Twinkle {\n  constructor() {\n    this.lines = new _TwinkleValues_js__WEBPACK_IMPORTED_MODULE_1__.TwinkleValues();\n    this.particles = new _TwinkleValues_js__WEBPACK_IMPORTED_MODULE_1__.TwinkleValues();\n  }\n  load(data) {\n    if ((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    this.lines.load(data.lines);\n    this.particles.load(data.particles);\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/updater-twinkle/./dist/browser/Options/Classes/Twinkle.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/TwinkleValues.js":
/*!*******************************************************!*\
  !*** ./dist/browser/Options/Classes/TwinkleValues.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TwinkleValues: () => (/* binding */ TwinkleValues)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n\nclass TwinkleValues {\n  constructor() {\n    this.enable = false;\n    this.frequency = 0.05;\n    this.opacity = 1;\n  }\n  load(data) {\n    if ((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    if (data.color !== undefined) {\n      this.color = _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.OptionsColor.create(this.color, data.color);\n    }\n    if (data.enable !== undefined) {\n      this.enable = data.enable;\n    }\n    if (data.frequency !== undefined) {\n      this.frequency = data.frequency;\n    }\n    if (data.opacity !== undefined) {\n      this.opacity = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.setRangeValue)(data.opacity);\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/updater-twinkle/./dist/browser/Options/Classes/TwinkleValues.js?");

/***/ }),

/***/ "./dist/browser/TwinkleUpdater.js":
/*!****************************************!*\
  !*** ./dist/browser/TwinkleUpdater.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TwinkleUpdater: () => (/* binding */ TwinkleUpdater)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Options_Classes_Twinkle_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Options/Classes/Twinkle.js */ \"./dist/browser/Options/Classes/Twinkle.js\");\n\n\nclass TwinkleUpdater {\n  constructor(engine) {\n    this._engine = engine;\n  }\n  getColorStyles(particle, context, radius, opacity) {\n    const pOptions = particle.options,\n      twinkleOptions = pOptions.twinkle;\n    if (!twinkleOptions) {\n      return {};\n    }\n    const twinkle = twinkleOptions.particles,\n      twinkling = twinkle.enable && (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRandom)() < twinkle.frequency,\n      zIndexOptions = particle.options.zIndex,\n      zOffset = 1,\n      zOpacityFactor = (zOffset - particle.zIndexFactor) ** zIndexOptions.opacityRate,\n      twinklingOpacity = twinkling ? (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(twinkle.opacity) * zOpacityFactor : opacity,\n      twinkleRgb = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.rangeColorToHsl)(this._engine, twinkle.color),\n      twinkleStyle = twinkleRgb ? (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getStyleFromHsl)(twinkleRgb, twinklingOpacity) : undefined,\n      res = {},\n      needsTwinkle = twinkling && twinkleStyle;\n    res.fill = needsTwinkle ? twinkleStyle : undefined;\n    res.stroke = needsTwinkle ? twinkleStyle : undefined;\n    return res;\n  }\n  async init() {\n    await Promise.resolve();\n  }\n  isEnabled(particle) {\n    const pOptions = particle.options,\n      twinkleOptions = pOptions.twinkle;\n    if (!twinkleOptions) {\n      return false;\n    }\n    return twinkleOptions.particles.enable;\n  }\n  loadOptions(options, ...sources) {\n    if (!options.twinkle) {\n      options.twinkle = new _Options_Classes_Twinkle_js__WEBPACK_IMPORTED_MODULE_1__.Twinkle();\n    }\n    for (const source of sources) {\n      options.twinkle.load(source?.twinkle);\n    }\n  }\n  async update() {\n    await Promise.resolve();\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/updater-twinkle/./dist/browser/TwinkleUpdater.js?");

/***/ }),

/***/ "./dist/browser/index.js":
/*!*******************************!*\
  !*** ./dist/browser/index.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadTwinkleUpdater: () => (/* binding */ loadTwinkleUpdater)\n/* harmony export */ });\n/* harmony import */ var _TwinkleUpdater_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./TwinkleUpdater.js */ \"./dist/browser/TwinkleUpdater.js\");\n\nasync function loadTwinkleUpdater(engine, refresh = true) {\n  engine.checkVersion(\"3.8.1\");\n  await engine.addParticleUpdater(\"twinkle\", () => {\n    return Promise.resolve(new _TwinkleUpdater_js__WEBPACK_IMPORTED_MODULE_0__.TwinkleUpdater(engine));\n  }, refresh);\n}\n\n//# sourceURL=webpack://@tsparticles/updater-twinkle/./dist/browser/index.js?");

/***/ }),

/***/ "@tsparticles/engine":
/*!*********************************************************************************************************************************!*\
  !*** external {"commonjs":"@tsparticles/engine","commonjs2":"@tsparticles/engine","amd":"@tsparticles/engine","root":"window"} ***!
  \*********************************************************************************************************************************/
/***/ ((module) => {

module.exports = __WEBPACK_EXTERNAL_MODULE__tsparticles_engine__;

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__("./dist/browser/index.js");
/******/ 	
/******/ 	return __webpack_exports__;
/******/ })()
;
});