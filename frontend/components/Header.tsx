'use client'

import Link from 'next/link'
import Image from 'next/image'
import { useState, useEffect } from 'react'

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)

  // Handle scroll effect for sticky header
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setIsScrolled(true)
      } else {
        setIsScrolled(false)
      }
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  return (
    <header
      className={`bg-white sticky top-0 z-50 transition-all duration-200 ${
        isScrolled ? 'shadow-lg py-2' : 'shadow-md py-3'
      }`}
    >
      <div className="container">
        <div className="flex justify-between items-center">
          <Link href="/" className="flex items-center space-x-3">
            <div className={`relative transition-all duration-200 ${isScrolled ? 'w-10 h-10' : 'w-12 h-12'}`}>
              <Image
                src="/logo.png"
                alt="Northern Nepalese United FC Logo"
                fill
                className="object-contain"
                priority
              />
            </div>
            <div>
              <span className="text-xl font-bold text-primary-800 hidden sm:inline">Northern Nepalese United FC</span>
              <span className="text-xl font-bold text-primary-800 sm:hidden">NNUFC</span>
            </div>
          </Link>

          {/* Mobile menu button */}
          <button
            className="md:hidden p-2 text-primary-800 focus:outline-none focus:ring-2 focus:ring-primary-400 rounded-lg"
            onClick={toggleMenu}
            aria-label="Toggle menu"
            aria-expanded={isMenuOpen}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              className="h-6 w-6"
            >
              {isMenuOpen ? (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              ) : (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              )}
            </svg>
          </button>

          {/* Desktop navigation */}
          <nav className="hidden md:flex space-x-1 lg:space-x-6">
            <NavLink href="/">Home</NavLink>
            <NavLink href="/about">About</NavLink>
            <NavLink href="/team">Team</NavLink>
            <NavLink href="/junior-academy">Junior Academy</NavLink>
            <NavLink href="/news">News</NavLink>
            <NavLink href="/events">Events</NavLink>
            <NavLink href="/sponsors">Sponsors</NavLink>
            <NavLink href="/gallery">Gallery</NavLink>
            <NavLink href="/contact">Contact</NavLink>
          </nav>
        </div>

        {/* Mobile navigation - slide down animation */}
        <div
          className={`md:hidden overflow-hidden transition-all duration-300 ease-in-out ${
            isMenuOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
          }`}
        >
          <nav className="mt-4 space-y-2 border-t border-neutral-200 pt-3">
            <MobileNavLink href="/" onClick={() => setIsMenuOpen(false)}>Home</MobileNavLink>
            <MobileNavLink href="/about" onClick={() => setIsMenuOpen(false)}>About</MobileNavLink>
            <MobileNavLink href="/team" onClick={() => setIsMenuOpen(false)}>Team</MobileNavLink>
            <MobileNavLink href="/junior-academy" onClick={() => setIsMenuOpen(false)}>Junior Academy</MobileNavLink>
            <MobileNavLink href="/news" onClick={() => setIsMenuOpen(false)}>News</MobileNavLink>
            <MobileNavLink href="/events" onClick={() => setIsMenuOpen(false)}>Events</MobileNavLink>
            <MobileNavLink href="/sponsors" onClick={() => setIsMenuOpen(false)}>Sponsors</MobileNavLink>
            <MobileNavLink href="/gallery" onClick={() => setIsMenuOpen(false)}>Gallery</MobileNavLink>
            <MobileNavLink href="/contact" onClick={() => setIsMenuOpen(false)}>Contact</MobileNavLink>
          </nav>
        </div>
      </div>
    </header>
  )
}

// Desktop Navigation Link Component
function NavLink({ href, children }: { href: string; children: React.ReactNode }) {
  return (
    <Link
      href={href}
      className="px-3 py-2 rounded-lg font-medium text-primary-700 transition-colors hover:bg-primary-50 hover:text-primary-900"
    >
      {children}
    </Link>
  )
}

// Mobile Navigation Link Component
function MobileNavLink({ href, onClick, children }: {
  href: string;
  onClick: () => void;
  children: React.ReactNode
}) {
  return (
    <Link
      href={href}
      className="block py-2 px-1 text-primary-700 hover:bg-primary-50 hover:text-primary-900 rounded-lg font-medium transition-colors"
      onClick={onClick}
    >
      {children}
    </Link>
  )
}
