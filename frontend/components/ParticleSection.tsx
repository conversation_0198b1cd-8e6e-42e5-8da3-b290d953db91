'use client';

import React, { useCallback } from 'react';
import Particles from '@tsparticles/react';
import type { Engine, ISourceOptions } from '@tsparticles/engine'; // Changed import source, removed Container
// import { loadLinksPreset } from 'tsparticles-preset-links'; // A common preset - Removing to simplify

interface ParticleSectionProps {
  children: React.ReactNode;
  className?: string;
  particlesOptions?: ISourceOptions; // Allow custom options
}

const ParticleSection: React.FC<ParticleSectionProps> = ({
  children,
  className = '',
  particlesOptions,
}) => {
  const customInit = useCallback(async (_engine: Engine) => {
    // console.log(engine); // For debugging if needed
    // Engine is available, presets can be loaded here if needed,
    // but for now, we'll define all options directly.
    // await loadLinksPreset(engine); 
  }, []);
  // const particlesLoaded = useCallback(async (_container?: Container) => { 
  //   // await console.log(_container); 
  // }, []);

  const defaultOptions: ISourceOptions = {
    // preset: 'links', // Removing preset and defining manually 
    background: {
      color: {
        value: 'transparent', 
      },
    },
    particles: {
      color: {
        value: '#cccccc', 
      },
      links: {
        color: '#dddddd', 
        distance: 150,
        enable: true,
        opacity: 0.2, 
        width: 1,
      },
      move: {
        direction: 'none',
        enable: true,
        outModes: {
          default: 'bounce',
        },
        random: false,
        speed: 0.5, 
        straight: false,
      },
      number: {
        density: {
          enable: true,
          // area: 1000, // 'area' might be deprecated or part of the preset
        },
        value: 30, 
      },
      opacity: {
        value: 0.3, // Make particles subtle
      },
      shape: {
        type: 'circle',
      },
      size: {
        value: { min: 1, max: 3 }, // Small particles
      },
    },
    detectRetina: true,
  };

  const options = particlesOptions || defaultOptions;

  return (
    <div className={`relative ${className}`}>
      <Particles
        id={`tsparticles-${Math.random().toString(36).substring(7)}`} // Unique ID for each instance
        // init={customInit} // Temporarily removed to test type issue
        // loaded={particlesLoaded} // Use if you need to interact with the container
        options={options}
        className="absolute inset-0 z-0" // Position behind content
      />
      <div className="relative z-10">{children}</div> {/* Content on top */}
    </div>
  );
};

export default ParticleSection;