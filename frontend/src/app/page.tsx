import Image from "next/image";
import Link from "next/link";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { client } from "@/lib/sanity";
import { latestNewsQuery, upcomingEventsQuery, sponsorsQuery } from "@/lib/queries";
import { urlForImage } from "@/lib/sanity";

// Define types for our Sanity data
interface SanityImage {
  asset: {
    _ref: string;
  };
  // other possible image fields
}

interface NewsArticle {
  _id: string;
  title: string;
  publishedAt: string;
  summary?: string;
  mainImage?: SanityImage;
  slug: {
    current: string;
  };
}

interface Event {
  _id: string;
  title: string;
  date: string;
  eventTime?: string;
  location?: string;
  eventType?: string;
  opponent?: string;
  homeOrAway?: 'home' | 'away';
  slug: {
    current: string;
  };
}

interface Sponsor {
  _id: string;
  name: string;
  logo?: SanityImage;
}

// This makes the page dynamic and will revalidate every 60 seconds
export const revalidate = 60;

async function getLatestNews() {
  return await client.fetch(latestNewsQuery) as NewsArticle[];
}

async function getUpcomingEvents() {
  return await client.fetch(upcomingEventsQuery) as Event[];
}

async function getSponsors() {
  return await client.fetch(sponsorsQuery) as Sponsor[];
}

export default async function Home() {
  // Fetch data from Sanity
  const [latestNews, upcomingEvents, sponsors] = await Promise.all([
    getLatestNews(),
    getUpcomingEvents(),
    getSponsors(),
  ]);
  return (
    <>
      <Header />
      <main className="overflow-hidden">
        {/* Hero Section - Enhanced with background image and more dynamic content */}
        <section className="relative min-h-[90vh] flex items-center">
          <div className="absolute inset-0 z-0">
            <Image 
              src="/teamphoto.jpg" 
              alt="Football team" 
              fill 
              priority
              className="object-cover brightness-75" 
            />
          </div>
          
          {/* Dark gradient from bottom for text contrast */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-primary-900/40 to-transparent z-[1]"></div>
          
          <div className="container relative z-10 text-white">
            <div className="max-w-3xl mx-auto md:mx-0">
              <div className="animate-fade-in-up backdrop-blur-sm bg-primary-900/30 p-8 rounded-lg inline-block">
                <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight drop-shadow-md">
                  Northern Nepalese United <span className="text-yellow-400">FC</span>
                </h1>
                <p className="text-xl md:text-2xl mb-10 text-gray-100 drop-shadow-md">
                  Bringing together passion, community and excellence in football
                </p>
                <div className="flex flex-wrap gap-5">
                  <Link
                    href="/about"
                    className="bg-white text-primary-800 px-8 py-4 rounded-lg font-semibold hover:bg-yellow-400 hover:text-primary-900 transition-all duration-300 shadow-lg transform hover:-translate-y-1"
                  >
                    Our Story
                  </Link>
                  <Link
                    href="/contact"
                    className="bg-transparent border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-primary-800 transition-all duration-300 transform hover:-translate-y-1"
                  >
                    Join the Team
                  </Link>
                </div>
              </div>
            </div>
          </div>
          <div className="absolute bottom-10 left-0 right-0 text-center z-10">
            <div className="animate-bounce inline-block">
              <svg className="w-8 h-8 text-white filter drop-shadow-md" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
              </svg>
            </div>
          </div>
        </section>

        {/* Match Highlight Section - New dynamic section */}
        <section className="py-10 bg-primary-900 text-white overflow-hidden">
          <div className="container">
            <div className="flex flex-col md:flex-row items-center justify-between">
              <div className="text-center md:text-left mb-6 md:mb-0">
                <p className="text-yellow-400 text-lg font-semibold">Next Match</p>
                {upcomingEvents && upcomingEvents.length > 0 ? (
                  <div className="mt-2">
                    <h3 className="text-2xl md:text-4xl font-bold">NNUFC vs {upcomingEvents[0].opponent || 'TBA'}</h3>
                    <p className="text-lg opacity-90 mt-1">
                      {new Date(upcomingEvents[0].date).toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric' })} 
                      {upcomingEvents[0].eventTime && ` - ${upcomingEvents[0].eventTime}`}
                    </p>
                  </div>
                ) : (
                  <p className="text-xl mt-2">No upcoming matches</p>
                )}
              </div>
              <Link 
                href={upcomingEvents && upcomingEvents.length > 0 ? `/events/${upcomingEvents[0].slug.current}` : "/events"} 
                className="bg-yellow-400 text-primary-900 px-6 py-3 rounded font-semibold hover:bg-yellow-300 transition-colors shadow-md"
              >
                Match Details
              </Link>
            </div>
          </div>
        </section>

        {/* Latest News Section - Enhanced with better cards */}
        <section className="py-20 bg-gradient-to-r from-primary-900/5 to-primary-800/5">
          <div className="container">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold mb-4">Latest News</h2>
              <div className="w-24 h-1 bg-primary-600 mx-auto"></div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {latestNews && latestNews.length > 0 ? (
                latestNews.map((article: NewsArticle) => (
                  <div key={article._id} className="group bg-white rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                    <div className="relative h-56 overflow-hidden">
                      {article.mainImage ? (
                        <Image
                          src={urlForImage(article.mainImage).url()}
                          alt={article.title}
                          fill
                          className="object-cover transition-transform duration-500 group-hover:scale-110"
                        />
                      ) : (
                        <div className="h-full bg-gray-200 flex items-center justify-center">
                          <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                          </svg>
                        </div>
                      )}
                      <div className="absolute top-3 right-3 bg-primary-600 text-white text-xs px-3 py-1 rounded-full">
                        News
                      </div>
                    </div>
                    <div className="p-6">
                      <p className="text-gray-500 text-sm mb-2">
                        {new Date(article.publishedAt).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })}
                      </p>
                      <h3 className="text-xl font-bold mb-3 line-clamp-2">{article.title}</h3>
                      {article.summary && (
                        <p className="text-gray-700 mb-4 line-clamp-3">{article.summary}</p>
                      )}
                      <Link
                        href={`/news/${article.slug.current}`}
                        className="inline-flex items-center text-primary-600 font-semibold hover:text-primary-800 transition-colors"
                      >
                        Read Article
                        <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                        </svg>
                      </Link>
                    </div>
                  </div>
                ))
              ) : (
                <div className="col-span-3 text-center py-8 bg-white rounded-lg shadow-sm">
                  <svg className="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                  </svg>
                  <p className="text-gray-600 text-lg">No news articles available yet.</p>
                  <p className="mt-2 text-gray-500">Check back soon for updates!</p>
                </div>
              )}
            </div>

            <div className="text-center mt-12">
              <Link
                href="/news"
                className="bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-8 rounded-lg inline-flex items-center transition-colors duration-300"
              >
                All News
                <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                </svg>
              </Link>
            </div>
          </div>
        </section>

        {/* Upcoming Events Section - Enhanced with timeline style */}
        <section className="py-20 bg-white">
          <div className="container">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold mb-4">Upcoming Events</h2>
              <div className="w-24 h-1 bg-primary-600 mx-auto"></div>
            </div>

            <div className="max-w-4xl mx-auto">
              {upcomingEvents && upcomingEvents.length > 0 ? (
                <div className="space-y-8">
                  {upcomingEvents.map((event: Event, index) => (
                    <div key={event._id} className="group relative">
                      {/* Timeline connector */}
                      {index < upcomingEvents.length - 1 && (
                        <div className="absolute top-14 bottom-0 left-[22px] w-1 bg-gray-200 z-0"></div>
                      )}
                      <div className="flex gap-6 relative z-10">
                        <div className="relative min-w-[46px]">
                          <div className="w-12 h-12 rounded-full bg-primary-600 text-white flex items-center justify-center group-hover:bg-primary-800 transition-colors">
                            <span className="font-bold">{new Date(event.date).getDate()}</span>
                          </div>
                        </div>
                        <div className="bg-gray-50 rounded-xl p-6 shadow-sm w-full hover:shadow-md transition-shadow">
                          <div className="flex flex-col md:flex-row justify-between md:items-center mb-3">
                            <h3 className="text-xl font-bold">{event.title}</h3>
                            {event.eventType && (
                              <span className="bg-primary-100 text-primary-800 text-xs px-3 py-1 rounded-full uppercase font-semibold md:ml-2 mt-2 md:mt-0 inline-block md:inline">
                                {event.eventType}
                              </span>
                            )}
                          </div>
                          
                          <div className="flex flex-col md:flex-row md:items-center text-gray-600 gap-1 md:gap-4 mb-3">
                            <p className="flex items-center">
                              <svg className="w-4 h-4 mr-2 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                              </svg>
                              {new Date(event.date).toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric' })}
                            </p>
                            {event.eventTime && (
                              <p className="flex items-center">
                                <svg className="w-4 h-4 mr-2 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                {event.eventTime}
                              </p>
                            )}
                            {event.location && (
                              <p className="flex items-center">
                                <svg className="w-4 h-4 mr-2 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                {event.location}
                              </p>
                            )}
                          </div>

                          {event.opponent && (
                            <div className="bg-gray-100 p-3 rounded-lg mb-4">
                              <p className="text-gray-800 font-medium flex items-center">
                                <svg className="w-5 h-5 mr-2 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                {event.homeOrAway === 'home' ? 'Home game vs ' : 'Away game vs '}{event.opponent}
                              </p>
                            </div>
                          )}

                          <Link
                            href={`/events/${event.slug.current}`}
                            className="inline-flex items-center text-primary-600 font-semibold hover:text-primary-800 transition-colors"
                          >
                            View Details
                            <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                            </svg>
                          </Link>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12 bg-gray-50 rounded-xl">
                  <svg className="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                  <p className="text-gray-600 text-lg">No upcoming events scheduled yet.</p>
                  <p className="mt-2 text-gray-500">Check back soon for updates!</p>
                </div>
              )}
            </div>

            <div className="text-center mt-12">
              <Link
                href="/events"
                className="bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-8 rounded-lg inline-flex items-center transition-colors duration-300"
              >
                Calendar
                <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                </svg>
              </Link>
            </div>
          </div>
        </section>

        {/* Team Highlights Section - New section */}
        <section className="relative py-20 bg-primary-900 text-white">
          <div className="absolute inset-0 bg-pattern opacity-10"></div>
          <div className="container relative z-10">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold mb-4">Team Highlights</h2>
              <div className="w-24 h-1 bg-yellow-400 mx-auto"></div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="bg-primary-800/60 backdrop-blur-sm p-8 rounded-xl shadow-lg transform hover:-translate-y-2 transition-all duration-300">
                <div className="bg-yellow-400 w-16 h-16 rounded-full flex items-center justify-center mb-6 mx-auto">
                  <svg className="w-8 h-8 text-primary-900" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-4 text-center">Community</h3>
                <p className="text-gray-300 text-center">
                  Bringing together the Nepalese community through the beautiful game of football.
                </p>
              </div>
              
              <div className="bg-primary-800/60 backdrop-blur-sm p-8 rounded-xl shadow-lg transform hover:-translate-y-2 transition-all duration-300">
                <div className="bg-yellow-400 w-16 h-16 rounded-full flex items-center justify-center mb-6 mx-auto">
                  <svg className="w-8 h-8 text-primary-900" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-4 text-center">Passion</h3>
                <p className="text-gray-300 text-center">
                  Driven by our love for football and determination to excel on the field.
                </p>
              </div>
              
              <div className="bg-primary-800/60 backdrop-blur-sm p-8 rounded-xl shadow-lg transform hover:-translate-y-2 transition-all duration-300">
                <div className="bg-yellow-400 w-16 h-16 rounded-full flex items-center justify-center mb-6 mx-auto">
                  <svg className="w-8 h-8 text-primary-900" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-4 text-center">Teamwork</h3>
                <p className="text-gray-300 text-center">
                  Uniting diverse talents and skills to achieve greatness together as one team.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Sponsors Section - Enhanced with better layout */}
        <section className="py-20 bg-gradient-to-r from-primary-900/5 to-primary-800/5">
          <div className="container">
            <div className="text-center mb-12">
              <p className="text-primary-600 font-semibold mb-2">OUR PARTNERS</p>
              <h2 className="text-4xl font-bold mb-4">Official Sponsors</h2>
              <div className="w-24 h-1 bg-primary-600 mx-auto mb-6"></div>
              <p className="text-gray-600 max-w-2xl mx-auto">
                We&apos;re proud to be supported by these amazing organizations that help make our club&apos;s vision a reality
              </p>
            </div>

            <div className="bg-white rounded-2xl shadow-sm p-8 md:p-12">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-8 md:gap-12 items-center">
                {sponsors && sponsors.length > 0 ? (
                  sponsors.map((sponsor: Sponsor) => (
                    <div key={sponsor._id} className="flex justify-center group">
                      {sponsor.logo ? (
                        <div className="h-24 w-48 relative grayscale hover:grayscale-0 transition-all duration-500 transform hover:scale-110">
                          <Image
                            src={urlForImage(sponsor.logo).url()}
                            alt={sponsor.name}
                            fill
                            className="object-contain"
                          />
                        </div>
                      ) : (
                        <div className="h-24 w-48 bg-gray-100 rounded-lg flex items-center justify-center p-2 group-hover:bg-gray-200 transition-colors">
                          <p className="text-center font-semibold text-gray-700">{sponsor.name}</p>
                        </div>
                      )}
                    </div>
                  ))
                ) : (
                  <div className="col-span-4 text-center py-8">
                    <svg className="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                    <p className="text-gray-600 text-lg">No sponsors available yet.</p>
                    <p className="mt-2 text-gray-500">Check back soon for updates!</p>
                  </div>
                )}
              </div>
            </div>

            <div className="text-center mt-12">
              <Link
                href="/sponsors"
                className="inline-flex items-center gap-2 font-semibold text-primary-600 hover:text-primary-800 transition-colors"
              >
                Become a Sponsor
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                </svg>
              </Link>
            </div>
          </div>
        </section>

        {/* Call to Action Section - New section */}
        <section className="bg-gradient-to-r from-primary-800 to-primary-900 py-10">
          <div className="container">
            <div className="flex flex-col md:flex-row items-center justify-between max-w-5xl mx-auto">
              <div className="text-white mb-6 md:mb-0 text-center md:text-left">
                <h2 className="text-2xl font-bold mb-2">Ready to Join Our Team?</h2>
                <p className="text-sm text-gray-300 max-w-xl">
                  Whether you&apos;re a player, supporter, or potential sponsor, we&apos;d love to welcome you to the NNUFC family.
                </p>
              </div>
              <div className="flex gap-3">
                <Link
                  href="/contact"
                  className="bg-white text-primary-800 px-5 py-2 rounded-lg font-semibold hover:bg-yellow-400 transition-colors shadow-md text-sm"
                >
                  Contact Us
                </Link>
                <Link
                  href="/membership"
                  className="bg-transparent border-2 border-white text-white px-5 py-2 rounded-lg font-semibold hover:bg-white hover:text-primary-800 transition-colors text-sm"
                >
                  Membership
                </Link>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
}
