@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Base colors */
  --background: #f8f9fa; /* Off-white for a softer look */
  --foreground: #212529; /* Dark gray for text, good contrast */

  /* New Primary Color Palette (Modern Blue) */
  --primary-50: #e6f0ff;
  --primary-100: #b3d1ff;
  --primary-200: #80b3ff;
  --primary-300: #4d94ff;
  --primary-400: #1a75ff;
  --primary-500: #0052cc; /* Main primary blue */
  --primary-600: #0042a3;
  --primary-700: #00317a;
  --primary-800: #002152;
  --primary-900: #001129;
  --primary-950: #000814;

  /* New Secondary/Accent Color Palette (Vibrant Gold/Yellow) */
  --secondary-50: #fff8e1;
  --secondary-100: #ffecb3;
  --secondary-200: #ffe082;
  --secondary-300: #ffd54f;
  --secondary-400: #ffca28;
  --secondary-500: #ffc107; /* Main secondary/accent gold */
  --secondary-600: #e6ac00;
  --secondary-700: #cc9700;
  --secondary-800: #b38200;
  --secondary-900: #996e00;
  --secondary-950: #664900;

  /* Neutral colors (keeping these as they are generally good) */
  --neutral-50: #f9fafb; /* Lightest gray */
  --neutral-100: #f3f4f6;
  --neutral-200: #e5e7eb;
  --neutral-300: #d1d5db;
  --neutral-400: #9ca3af;
  --neutral-500: #6b7280;
  --neutral-600: #4b5563;
  --neutral-700: #374151;
  --neutral-800: #1f2937;
  --neutral-900: #111827;
  --neutral-950: #030712;

  /* Success, warning, error colors */
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #121212; /* Very dark gray for dark mode */
    --foreground: #e0e0e0; /* Light gray for text in dark mode */

    /* Adjust primary and secondary for dark mode if needed, or they can inherit */
    /* For instance, you might want slightly less saturated colors in dark mode */
    /* Example:
    --primary-500: #1a75ff;
    --secondary-500: #ffca28;
    */
  }
}

body {
  color: var(--foreground);
  background: var(--background);
}

@layer base {
  h1 {
    @apply text-4xl font-bold mb-6;
  }
  h2 {
    @apply text-3xl font-bold mb-4;
  }
  h3 {
    @apply text-2xl font-bold mb-3;
  }
  h4 {
    @apply text-xl font-bold mb-2;
  }
  p {
    @apply mb-4;
  }
  a {
    @apply text-primary-600 hover:text-primary-800 transition-colors;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors;
  }

  .btn-secondary {
    @apply bg-secondary-600 hover:bg-secondary-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors;
  }

  .btn-outline {
    @apply bg-white border border-primary-600 text-primary-600 hover:bg-primary-50 font-semibold py-2 px-4 rounded-lg transition-colors;
  }

  .container {
    @apply px-4 mx-auto max-w-7xl;
  }

  .card {
    @apply bg-white rounded-lg shadow-md overflow-hidden;
  }
}

/* Custom Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
}

/* Enhanced Hero Section Styles */
.hero-backdrop {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

.hero-text-shadow {
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.8), 0 2px 4px rgba(0, 0, 0, 0.6);
}

.hero-button-shadow {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 4px 10px rgba(0, 0, 0, 0.2);
}

.hero-button-shadow:hover {
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4), 0 6px 15px rgba(0, 0, 0, 0.3);
}

.bg-pattern {
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M54.627 0l.83.828-1.415 1.415L51.8 0h2.827zM5.373 0l-.83.828L5.96 2.243 8.2 0H5.374zM48.97 0l3.657 3.657-1.414 1.414L46.143 0h2.828zM11.03 0L7.372 3.657 8.787 5.07 13.857 0H11.03zm32.284 0L49.8 6.485 48.384 7.9l-7.9-7.9h2.83zM16.686 0L10.2 6.485 11.616 7.9l7.9-7.9h-2.83zm20.97 0l9.315 9.314-1.414 1.414L34.828 0h2.83zM22.344 0L13.03 9.314l1.414 1.414L25.172 0h-2.83zM32 0l12.142 12.142-1.414 1.414L30 .828 17.272 13.556l-1.414-1.414L28 0h4zM.284 0l28 28-1.414 1.414L0 2.544v2.83L26.272 32l-1.414 1.414-28-28V0h3.428zM60 0L45.414 14.586l-1.414-1.414L58.586 0H60zm0 5.542L32 33.542l-1.414-1.414L60 3.128v2.414zm0 5.656L42.414 42.586l-1.414-1.414L60 8.786v2.413zM60 20L35.414 44.586l-1.414-1.414L60 17.585v2.414zM30 34.828L14.142 50.686l1.414 1.414L31.414 36.242l1.414 1.414L15.556 54.03l1.414 1.414L33.414 39l1.414 1.414-5.657 5.656 1.414 1.414L39.9 38.17l-9.9-3.343zm30 11.657L45.414 60H48l12-12-12-12v2.485l9.9 9.9-9.9 9.9v2.485zM42 34.3c0 1.59 1.078 3.057 2.773 3.97l.227.116V34.3c0-.743.27-1.494.76-2.073l.11-.133c-.54-.327-1.128-.49-1.753-.49-.978 0-1.88.444-2.476 1.235A3.729 3.729 0 0 0 42 34.3zm6 0v4.396c1.67-.934 2.736-2.446 2.736-4.05 0-.743-.27-1.494-.76-2.073l-.116-.133c-.54-.327-1.123-.49-1.743-.49-.954 0-1.83.418-2.432 1.18-.386.495-.607 1.095-.66 1.717.194-.074.406-.114.63-.114.443 0 .887.164 1.26.5.374.336.57.788.57 1.27z' fill='%23ffffff' fill-opacity='0.08' fill-rule='evenodd'/%3E%3C/svg%3E");
}
