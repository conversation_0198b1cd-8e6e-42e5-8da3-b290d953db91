[{"/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/Footer.tsx": "1", "/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/Header.tsx": "2", "/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/Layout.tsx": "3", "/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/PortableText.tsx": "4", "/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/lib/queries.ts": "5", "/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/lib/sanity.ts": "6", "/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/src/app/about/page.tsx": "7", "/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/src/app/contact/page.tsx": "8", "/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/src/app/events/page.tsx": "9", "/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/src/app/gallery/page.tsx": "10", "/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/src/app/layout.tsx": "11", "/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/src/app/news/[slug]/page.tsx": "12", "/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/src/app/news/page.tsx": "13", "/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/src/app/page.tsx": "14", "/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/src/app/sponsors/page.tsx": "15", "/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/src/app/team/page.tsx": "16", "/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/ParticleSection.tsx": "17", "/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/src/app/events/[slug]/page.tsx": "18", "/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/src/app/sitemap.ts": "19", "/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/src/lib/queries.ts": "20"}, {"size": 8659, "mtime": 1747980322149, "results": "21", "hashOfConfig": "22"}, {"size": 5116, "mtime": 1747979131086, "results": "23", "hashOfConfig": "22"}, {"size": 302, "mtime": 1747972484560, "results": "24", "hashOfConfig": "22"}, {"size": 987, "mtime": 1747972432976, "results": "25", "hashOfConfig": "22"}, {"size": 2003, "mtime": 1747981077354, "results": "26", "hashOfConfig": "22"}, {"size": 624, "mtime": 1747972379949, "results": "27", "hashOfConfig": "22"}, {"size": 15401, "mtime": 1747988494453, "results": "28", "hashOfConfig": "22"}, {"size": 15540, "mtime": 1747978880934, "results": "29", "hashOfConfig": "22"}, {"size": 9600, "mtime": 1747978113052, "results": "30", "hashOfConfig": "22"}, {"size": 4106, "mtime": 1747978776057, "results": "31", "hashOfConfig": "22"}, {"size": 2029, "mtime": 1747987871331, "results": "32", "hashOfConfig": "22"}, {"size": 5359, "mtime": 1747978041053, "results": "33", "hashOfConfig": "22"}, {"size": 4416, "mtime": 1747977988087, "results": "34", "hashOfConfig": "22"}, {"size": 9640, "mtime": 1747995015027, "results": "35", "hashOfConfig": "22"}, {"size": 12885, "mtime": 1747978874052, "results": "36", "hashOfConfig": "22"}, {"size": 11684, "mtime": 1747986955701, "results": "37", "hashOfConfig": "22"}, {"size": 1986, "mtime": 1747995237032, "results": "38", "hashOfConfig": "22"}, {"size": 13359, "mtime": 1747981142524, "results": "39", "hashOfConfig": "22"}, {"size": 1610, "mtime": 1747987756482, "results": "40", "hashOfConfig": "22"}, {"size": 1383, "mtime": 1747980481435, "results": "41", "hashOfConfig": "22"}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "u13ru3", {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/Footer.tsx", [], [], "/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/Header.tsx", [], [], "/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/Layout.tsx", [], [], "/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/PortableText.tsx", ["102", "103", "104"], [], "/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/lib/queries.ts", [], [], "/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/lib/sanity.ts", ["105"], [], "/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/src/app/about/page.tsx", [], [], "/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/src/app/contact/page.tsx", [], [], "/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/src/app/events/page.tsx", [], [], "/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/src/app/gallery/page.tsx", [], [], "/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/src/app/layout.tsx", [], [], "/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/src/app/news/[slug]/page.tsx", ["106", "107", "108"], [], "/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/src/app/news/page.tsx", ["109"], [], "/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/src/app/page.tsx", ["110", "111", "112", "113"], [], "/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/src/app/sponsors/page.tsx", [], [], "/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/src/app/team/page.tsx", [], [], "/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/ParticleSection.tsx", ["114"], [], "/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/src/app/events/[slug]/page.tsx", ["115", "116", "117", "118", "119", "120"], [], "/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/src/app/sitemap.ts", [], [], "/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/src/lib/queries.ts", [], [], {"ruleId": "121", "severity": 2, "message": "122", "line": 7, "column": 24, "nodeType": "123", "messageId": "124", "endLine": 7, "endColumn": 27, "suggestions": "125"}, {"ruleId": "121", "severity": 2, "message": "122", "line": 24, "column": 33, "nodeType": "123", "messageId": "124", "endLine": 24, "endColumn": 36, "suggestions": "126"}, {"ruleId": "121", "severity": 2, "message": "122", "line": 35, "column": 62, "nodeType": "123", "messageId": "124", "endLine": 35, "endColumn": 65, "suggestions": "127"}, {"ruleId": "121", "severity": 2, "message": "122", "line": 21, "column": 37, "nodeType": "123", "messageId": "124", "endLine": 21, "endColumn": 40, "suggestions": "128"}, {"ruleId": "129", "severity": 2, "message": "130", "line": 8, "column": 10, "nodeType": null, "messageId": "131", "endLine": 8, "endColumn": 21}, {"ruleId": "132", "severity": 2, "message": "133", "line": 85, "column": 34, "nodeType": "134", "messageId": "135", "suggestions": "136"}, {"ruleId": "132", "severity": 2, "message": "133", "line": 85, "column": 55, "nodeType": "134", "messageId": "135", "suggestions": "137"}, {"ruleId": "121", "severity": 2, "message": "122", "line": 51, "column": 45, "nodeType": "123", "messageId": "124", "endLine": 51, "endColumn": 48, "suggestions": "138"}, {"ruleId": "121", "severity": 2, "message": "122", "line": 67, "column": 42, "nodeType": "123", "messageId": "124", "endLine": 67, "endColumn": 45, "suggestions": "139"}, {"ruleId": "121", "severity": 2, "message": "122", "line": 124, "column": 44, "nodeType": "123", "messageId": "124", "endLine": 124, "endColumn": 47, "suggestions": "140"}, {"ruleId": "121", "severity": 2, "message": "122", "line": 186, "column": 40, "nodeType": "123", "messageId": "124", "endLine": 186, "endColumn": 43, "suggestions": "141"}, {"ruleId": "132", "severity": 2, "message": "133", "line": 221, "column": 83, "nodeType": "134", "messageId": "135", "suggestions": "142"}, {"ruleId": "121", "severity": 2, "message": "122", "line": 72, "column": 37, "nodeType": "123", "messageId": "124", "endLine": 72, "endColumn": 40, "suggestions": "143"}, {"ruleId": "129", "severity": 2, "message": "144", "line": 3, "column": 8, "nodeType": null, "messageId": "131", "endLine": 3, "endColumn": 13}, {"ruleId": "129", "severity": 2, "message": "145", "line": 6, "column": 10, "nodeType": null, "messageId": "131", "endLine": 6, "endColumn": 21}, {"ruleId": "129", "severity": 2, "message": "146", "line": 14, "column": 11, "nodeType": null, "messageId": "131", "endLine": 14, "endColumn": 16}, {"ruleId": "121", "severity": 2, "message": "122", "line": 24, "column": 17, "nodeType": "123", "messageId": "124", "endLine": 24, "endColumn": 20, "suggestions": "147"}, {"ruleId": "132", "severity": 2, "message": "133", "line": 89, "column": 32, "nodeType": "134", "messageId": "135", "suggestions": "148"}, {"ruleId": "132", "severity": 2, "message": "133", "line": 89, "column": 53, "nodeType": "134", "messageId": "135", "suggestions": "149"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["150", "151"], ["152", "153"], ["154", "155"], ["156", "157"], "@typescript-eslint/no-unused-vars", "'NewsArticle' is defined but never used.", "unusedVar", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["158", "159", "160", "161"], ["162", "163", "164", "165"], ["166", "167"], ["168", "169"], ["170", "171"], ["172", "173"], ["174", "175", "176", "177"], ["178", "179"], "'Image' is defined but never used.", "'urlForImage' is defined but never used.", "'Event' is defined but never used.", ["180", "181"], ["182", "183", "184", "185"], ["186", "187", "188", "189"], {"messageId": "190", "fix": "191", "desc": "192"}, {"messageId": "193", "fix": "194", "desc": "195"}, {"messageId": "190", "fix": "196", "desc": "192"}, {"messageId": "193", "fix": "197", "desc": "195"}, {"messageId": "190", "fix": "198", "desc": "192"}, {"messageId": "193", "fix": "199", "desc": "195"}, {"messageId": "190", "fix": "200", "desc": "192"}, {"messageId": "193", "fix": "201", "desc": "195"}, {"messageId": "202", "data": "203", "fix": "204", "desc": "205"}, {"messageId": "202", "data": "206", "fix": "207", "desc": "208"}, {"messageId": "202", "data": "209", "fix": "210", "desc": "211"}, {"messageId": "202", "data": "212", "fix": "213", "desc": "214"}, {"messageId": "202", "data": "215", "fix": "216", "desc": "205"}, {"messageId": "202", "data": "217", "fix": "218", "desc": "208"}, {"messageId": "202", "data": "219", "fix": "220", "desc": "211"}, {"messageId": "202", "data": "221", "fix": "222", "desc": "214"}, {"messageId": "190", "fix": "223", "desc": "192"}, {"messageId": "193", "fix": "224", "desc": "195"}, {"messageId": "190", "fix": "225", "desc": "192"}, {"messageId": "193", "fix": "226", "desc": "195"}, {"messageId": "190", "fix": "227", "desc": "192"}, {"messageId": "193", "fix": "228", "desc": "195"}, {"messageId": "190", "fix": "229", "desc": "192"}, {"messageId": "193", "fix": "230", "desc": "195"}, {"messageId": "202", "data": "231", "fix": "232", "desc": "205"}, {"messageId": "202", "data": "233", "fix": "234", "desc": "208"}, {"messageId": "202", "data": "235", "fix": "236", "desc": "211"}, {"messageId": "202", "data": "237", "fix": "238", "desc": "214"}, {"messageId": "190", "fix": "239", "desc": "192"}, {"messageId": "193", "fix": "240", "desc": "195"}, {"messageId": "190", "fix": "241", "desc": "192"}, {"messageId": "193", "fix": "242", "desc": "195"}, {"messageId": "202", "data": "243", "fix": "244", "desc": "205"}, {"messageId": "202", "data": "245", "fix": "246", "desc": "208"}, {"messageId": "202", "data": "247", "fix": "248", "desc": "211"}, {"messageId": "202", "data": "249", "fix": "250", "desc": "214"}, {"messageId": "202", "data": "251", "fix": "252", "desc": "205"}, {"messageId": "202", "data": "253", "fix": "254", "desc": "208"}, {"messageId": "202", "data": "255", "fix": "256", "desc": "211"}, {"messageId": "202", "data": "257", "fix": "258", "desc": "214"}, "suggestUnknown", {"range": "259", "text": "260"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "261", "text": "262"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "263", "text": "260"}, {"range": "264", "text": "262"}, {"range": "265", "text": "260"}, {"range": "266", "text": "262"}, {"range": "267", "text": "260"}, {"range": "268", "text": "262"}, "replaceWithAlt", {"alt": "269"}, {"range": "270", "text": "271"}, "Replace with `&apos;`.", {"alt": "272"}, {"range": "273", "text": "274"}, "Replace with `&lsquo;`.", {"alt": "275"}, {"range": "276", "text": "277"}, "Replace with `&#39;`.", {"alt": "278"}, {"range": "279", "text": "280"}, "Replace with `&rsquo;`.", {"alt": "269"}, {"range": "281", "text": "282"}, {"alt": "272"}, {"range": "283", "text": "284"}, {"alt": "275"}, {"range": "285", "text": "286"}, {"alt": "278"}, {"range": "287", "text": "288"}, {"range": "289", "text": "260"}, {"range": "290", "text": "262"}, {"range": "291", "text": "260"}, {"range": "292", "text": "262"}, {"range": "293", "text": "260"}, {"range": "294", "text": "262"}, {"range": "295", "text": "260"}, {"range": "296", "text": "262"}, {"alt": "269"}, {"range": "297", "text": "298"}, {"alt": "272"}, {"range": "299", "text": "300"}, {"alt": "275"}, {"range": "301", "text": "302"}, {"alt": "278"}, {"range": "303", "text": "304"}, {"range": "305", "text": "260"}, {"range": "306", "text": "262"}, {"range": "307", "text": "260"}, {"range": "308", "text": "262"}, {"alt": "269"}, {"range": "309", "text": "310"}, {"alt": "272"}, {"range": "311", "text": "312"}, {"alt": "275"}, {"range": "313", "text": "314"}, {"alt": "278"}, {"range": "315", "text": "316"}, {"alt": "269"}, {"range": "317", "text": "318"}, {"alt": "272"}, {"range": "319", "text": "320"}, {"alt": "275"}, {"range": "321", "text": "322"}, {"alt": "278"}, {"range": "323", "text": "324"}, [206, 209], "unknown", [206, 209], "never", [594, 597], [594, 597], [901, 904], [901, 904], [584, 587], [584, 587], "&apos;", [2535, 2636], "\n                  The article you&apos;re looking for doesn't exist or has been removed.\n                ", "&lsquo;", [2535, 2636], "\n                  The article you&lsquo;re looking for doesn't exist or has been removed.\n                ", "&#39;", [2535, 2636], "\n                  The article you&#39;re looking for doesn't exist or has been removed.\n                ", "&rsquo;", [2535, 2636], "\n                  The article you&rsquo;re looking for doesn't exist or has been removed.\n                ", [2535, 2636], "\n                  The article you're looking for doesn&apos;t exist or has been removed.\n                ", [2535, 2636], "\n                  The article you're looking for doesn&lsquo;t exist or has been removed.\n                ", [2535, 2636], "\n                  The article you're looking for doesn&#39;t exist or has been removed.\n                ", [2535, 2636], "\n                  The article you're looking for doesn&rsquo;t exist or has been removed.\n                ", [1615, 1618], [1615, 1618], [2527, 2530], [2527, 2530], [4825, 4828], [4825, 4828], [7572, 7575], [7572, 7575], [9043, 9236], "\n                  Northern Nepalese United FC is more than just a football club—we&apos;re a community that celebrates our heritage while embracing the Australian sporting culture.\n                ", [9043, 9236], "\n                  Northern Nepalese United FC is more than just a football club—we&lsquo;re a community that celebrates our heritage while embracing the Australian sporting culture.\n                ", [9043, 9236], "\n                  Northern Nepalese United FC is more than just a football club—we&#39;re a community that celebrates our heritage while embracing the Australian sporting culture.\n                ", [9043, 9236], "\n                  Northern Nepalese United FC is more than just a football club—we&rsquo;re a community that celebrates our heritage while embracing the Australian sporting culture.\n                ", [1490, 1493], [1490, 1493], [671, 674], [671, 674], [2613, 2712], "\n                  The event you&apos;re looking for doesn't exist or has been removed.\n                ", [2613, 2712], "\n                  The event you&lsquo;re looking for doesn't exist or has been removed.\n                ", [2613, 2712], "\n                  The event you&#39;re looking for doesn't exist or has been removed.\n                ", [2613, 2712], "\n                  The event you&rsquo;re looking for doesn't exist or has been removed.\n                ", [2613, 2712], "\n                  The event you're looking for doesn&apos;t exist or has been removed.\n                ", [2613, 2712], "\n                  The event you're looking for doesn&lsquo;t exist or has been removed.\n                ", [2613, 2712], "\n                  The event you're looking for doesn&#39;t exist or has been removed.\n                ", [2613, 2712], "\n                  The event you're looking for doesn&rsquo;t exist or has been removed.\n                "]