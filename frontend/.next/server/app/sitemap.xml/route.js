"use strict";(()=>{var e={};e.id=475,e.ids=[475],e.modules={10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},55511:e=>{e.exports=require("crypto")},94735:e=>{e.exports=require("events")},81630:e=>{e.exports=require("http")},55591:e=>{e.exports=require("https")},79551:e=>{e.exports=require("url")},28354:e=>{e.exports=require("util")},53490:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>v,serverHooks:()=>_,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>y});var i={};r.r(i),r.d(i,{default:()=>p});var o={};r.r(o),r.d(o,{GET:()=>f});var n=r(42706),a=r(28203),s=r(45994),l=r(39187),d=r(19405),u=r(53211);async function p(){let e=process.env.NEXT_PUBLIC_BASE_URL||"http://localhost:3000";return[...["","/about","/team","/news","/events","/sponsors","/gallery","/contact"].map(t=>({url:`${e}${t}`,lastModified:new Date().toISOString()})),...(await d.S.fetch(u.tT)).map(t=>({url:`${e}/events/${t.slug.current}`,lastModified:t._updatedAt||t.date||new Date().toISOString()})),...(await d.S.fetch(u.kR)).map(t=>({url:`${e}/news/${t.slug.current}`,lastModified:t._updatedAt||t.publishedAt||new Date().toISOString()}))]}var c=r(13192);let m={...i}.default;if("function"!=typeof m)throw Error('Default export is missing in "/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/src/app/sitemap.ts"');async function f(e,t){let{__metadata_id__:r,...i}=await t.params||{},o=!!r&&r.endsWith(".xml");if(r&&!o)return new l.NextResponse("Not Found",{status:404});let n=r&&o?r.slice(0,-4):void 0,a=await m({id:n}),s=(0,c.resolveRouteData)(a,"sitemap");return new l.NextResponse(s,{headers:{"Content-Type":"application/xml","Cache-Control":"public, max-age=0, must-revalidate"}})}let v=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/sitemap.xml/route",pathname:"/sitemap.xml",filename:"sitemap",bundlePath:"app/sitemap.xml/route"},resolvedPagePath:"next-metadata-route-loader?filePath=%2FUsers%2Fsonamkhadka%2FDesktop%2FDeveloper%2Fprojects%2Fprocessing%2Fbrisbane-fc-website%2Ffrontend%2Fsrc%2Fapp%2Fsitemap.ts&isDynamicRouteExtension=1!?__next_metadata_route__",nextConfigOutput:"",userland:o}),{workAsyncStorage:g,workUnitAsyncStorage:y,serverHooks:_}=v;function h(){return(0,s.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:y})}},53211:(e,t,r)=>{r.d(t,{AY:()=>l,MC:()=>n,Xd:()=>d,bK:()=>s,kR:()=>o,lt:()=>i,tT:()=>a,vW:()=>p,zg:()=>u});let i=`
  *[_type == "newsArticle"] | order(publishedAt desc)[0...3] {
    _id,
    title,
    slug,
    publishedAt,
    mainImage,
    summary
  }
`,o=`
  *[_type == "newsArticle"] | order(publishedAt desc) {
    _id,
    title,
    slug,
    publishedAt,
    mainImage,
    summary
  }
`,n=`
  *[_type == "event" && date > now()] | order(date asc)[0...5] {
    _id,
    title,
    slug,
    date,
    location,
    eventType,
    opponent,
    homeOrAway
  }
`,a=`
  *[_type == "event"] | order(date desc) {
    _id,
    title,
    slug,
    date,
    location,
    eventType,
    opponent,
    homeOrAway,
    result
  }
`,s=`
  *[_type == "event" && slug.current == $slug][0] {
    _id,
    title,
    slug,
    date,
    location,
    eventType,
    opponent,
    homeOrAway,
    result,
    description
  }
`,l=`
  *[_type == "sponsor"] | order(displayOrder asc) {
    _id,
    name,
    logo,
    websiteUrl,
    sponsorshipLevel
  }
`,d=`
  *[_type == "galleryImage"] | order(dateTaken desc) {
    _id,
    title,
    imageFile,
    dateTaken
  }
`,u=`
  *[_type == "player"] | order(jerseyNumber asc) {
    _id,
    name,
    position,
    jerseyNumber,
    image,
    stats,
    bio
  }
`,p=`
  *[_type == "staff"] | order(displayOrder asc) {
    _id,
    name,
    role,
    image,
    bio,
    contactInfo
  }
`},19405:(e,t,r)=>{r.d(t,{S:()=>l,i:()=>u});var i=r(3709),o=r(25e3),n=r.n(o);let a="9at30otk",s="production",l=(0,i.UU)({projectId:a,dataset:s,apiVersion:"2024-05-23",useCdn:"undefined"!=typeof document}),d=n()({projectId:a,dataset:s});function u(e){return d.image(e)}},13192:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{resolveManifest:function(){return a},resolveRobots:function(){return o},resolveRouteData:function(){return s},resolveSitemap:function(){return n}});let i=r(90026);function o(e){let t="";for(let r of Array.isArray(e.rules)?e.rules:[e.rules]){for(let e of(0,i.resolveArray)(r.userAgent||["*"]))t+=`User-Agent: ${e}
`;if(r.allow)for(let e of(0,i.resolveArray)(r.allow))t+=`Allow: ${e}
`;if(r.disallow)for(let e of(0,i.resolveArray)(r.disallow))t+=`Disallow: ${e}
`;r.crawlDelay&&(t+=`Crawl-delay: ${r.crawlDelay}
`),t+="\n"}return e.host&&(t+=`Host: ${e.host}
`),e.sitemap&&(0,i.resolveArray)(e.sitemap).forEach(e=>{t+=`Sitemap: ${e}
`}),t}function n(e){let t=e.some(e=>Object.keys(e.alternates??{}).length>0),r=e.some(e=>{var t;return!!(null==(t=e.images)?void 0:t.length)}),i=e.some(e=>{var t;return!!(null==(t=e.videos)?void 0:t.length)}),o="";for(let l of(o+='<?xml version="1.0" encoding="UTF-8"?>\n',o+='<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"',r&&(o+=' xmlns:image="http://www.google.com/schemas/sitemap-image/1.1"'),i&&(o+=' xmlns:video="http://www.google.com/schemas/sitemap-video/1.1"'),t?o+=' xmlns:xhtml="http://www.w3.org/1999/xhtml">\n':o+=">\n",e)){var n,a,s;o+="<url>\n",o+=`<loc>${l.url}</loc>
`;let e=null==(n=l.alternates)?void 0:n.languages;if(e&&Object.keys(e).length)for(let t in e)o+=`<xhtml:link rel="alternate" hreflang="${t}" href="${e[t]}" />
`;if(null==(a=l.images)?void 0:a.length)for(let e of l.images)o+=`<image:image>
<image:loc>${e}</image:loc>
</image:image>
`;if(null==(s=l.videos)?void 0:s.length)for(let e of l.videos)o+=["<video:video>",`<video:title>${e.title}</video:title>`,`<video:thumbnail_loc>${e.thumbnail_loc}</video:thumbnail_loc>`,`<video:description>${e.description}</video:description>`,e.content_loc&&`<video:content_loc>${e.content_loc}</video:content_loc>`,e.player_loc&&`<video:player_loc>${e.player_loc}</video:player_loc>`,e.duration&&`<video:duration>${e.duration}</video:duration>`,e.view_count&&`<video:view_count>${e.view_count}</video:view_count>`,e.tag&&`<video:tag>${e.tag}</video:tag>`,e.rating&&`<video:rating>${e.rating}</video:rating>`,e.expiration_date&&`<video:expiration_date>${e.expiration_date}</video:expiration_date>`,e.publication_date&&`<video:publication_date>${e.publication_date}</video:publication_date>`,e.family_friendly&&`<video:family_friendly>${e.family_friendly}</video:family_friendly>`,e.requires_subscription&&`<video:requires_subscription>${e.requires_subscription}</video:requires_subscription>`,e.live&&`<video:live>${e.live}</video:live>`,e.restriction&&`<video:restriction relationship="${e.restriction.relationship}">${e.restriction.content}</video:restriction>`,e.platform&&`<video:platform relationship="${e.platform.relationship}">${e.platform.content}</video:platform>`,e.uploader&&`<video:uploader${e.uploader.info&&` info="${e.uploader.info}"`}>${e.uploader.content}</video:uploader>`,`</video:video>
`].filter(Boolean).join("\n");if(l.lastModified){let e=l.lastModified instanceof Date?l.lastModified.toISOString():l.lastModified;o+=`<lastmod>${e}</lastmod>
`}l.changeFrequency&&(o+=`<changefreq>${l.changeFrequency}</changefreq>
`),"number"==typeof l.priority&&(o+=`<priority>${l.priority}</priority>
`),o+="</url>\n"}return o+"</urlset>\n"}function a(e){return JSON.stringify(e)}function s(e,t){return"robots"===t?o(e):"sitemap"===t?n(e):"manifest"===t?a(e):""}},90026:(e,t)=>{function r(e){return Array.isArray(e)?e:[e]}function i(e){if(null!=e)return r(e)}function o(e){let t;if("string"==typeof e)try{t=(e=new URL(e)).origin}catch{}return t}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getOrigin:function(){return o},resolveArray:function(){return r},resolveAsArrayOrUndefined:function(){return i}})}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[638,310,452],()=>r(53490));module.exports=i})();