exports.id=273,exports.ids=[273],exports.modules={71726:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,13219,23)),Promise.resolve().then(r.t.bind(r,34863,23)),Promise.resolve().then(r.t.bind(r,25155,23)),Promise.resolve().then(r.t.bind(r,40802,23)),Promise.resolve().then(r.t.bind(r,9350,23)),Promise.resolve().then(r.t.bind(r,48530,23)),Promise.resolve().then(r.t.bind(r,88921,23))},11478:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,66959,23)),Promise.resolve().then(r.t.bind(r,33875,23)),Promise.resolve().then(r.t.bind(r,88903,23)),Promise.resolve().then(r.t.bind(r,57174,23)),Promise.resolve().then(r.t.bind(r,84178,23)),Promise.resolve().then(r.t.bind(r,87190,23)),Promise.resolve().then(r.t.bind(r,61365,23))},24040:()=>{},87592:()=>{},74485:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});var s=r(45512),n=r(28531),l=r.n(n),i=r(45103),o=r(58009);function a(){let[e,t]=(0,o.useState)(!1),[r,n]=(0,o.useState)(!1);return(0,s.jsx)("header",{className:`bg-white sticky top-0 z-50 transition-all duration-200 ${r?"shadow-lg py-2":"shadow-md py-3"}`,children:(0,s.jsxs)("div",{className:"container",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)(l(),{href:"/",className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:`relative transition-all duration-200 ${r?"w-10 h-10":"w-12 h-12"}`,children:(0,s.jsx)(i.default,{src:"/logo.png",alt:"Northern Nepalese United FC Logo",fill:!0,className:"object-contain",priority:!0})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-xl font-bold text-primary-800 hidden sm:inline",children:"Northern Nepalese United FC"}),(0,s.jsx)("span",{className:"text-xl font-bold text-primary-800 sm:hidden",children:"NNUFC"})]})]}),(0,s.jsx)("button",{className:"md:hidden p-2 text-primary-800 focus:outline-none focus:ring-2 focus:ring-primary-400 rounded-lg",onClick:()=>{t(!e)},"aria-label":"Toggle menu","aria-expanded":e,children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",className:"h-6 w-6",children:e?(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})}),(0,s.jsxs)("nav",{className:"hidden md:flex space-x-1 lg:space-x-6",children:[(0,s.jsx)(h,{href:"/",children:"Home"}),(0,s.jsx)(h,{href:"/about",children:"About"}),(0,s.jsx)(h,{href:"/team",children:"Team"}),(0,s.jsx)(h,{href:"/news",children:"News"}),(0,s.jsx)(h,{href:"/events",children:"Events"}),(0,s.jsx)(h,{href:"/sponsors",children:"Sponsors"}),(0,s.jsx)(h,{href:"/gallery",children:"Gallery"}),(0,s.jsx)(h,{href:"/contact",children:"Contact"})]})]}),(0,s.jsx)("div",{className:`md:hidden overflow-hidden transition-all duration-300 ease-in-out ${e?"max-h-96 opacity-100":"max-h-0 opacity-0"}`,children:(0,s.jsxs)("nav",{className:"mt-4 space-y-2 border-t border-neutral-200 pt-3",children:[(0,s.jsx)(c,{href:"/",onClick:()=>t(!1),children:"Home"}),(0,s.jsx)(c,{href:"/about",onClick:()=>t(!1),children:"About"}),(0,s.jsx)(c,{href:"/team",onClick:()=>t(!1),children:"Team"}),(0,s.jsx)(c,{href:"/news",onClick:()=>t(!1),children:"News"}),(0,s.jsx)(c,{href:"/events",onClick:()=>t(!1),children:"Events"}),(0,s.jsx)(c,{href:"/sponsors",onClick:()=>t(!1),children:"Sponsors"}),(0,s.jsx)(c,{href:"/gallery",onClick:()=>t(!1),children:"Gallery"}),(0,s.jsx)(c,{href:"/contact",onClick:()=>t(!1),children:"Contact"})]})})]})})}function h({href:e,children:t}){return(0,s.jsx)(l(),{href:e,className:"px-3 py-2 rounded-lg font-medium text-primary-700 transition-colors hover:bg-primary-50 hover:text-primary-900",children:t})}function c({href:e,onClick:t,children:r}){return(0,s.jsx)(l(),{href:e,className:"block py-2 px-1 text-primary-700 hover:bg-primary-50 hover:text-primary-900 rounded-lg font-medium transition-colors",onClick:t,children:r})}},47735:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(62740),n=r(59607),l=r.n(n);function i(){let e=new Date().getFullYear();return(0,s.jsx)("footer",{className:"bg-gradient-to-b from-neutral-800 to-neutral-900 text-white",children:(0,s.jsxs)("div",{className:"container py-12",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,s.jsxs)("div",{className:"md:col-span-2",children:[(0,s.jsx)("h3",{className:"text-xl font-bold mb-4 text-white",children:"Northern Nepalese United FC"}),(0,s.jsx)("p",{className:"mb-4 text-neutral-300",children:"Brisbane-based Nepalese football club est. 2023, bringing together the community through the beautiful game."}),(0,s.jsxs)("div",{className:"flex space-x-4 mt-6",children:[(0,s.jsx)("a",{href:"https://facebook.com",target:"_blank",rel:"noopener noreferrer","aria-label":"Facebook",className:"bg-neutral-700 hover:bg-neutral-600 text-white p-2 rounded-full transition-colors",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"currentColor",children:(0,s.jsx)("path",{d:"M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"})})}),(0,s.jsx)("a",{href:"https://twitter.com",target:"_blank",rel:"noopener noreferrer","aria-label":"Twitter",className:"bg-neutral-700 hover:bg-neutral-600 text-white p-2 rounded-full transition-colors",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"currentColor",children:(0,s.jsx)("path",{d:"M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"})})}),(0,s.jsx)("a",{href:"https://instagram.com",target:"_blank",rel:"noopener noreferrer","aria-label":"Instagram",className:"bg-neutral-700 hover:bg-neutral-600 text-white p-2 rounded-full transition-colors",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"currentColor",children:(0,s.jsx)("path",{d:"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"})})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-xl font-bold mb-4 text-white",children:"Club Links"}),(0,s.jsxs)("ul",{className:"space-y-2 text-neutral-300",children:[(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/about",className:"text-neutral-300 hover:text-white hover:underline transition-colors no-underline",children:"About Us"})}),(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/team",className:"text-neutral-300 hover:text-white hover:underline transition-colors no-underline",children:"Our Team"})}),(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/news",className:"text-neutral-300 hover:text-white hover:underline transition-colors no-underline",children:"News"})}),(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/events",className:"text-neutral-300 hover:text-white hover:underline transition-colors no-underline",children:"Events & Fixtures"})}),(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/gallery",className:"text-neutral-300 hover:text-white hover:underline transition-colors no-underline",children:"Gallery"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-xl font-bold mb-4 text-white",children:"Contact"}),(0,s.jsxs)("address",{className:"not-italic text-neutral-300",children:[(0,s.jsxs)("p",{className:"mb-2 flex items-start",children:[(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2 mt-0.5 text-neutral-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]}),"John Oxley Reserve",(0,s.jsx)("br",{}),"Murrumba Downs",(0,s.jsx)("br",{}),"Brisbane, QLD"]}),(0,s.jsxs)("p",{className:"mb-2 flex items-start",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2 mt-0.5 text-neutral-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})}),(0,s.jsx)("a",{href:"tel:+61424770570",className:"text-neutral-300 hover:text-white hover:underline transition-colors no-underline",children:"0424 770 570"})]}),(0,s.jsxs)("p",{className:"flex items-start",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2 mt-0.5 text-neutral-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})}),(0,s.jsx)("a",{href:"mailto:<EMAIL>",className:"text-neutral-300 hover:text-white hover:underline transition-colors no-underline",children:"<EMAIL>"})]})]})]})]}),(0,s.jsx)("div",{className:"border-t border-neutral-700 mt-10 pt-8 text-center text-neutral-400",children:(0,s.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[(0,s.jsxs)("p",{children:["\xa9 ",e," Northern Nepalese United - NNUFC. All rights reserved."]}),(0,s.jsxs)("ul",{className:"flex space-x-4 mt-4 md:mt-0",children:[(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/sponsors",className:"text-neutral-400 hover:text-white hover:underline transition-colors no-underline",children:"Sponsors"})}),(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/contact",className:"text-neutral-400 hover:text-white hover:underline transition-colors no-underline",children:"Contact"})})]})]})})]})})}},11993:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/Header.tsx","default")},71354:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u,metadata:()=>m});var s=r(62740),n=r(2202),l=r.n(n),i=r(64988),o=r.n(i);r(61135);let a="Northern Nepalese United FC",h="Northern Nepalese United - NNUFC | Official Website",c="The official website of Northern Nepalese United Football Club. Stay updated with the latest news, events, and information about our Brisbane-based Nepalese football team.",d=process.env.NEXT_PUBLIC_BASE_URL||"http://localhost:3000",x=`${d}/logo.png`,m={title:{default:h,template:`%s | ${a}`},description:c,openGraph:{title:h,description:c,url:d,siteName:a,images:[{url:x,width:512,height:512}],locale:"en_AU",type:"website"},twitter:{card:"summary_large_image",title:h,description:c,images:[x]},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function u({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${l().variable} ${o().variable} antialiased`,children:e})})}},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(88077);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},61135:()=>{}};