exports.id=856,exports.ids=[856],exports.modules={66447:(e,r)=>{"use strict";r.q=function(e){for(var r,t=[],n=String(e||""),a=n.indexOf(","),l=0,o=!1;!o;)-1===a&&(a=n.length,o=!0),((r=n.slice(l,a).trim())||!o)&&t.push(r),l=a+1,a=n.indexOf(",",l);return t}},54612:e=>{"use strict";e.exports=function(e,t){for(var n,a,l,o=e||"",i=t||"div",s={},u=0;u<o.length;)r.lastIndex=u,l=r.exec(o),(n=o.slice(u,l?l.index:o.length))&&(a?"#"===a?s.id=n:s.className?s.className.push(n):s.className=[n]:i=n,u+=n.length),l&&(a=l[0],u++);return{type:"element",tagName:i,properties:s,children:[]}};var r=/[#.]/g},16932:(e,r,t)=>{"use strict";var n=t(27920),a=t(58904),l=t(54612),o=t(74976).q,i=t(66447).q;e.exports=function(e,r,t){var a=t?function(e){for(var r,t=e.length,n=-1,a={};++n<t;)a[(r=e[n]).toLowerCase()]=r;return a}(t):null;return function(t,c){var p,g,d,f,m=l(t,r),h=Array.prototype.slice.call(arguments,2),b=m.tagName.toLowerCase();if(m.tagName=a&&s.call(a,b)?a[b]:b,c&&("string"==typeof(p=c)||"length"in p||(g=m.tagName,d=p.type,"input"!==g&&d&&"string"==typeof d&&("object"==typeof p.children&&"length"in p.children||((d=d.toLowerCase(),"button"===g)?"menu"!==d&&"submit"!==d&&"reset"!==d&&"button"!==d:"value"in p))))&&(h.unshift(c),c=null),c)for(f in c)(function(r,t,a){var l,s,c;null!=a&&a==a&&(s=(l=n(e,t)).property,"string"==typeof(c=a)&&(l.spaceSeparated?c=o(c):l.commaSeparated?c=i(c):l.commaOrSpaceSeparated&&(c=o(i(c).join(" ")))),"style"===s&&"string"!=typeof a&&(c=function(e){var r,t=[];for(r in e)t.push([r,e[r]].join(": "));return t.join("; ")}(c)),"className"===s&&r.className&&(c=r.className.concat(c)),r[s]=function(e,r,t){var n,a,l;if("object"!=typeof t||!("length"in t))return u(e,r,t);for(a=t.length,n=-1,l=[];++n<a;)l[n]=u(e,r,t[n]);return l}(l,s,c))})(m.properties,f,c[f]);return function e(r,t){var n,a;if("string"==typeof t||"number"==typeof t){r.push({type:"text",value:String(t)});return}if("object"==typeof t&&"length"in t){for(n=-1,a=t.length;++n<a;)e(r,t[n]);return}if("object"!=typeof t||!("type"in t))throw Error("Expected node, nodes, or string, got `"+t+"`");r.push(t)}(m.children,h),"template"===m.tagName&&(m.content={type:"root",children:m.children},m.children=[]),m}};var s={}.hasOwnProperty;function u(e,r,t){var n=t;return e.number||e.positiveNumber?isNaN(n)||""===n||(n=Number(n)):(e.boolean||e.overloadedBoolean)&&"string"==typeof n&&(""===n||a(t)===a(r))&&(n=!0),n}},33129:(e,r,t)=>{"use strict";var n=t(18650),a=t(16932)(n,"div");a.displayName="html",e.exports=a},74910:(e,r,t)=>{"use strict";e.exports=t(33129)},45654:e=>{"use strict";e.exports=function(e){var r="string"==typeof e?e.charCodeAt(0):e;return r>=97&&r<=122||r>=65&&r<=90}},12678:(e,r,t)=>{"use strict";var n=t(45654),a=t(74555);e.exports=function(e){return n(e)||a(e)}},74555:e=>{"use strict";e.exports=function(e){var r="string"==typeof e?e.charCodeAt(0):e;return r>=48&&r<=57}},10583:e=>{"use strict";e.exports=function(e){var r="string"==typeof e?e.charCodeAt(0):e;return r>=97&&r<=102||r>=65&&r<=70||r>=48&&r<=57}},69006:(e,r,t)=>{"use strict";var n=t(33323);e.exports=function(e){return!!a.call(n,e)&&n[e]};var a={}.hasOwnProperty},9892:(e,r,t)=>{"use strict";var n=t(36347),a=t(83921),l=t(74555),o=t(10583),i=t(12678),s=t(69006);e.exports=function(e,r){var t,l,o={};for(l in r||(r={}),g)t=r[l],o[l]=null==t?g[l]:t;return(o.position.indent||o.position.start)&&(o.indent=o.position.indent||[],o.position=o.position.start),function(e,r){var t,l,o,g,y,w,x,k,q,A,S,E,L,N,D,C,F,T,R,O,P,U=r.additional,j=r.nonTerminated,B=r.text,V=r.reference,I=r.warning,M=r.textContext,z=r.referenceContext,H=r.warningContext,$=r.position,G=r.indent||[],_=e.length,Z=0,W=-1,J=$.column||1,Y=$.line||1,K="",Q=[];for("string"==typeof U&&(U=U.charCodeAt(0)),T=X(),A=I?function(e,r){var t=X();t.column+=r,t.offset+=r,I.call(H,v[e],t,e)}:p,Z--,_++;++Z<_;)if(10===x&&(J=G[W]||1),38===(x=e.charCodeAt(Z))){if(9===(q=e.charCodeAt(Z+1))||10===q||12===q||32===q||38===q||60===q||q!=q||U&&q===U){K+=c(x),J++;continue}for(N=D=Z+1,P=D,35===q?(P=++N,88===(q=e.charCodeAt(P))||120===q?(C=f,P=++N):C=m):C=d,o="",L="",w="",F=b[C],P--;++P<_&&F(q=e.charCodeAt(P));)w+=c(q),C===d&&u.call(n,w)&&(o=w,L=n[w]);(y=59===e.charCodeAt(P))&&(P++,(g=C===d&&s(w))&&(o=w,L=g)),O=1+P-D,(y||j)&&(w?C===d?(y&&!L?A(5,1):(o!==w&&(O=1+(P=N+o.length)-N,y=!1),y||(S=o?1:3,r.attribute?61===(q=e.charCodeAt(P))?(A(S,O),L=null):i(q)?L=null:A(S,O):A(S,O))),k=L):(y||A(2,O),(t=k=parseInt(w,h[C]))>=55296&&t<=57343||t>1114111?(A(7,O),k=c(65533)):k in a?(A(6,O),k=a[k]):(E="",((l=k)>=1&&l<=8||11===l||l>=13&&l<=31||l>=127&&l<=159||l>=64976&&l<=65007||(65535&l)==65535||(65535&l)==65534)&&A(6,O),k>65535&&(k-=65536,E+=c(k>>>10|55296),k=56320|1023&k),k=E+c(k))):C!==d&&A(4,O)),k?(ee(),T=X(),Z=P-1,J+=P-D+1,Q.push(k),R=X(),R.offset++,V&&V.call(z,k,{start:T,end:R},e.slice(D-1,P)),T=R):(w=e.slice(D-1,P),K+=w,J+=w.length,Z=P-1)}else 10===x&&(Y++,W++,J=0),x==x?(K+=c(x),J++):ee();return Q.join("");function X(){return{line:Y,column:J,offset:Z+($.offset||0)}}function ee(){K&&(Q.push(K),B&&B.call(M,K,{start:T,end:X()}),K="")}}(e,o)};var u={}.hasOwnProperty,c=String.fromCharCode,p=Function.prototype,g={warning:null,reference:null,text:null,warningContext:null,referenceContext:null,textContext:null,position:{},additional:null,attribute:!1,nonTerminated:!0},d="named",f="hexadecimal",m="decimal",h={};h[f]=16,h[m]=10;var b={};b[d]=i,b[m]=l,b[f]=o;var v={};v[1]="Named character references must be terminated by a semicolon",v[2]="Numeric character references must be terminated by a semicolon",v[3]="Named character references cannot be empty",v[4]="Numeric character references cannot be empty",v[5]="Named character references must be known",v[6]="Numeric character references cannot be disallowed",v[7]="Numeric character references cannot be outside the permissible Unicode range"},44191:e=>{var r=function(e){var r=/(?:^|\s)lang(?:uage)?-([\w-]+)(?=\s|$)/i,t=0,n={},a={manual:e.Prism&&e.Prism.manual,disableWorkerMessageHandler:e.Prism&&e.Prism.disableWorkerMessageHandler,util:{encode:function e(r){return r instanceof l?new l(r.type,e(r.content),r.alias):Array.isArray(r)?r.map(e):r.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/\u00a0/g," ")},type:function(e){return Object.prototype.toString.call(e).slice(8,-1)},objId:function(e){return e.__id||Object.defineProperty(e,"__id",{value:++t}),e.__id},clone:function e(r,t){var n,l;switch(t=t||{},a.util.type(r)){case"Object":if(t[l=a.util.objId(r)])return t[l];for(var o in n={},t[l]=n,r)r.hasOwnProperty(o)&&(n[o]=e(r[o],t));return n;case"Array":if(t[l=a.util.objId(r)])return t[l];return n=[],t[l]=n,r.forEach(function(r,a){n[a]=e(r,t)}),n;default:return r}},getLanguage:function(e){for(;e;){var t=r.exec(e.className);if(t)return t[1].toLowerCase();e=e.parentElement}return"none"},setLanguage:function(e,t){e.className=e.className.replace(RegExp(r,"gi"),""),e.classList.add("language-"+t)},currentScript:function(){if("undefined"==typeof document)return null;if("currentScript"in document)return document.currentScript;try{throw Error()}catch(n){var e=(/at [^(\r\n]*\((.*):[^:]+:[^:]+\)$/i.exec(n.stack)||[])[1];if(e){var r=document.getElementsByTagName("script");for(var t in r)if(r[t].src==e)return r[t]}return null}},isActive:function(e,r,t){for(var n="no-"+r;e;){var a=e.classList;if(a.contains(r))return!0;if(a.contains(n))return!1;e=e.parentElement}return!!t}},languages:{plain:n,plaintext:n,text:n,txt:n,extend:function(e,r){var t=a.util.clone(a.languages[e]);for(var n in r)t[n]=r[n];return t},insertBefore:function(e,r,t,n){var l=(n=n||a.languages)[e],o={};for(var i in l)if(l.hasOwnProperty(i)){if(i==r)for(var s in t)t.hasOwnProperty(s)&&(o[s]=t[s]);t.hasOwnProperty(i)||(o[i]=l[i])}var u=n[e];return n[e]=o,a.languages.DFS(a.languages,function(r,t){t===u&&r!=e&&(this[r]=o)}),o},DFS:function e(r,t,n,l){l=l||{};var o=a.util.objId;for(var i in r)if(r.hasOwnProperty(i)){t.call(r,i,r[i],n||i);var s=r[i],u=a.util.type(s);"Object"!==u||l[o(s)]?"Array"!==u||l[o(s)]||(l[o(s)]=!0,e(s,t,i,l)):(l[o(s)]=!0,e(s,t,null,l))}}},plugins:{},highlightAll:function(e,r){a.highlightAllUnder(document,e,r)},highlightAllUnder:function(e,r,t){var n={callback:t,container:e,selector:'code[class*="language-"], [class*="language-"] code, code[class*="lang-"], [class*="lang-"] code'};a.hooks.run("before-highlightall",n),n.elements=Array.prototype.slice.apply(n.container.querySelectorAll(n.selector)),a.hooks.run("before-all-elements-highlight",n);for(var l,o=0;l=n.elements[o++];)a.highlightElement(l,!0===r,n.callback)},highlightElement:function(r,t,n){var l=a.util.getLanguage(r),o=a.languages[l];a.util.setLanguage(r,l);var i=r.parentElement;i&&"pre"===i.nodeName.toLowerCase()&&a.util.setLanguage(i,l);var s=r.textContent,u={element:r,language:l,grammar:o,code:s};function c(e){u.highlightedCode=e,a.hooks.run("before-insert",u),u.element.innerHTML=u.highlightedCode,a.hooks.run("after-highlight",u),a.hooks.run("complete",u),n&&n.call(u.element)}if(a.hooks.run("before-sanity-check",u),(i=u.element.parentElement)&&"pre"===i.nodeName.toLowerCase()&&!i.hasAttribute("tabindex")&&i.setAttribute("tabindex","0"),!u.code){a.hooks.run("complete",u),n&&n.call(u.element);return}if(a.hooks.run("before-highlight",u),!u.grammar){c(a.util.encode(u.code));return}if(t&&e.Worker){var p=new Worker(a.filename);p.onmessage=function(e){c(e.data)},p.postMessage(JSON.stringify({language:u.language,code:u.code,immediateClose:!0}))}else c(a.highlight(u.code,u.grammar,u.language))},highlight:function(e,r,t){var n={code:e,grammar:r,language:t};if(a.hooks.run("before-tokenize",n),!n.grammar)throw Error('The language "'+n.language+'" has no grammar.');return n.tokens=a.tokenize(n.code,n.grammar),a.hooks.run("after-tokenize",n),l.stringify(a.util.encode(n.tokens),n.language)},tokenize:function(e,r){var t=r.rest;if(t){for(var n in t)r[n]=t[n];delete r.rest}var u=new i;return s(u,u.head,e),function e(r,t,n,i,u,c){for(var p in n)if(n.hasOwnProperty(p)&&n[p]){var g=n[p];g=Array.isArray(g)?g:[g];for(var d=0;d<g.length;++d){if(c&&c.cause==p+","+d)return;var f=g[d],m=f.inside,h=!!f.lookbehind,b=!!f.greedy,v=f.alias;if(b&&!f.pattern.global){var y=f.pattern.toString().match(/[imsuy]*$/)[0];f.pattern=RegExp(f.pattern.source,y+"g")}for(var w=f.pattern||f,x=i.next,k=u;x!==t.tail&&(!c||!(k>=c.reach));k+=x.value.length,x=x.next){var q,A=x.value;if(t.length>r.length)return;if(!(A instanceof l)){var S=1;if(b){if(!(q=o(w,k,r,h))||q.index>=r.length)break;var E=q.index,L=q.index+q[0].length,N=k;for(N+=x.value.length;E>=N;)N+=(x=x.next).value.length;if(N-=x.value.length,k=N,x.value instanceof l)continue;for(var D=x;D!==t.tail&&(N<L||"string"==typeof D.value);D=D.next)S++,N+=D.value.length;S--,A=r.slice(k,N),q.index-=k}else if(!(q=o(w,0,A,h)))continue;var E=q.index,C=q[0],F=A.slice(0,E),T=A.slice(E+C.length),R=k+A.length;c&&R>c.reach&&(c.reach=R);var O=x.prev;if(F&&(O=s(t,O,F),k+=F.length),function(e,r,t){for(var n=r.next,a=0;a<t&&n!==e.tail;a++)n=n.next;r.next=n,n.prev=r,e.length-=a}(t,O,S),x=s(t,O,new l(p,m?a.tokenize(C,m):C,v,C)),T&&s(t,x,T),S>1){var P={cause:p+","+d,reach:R};e(r,t,n,x.prev,k,P),c&&P.reach>c.reach&&(c.reach=P.reach)}}}}}}(e,u,r,u.head,0),function(e){for(var r=[],t=e.head.next;t!==e.tail;)r.push(t.value),t=t.next;return r}(u)},hooks:{all:{},add:function(e,r){var t=a.hooks.all;t[e]=t[e]||[],t[e].push(r)},run:function(e,r){var t=a.hooks.all[e];if(t&&t.length)for(var n,l=0;n=t[l++];)n(r)}},Token:l};function l(e,r,t,n){this.type=e,this.content=r,this.alias=t,this.length=0|(n||"").length}function o(e,r,t,n){e.lastIndex=r;var a=e.exec(t);if(a&&n&&a[1]){var l=a[1].length;a.index+=l,a[0]=a[0].slice(l)}return a}function i(){var e={value:null,prev:null,next:null},r={value:null,prev:e,next:null};e.next=r,this.head=e,this.tail=r,this.length=0}function s(e,r,t){var n=r.next,a={value:t,prev:r,next:n};return r.next=a,n.prev=a,e.length++,a}if(e.Prism=a,l.stringify=function e(r,t){if("string"==typeof r)return r;if(Array.isArray(r)){var n="";return r.forEach(function(r){n+=e(r,t)}),n}var l={type:r.type,content:e(r.content,t),tag:"span",classes:["token",r.type],attributes:{},language:t},o=r.alias;o&&(Array.isArray(o)?Array.prototype.push.apply(l.classes,o):l.classes.push(o)),a.hooks.run("wrap",l);var i="";for(var s in l.attributes)i+=" "+s+'="'+(l.attributes[s]||"").replace(/"/g,"&quot;")+'"';return"<"+l.tag+' class="'+l.classes.join(" ")+'"'+i+">"+l.content+"</"+l.tag+">"},!e.document)return e.addEventListener&&(a.disableWorkerMessageHandler||e.addEventListener("message",function(r){var t=JSON.parse(r.data),n=t.language,l=t.code,o=t.immediateClose;e.postMessage(a.highlight(l,a.languages[n],n)),o&&e.close()},!1)),a;var u=a.util.currentScript();function c(){a.manual||a.highlightAll()}if(u&&(a.filename=u.src,u.hasAttribute("data-manual")&&(a.manual=!0)),!a.manual){var p=document.readyState;"loading"===p||"interactive"===p&&u&&u.defer?document.addEventListener("DOMContentLoaded",c):window.requestAnimationFrame?window.requestAnimationFrame(c):window.setTimeout(c,16)}return a}("undefined"!=typeof window?window:"undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope?self:{});e.exports&&(e.exports=r),"undefined"!=typeof global&&(global.Prism=r)},27920:(e,r,t)=>{"use strict";var n=t(58904),a=t(48550),l=t(66990),o="data";e.exports=function(e,r){var t,g,d,f=n(r),m=r,h=l;return f in e.normal?e.property[e.normal[f]]:(f.length>4&&f.slice(0,4)===o&&i.test(r)&&("-"===r.charAt(4)?m=o+(t=r.slice(5).replace(s,p)).charAt(0).toUpperCase()+t.slice(1):(d=(g=r).slice(4),r=s.test(d)?g:("-"!==(d=d.replace(u,c)).charAt(0)&&(d="-"+d),o+d)),h=a),new h(m,r))};var i=/^data[-\w.:]+$/i,s=/-[a-z]/g,u=/[A-Z]/g;function c(e){return"-"+e.toLowerCase()}function p(e){return e.charAt(1).toUpperCase()}},18650:(e,r,t)=>{"use strict";var n=t(66896),a=t(84403),l=t(23870),o=t(27805),i=t(45594),s=t(82026);e.exports=n([l,a,o,i,s])},45594:(e,r,t)=>{"use strict";var n=t(21217),a=t(70518),l=n.booleanish,o=n.number,i=n.spaceSeparated;e.exports=a({transform:function(e,r){return"role"===r?r:"aria-"+r.slice(4).toLowerCase()},properties:{ariaActiveDescendant:null,ariaAtomic:l,ariaAutoComplete:null,ariaBusy:l,ariaChecked:l,ariaColCount:o,ariaColIndex:o,ariaColSpan:o,ariaControls:i,ariaCurrent:null,ariaDescribedBy:i,ariaDetails:null,ariaDisabled:l,ariaDropEffect:i,ariaErrorMessage:null,ariaExpanded:l,ariaFlowTo:i,ariaGrabbed:l,ariaHasPopup:null,ariaHidden:l,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:i,ariaLevel:o,ariaLive:null,ariaModal:l,ariaMultiLine:l,ariaMultiSelectable:l,ariaOrientation:null,ariaOwns:i,ariaPlaceholder:null,ariaPosInSet:o,ariaPressed:l,ariaReadOnly:l,ariaRelevant:null,ariaRequired:l,ariaRoleDescription:i,ariaRowCount:o,ariaRowIndex:o,ariaRowSpan:o,ariaSelected:l,ariaSetSize:o,ariaSort:null,ariaValueMax:o,ariaValueMin:o,ariaValueNow:o,ariaValueText:null,role:null}})},82026:(e,r,t)=>{"use strict";var n=t(21217),a=t(70518),l=t(52263),o=n.boolean,i=n.overloadedBoolean,s=n.booleanish,u=n.number,c=n.spaceSeparated,p=n.commaSeparated;e.exports=a({space:"html",attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},transform:l,mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:p,acceptCharset:c,accessKey:c,action:null,allow:null,allowFullScreen:o,allowPaymentRequest:o,allowUserMedia:o,alt:null,as:null,async:o,autoCapitalize:null,autoComplete:c,autoFocus:o,autoPlay:o,capture:o,charSet:null,checked:o,cite:null,className:c,cols:u,colSpan:null,content:null,contentEditable:s,controls:o,controlsList:c,coords:u|p,crossOrigin:null,data:null,dateTime:null,decoding:null,default:o,defer:o,dir:null,dirName:null,disabled:o,download:i,draggable:s,encType:null,enterKeyHint:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:o,formTarget:null,headers:c,height:u,hidden:o,high:u,href:null,hrefLang:null,htmlFor:c,httpEquiv:c,id:null,imageSizes:null,imageSrcSet:p,inputMode:null,integrity:null,is:null,isMap:o,itemId:null,itemProp:c,itemRef:c,itemScope:o,itemType:c,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:o,low:u,manifest:null,max:null,maxLength:u,media:null,method:null,min:null,minLength:u,multiple:o,muted:o,name:null,nonce:null,noModule:o,noValidate:o,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforePrint:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextMenu:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:o,optimum:u,pattern:null,ping:c,placeholder:null,playsInline:o,poster:null,preload:null,readOnly:o,referrerPolicy:null,rel:c,required:o,reversed:o,rows:u,rowSpan:u,sandbox:c,scope:null,scoped:o,seamless:o,selected:o,shape:null,size:u,sizes:null,slot:null,span:u,spellCheck:s,src:null,srcDoc:null,srcLang:null,srcSet:p,start:u,step:null,style:null,tabIndex:u,target:null,title:null,translate:null,type:null,typeMustMatch:o,useMap:null,value:s,width:u,wrap:null,align:null,aLink:null,archive:c,axis:null,background:null,bgColor:null,border:u,borderColor:null,bottomMargin:u,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:o,declare:o,event:null,face:null,frame:null,frameBorder:null,hSpace:u,leftMargin:u,link:null,longDesc:null,lowSrc:null,marginHeight:u,marginWidth:u,noResize:o,noHref:o,noShade:o,noWrap:o,object:null,profile:null,prompt:null,rev:null,rightMargin:u,rules:null,scheme:null,scrolling:s,standby:null,summary:null,text:null,topMargin:u,valueType:null,version:null,vAlign:null,vLink:null,vSpace:u,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:o,disableRemotePlayback:o,prefix:null,property:null,results:u,security:null,unselectable:null}})},52263:(e,r,t)=>{"use strict";var n=t(4446);e.exports=function(e,r){return n(e,r.toLowerCase())}},4446:e=>{"use strict";e.exports=function(e,r){return r in e?e[r]:r}},70518:(e,r,t)=>{"use strict";var n=t(58904),a=t(94781),l=t(48550);e.exports=function(e){var r,t,o=e.space,i=e.mustUseProperty||[],s=e.attributes||{},u=e.properties,c=e.transform,p={},g={};for(r in u)t=new l(r,c(s,r),u[r],o),-1!==i.indexOf(r)&&(t.mustUseProperty=!0),p[r]=t,g[n(r)]=r,g[n(t.attribute)]=r;return new a(p,g,o)}},48550:(e,r,t)=>{"use strict";var n=t(66990),a=t(21217);e.exports=i,i.prototype=new n,i.prototype.defined=!0;var l=["boolean","booleanish","overloadedBoolean","number","commaSeparated","spaceSeparated","commaOrSpaceSeparated"],o=l.length;function i(e,r,t,i){var s,u,c,p=-1;for(i&&(this.space=i),n.call(this,e,r);++p<o;)s=c=l[p],(u=(t&a[c])===a[c])&&(this[s]=u)}},66990:e=>{"use strict";e.exports=t;var r=t.prototype;function t(e,r){this.property=e,this.attribute=r}r.space=null,r.attribute=null,r.property=null,r.boolean=!1,r.booleanish=!1,r.overloadedBoolean=!1,r.number=!1,r.commaSeparated=!1,r.spaceSeparated=!1,r.commaOrSpaceSeparated=!1,r.mustUseProperty=!1,r.defined=!1},66896:(e,r,t)=>{"use strict";var n=t(7654),a=t(94781);e.exports=function(e){for(var r,t,l=e.length,o=[],i=[],s=-1;++s<l;)r=e[s],o.push(r.property),i.push(r.normal),t=r.space;return new a(n.apply(null,o),n.apply(null,i),t)}},94781:e=>{"use strict";e.exports=t;var r=t.prototype;function t(e,r,t){this.property=e,this.normal=r,t&&(this.space=t)}r.space=null,r.normal={},r.property={}},21217:(e,r)=>{"use strict";var t=0;function n(){return Math.pow(2,++t)}r.boolean=n(),r.booleanish=n(),r.overloadedBoolean=n(),r.number=n(),r.spaceSeparated=n(),r.commaSeparated=n(),r.commaOrSpaceSeparated=n()},84403:(e,r,t)=>{"use strict";var n=t(70518);e.exports=n({space:"xlink",transform:function(e,r){return"xlink:"+r.slice(5).toLowerCase()},properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null}})},23870:(e,r,t)=>{"use strict";var n=t(70518);e.exports=n({space:"xml",transform:function(e,r){return"xml:"+r.slice(3).toLowerCase()},properties:{xmlLang:null,xmlBase:null,xmlSpace:null}})},27805:(e,r,t)=>{"use strict";var n=t(70518),a=t(52263);e.exports=n({space:"xmlns",attributes:{xmlnsxlink:"xmlns:xlink"},transform:a,properties:{xmlns:null,xmlnsXLink:null}})},58904:e=>{"use strict";e.exports=function(e){return e.toLowerCase()}},87773:(e,r,t)=>{"use strict";var n=t(58009),a=t(62310),l=t(65766),o=t(44358),i=n.createElement;function s(e){var r="language-".concat(e.language),t={className:r},n={className:[e.className||"refractor",r].filter(Boolean).join(" ")};e.inline&&(t.style={display:"inline"},t.className=e.className||"refractor");var s=a.highlight(e.value,e.language);e.markers&&e.markers.length>0&&(s=o(s,{markers:e.markers}));var u=i("code",t,0===s.length?e.value:s.map(l.depth(0)));return e.inline?u:i("pre",n,u)}s.registerLanguage=function(e){return a.register(e)},s.hasLanguage=function(e){return a.registered(e)},e.exports=s},44358:(e,r,t)=>{"use strict";var n=t(41918),a=t(32831),l=t(1881);e.exports=function(e,r){var t=r.markers.map(function(e){return e.line?e:{line:e}}).sort(function(e,r){return e.line-r.line});return function(e,r,t){if(0===r.length||0===e.length)return e;for(var o=r.reduce(function(e,r){return function(e,r){var t={type:"root",children:r},o=new l,i=new l,s=new l,u=[];function c(e,r,n){u.push(r),n.forEach(function(r){e.has(r)||(e.set(r,Object.assign({},r,{children:[]})),r!==t&&u.push(r))});for(var a=n.length;a--;){var l=e.get(n[a]),o=n[a+1],i=e.get(o)||r;-1===l.children.indexOf(i)&&l.children.push(i)}}a(t,function(r,t){if(!r.children){if(r.lineStart<e){c(o,r,t);return}if(r.lineStart===e){c(i,r,t);return}r.lineEnd>e&&u.some(function(e){return -1!==t.indexOf(e)})&&c(s,r,t)}});var p=n(t,function(e){return -1===u.indexOf(e)}),g=function(e){var r=e.get(t);return r?(a(r,function(e,r){if(e.children){e.lineStart=0,e.lineEnd=0;return}r.forEach(function(r){r.lineStart=Math.max(r.lineStart,e.lineStart),r.lineEnd=Math.max(r.lineEnd,e.lineEnd)})}),r.children):[]},d=[].concat(g(o),g(i),g(s),p?p.children:[]);return o.clear(),i.clear(),s.clear(),d}(r.line,e)},e),i=[],s=0,u=0;u<r.length;u++){for(var c=r[u],p=o[s];p&&p.lineEnd<c.line;p=o[++s])i.push(p);for(var g=[],d=o[s];d&&d.lineEnd===c.line;d=o[++s])g.push(d);g.length>0&&i.push(function(e,r,t){var n=r.className||"refractor-marker";return{type:"element",tagName:r.component||"div",properties:r.component?Object.assign({},t,{className:n}):{className:n},children:e,lineStart:r.line,lineEnd:e[e.length-1].lineEnd,isMarker:!0}}(g,c,t))}for(;s<o.length;)i.push(o[s++]);return i}(function e(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{lineNumber:1};return r.reduce(function(r,n){var a=t.lineNumber;if("text"===n.type){if(-1===n.value.indexOf("\n"))return n.lineStart=a,n.lineEnd=a,r.nodes.push(n),r;for(var l=n.value.split("\n"),o=0;o<l.length;o++){var i=0===o?t.lineNumber:++t.lineNumber;r.nodes.push({type:"text",value:o===l.length-1?l[o]:"".concat(l[o],"\n"),lineStart:i,lineEnd:i})}return r.lineNumber=t.lineNumber,r}if(n.children){var s=e(n.children,t),u=s.nodes[0],c=s.nodes[s.nodes.length-1];return n.lineStart=u?u.lineStart:a,n.lineEnd=c?c.lineEnd:a,n.children=s.nodes,r.lineNumber=s.lineNumber,r.nodes.push(n),r}return r.nodes.push(n),r},{nodes:[],lineNumber:t.lineNumber})}(e).nodes,t,r)}},1881:e=>{"use strict";function r(){this.map=new WeakMap}function t(){this.keys=[],this.values=[]}e.exports="function"==typeof WeakMap?r:t,r.prototype.has=function(e){return this.map.has(e)},r.prototype.set=function(e,r){return this.map.set(e,r),this},r.prototype.get=function(e){return this.map.get(e)},r.prototype.clear=function(){},t.prototype.has=function(e){return -1!==this.keys.indexOf(e)},t.prototype.set=function(e,r){var t=this.keys.indexOf(e);return -1===t?(this.keys.push(e),this.values.push(r)):this.values[t]=r,this},t.prototype.get=function(e){var r=this.keys.indexOf(e);return -1===r?void 0:this.values[r]},t.prototype.clear=function(){this.keys=[],this.values=[]}},65766:(e,r,t)=>{"use strict";var n=t(58009);r.depth=function e(r){return function(t,a){return function(r,t,a){if(r.tagName){var l=r.properties&&Array.isArray(r.properties.className)?r.properties.className.join(" "):r.properties.className;return n.createElement(r.tagName,Object.assign({key:"fract-".concat(a,"-").concat(t)},r.properties,{className:l}),r.children&&r.children.map(e(a+1)))}return r.value}(t,a,r)}}},62310:(e,r,t)=>{"use strict";var n="object"==typeof globalThis?globalThis:"object"==typeof self?self:"object"==typeof window?window:"object"==typeof global?global:{},a=function(){var e="Prism"in n,r=e?n.Prism:void 0;return function(){e?n.Prism=r:delete n.Prism,e=void 0,r=void 0}}();n.Prism={manual:!0,disableWorkerMessageHandler:!0};var l=t(74910),o=t(9892),i=t(44191),s=t(7468),u=t(50989),c=t(46004),p=t(98243);a();var g={}.hasOwnProperty;function d(){}d.prototype=i;var f=new d;function m(e){if("function"!=typeof e||!e.displayName)throw Error("Expected `function` for `grammar`, got `"+e+"`");void 0===f.languages[e.displayName]&&e(f)}e.exports=f,f.highlight=function(e,r){var t,n=i.highlight;if("string"!=typeof e)throw Error("Expected `string` for `value`, got `"+e+"`");if("Object"===f.util.type(r))t=r,r=null;else{if("string"!=typeof r)throw Error("Expected `string` for `name`, got `"+r+"`");if(g.call(f.languages,r))t=f.languages[r];else throw Error("Unknown language: `"+r+"` is not registered")}return n.call(this,e,t,r)},f.register=m,f.alias=function(e,r){var t,n,a,l,o=f.languages,i=e;for(t in r&&((i={})[e]=r),i)for(a=(n="string"==typeof(n=i[t])?[n]:n).length,l=-1;++l<a;)o[n[l]]=o[t]},f.registered=function(e){if("string"!=typeof e)throw Error("Expected `string` for `language`, got `"+e+"`");return g.call(f.languages,e)},f.listLanguages=function(){var e,r=f.languages,t=[];for(e in r)g.call(r,e)&&"object"==typeof r[e]&&t.push(e);return t},m(s),m(u),m(c),m(p),f.util.encode=function(e){return e},f.Token.stringify=function(e,r,t){var n;return"string"==typeof e?{type:"text",value:e}:"Array"===f.util.type(e)?function(e,r){for(var t,n=[],a=e.length,l=-1;++l<a;)""!==(t=e[l])&&null!=t&&n.push(t);for(l=-1,a=n.length;++l<a;)t=n[l],n[l]=f.Token.stringify(t,r,n);return n}(e,r):(n={type:e.type,content:f.Token.stringify(e.content,r,t),tag:"span",classes:["token",e.type],attributes:{},language:r,parent:t},e.alias&&(n.classes=n.classes.concat(e.alias)),f.hooks.run("wrap",n),l(n.tag+"."+n.classes.join("."),function(e){var r;for(r in e)e[r]=o(e[r]);return e}(n.attributes),n.content))}},46004:e=>{"use strict";function r(e){e.languages.clike={comment:[{pattern:/(^|[^\\])\/\*[\s\S]*?(?:\*\/|$)/,lookbehind:!0,greedy:!0},{pattern:/(^|[^\\:])\/\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,greedy:!0},"class-name":{pattern:/(\b(?:class|extends|implements|instanceof|interface|new|trait)\s+|\bcatch\s+\()[\w.\\]+/i,lookbehind:!0,inside:{punctuation:/[.\\]/}},keyword:/\b(?:break|catch|continue|do|else|finally|for|function|if|in|instanceof|new|null|return|throw|try|while)\b/,boolean:/\b(?:false|true)\b/,function:/\b\w+(?=\()/,number:/\b0x[\da-f]+\b|(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:e[+-]?\d+)?/i,operator:/[<>]=?|[!=]=?=?|--?|\+\+?|&&?|\|\|?|[?*/~^%]/,punctuation:/[{}[\];(),.:]/}}e.exports=r,r.displayName="clike",r.aliases=[]},50989:e=>{"use strict";function r(e){var r,t;r=/(?:"(?:\\(?:\r\n|[\s\S])|[^"\\\r\n])*"|'(?:\\(?:\r\n|[\s\S])|[^'\\\r\n])*')/,e.languages.css={comment:/\/\*[\s\S]*?\*\//,atrule:{pattern:/@[\w-](?:[^;{\s]|\s+(?![\s{]))*(?:;|(?=\s*\{))/,inside:{rule:/^@[\w-]+/,"selector-function-argument":{pattern:/(\bselector\s*\(\s*(?![\s)]))(?:[^()\s]|\s+(?![\s)])|\((?:[^()]|\([^()]*\))*\))+(?=\s*\))/,lookbehind:!0,alias:"selector"},keyword:{pattern:/(^|[^\w-])(?:and|not|only|or)(?![\w-])/,lookbehind:!0}}},url:{pattern:RegExp("\\burl\\((?:"+r.source+"|"+/(?:[^\\\r\n()"']|\\[\s\S])*/.source+")\\)","i"),greedy:!0,inside:{function:/^url/i,punctuation:/^\(|\)$/,string:{pattern:RegExp("^"+r.source+"$"),alias:"url"}}},selector:{pattern:RegExp("(^|[{}\\s])[^{}\\s](?:[^{};\"'\\s]|\\s+(?![\\s{])|"+r.source+")*(?=\\s*\\{)"),lookbehind:!0},string:{pattern:r,greedy:!0},property:{pattern:/(^|[^-\w\xA0-\uFFFF])(?!\s)[-_a-z\xA0-\uFFFF](?:(?!\s)[-\w\xA0-\uFFFF])*(?=\s*:)/i,lookbehind:!0},important:/!important\b/i,function:{pattern:/(^|[^-a-z0-9])[-a-z0-9]+(?=\()/i,lookbehind:!0},punctuation:/[(){};:,]/},e.languages.css.atrule.inside.rest=e.languages.css,(t=e.languages.markup)&&(t.tag.addInlined("style","css"),t.tag.addAttribute("style","css"))}e.exports=r,r.displayName="css",r.aliases=[]},98243:e=>{"use strict";function r(e){e.languages.javascript=e.languages.extend("clike",{"class-name":[e.languages.clike["class-name"],{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$A-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\.(?:constructor|prototype))/,lookbehind:!0}],keyword:[{pattern:/((?:^|\})\s*)catch\b/,lookbehind:!0},{pattern:/(^|[^.]|\.\.\.\s*)\b(?:as|assert(?=\s*\{)|async(?=\s*(?:function\b|\(|[$\w\xA0-\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally(?=\s*(?:\{|$))|for|from(?=\s*(?:['"]|$))|function|(?:get|set)(?=\s*(?:[#\[$\w\xA0-\uFFFF]|$))|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\b/,lookbehind:!0}],function:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*(?:\.\s*(?:apply|bind|call)\s*)?\()/,number:{pattern:RegExp(/(^|[^\w$])/.source+"(?:"+(/NaN|Infinity/.source+"|"+/0[bB][01]+(?:_[01]+)*n?/.source+"|"+/0[oO][0-7]+(?:_[0-7]+)*n?/.source+"|"+/0[xX][\dA-Fa-f]+(?:_[\dA-Fa-f]+)*n?/.source+"|")+/\d+(?:_\d+)*n/.source+"|"+/(?:\d+(?:_\d+)*(?:\.(?:\d+(?:_\d+)*)?)?|\.\d+(?:_\d+)*)(?:[Ee][+-]?\d+(?:_\d+)*)?/.source+")"+/(?![\w$])/.source),lookbehind:!0},operator:/--|\+\+|\*\*=?|=>|&&=?|\|\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\.{3}|\?\?=?|\?\.?|[~:]/}),e.languages.javascript["class-name"][0].pattern=/(\b(?:class|extends|implements|instanceof|interface|new)\s+)[\w.\\]+/,e.languages.insertBefore("javascript","keyword",{regex:{pattern:/((?:^|[^$\w\xA0-\uFFFF."'\])\s]|\b(?:return|yield))\s*)\/(?:\[(?:[^\]\\\r\n]|\\.)*\]|\\.|[^/\\\[\r\n])+\/[dgimyus]{0,7}(?=(?:\s|\/\*(?:[^*]|\*(?!\/))*\*\/)*(?:$|[\r\n,.;:})\]]|\/\/))/,lookbehind:!0,greedy:!0,inside:{"regex-source":{pattern:/^(\/)[\s\S]+(?=\/[a-z]*$)/,lookbehind:!0,alias:"language-regex",inside:e.languages.regex},"regex-delimiter":/^\/|\/$/,"regex-flags":/^[a-z]+$/}},"function-variable":{pattern:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*[=:]\s*(?:async\s*)?(?:\bfunction\b|(?:\((?:[^()]|\([^()]*\))*\)|(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)\s*=>))/,alias:"function"},parameter:[{pattern:/(function(?:\s+(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)?\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\))/,lookbehind:!0,inside:e.languages.javascript},{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$a-z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*=>)/i,lookbehind:!0,inside:e.languages.javascript},{pattern:/(\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*=>)/,lookbehind:!0,inside:e.languages.javascript},{pattern:/((?:\b|\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\w\xA0-\uFFFF]))(?:(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*\s*)\(\s*|\]\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*\{)/,lookbehind:!0,inside:e.languages.javascript}],constant:/\b[A-Z](?:[A-Z_]|\dx?)*\b/}),e.languages.insertBefore("javascript","string",{hashbang:{pattern:/^#!.*/,greedy:!0,alias:"comment"},"template-string":{pattern:/`(?:\\[\s\S]|\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}|(?!\$\{)[^\\`])*`/,greedy:!0,inside:{"template-punctuation":{pattern:/^`|`$/,alias:"string"},interpolation:{pattern:/((?:^|[^\\])(?:\\{2})*)\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}/,lookbehind:!0,inside:{"interpolation-punctuation":{pattern:/^\$\{|\}$/,alias:"punctuation"},rest:e.languages.javascript}},string:/[\s\S]+/}},"string-property":{pattern:/((?:^|[,{])[ \t]*)(["'])(?:\\(?:\r\n|[\s\S])|(?!\2)[^\\\r\n])*\2(?=\s*:)/m,lookbehind:!0,greedy:!0,alias:"property"}}),e.languages.insertBefore("javascript","operator",{"literal-property":{pattern:/((?:^|[,{])[ \t]*)(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*:)/m,lookbehind:!0,alias:"property"}}),e.languages.markup&&(e.languages.markup.tag.addInlined("script","javascript"),e.languages.markup.tag.addAttribute(/on(?:abort|blur|change|click|composition(?:end|start|update)|dblclick|error|focus(?:in|out)?|key(?:down|up)|load|mouse(?:down|enter|leave|move|out|over|up)|reset|resize|scroll|select|slotchange|submit|unload|wheel)/.source,"javascript")),e.languages.js=e.languages.javascript}e.exports=r,r.displayName="javascript",r.aliases=["js"]},7468:e=>{"use strict";function r(e){e.languages.markup={comment:{pattern:/<!--(?:(?!<!--)[\s\S])*?-->/,greedy:!0},prolog:{pattern:/<\?[\s\S]+?\?>/,greedy:!0},doctype:{pattern:/<!DOCTYPE(?:[^>"'[\]]|"[^"]*"|'[^']*')+(?:\[(?:[^<"'\]]|"[^"]*"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\]\s*)?>/i,greedy:!0,inside:{"internal-subset":{pattern:/(^[^\[]*\[)[\s\S]+(?=\]>$)/,lookbehind:!0,greedy:!0,inside:null},string:{pattern:/"[^"]*"|'[^']*'/,greedy:!0},punctuation:/^<!|>$|[[\]]/,"doctype-tag":/^DOCTYPE/i,name:/[^\s<>'"]+/}},cdata:{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,greedy:!0},tag:{pattern:/<\/?(?!\d)[^\s>\/=$<%]+(?:\s(?:\s*[^\s>\/=]+(?:\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))|(?=[\s/>])))+)?\s*\/?>/,greedy:!0,inside:{tag:{pattern:/^<\/?[^\s>\/]+/,inside:{punctuation:/^<\/?/,namespace:/^[^\s>\/:]+:/}},"special-attr":[],"attr-value":{pattern:/=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+)/,inside:{punctuation:[{pattern:/^=/,alias:"attr-equals"},/"|'/]}},punctuation:/\/?>/,"attr-name":{pattern:/[^\s>\/]+/,inside:{namespace:/^[^\s>\/:]+:/}}}},entity:[{pattern:/&[\da-z]{1,8};/i,alias:"named-entity"},/&#x?[\da-f]{1,8};/i]},e.languages.markup.tag.inside["attr-value"].inside.entity=e.languages.markup.entity,e.languages.markup.doctype.inside["internal-subset"].inside=e.languages.markup,e.hooks.add("wrap",function(e){"entity"===e.type&&(e.attributes.title=e.content.value.replace(/&amp;/,"&"))}),Object.defineProperty(e.languages.markup.tag,"addInlined",{value:function(r,t){var n={};n["language-"+t]={pattern:/(^<!\[CDATA\[)[\s\S]+?(?=\]\]>$)/i,lookbehind:!0,inside:e.languages[t]},n.cdata=/^<!\[CDATA\[|\]\]>$/i;var a={"included-cdata":{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,inside:n}};a["language-"+t]={pattern:/[\s\S]+/,inside:e.languages[t]};var l={};l[r]={pattern:RegExp(/(<__[^>]*>)(?:<!\[CDATA\[(?:[^\]]|\](?!\]>))*\]\]>|(?!<!\[CDATA\[)[\s\S])*?(?=<\/__>)/.source.replace(/__/g,function(){return r}),"i"),lookbehind:!0,greedy:!0,inside:a},e.languages.insertBefore("markup","cdata",l)}}),Object.defineProperty(e.languages.markup.tag,"addAttribute",{value:function(r,t){e.languages.markup.tag.inside["special-attr"].push({pattern:RegExp(/(^|["'\s])/.source+"(?:"+r+")"+/\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))/.source,"i"),lookbehind:!0,inside:{"attr-name":/^[^\s=]+/,"attr-value":{pattern:/=[\s\S]+/,inside:{value:{pattern:/(^=\s*(["']|(?!["'])))\S[\s\S]*(?=\2$)/,lookbehind:!0,alias:[t,"language-"+t],inside:e.languages[t]},punctuation:[{pattern:/^=/,alias:"attr-equals"},/"|'/]}}}})}}),e.languages.html=e.languages.markup,e.languages.mathml=e.languages.markup,e.languages.svg=e.languages.markup,e.languages.xml=e.languages.extend("markup",{}),e.languages.ssml=e.languages.xml,e.languages.atom=e.languages.xml,e.languages.rss=e.languages.xml}e.exports=r,r.displayName="markup",r.aliases=["html","mathml","svg","xml","ssml","atom","rss"]},74976:(e,r)=>{"use strict";r.q=function(e){var r=String(e||"").trim();return""===r?[]:r.split(t)};var t=/[ \t\n\r\f]+/g},41918:(e,r,t)=>{"use strict";var n=t(70863);e.exports=function(e,r,t){var l=n(t||r),o=null==r.cascade||r.cascade;return function e(r,t,n){var i,s,u,c,p;if(!l(r,t,n))return null;if(r.children){for(i=[],s=-1;++s<r.children.length;)(u=e(r.children[s],s,r))&&i.push(u);if(o&&r.children.length&&!i.length)return null}for(p in c={},r)a.call(r,p)&&(c[p]="children"===p?i:r[p]);return c}(e,null,null)};var a={}.hasOwnProperty},70863:e=>{"use strict";function r(){return!0}e.exports=function e(t){if(null==t)return r;if("string"==typeof t)return function(e){return!!(e&&e.type===t)};if("object"==typeof t)return"length"in t?function(r){for(var t=[],n=-1;++n<r.length;)t[n]=e(r[n]);return function(){for(var e=-1;++e<t.length;)if(t[e].apply(this,arguments))return!0;return!1}}(t):function(e){var r;for(r in t)if(e[r]!==t[r])return!1;return!0};if("function"==typeof t)return t;throw Error("Expected function, string, or object as test")}},37060:e=>{e.exports=function(e){return"\x1b[33m"+e+"\x1b[39m"}},32831:(e,r,t)=>{"use strict";e.exports=o;var n=t(70863),a=t(37060),l="skip";function o(e,r,t,o){var i,s;"function"==typeof r&&"function"!=typeof t&&(o=t,t=r,r=null),s=n(r),i=o?-1:1,(function e(n,u,c){var p,g="object"==typeof n&&null!==n?n:{};return"string"==typeof g.type&&(p="string"==typeof g.tagName?g.tagName:"string"==typeof g.name?g.name:void 0,d.displayName="node ("+a(g.type+(p?"<"+p+">":""))+")"),d;function d(){var a,p,g,d=c.concat(n),f=[];if((!r||s(n,u,c[c.length-1]||null))&&!1===(f=null!==(a=t(n,c))&&"object"==typeof a&&"length"in a?a:"number"==typeof a?[!0,a]:[a])[0])return f;if(n.children&&f[0]!==l)for(g=(o?n.children.length:-1)+i;g>-1&&g<n.children.length;){if(!1===(p=e(n.children[g],g,d)())[0])return p;g="number"==typeof p[1]?p[1]:g+i}return f}})(e,null,[])()}o.CONTINUE=!0,o.SKIP=l,o.EXIT=!1},7654:e=>{e.exports=function(){for(var e={},t=0;t<arguments.length;t++){var n=arguments[t];for(var a in n)r.call(n,a)&&(e[a]=n[a])}return e};var r=Object.prototype.hasOwnProperty},92475:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var n=t(45512),a=t(4548),l=t(87773);function o(e){let r,t,o,i;let s=(0,a.c)(13),{language:u,value:c}=e,p="string"==typeof u?u:void 0;s[0]!==p?(r=!!p&&l.hasLanguage(p),s[0]=p,s[1]=r):r=s[1];let g=r;return s[2]!==p||s[3]!==g||s[4]!==c?(t=!(p&&g)&&(0,n.jsx)("code",{children:c}),s[2]=p,s[3]=g,s[4]=c,s[5]=t):t=s[5],s[6]!==p||s[7]!==g||s[8]!==c?(o=p&&g&&(0,n.jsx)(l,{inline:!0,language:p,value:String(c)}),s[6]=p,s[7]=g,s[8]=c,s[9]=o):o=s[9],s[10]!==t||s[11]!==o?(i=(0,n.jsxs)(n.Fragment,{children:[t,o]}),s[10]=t,s[11]=o,s[12]=i):i=s[12],i}o.displayName="LazyRefractor"},36347:e=>{"use strict";e.exports=JSON.parse('{"AElig":"\xc6","AMP":"&","Aacute":"\xc1","Acirc":"\xc2","Agrave":"\xc0","Aring":"\xc5","Atilde":"\xc3","Auml":"\xc4","COPY":"\xa9","Ccedil":"\xc7","ETH":"\xd0","Eacute":"\xc9","Ecirc":"\xca","Egrave":"\xc8","Euml":"\xcb","GT":">","Iacute":"\xcd","Icirc":"\xce","Igrave":"\xcc","Iuml":"\xcf","LT":"<","Ntilde":"\xd1","Oacute":"\xd3","Ocirc":"\xd4","Ograve":"\xd2","Oslash":"\xd8","Otilde":"\xd5","Ouml":"\xd6","QUOT":"\\"","REG":"\xae","THORN":"\xde","Uacute":"\xda","Ucirc":"\xdb","Ugrave":"\xd9","Uuml":"\xdc","Yacute":"\xdd","aacute":"\xe1","acirc":"\xe2","acute":"\xb4","aelig":"\xe6","agrave":"\xe0","amp":"&","aring":"\xe5","atilde":"\xe3","auml":"\xe4","brvbar":"\xa6","ccedil":"\xe7","cedil":"\xb8","cent":"\xa2","copy":"\xa9","curren":"\xa4","deg":"\xb0","divide":"\xf7","eacute":"\xe9","ecirc":"\xea","egrave":"\xe8","eth":"\xf0","euml":"\xeb","frac12":"\xbd","frac14":"\xbc","frac34":"\xbe","gt":">","iacute":"\xed","icirc":"\xee","iexcl":"\xa1","igrave":"\xec","iquest":"\xbf","iuml":"\xef","laquo":"\xab","lt":"<","macr":"\xaf","micro":"\xb5","middot":"\xb7","nbsp":"\xa0","not":"\xac","ntilde":"\xf1","oacute":"\xf3","ocirc":"\xf4","ograve":"\xf2","ordf":"\xaa","ordm":"\xba","oslash":"\xf8","otilde":"\xf5","ouml":"\xf6","para":"\xb6","plusmn":"\xb1","pound":"\xa3","quot":"\\"","raquo":"\xbb","reg":"\xae","sect":"\xa7","shy":"\xad","sup1":"\xb9","sup2":"\xb2","sup3":"\xb3","szlig":"\xdf","thorn":"\xfe","times":"\xd7","uacute":"\xfa","ucirc":"\xfb","ugrave":"\xf9","uml":"\xa8","uuml":"\xfc","yacute":"\xfd","yen":"\xa5","yuml":"\xff"}')},33323:e=>{"use strict";e.exports=JSON.parse('{"AEli":"\xc6","AElig":"\xc6","AM":"&","AMP":"&","Aacut":"\xc1","Aacute":"\xc1","Abreve":"Ă","Acir":"\xc2","Acirc":"\xc2","Acy":"А","Afr":"\uD835\uDD04","Agrav":"\xc0","Agrave":"\xc0","Alpha":"Α","Amacr":"Ā","And":"⩓","Aogon":"Ą","Aopf":"\uD835\uDD38","ApplyFunction":"⁡","Arin":"\xc5","Aring":"\xc5","Ascr":"\uD835\uDC9C","Assign":"≔","Atild":"\xc3","Atilde":"\xc3","Aum":"\xc4","Auml":"\xc4","Backslash":"∖","Barv":"⫧","Barwed":"⌆","Bcy":"Б","Because":"∵","Bernoullis":"ℬ","Beta":"Β","Bfr":"\uD835\uDD05","Bopf":"\uD835\uDD39","Breve":"˘","Bscr":"ℬ","Bumpeq":"≎","CHcy":"Ч","COP":"\xa9","COPY":"\xa9","Cacute":"Ć","Cap":"⋒","CapitalDifferentialD":"ⅅ","Cayleys":"ℭ","Ccaron":"Č","Ccedi":"\xc7","Ccedil":"\xc7","Ccirc":"Ĉ","Cconint":"∰","Cdot":"Ċ","Cedilla":"\xb8","CenterDot":"\xb7","Cfr":"ℭ","Chi":"Χ","CircleDot":"⊙","CircleMinus":"⊖","CirclePlus":"⊕","CircleTimes":"⊗","ClockwiseContourIntegral":"∲","CloseCurlyDoubleQuote":"”","CloseCurlyQuote":"’","Colon":"∷","Colone":"⩴","Congruent":"≡","Conint":"∯","ContourIntegral":"∮","Copf":"ℂ","Coproduct":"∐","CounterClockwiseContourIntegral":"∳","Cross":"⨯","Cscr":"\uD835\uDC9E","Cup":"⋓","CupCap":"≍","DD":"ⅅ","DDotrahd":"⤑","DJcy":"Ђ","DScy":"Ѕ","DZcy":"Џ","Dagger":"‡","Darr":"↡","Dashv":"⫤","Dcaron":"Ď","Dcy":"Д","Del":"∇","Delta":"Δ","Dfr":"\uD835\uDD07","DiacriticalAcute":"\xb4","DiacriticalDot":"˙","DiacriticalDoubleAcute":"˝","DiacriticalGrave":"`","DiacriticalTilde":"˜","Diamond":"⋄","DifferentialD":"ⅆ","Dopf":"\uD835\uDD3B","Dot":"\xa8","DotDot":"⃜","DotEqual":"≐","DoubleContourIntegral":"∯","DoubleDot":"\xa8","DoubleDownArrow":"⇓","DoubleLeftArrow":"⇐","DoubleLeftRightArrow":"⇔","DoubleLeftTee":"⫤","DoubleLongLeftArrow":"⟸","DoubleLongLeftRightArrow":"⟺","DoubleLongRightArrow":"⟹","DoubleRightArrow":"⇒","DoubleRightTee":"⊨","DoubleUpArrow":"⇑","DoubleUpDownArrow":"⇕","DoubleVerticalBar":"∥","DownArrow":"↓","DownArrowBar":"⤓","DownArrowUpArrow":"⇵","DownBreve":"̑","DownLeftRightVector":"⥐","DownLeftTeeVector":"⥞","DownLeftVector":"↽","DownLeftVectorBar":"⥖","DownRightTeeVector":"⥟","DownRightVector":"⇁","DownRightVectorBar":"⥗","DownTee":"⊤","DownTeeArrow":"↧","Downarrow":"⇓","Dscr":"\uD835\uDC9F","Dstrok":"Đ","ENG":"Ŋ","ET":"\xd0","ETH":"\xd0","Eacut":"\xc9","Eacute":"\xc9","Ecaron":"Ě","Ecir":"\xca","Ecirc":"\xca","Ecy":"Э","Edot":"Ė","Efr":"\uD835\uDD08","Egrav":"\xc8","Egrave":"\xc8","Element":"∈","Emacr":"Ē","EmptySmallSquare":"◻","EmptyVerySmallSquare":"▫","Eogon":"Ę","Eopf":"\uD835\uDD3C","Epsilon":"Ε","Equal":"⩵","EqualTilde":"≂","Equilibrium":"⇌","Escr":"ℰ","Esim":"⩳","Eta":"Η","Eum":"\xcb","Euml":"\xcb","Exists":"∃","ExponentialE":"ⅇ","Fcy":"Ф","Ffr":"\uD835\uDD09","FilledSmallSquare":"◼","FilledVerySmallSquare":"▪","Fopf":"\uD835\uDD3D","ForAll":"∀","Fouriertrf":"ℱ","Fscr":"ℱ","GJcy":"Ѓ","G":">","GT":">","Gamma":"Γ","Gammad":"Ϝ","Gbreve":"Ğ","Gcedil":"Ģ","Gcirc":"Ĝ","Gcy":"Г","Gdot":"Ġ","Gfr":"\uD835\uDD0A","Gg":"⋙","Gopf":"\uD835\uDD3E","GreaterEqual":"≥","GreaterEqualLess":"⋛","GreaterFullEqual":"≧","GreaterGreater":"⪢","GreaterLess":"≷","GreaterSlantEqual":"⩾","GreaterTilde":"≳","Gscr":"\uD835\uDCA2","Gt":"≫","HARDcy":"Ъ","Hacek":"ˇ","Hat":"^","Hcirc":"Ĥ","Hfr":"ℌ","HilbertSpace":"ℋ","Hopf":"ℍ","HorizontalLine":"─","Hscr":"ℋ","Hstrok":"Ħ","HumpDownHump":"≎","HumpEqual":"≏","IEcy":"Е","IJlig":"Ĳ","IOcy":"Ё","Iacut":"\xcd","Iacute":"\xcd","Icir":"\xce","Icirc":"\xce","Icy":"И","Idot":"İ","Ifr":"ℑ","Igrav":"\xcc","Igrave":"\xcc","Im":"ℑ","Imacr":"Ī","ImaginaryI":"ⅈ","Implies":"⇒","Int":"∬","Integral":"∫","Intersection":"⋂","InvisibleComma":"⁣","InvisibleTimes":"⁢","Iogon":"Į","Iopf":"\uD835\uDD40","Iota":"Ι","Iscr":"ℐ","Itilde":"Ĩ","Iukcy":"І","Ium":"\xcf","Iuml":"\xcf","Jcirc":"Ĵ","Jcy":"Й","Jfr":"\uD835\uDD0D","Jopf":"\uD835\uDD41","Jscr":"\uD835\uDCA5","Jsercy":"Ј","Jukcy":"Є","KHcy":"Х","KJcy":"Ќ","Kappa":"Κ","Kcedil":"Ķ","Kcy":"К","Kfr":"\uD835\uDD0E","Kopf":"\uD835\uDD42","Kscr":"\uD835\uDCA6","LJcy":"Љ","L":"<","LT":"<","Lacute":"Ĺ","Lambda":"Λ","Lang":"⟪","Laplacetrf":"ℒ","Larr":"↞","Lcaron":"Ľ","Lcedil":"Ļ","Lcy":"Л","LeftAngleBracket":"⟨","LeftArrow":"←","LeftArrowBar":"⇤","LeftArrowRightArrow":"⇆","LeftCeiling":"⌈","LeftDoubleBracket":"⟦","LeftDownTeeVector":"⥡","LeftDownVector":"⇃","LeftDownVectorBar":"⥙","LeftFloor":"⌊","LeftRightArrow":"↔","LeftRightVector":"⥎","LeftTee":"⊣","LeftTeeArrow":"↤","LeftTeeVector":"⥚","LeftTriangle":"⊲","LeftTriangleBar":"⧏","LeftTriangleEqual":"⊴","LeftUpDownVector":"⥑","LeftUpTeeVector":"⥠","LeftUpVector":"↿","LeftUpVectorBar":"⥘","LeftVector":"↼","LeftVectorBar":"⥒","Leftarrow":"⇐","Leftrightarrow":"⇔","LessEqualGreater":"⋚","LessFullEqual":"≦","LessGreater":"≶","LessLess":"⪡","LessSlantEqual":"⩽","LessTilde":"≲","Lfr":"\uD835\uDD0F","Ll":"⋘","Lleftarrow":"⇚","Lmidot":"Ŀ","LongLeftArrow":"⟵","LongLeftRightArrow":"⟷","LongRightArrow":"⟶","Longleftarrow":"⟸","Longleftrightarrow":"⟺","Longrightarrow":"⟹","Lopf":"\uD835\uDD43","LowerLeftArrow":"↙","LowerRightArrow":"↘","Lscr":"ℒ","Lsh":"↰","Lstrok":"Ł","Lt":"≪","Map":"⤅","Mcy":"М","MediumSpace":" ","Mellintrf":"ℳ","Mfr":"\uD835\uDD10","MinusPlus":"∓","Mopf":"\uD835\uDD44","Mscr":"ℳ","Mu":"Μ","NJcy":"Њ","Nacute":"Ń","Ncaron":"Ň","Ncedil":"Ņ","Ncy":"Н","NegativeMediumSpace":"​","NegativeThickSpace":"​","NegativeThinSpace":"​","NegativeVeryThinSpace":"​","NestedGreaterGreater":"≫","NestedLessLess":"≪","NewLine":"\\n","Nfr":"\uD835\uDD11","NoBreak":"⁠","NonBreakingSpace":"\xa0","Nopf":"ℕ","Not":"⫬","NotCongruent":"≢","NotCupCap":"≭","NotDoubleVerticalBar":"∦","NotElement":"∉","NotEqual":"≠","NotEqualTilde":"≂̸","NotExists":"∄","NotGreater":"≯","NotGreaterEqual":"≱","NotGreaterFullEqual":"≧̸","NotGreaterGreater":"≫̸","NotGreaterLess":"≹","NotGreaterSlantEqual":"⩾̸","NotGreaterTilde":"≵","NotHumpDownHump":"≎̸","NotHumpEqual":"≏̸","NotLeftTriangle":"⋪","NotLeftTriangleBar":"⧏̸","NotLeftTriangleEqual":"⋬","NotLess":"≮","NotLessEqual":"≰","NotLessGreater":"≸","NotLessLess":"≪̸","NotLessSlantEqual":"⩽̸","NotLessTilde":"≴","NotNestedGreaterGreater":"⪢̸","NotNestedLessLess":"⪡̸","NotPrecedes":"⊀","NotPrecedesEqual":"⪯̸","NotPrecedesSlantEqual":"⋠","NotReverseElement":"∌","NotRightTriangle":"⋫","NotRightTriangleBar":"⧐̸","NotRightTriangleEqual":"⋭","NotSquareSubset":"⊏̸","NotSquareSubsetEqual":"⋢","NotSquareSuperset":"⊐̸","NotSquareSupersetEqual":"⋣","NotSubset":"⊂⃒","NotSubsetEqual":"⊈","NotSucceeds":"⊁","NotSucceedsEqual":"⪰̸","NotSucceedsSlantEqual":"⋡","NotSucceedsTilde":"≿̸","NotSuperset":"⊃⃒","NotSupersetEqual":"⊉","NotTilde":"≁","NotTildeEqual":"≄","NotTildeFullEqual":"≇","NotTildeTilde":"≉","NotVerticalBar":"∤","Nscr":"\uD835\uDCA9","Ntild":"\xd1","Ntilde":"\xd1","Nu":"Ν","OElig":"Œ","Oacut":"\xd3","Oacute":"\xd3","Ocir":"\xd4","Ocirc":"\xd4","Ocy":"О","Odblac":"Ő","Ofr":"\uD835\uDD12","Ograv":"\xd2","Ograve":"\xd2","Omacr":"Ō","Omega":"Ω","Omicron":"Ο","Oopf":"\uD835\uDD46","OpenCurlyDoubleQuote":"“","OpenCurlyQuote":"‘","Or":"⩔","Oscr":"\uD835\uDCAA","Oslas":"\xd8","Oslash":"\xd8","Otild":"\xd5","Otilde":"\xd5","Otimes":"⨷","Oum":"\xd6","Ouml":"\xd6","OverBar":"‾","OverBrace":"⏞","OverBracket":"⎴","OverParenthesis":"⏜","PartialD":"∂","Pcy":"П","Pfr":"\uD835\uDD13","Phi":"Φ","Pi":"Π","PlusMinus":"\xb1","Poincareplane":"ℌ","Popf":"ℙ","Pr":"⪻","Precedes":"≺","PrecedesEqual":"⪯","PrecedesSlantEqual":"≼","PrecedesTilde":"≾","Prime":"″","Product":"∏","Proportion":"∷","Proportional":"∝","Pscr":"\uD835\uDCAB","Psi":"Ψ","QUO":"\\"","QUOT":"\\"","Qfr":"\uD835\uDD14","Qopf":"ℚ","Qscr":"\uD835\uDCAC","RBarr":"⤐","RE":"\xae","REG":"\xae","Racute":"Ŕ","Rang":"⟫","Rarr":"↠","Rarrtl":"⤖","Rcaron":"Ř","Rcedil":"Ŗ","Rcy":"Р","Re":"ℜ","ReverseElement":"∋","ReverseEquilibrium":"⇋","ReverseUpEquilibrium":"⥯","Rfr":"ℜ","Rho":"Ρ","RightAngleBracket":"⟩","RightArrow":"→","RightArrowBar":"⇥","RightArrowLeftArrow":"⇄","RightCeiling":"⌉","RightDoubleBracket":"⟧","RightDownTeeVector":"⥝","RightDownVector":"⇂","RightDownVectorBar":"⥕","RightFloor":"⌋","RightTee":"⊢","RightTeeArrow":"↦","RightTeeVector":"⥛","RightTriangle":"⊳","RightTriangleBar":"⧐","RightTriangleEqual":"⊵","RightUpDownVector":"⥏","RightUpTeeVector":"⥜","RightUpVector":"↾","RightUpVectorBar":"⥔","RightVector":"⇀","RightVectorBar":"⥓","Rightarrow":"⇒","Ropf":"ℝ","RoundImplies":"⥰","Rrightarrow":"⇛","Rscr":"ℛ","Rsh":"↱","RuleDelayed":"⧴","SHCHcy":"Щ","SHcy":"Ш","SOFTcy":"Ь","Sacute":"Ś","Sc":"⪼","Scaron":"Š","Scedil":"Ş","Scirc":"Ŝ","Scy":"С","Sfr":"\uD835\uDD16","ShortDownArrow":"↓","ShortLeftArrow":"←","ShortRightArrow":"→","ShortUpArrow":"↑","Sigma":"Σ","SmallCircle":"∘","Sopf":"\uD835\uDD4A","Sqrt":"√","Square":"□","SquareIntersection":"⊓","SquareSubset":"⊏","SquareSubsetEqual":"⊑","SquareSuperset":"⊐","SquareSupersetEqual":"⊒","SquareUnion":"⊔","Sscr":"\uD835\uDCAE","Star":"⋆","Sub":"⋐","Subset":"⋐","SubsetEqual":"⊆","Succeeds":"≻","SucceedsEqual":"⪰","SucceedsSlantEqual":"≽","SucceedsTilde":"≿","SuchThat":"∋","Sum":"∑","Sup":"⋑","Superset":"⊃","SupersetEqual":"⊇","Supset":"⋑","THOR":"\xde","THORN":"\xde","TRADE":"™","TSHcy":"Ћ","TScy":"Ц","Tab":"\\t","Tau":"Τ","Tcaron":"Ť","Tcedil":"Ţ","Tcy":"Т","Tfr":"\uD835\uDD17","Therefore":"∴","Theta":"Θ","ThickSpace":"  ","ThinSpace":" ","Tilde":"∼","TildeEqual":"≃","TildeFullEqual":"≅","TildeTilde":"≈","Topf":"\uD835\uDD4B","TripleDot":"⃛","Tscr":"\uD835\uDCAF","Tstrok":"Ŧ","Uacut":"\xda","Uacute":"\xda","Uarr":"↟","Uarrocir":"⥉","Ubrcy":"Ў","Ubreve":"Ŭ","Ucir":"\xdb","Ucirc":"\xdb","Ucy":"У","Udblac":"Ű","Ufr":"\uD835\uDD18","Ugrav":"\xd9","Ugrave":"\xd9","Umacr":"Ū","UnderBar":"_","UnderBrace":"⏟","UnderBracket":"⎵","UnderParenthesis":"⏝","Union":"⋃","UnionPlus":"⊎","Uogon":"Ų","Uopf":"\uD835\uDD4C","UpArrow":"↑","UpArrowBar":"⤒","UpArrowDownArrow":"⇅","UpDownArrow":"↕","UpEquilibrium":"⥮","UpTee":"⊥","UpTeeArrow":"↥","Uparrow":"⇑","Updownarrow":"⇕","UpperLeftArrow":"↖","UpperRightArrow":"↗","Upsi":"ϒ","Upsilon":"Υ","Uring":"Ů","Uscr":"\uD835\uDCB0","Utilde":"Ũ","Uum":"\xdc","Uuml":"\xdc","VDash":"⊫","Vbar":"⫫","Vcy":"В","Vdash":"⊩","Vdashl":"⫦","Vee":"⋁","Verbar":"‖","Vert":"‖","VerticalBar":"∣","VerticalLine":"|","VerticalSeparator":"❘","VerticalTilde":"≀","VeryThinSpace":" ","Vfr":"\uD835\uDD19","Vopf":"\uD835\uDD4D","Vscr":"\uD835\uDCB1","Vvdash":"⊪","Wcirc":"Ŵ","Wedge":"⋀","Wfr":"\uD835\uDD1A","Wopf":"\uD835\uDD4E","Wscr":"\uD835\uDCB2","Xfr":"\uD835\uDD1B","Xi":"Ξ","Xopf":"\uD835\uDD4F","Xscr":"\uD835\uDCB3","YAcy":"Я","YIcy":"Ї","YUcy":"Ю","Yacut":"\xdd","Yacute":"\xdd","Ycirc":"Ŷ","Ycy":"Ы","Yfr":"\uD835\uDD1C","Yopf":"\uD835\uDD50","Yscr":"\uD835\uDCB4","Yuml":"Ÿ","ZHcy":"Ж","Zacute":"Ź","Zcaron":"Ž","Zcy":"З","Zdot":"Ż","ZeroWidthSpace":"​","Zeta":"Ζ","Zfr":"ℨ","Zopf":"ℤ","Zscr":"\uD835\uDCB5","aacut":"\xe1","aacute":"\xe1","abreve":"ă","ac":"∾","acE":"∾̳","acd":"∿","acir":"\xe2","acirc":"\xe2","acut":"\xb4","acute":"\xb4","acy":"а","aeli":"\xe6","aelig":"\xe6","af":"⁡","afr":"\uD835\uDD1E","agrav":"\xe0","agrave":"\xe0","alefsym":"ℵ","aleph":"ℵ","alpha":"α","amacr":"ā","amalg":"⨿","am":"&","amp":"&","and":"∧","andand":"⩕","andd":"⩜","andslope":"⩘","andv":"⩚","ang":"∠","ange":"⦤","angle":"∠","angmsd":"∡","angmsdaa":"⦨","angmsdab":"⦩","angmsdac":"⦪","angmsdad":"⦫","angmsdae":"⦬","angmsdaf":"⦭","angmsdag":"⦮","angmsdah":"⦯","angrt":"∟","angrtvb":"⊾","angrtvbd":"⦝","angsph":"∢","angst":"\xc5","angzarr":"⍼","aogon":"ą","aopf":"\uD835\uDD52","ap":"≈","apE":"⩰","apacir":"⩯","ape":"≊","apid":"≋","apos":"\'","approx":"≈","approxeq":"≊","arin":"\xe5","aring":"\xe5","ascr":"\uD835\uDCB6","ast":"*","asymp":"≈","asympeq":"≍","atild":"\xe3","atilde":"\xe3","aum":"\xe4","auml":"\xe4","awconint":"∳","awint":"⨑","bNot":"⫭","backcong":"≌","backepsilon":"϶","backprime":"‵","backsim":"∽","backsimeq":"⋍","barvee":"⊽","barwed":"⌅","barwedge":"⌅","bbrk":"⎵","bbrktbrk":"⎶","bcong":"≌","bcy":"б","bdquo":"„","becaus":"∵","because":"∵","bemptyv":"⦰","bepsi":"϶","bernou":"ℬ","beta":"β","beth":"ℶ","between":"≬","bfr":"\uD835\uDD1F","bigcap":"⋂","bigcirc":"◯","bigcup":"⋃","bigodot":"⨀","bigoplus":"⨁","bigotimes":"⨂","bigsqcup":"⨆","bigstar":"★","bigtriangledown":"▽","bigtriangleup":"△","biguplus":"⨄","bigvee":"⋁","bigwedge":"⋀","bkarow":"⤍","blacklozenge":"⧫","blacksquare":"▪","blacktriangle":"▴","blacktriangledown":"▾","blacktriangleleft":"◂","blacktriangleright":"▸","blank":"␣","blk12":"▒","blk14":"░","blk34":"▓","block":"█","bne":"=⃥","bnequiv":"≡⃥","bnot":"⌐","bopf":"\uD835\uDD53","bot":"⊥","bottom":"⊥","bowtie":"⋈","boxDL":"╗","boxDR":"╔","boxDl":"╖","boxDr":"╓","boxH":"═","boxHD":"╦","boxHU":"╩","boxHd":"╤","boxHu":"╧","boxUL":"╝","boxUR":"╚","boxUl":"╜","boxUr":"╙","boxV":"║","boxVH":"╬","boxVL":"╣","boxVR":"╠","boxVh":"╫","boxVl":"╢","boxVr":"╟","boxbox":"⧉","boxdL":"╕","boxdR":"╒","boxdl":"┐","boxdr":"┌","boxh":"─","boxhD":"╥","boxhU":"╨","boxhd":"┬","boxhu":"┴","boxminus":"⊟","boxplus":"⊞","boxtimes":"⊠","boxuL":"╛","boxuR":"╘","boxul":"┘","boxur":"└","boxv":"│","boxvH":"╪","boxvL":"╡","boxvR":"╞","boxvh":"┼","boxvl":"┤","boxvr":"├","bprime":"‵","breve":"˘","brvba":"\xa6","brvbar":"\xa6","bscr":"\uD835\uDCB7","bsemi":"⁏","bsim":"∽","bsime":"⋍","bsol":"\\\\","bsolb":"⧅","bsolhsub":"⟈","bull":"•","bullet":"•","bump":"≎","bumpE":"⪮","bumpe":"≏","bumpeq":"≏","cacute":"ć","cap":"∩","capand":"⩄","capbrcup":"⩉","capcap":"⩋","capcup":"⩇","capdot":"⩀","caps":"∩︀","caret":"⁁","caron":"ˇ","ccaps":"⩍","ccaron":"č","ccedi":"\xe7","ccedil":"\xe7","ccirc":"ĉ","ccups":"⩌","ccupssm":"⩐","cdot":"ċ","cedi":"\xb8","cedil":"\xb8","cemptyv":"⦲","cen":"\xa2","cent":"\xa2","centerdot":"\xb7","cfr":"\uD835\uDD20","chcy":"ч","check":"✓","checkmark":"✓","chi":"χ","cir":"○","cirE":"⧃","circ":"ˆ","circeq":"≗","circlearrowleft":"↺","circlearrowright":"↻","circledR":"\xae","circledS":"Ⓢ","circledast":"⊛","circledcirc":"⊚","circleddash":"⊝","cire":"≗","cirfnint":"⨐","cirmid":"⫯","cirscir":"⧂","clubs":"♣","clubsuit":"♣","colon":":","colone":"≔","coloneq":"≔","comma":",","commat":"@","comp":"∁","compfn":"∘","complement":"∁","complexes":"ℂ","cong":"≅","congdot":"⩭","conint":"∮","copf":"\uD835\uDD54","coprod":"∐","cop":"\xa9","copy":"\xa9","copysr":"℗","crarr":"↵","cross":"✗","cscr":"\uD835\uDCB8","csub":"⫏","csube":"⫑","csup":"⫐","csupe":"⫒","ctdot":"⋯","cudarrl":"⤸","cudarrr":"⤵","cuepr":"⋞","cuesc":"⋟","cularr":"↶","cularrp":"⤽","cup":"∪","cupbrcap":"⩈","cupcap":"⩆","cupcup":"⩊","cupdot":"⊍","cupor":"⩅","cups":"∪︀","curarr":"↷","curarrm":"⤼","curlyeqprec":"⋞","curlyeqsucc":"⋟","curlyvee":"⋎","curlywedge":"⋏","curre":"\xa4","curren":"\xa4","curvearrowleft":"↶","curvearrowright":"↷","cuvee":"⋎","cuwed":"⋏","cwconint":"∲","cwint":"∱","cylcty":"⌭","dArr":"⇓","dHar":"⥥","dagger":"†","daleth":"ℸ","darr":"↓","dash":"‐","dashv":"⊣","dbkarow":"⤏","dblac":"˝","dcaron":"ď","dcy":"д","dd":"ⅆ","ddagger":"‡","ddarr":"⇊","ddotseq":"⩷","de":"\xb0","deg":"\xb0","delta":"δ","demptyv":"⦱","dfisht":"⥿","dfr":"\uD835\uDD21","dharl":"⇃","dharr":"⇂","diam":"⋄","diamond":"⋄","diamondsuit":"♦","diams":"♦","die":"\xa8","digamma":"ϝ","disin":"⋲","div":"\xf7","divid":"\xf7","divide":"\xf7","divideontimes":"⋇","divonx":"⋇","djcy":"ђ","dlcorn":"⌞","dlcrop":"⌍","dollar":"$","dopf":"\uD835\uDD55","dot":"˙","doteq":"≐","doteqdot":"≑","dotminus":"∸","dotplus":"∔","dotsquare":"⊡","doublebarwedge":"⌆","downarrow":"↓","downdownarrows":"⇊","downharpoonleft":"⇃","downharpoonright":"⇂","drbkarow":"⤐","drcorn":"⌟","drcrop":"⌌","dscr":"\uD835\uDCB9","dscy":"ѕ","dsol":"⧶","dstrok":"đ","dtdot":"⋱","dtri":"▿","dtrif":"▾","duarr":"⇵","duhar":"⥯","dwangle":"⦦","dzcy":"џ","dzigrarr":"⟿","eDDot":"⩷","eDot":"≑","eacut":"\xe9","eacute":"\xe9","easter":"⩮","ecaron":"ě","ecir":"\xea","ecirc":"\xea","ecolon":"≕","ecy":"э","edot":"ė","ee":"ⅇ","efDot":"≒","efr":"\uD835\uDD22","eg":"⪚","egrav":"\xe8","egrave":"\xe8","egs":"⪖","egsdot":"⪘","el":"⪙","elinters":"⏧","ell":"ℓ","els":"⪕","elsdot":"⪗","emacr":"ē","empty":"∅","emptyset":"∅","emptyv":"∅","emsp13":" ","emsp14":" ","emsp":" ","eng":"ŋ","ensp":" ","eogon":"ę","eopf":"\uD835\uDD56","epar":"⋕","eparsl":"⧣","eplus":"⩱","epsi":"ε","epsilon":"ε","epsiv":"ϵ","eqcirc":"≖","eqcolon":"≕","eqsim":"≂","eqslantgtr":"⪖","eqslantless":"⪕","equals":"=","equest":"≟","equiv":"≡","equivDD":"⩸","eqvparsl":"⧥","erDot":"≓","erarr":"⥱","escr":"ℯ","esdot":"≐","esim":"≂","eta":"η","et":"\xf0","eth":"\xf0","eum":"\xeb","euml":"\xeb","euro":"€","excl":"!","exist":"∃","expectation":"ℰ","exponentiale":"ⅇ","fallingdotseq":"≒","fcy":"ф","female":"♀","ffilig":"ﬃ","fflig":"ﬀ","ffllig":"ﬄ","ffr":"\uD835\uDD23","filig":"ﬁ","fjlig":"fj","flat":"♭","fllig":"ﬂ","fltns":"▱","fnof":"ƒ","fopf":"\uD835\uDD57","forall":"∀","fork":"⋔","forkv":"⫙","fpartint":"⨍","frac1":"\xbc","frac12":"\xbd","frac13":"⅓","frac14":"\xbc","frac15":"⅕","frac16":"⅙","frac18":"⅛","frac23":"⅔","frac25":"⅖","frac3":"\xbe","frac34":"\xbe","frac35":"⅗","frac38":"⅜","frac45":"⅘","frac56":"⅚","frac58":"⅝","frac78":"⅞","frasl":"⁄","frown":"⌢","fscr":"\uD835\uDCBB","gE":"≧","gEl":"⪌","gacute":"ǵ","gamma":"γ","gammad":"ϝ","gap":"⪆","gbreve":"ğ","gcirc":"ĝ","gcy":"г","gdot":"ġ","ge":"≥","gel":"⋛","geq":"≥","geqq":"≧","geqslant":"⩾","ges":"⩾","gescc":"⪩","gesdot":"⪀","gesdoto":"⪂","gesdotol":"⪄","gesl":"⋛︀","gesles":"⪔","gfr":"\uD835\uDD24","gg":"≫","ggg":"⋙","gimel":"ℷ","gjcy":"ѓ","gl":"≷","glE":"⪒","gla":"⪥","glj":"⪤","gnE":"≩","gnap":"⪊","gnapprox":"⪊","gne":"⪈","gneq":"⪈","gneqq":"≩","gnsim":"⋧","gopf":"\uD835\uDD58","grave":"`","gscr":"ℊ","gsim":"≳","gsime":"⪎","gsiml":"⪐","g":">","gt":">","gtcc":"⪧","gtcir":"⩺","gtdot":"⋗","gtlPar":"⦕","gtquest":"⩼","gtrapprox":"⪆","gtrarr":"⥸","gtrdot":"⋗","gtreqless":"⋛","gtreqqless":"⪌","gtrless":"≷","gtrsim":"≳","gvertneqq":"≩︀","gvnE":"≩︀","hArr":"⇔","hairsp":" ","half":"\xbd","hamilt":"ℋ","hardcy":"ъ","harr":"↔","harrcir":"⥈","harrw":"↭","hbar":"ℏ","hcirc":"ĥ","hearts":"♥","heartsuit":"♥","hellip":"…","hercon":"⊹","hfr":"\uD835\uDD25","hksearow":"⤥","hkswarow":"⤦","hoarr":"⇿","homtht":"∻","hookleftarrow":"↩","hookrightarrow":"↪","hopf":"\uD835\uDD59","horbar":"―","hscr":"\uD835\uDCBD","hslash":"ℏ","hstrok":"ħ","hybull":"⁃","hyphen":"‐","iacut":"\xed","iacute":"\xed","ic":"⁣","icir":"\xee","icirc":"\xee","icy":"и","iecy":"е","iexc":"\xa1","iexcl":"\xa1","iff":"⇔","ifr":"\uD835\uDD26","igrav":"\xec","igrave":"\xec","ii":"ⅈ","iiiint":"⨌","iiint":"∭","iinfin":"⧜","iiota":"℩","ijlig":"ĳ","imacr":"ī","image":"ℑ","imagline":"ℐ","imagpart":"ℑ","imath":"ı","imof":"⊷","imped":"Ƶ","in":"∈","incare":"℅","infin":"∞","infintie":"⧝","inodot":"ı","int":"∫","intcal":"⊺","integers":"ℤ","intercal":"⊺","intlarhk":"⨗","intprod":"⨼","iocy":"ё","iogon":"į","iopf":"\uD835\uDD5A","iota":"ι","iprod":"⨼","iques":"\xbf","iquest":"\xbf","iscr":"\uD835\uDCBE","isin":"∈","isinE":"⋹","isindot":"⋵","isins":"⋴","isinsv":"⋳","isinv":"∈","it":"⁢","itilde":"ĩ","iukcy":"і","ium":"\xef","iuml":"\xef","jcirc":"ĵ","jcy":"й","jfr":"\uD835\uDD27","jmath":"ȷ","jopf":"\uD835\uDD5B","jscr":"\uD835\uDCBF","jsercy":"ј","jukcy":"є","kappa":"κ","kappav":"ϰ","kcedil":"ķ","kcy":"к","kfr":"\uD835\uDD28","kgreen":"ĸ","khcy":"х","kjcy":"ќ","kopf":"\uD835\uDD5C","kscr":"\uD835\uDCC0","lAarr":"⇚","lArr":"⇐","lAtail":"⤛","lBarr":"⤎","lE":"≦","lEg":"⪋","lHar":"⥢","lacute":"ĺ","laemptyv":"⦴","lagran":"ℒ","lambda":"λ","lang":"⟨","langd":"⦑","langle":"⟨","lap":"⪅","laqu":"\xab","laquo":"\xab","larr":"←","larrb":"⇤","larrbfs":"⤟","larrfs":"⤝","larrhk":"↩","larrlp":"↫","larrpl":"⤹","larrsim":"⥳","larrtl":"↢","lat":"⪫","latail":"⤙","late":"⪭","lates":"⪭︀","lbarr":"⤌","lbbrk":"❲","lbrace":"{","lbrack":"[","lbrke":"⦋","lbrksld":"⦏","lbrkslu":"⦍","lcaron":"ľ","lcedil":"ļ","lceil":"⌈","lcub":"{","lcy":"л","ldca":"⤶","ldquo":"“","ldquor":"„","ldrdhar":"⥧","ldrushar":"⥋","ldsh":"↲","le":"≤","leftarrow":"←","leftarrowtail":"↢","leftharpoondown":"↽","leftharpoonup":"↼","leftleftarrows":"⇇","leftrightarrow":"↔","leftrightarrows":"⇆","leftrightharpoons":"⇋","leftrightsquigarrow":"↭","leftthreetimes":"⋋","leg":"⋚","leq":"≤","leqq":"≦","leqslant":"⩽","les":"⩽","lescc":"⪨","lesdot":"⩿","lesdoto":"⪁","lesdotor":"⪃","lesg":"⋚︀","lesges":"⪓","lessapprox":"⪅","lessdot":"⋖","lesseqgtr":"⋚","lesseqqgtr":"⪋","lessgtr":"≶","lesssim":"≲","lfisht":"⥼","lfloor":"⌊","lfr":"\uD835\uDD29","lg":"≶","lgE":"⪑","lhard":"↽","lharu":"↼","lharul":"⥪","lhblk":"▄","ljcy":"љ","ll":"≪","llarr":"⇇","llcorner":"⌞","llhard":"⥫","lltri":"◺","lmidot":"ŀ","lmoust":"⎰","lmoustache":"⎰","lnE":"≨","lnap":"⪉","lnapprox":"⪉","lne":"⪇","lneq":"⪇","lneqq":"≨","lnsim":"⋦","loang":"⟬","loarr":"⇽","lobrk":"⟦","longleftarrow":"⟵","longleftrightarrow":"⟷","longmapsto":"⟼","longrightarrow":"⟶","looparrowleft":"↫","looparrowright":"↬","lopar":"⦅","lopf":"\uD835\uDD5D","loplus":"⨭","lotimes":"⨴","lowast":"∗","lowbar":"_","loz":"◊","lozenge":"◊","lozf":"⧫","lpar":"(","lparlt":"⦓","lrarr":"⇆","lrcorner":"⌟","lrhar":"⇋","lrhard":"⥭","lrm":"‎","lrtri":"⊿","lsaquo":"‹","lscr":"\uD835\uDCC1","lsh":"↰","lsim":"≲","lsime":"⪍","lsimg":"⪏","lsqb":"[","lsquo":"‘","lsquor":"‚","lstrok":"ł","l":"<","lt":"<","ltcc":"⪦","ltcir":"⩹","ltdot":"⋖","lthree":"⋋","ltimes":"⋉","ltlarr":"⥶","ltquest":"⩻","ltrPar":"⦖","ltri":"◃","ltrie":"⊴","ltrif":"◂","lurdshar":"⥊","luruhar":"⥦","lvertneqq":"≨︀","lvnE":"≨︀","mDDot":"∺","mac":"\xaf","macr":"\xaf","male":"♂","malt":"✠","maltese":"✠","map":"↦","mapsto":"↦","mapstodown":"↧","mapstoleft":"↤","mapstoup":"↥","marker":"▮","mcomma":"⨩","mcy":"м","mdash":"—","measuredangle":"∡","mfr":"\uD835\uDD2A","mho":"℧","micr":"\xb5","micro":"\xb5","mid":"∣","midast":"*","midcir":"⫰","middo":"\xb7","middot":"\xb7","minus":"−","minusb":"⊟","minusd":"∸","minusdu":"⨪","mlcp":"⫛","mldr":"…","mnplus":"∓","models":"⊧","mopf":"\uD835\uDD5E","mp":"∓","mscr":"\uD835\uDCC2","mstpos":"∾","mu":"μ","multimap":"⊸","mumap":"⊸","nGg":"⋙̸","nGt":"≫⃒","nGtv":"≫̸","nLeftarrow":"⇍","nLeftrightarrow":"⇎","nLl":"⋘̸","nLt":"≪⃒","nLtv":"≪̸","nRightarrow":"⇏","nVDash":"⊯","nVdash":"⊮","nabla":"∇","nacute":"ń","nang":"∠⃒","nap":"≉","napE":"⩰̸","napid":"≋̸","napos":"ŉ","napprox":"≉","natur":"♮","natural":"♮","naturals":"ℕ","nbs":"\xa0","nbsp":"\xa0","nbump":"≎̸","nbumpe":"≏̸","ncap":"⩃","ncaron":"ň","ncedil":"ņ","ncong":"≇","ncongdot":"⩭̸","ncup":"⩂","ncy":"н","ndash":"–","ne":"≠","neArr":"⇗","nearhk":"⤤","nearr":"↗","nearrow":"↗","nedot":"≐̸","nequiv":"≢","nesear":"⤨","nesim":"≂̸","nexist":"∄","nexists":"∄","nfr":"\uD835\uDD2B","ngE":"≧̸","nge":"≱","ngeq":"≱","ngeqq":"≧̸","ngeqslant":"⩾̸","nges":"⩾̸","ngsim":"≵","ngt":"≯","ngtr":"≯","nhArr":"⇎","nharr":"↮","nhpar":"⫲","ni":"∋","nis":"⋼","nisd":"⋺","niv":"∋","njcy":"њ","nlArr":"⇍","nlE":"≦̸","nlarr":"↚","nldr":"‥","nle":"≰","nleftarrow":"↚","nleftrightarrow":"↮","nleq":"≰","nleqq":"≦̸","nleqslant":"⩽̸","nles":"⩽̸","nless":"≮","nlsim":"≴","nlt":"≮","nltri":"⋪","nltrie":"⋬","nmid":"∤","nopf":"\uD835\uDD5F","no":"\xac","not":"\xac","notin":"∉","notinE":"⋹̸","notindot":"⋵̸","notinva":"∉","notinvb":"⋷","notinvc":"⋶","notni":"∌","notniva":"∌","notnivb":"⋾","notnivc":"⋽","npar":"∦","nparallel":"∦","nparsl":"⫽⃥","npart":"∂̸","npolint":"⨔","npr":"⊀","nprcue":"⋠","npre":"⪯̸","nprec":"⊀","npreceq":"⪯̸","nrArr":"⇏","nrarr":"↛","nrarrc":"⤳̸","nrarrw":"↝̸","nrightarrow":"↛","nrtri":"⋫","nrtrie":"⋭","nsc":"⊁","nsccue":"⋡","nsce":"⪰̸","nscr":"\uD835\uDCC3","nshortmid":"∤","nshortparallel":"∦","nsim":"≁","nsime":"≄","nsimeq":"≄","nsmid":"∤","nspar":"∦","nsqsube":"⋢","nsqsupe":"⋣","nsub":"⊄","nsubE":"⫅̸","nsube":"⊈","nsubset":"⊂⃒","nsubseteq":"⊈","nsubseteqq":"⫅̸","nsucc":"⊁","nsucceq":"⪰̸","nsup":"⊅","nsupE":"⫆̸","nsupe":"⊉","nsupset":"⊃⃒","nsupseteq":"⊉","nsupseteqq":"⫆̸","ntgl":"≹","ntild":"\xf1","ntilde":"\xf1","ntlg":"≸","ntriangleleft":"⋪","ntrianglelefteq":"⋬","ntriangleright":"⋫","ntrianglerighteq":"⋭","nu":"ν","num":"#","numero":"№","numsp":" ","nvDash":"⊭","nvHarr":"⤄","nvap":"≍⃒","nvdash":"⊬","nvge":"≥⃒","nvgt":">⃒","nvinfin":"⧞","nvlArr":"⤂","nvle":"≤⃒","nvlt":"<⃒","nvltrie":"⊴⃒","nvrArr":"⤃","nvrtrie":"⊵⃒","nvsim":"∼⃒","nwArr":"⇖","nwarhk":"⤣","nwarr":"↖","nwarrow":"↖","nwnear":"⤧","oS":"Ⓢ","oacut":"\xf3","oacute":"\xf3","oast":"⊛","ocir":"\xf4","ocirc":"\xf4","ocy":"о","odash":"⊝","odblac":"ő","odiv":"⨸","odot":"⊙","odsold":"⦼","oelig":"œ","ofcir":"⦿","ofr":"\uD835\uDD2C","ogon":"˛","ograv":"\xf2","ograve":"\xf2","ogt":"⧁","ohbar":"⦵","ohm":"Ω","oint":"∮","olarr":"↺","olcir":"⦾","olcross":"⦻","oline":"‾","olt":"⧀","omacr":"ō","omega":"ω","omicron":"ο","omid":"⦶","ominus":"⊖","oopf":"\uD835\uDD60","opar":"⦷","operp":"⦹","oplus":"⊕","or":"∨","orarr":"↻","ord":"\xba","order":"ℴ","orderof":"ℴ","ordf":"\xaa","ordm":"\xba","origof":"⊶","oror":"⩖","orslope":"⩗","orv":"⩛","oscr":"ℴ","oslas":"\xf8","oslash":"\xf8","osol":"⊘","otild":"\xf5","otilde":"\xf5","otimes":"⊗","otimesas":"⨶","oum":"\xf6","ouml":"\xf6","ovbar":"⌽","par":"\xb6","para":"\xb6","parallel":"∥","parsim":"⫳","parsl":"⫽","part":"∂","pcy":"п","percnt":"%","period":".","permil":"‰","perp":"⊥","pertenk":"‱","pfr":"\uD835\uDD2D","phi":"φ","phiv":"ϕ","phmmat":"ℳ","phone":"☎","pi":"π","pitchfork":"⋔","piv":"ϖ","planck":"ℏ","planckh":"ℎ","plankv":"ℏ","plus":"+","plusacir":"⨣","plusb":"⊞","pluscir":"⨢","plusdo":"∔","plusdu":"⨥","pluse":"⩲","plusm":"\xb1","plusmn":"\xb1","plussim":"⨦","plustwo":"⨧","pm":"\xb1","pointint":"⨕","popf":"\uD835\uDD61","poun":"\xa3","pound":"\xa3","pr":"≺","prE":"⪳","prap":"⪷","prcue":"≼","pre":"⪯","prec":"≺","precapprox":"⪷","preccurlyeq":"≼","preceq":"⪯","precnapprox":"⪹","precneqq":"⪵","precnsim":"⋨","precsim":"≾","prime":"′","primes":"ℙ","prnE":"⪵","prnap":"⪹","prnsim":"⋨","prod":"∏","profalar":"⌮","profline":"⌒","profsurf":"⌓","prop":"∝","propto":"∝","prsim":"≾","prurel":"⊰","pscr":"\uD835\uDCC5","psi":"ψ","puncsp":" ","qfr":"\uD835\uDD2E","qint":"⨌","qopf":"\uD835\uDD62","qprime":"⁗","qscr":"\uD835\uDCC6","quaternions":"ℍ","quatint":"⨖","quest":"?","questeq":"≟","quo":"\\"","quot":"\\"","rAarr":"⇛","rArr":"⇒","rAtail":"⤜","rBarr":"⤏","rHar":"⥤","race":"∽̱","racute":"ŕ","radic":"√","raemptyv":"⦳","rang":"⟩","rangd":"⦒","range":"⦥","rangle":"⟩","raqu":"\xbb","raquo":"\xbb","rarr":"→","rarrap":"⥵","rarrb":"⇥","rarrbfs":"⤠","rarrc":"⤳","rarrfs":"⤞","rarrhk":"↪","rarrlp":"↬","rarrpl":"⥅","rarrsim":"⥴","rarrtl":"↣","rarrw":"↝","ratail":"⤚","ratio":"∶","rationals":"ℚ","rbarr":"⤍","rbbrk":"❳","rbrace":"}","rbrack":"]","rbrke":"⦌","rbrksld":"⦎","rbrkslu":"⦐","rcaron":"ř","rcedil":"ŗ","rceil":"⌉","rcub":"}","rcy":"р","rdca":"⤷","rdldhar":"⥩","rdquo":"”","rdquor":"”","rdsh":"↳","real":"ℜ","realine":"ℛ","realpart":"ℜ","reals":"ℝ","rect":"▭","re":"\xae","reg":"\xae","rfisht":"⥽","rfloor":"⌋","rfr":"\uD835\uDD2F","rhard":"⇁","rharu":"⇀","rharul":"⥬","rho":"ρ","rhov":"ϱ","rightarrow":"→","rightarrowtail":"↣","rightharpoondown":"⇁","rightharpoonup":"⇀","rightleftarrows":"⇄","rightleftharpoons":"⇌","rightrightarrows":"⇉","rightsquigarrow":"↝","rightthreetimes":"⋌","ring":"˚","risingdotseq":"≓","rlarr":"⇄","rlhar":"⇌","rlm":"‏","rmoust":"⎱","rmoustache":"⎱","rnmid":"⫮","roang":"⟭","roarr":"⇾","robrk":"⟧","ropar":"⦆","ropf":"\uD835\uDD63","roplus":"⨮","rotimes":"⨵","rpar":")","rpargt":"⦔","rppolint":"⨒","rrarr":"⇉","rsaquo":"›","rscr":"\uD835\uDCC7","rsh":"↱","rsqb":"]","rsquo":"’","rsquor":"’","rthree":"⋌","rtimes":"⋊","rtri":"▹","rtrie":"⊵","rtrif":"▸","rtriltri":"⧎","ruluhar":"⥨","rx":"℞","sacute":"ś","sbquo":"‚","sc":"≻","scE":"⪴","scap":"⪸","scaron":"š","sccue":"≽","sce":"⪰","scedil":"ş","scirc":"ŝ","scnE":"⪶","scnap":"⪺","scnsim":"⋩","scpolint":"⨓","scsim":"≿","scy":"с","sdot":"⋅","sdotb":"⊡","sdote":"⩦","seArr":"⇘","searhk":"⤥","searr":"↘","searrow":"↘","sec":"\xa7","sect":"\xa7","semi":";","seswar":"⤩","setminus":"∖","setmn":"∖","sext":"✶","sfr":"\uD835\uDD30","sfrown":"⌢","sharp":"♯","shchcy":"щ","shcy":"ш","shortmid":"∣","shortparallel":"∥","sh":"\xad","shy":"\xad","sigma":"σ","sigmaf":"ς","sigmav":"ς","sim":"∼","simdot":"⩪","sime":"≃","simeq":"≃","simg":"⪞","simgE":"⪠","siml":"⪝","simlE":"⪟","simne":"≆","simplus":"⨤","simrarr":"⥲","slarr":"←","smallsetminus":"∖","smashp":"⨳","smeparsl":"⧤","smid":"∣","smile":"⌣","smt":"⪪","smte":"⪬","smtes":"⪬︀","softcy":"ь","sol":"/","solb":"⧄","solbar":"⌿","sopf":"\uD835\uDD64","spades":"♠","spadesuit":"♠","spar":"∥","sqcap":"⊓","sqcaps":"⊓︀","sqcup":"⊔","sqcups":"⊔︀","sqsub":"⊏","sqsube":"⊑","sqsubset":"⊏","sqsubseteq":"⊑","sqsup":"⊐","sqsupe":"⊒","sqsupset":"⊐","sqsupseteq":"⊒","squ":"□","square":"□","squarf":"▪","squf":"▪","srarr":"→","sscr":"\uD835\uDCC8","ssetmn":"∖","ssmile":"⌣","sstarf":"⋆","star":"☆","starf":"★","straightepsilon":"ϵ","straightphi":"ϕ","strns":"\xaf","sub":"⊂","subE":"⫅","subdot":"⪽","sube":"⊆","subedot":"⫃","submult":"⫁","subnE":"⫋","subne":"⊊","subplus":"⪿","subrarr":"⥹","subset":"⊂","subseteq":"⊆","subseteqq":"⫅","subsetneq":"⊊","subsetneqq":"⫋","subsim":"⫇","subsub":"⫕","subsup":"⫓","succ":"≻","succapprox":"⪸","succcurlyeq":"≽","succeq":"⪰","succnapprox":"⪺","succneqq":"⪶","succnsim":"⋩","succsim":"≿","sum":"∑","sung":"♪","sup":"⊃","sup1":"\xb9","sup2":"\xb2","sup3":"\xb3","supE":"⫆","supdot":"⪾","supdsub":"⫘","supe":"⊇","supedot":"⫄","suphsol":"⟉","suphsub":"⫗","suplarr":"⥻","supmult":"⫂","supnE":"⫌","supne":"⊋","supplus":"⫀","supset":"⊃","supseteq":"⊇","supseteqq":"⫆","supsetneq":"⊋","supsetneqq":"⫌","supsim":"⫈","supsub":"⫔","supsup":"⫖","swArr":"⇙","swarhk":"⤦","swarr":"↙","swarrow":"↙","swnwar":"⤪","szli":"\xdf","szlig":"\xdf","target":"⌖","tau":"τ","tbrk":"⎴","tcaron":"ť","tcedil":"ţ","tcy":"т","tdot":"⃛","telrec":"⌕","tfr":"\uD835\uDD31","there4":"∴","therefore":"∴","theta":"θ","thetasym":"ϑ","thetav":"ϑ","thickapprox":"≈","thicksim":"∼","thinsp":" ","thkap":"≈","thksim":"∼","thor":"\xfe","thorn":"\xfe","tilde":"˜","time":"\xd7","times":"\xd7","timesb":"⊠","timesbar":"⨱","timesd":"⨰","tint":"∭","toea":"⤨","top":"⊤","topbot":"⌶","topcir":"⫱","topf":"\uD835\uDD65","topfork":"⫚","tosa":"⤩","tprime":"‴","trade":"™","triangle":"▵","triangledown":"▿","triangleleft":"◃","trianglelefteq":"⊴","triangleq":"≜","triangleright":"▹","trianglerighteq":"⊵","tridot":"◬","trie":"≜","triminus":"⨺","triplus":"⨹","trisb":"⧍","tritime":"⨻","trpezium":"⏢","tscr":"\uD835\uDCC9","tscy":"ц","tshcy":"ћ","tstrok":"ŧ","twixt":"≬","twoheadleftarrow":"↞","twoheadrightarrow":"↠","uArr":"⇑","uHar":"⥣","uacut":"\xfa","uacute":"\xfa","uarr":"↑","ubrcy":"ў","ubreve":"ŭ","ucir":"\xfb","ucirc":"\xfb","ucy":"у","udarr":"⇅","udblac":"ű","udhar":"⥮","ufisht":"⥾","ufr":"\uD835\uDD32","ugrav":"\xf9","ugrave":"\xf9","uharl":"↿","uharr":"↾","uhblk":"▀","ulcorn":"⌜","ulcorner":"⌜","ulcrop":"⌏","ultri":"◸","umacr":"ū","um":"\xa8","uml":"\xa8","uogon":"ų","uopf":"\uD835\uDD66","uparrow":"↑","updownarrow":"↕","upharpoonleft":"↿","upharpoonright":"↾","uplus":"⊎","upsi":"υ","upsih":"ϒ","upsilon":"υ","upuparrows":"⇈","urcorn":"⌝","urcorner":"⌝","urcrop":"⌎","uring":"ů","urtri":"◹","uscr":"\uD835\uDCCA","utdot":"⋰","utilde":"ũ","utri":"▵","utrif":"▴","uuarr":"⇈","uum":"\xfc","uuml":"\xfc","uwangle":"⦧","vArr":"⇕","vBar":"⫨","vBarv":"⫩","vDash":"⊨","vangrt":"⦜","varepsilon":"ϵ","varkappa":"ϰ","varnothing":"∅","varphi":"ϕ","varpi":"ϖ","varpropto":"∝","varr":"↕","varrho":"ϱ","varsigma":"ς","varsubsetneq":"⊊︀","varsubsetneqq":"⫋︀","varsupsetneq":"⊋︀","varsupsetneqq":"⫌︀","vartheta":"ϑ","vartriangleleft":"⊲","vartriangleright":"⊳","vcy":"в","vdash":"⊢","vee":"∨","veebar":"⊻","veeeq":"≚","vellip":"⋮","verbar":"|","vert":"|","vfr":"\uD835\uDD33","vltri":"⊲","vnsub":"⊂⃒","vnsup":"⊃⃒","vopf":"\uD835\uDD67","vprop":"∝","vrtri":"⊳","vscr":"\uD835\uDCCB","vsubnE":"⫋︀","vsubne":"⊊︀","vsupnE":"⫌︀","vsupne":"⊋︀","vzigzag":"⦚","wcirc":"ŵ","wedbar":"⩟","wedge":"∧","wedgeq":"≙","weierp":"℘","wfr":"\uD835\uDD34","wopf":"\uD835\uDD68","wp":"℘","wr":"≀","wreath":"≀","wscr":"\uD835\uDCCC","xcap":"⋂","xcirc":"◯","xcup":"⋃","xdtri":"▽","xfr":"\uD835\uDD35","xhArr":"⟺","xharr":"⟷","xi":"ξ","xlArr":"⟸","xlarr":"⟵","xmap":"⟼","xnis":"⋻","xodot":"⨀","xopf":"\uD835\uDD69","xoplus":"⨁","xotime":"⨂","xrArr":"⟹","xrarr":"⟶","xscr":"\uD835\uDCCD","xsqcup":"⨆","xuplus":"⨄","xutri":"△","xvee":"⋁","xwedge":"⋀","yacut":"\xfd","yacute":"\xfd","yacy":"я","ycirc":"ŷ","ycy":"ы","ye":"\xa5","yen":"\xa5","yfr":"\uD835\uDD36","yicy":"ї","yopf":"\uD835\uDD6A","yscr":"\uD835\uDCCE","yucy":"ю","yum":"\xff","yuml":"\xff","zacute":"ź","zcaron":"ž","zcy":"з","zdot":"ż","zeetrf":"ℨ","zeta":"ζ","zfr":"\uD835\uDD37","zhcy":"ж","zigrarr":"⇝","zopf":"\uD835\uDD6B","zscr":"\uD835\uDCCF","zwj":"‍","zwnj":"‌"}')},83921:e=>{"use strict";e.exports=JSON.parse('{"0":"�","128":"€","130":"‚","131":"ƒ","132":"„","133":"…","134":"†","135":"‡","136":"ˆ","137":"‰","138":"Š","139":"‹","140":"Œ","142":"Ž","145":"‘","146":"’","147":"“","148":"”","149":"•","150":"–","151":"—","152":"˜","153":"™","154":"š","155":"›","156":"œ","158":"ž","159":"Ÿ"}')}};