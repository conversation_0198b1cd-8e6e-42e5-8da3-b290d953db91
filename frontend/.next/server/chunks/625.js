"use strict";exports.id=625,exports.ids=[625],exports.modules={3625:(t,e,i)=>{var s,o,a,n,r,l,c,h,d,u,p,f,y,v,m,g,b,w,_,x,k,z,P,C,M,O,S,D,T,E,R,I,L,F;i.d(e,{loadFull:()=>aD});let B="generated",A="pointerleave",q="pointermove",V="touchend",U="tsParticles - Error",H={x:0,y:0,z:0},$={a:1,b:0,c:0,d:1},W="random",G=2*Math.PI,j="true",Q="false",N="canvas",X=0;function Y(t){return"boolean"==typeof t}function Z(t){return"string"==typeof t}function K(t){return"number"==typeof t}function J(t){return"object"==typeof t&&null!==t}function tt(t){return Array.isArray(t)}function te(t){return null==t}!function(t){t.bottom="bottom",t.bottomLeft="bottom-left",t.bottomRight="bottom-right",t.left="left",t.none="none",t.right="right",t.top="top",t.topLeft="top-left",t.topRight="top-right",t.outside="outside",t.inside="inside"}(s||(s={}));class ti{constructor(t,e,i){if(this._updateFromAngle=(t,e)=>{this.x=Math.cos(t)*e,this.y=Math.sin(t)*e},!K(t)&&t)this.x=t.x,this.y=t.y,this.z=t.z?t.z:H.z;else if(void 0!==t&&void 0!==e)this.x=t,this.y=e,this.z=i??H.z;else throw Error(`${U} Vector3d not initialized correctly`)}static get origin(){return ti.create(H.x,H.y,H.z)}get angle(){return Math.atan2(this.y,this.x)}set angle(t){this._updateFromAngle(t,this.length)}get length(){return Math.sqrt(this.getLengthSq())}set length(t){this._updateFromAngle(this.angle,t)}static clone(t){return ti.create(t.x,t.y,t.z)}static create(t,e,i){return new ti(t,e,i)}add(t){return ti.create(this.x+t.x,this.y+t.y,this.z+t.z)}addTo(t){this.x+=t.x,this.y+=t.y,this.z+=t.z}copy(){return ti.clone(this)}distanceTo(t){return this.sub(t).length}distanceToSq(t){return this.sub(t).getLengthSq()}div(t){return ti.create(this.x/t,this.y/t,this.z/t)}divTo(t){this.x/=t,this.y/=t,this.z/=t}getLengthSq(){return this.x**2+this.y**2}mult(t){return ti.create(this.x*t,this.y*t,this.z*t)}multTo(t){this.x*=t,this.y*=t,this.z*=t}normalize(){let t=this.length;0!=t&&this.multTo(1/t)}rotate(t){return ti.create(this.x*Math.cos(t)-this.y*Math.sin(t),this.x*Math.sin(t)+this.y*Math.cos(t),H.z)}setTo(t){this.x=t.x,this.y=t.y,this.z=t.z?t.z:H.z}sub(t){return ti.create(this.x-t.x,this.y-t.y,this.z-t.z)}subFrom(t){this.x-=t.x,this.y-=t.y,this.z-=t.z}}class ts extends ti{constructor(t,e){super(t,e,H.z)}static get origin(){return ts.create(H.x,H.y)}static clone(t){return ts.create(t.x,t.y)}static create(t,e){return new ts(t,e)}}let to=Math.random,ta={nextFrame:t=>requestAnimationFrame(t),cancel:t=>cancelAnimationFrame(t)};function tn(){return tr(to(),0,1-Number.EPSILON)}function tr(t,e,i){return Math.min(Math.max(t,e),i)}function tl(t,e,i,s){return Math.floor((t*i+e*s)/(i+s))}function tc(t){let e=tu(t),i=td(t);return e===i&&(i=0),tn()*(e-i)+i}function th(t){return K(t)?t:tc(t)}function td(t){return K(t)?t:t.min}function tu(t){return K(t)?t:t.max}function tp(t,e){if(t===e||void 0===e&&K(t))return t;let i=td(t),s=tu(t);return void 0!==e?{min:Math.min(i,e),max:Math.max(s,e)}:tp(i,s)}function tf(t,e){let i=t.x-e.x,s=t.y-e.y;return{dx:i,dy:s,distance:Math.sqrt(i**2+s**2)}}function ty(t,e){return tf(t,e).distance}function tv(t){return t*Math.PI/180}function tm(t,e,i,s){return ts.create(t.x*(i-s)/(i+s)+2*e.x*s/(i+s),t.y)}function tg(t){return{x:(t.position?.x??100*tn())*t.size.width/100,y:(t.position?.y??100*tn())*t.size.height/100}}function tb(t){let e={x:t.position?.x!==void 0?th(t.position.x):void 0,y:t.position?.y!==void 0?th(t.position.y):void 0};return tg({size:t.size,position:e})}function tw(t){return t?t.endsWith("%")?parseFloat(t)/100:parseFloat(t):1}(function(t){t.auto="auto",t.increase="increase",t.decrease="decrease",t.random="random"})(o||(o={})),function(t){t.increasing="increasing",t.decreasing="decreasing"}(a||(a={})),function(t){t.none="none",t.max="max",t.min="min"}(n||(n={})),function(t){t.bottom="bottom",t.left="left",t.right="right",t.top="top"}(r||(r={})),function(t){t.precise="precise",t.percent="percent"}(l||(l={})),function(t){t.max="max",t.min="min",t.random="random"}(c||(c={}));let t_={debug:console.debug,error:console.error,info:console.info,log:console.log,verbose:console.log,warning:console.warn};function tx(t){let e={bounced:!1},{pSide:i,pOtherSide:s,rectSide:o,rectOtherSide:a,velocity:n,factor:r}=t;return s.min<a.min||s.min>a.max||s.max<a.min||s.max>a.max||(i.max>=o.min&&i.max<=(o.max+o.min)*.5&&n>0||i.min<=o.max&&i.min>(o.max+o.min)*.5&&n<0)&&(e.velocity=-(n*r),e.bounced=!0),e}function tk(){return"undefined"==typeof window||!window||void 0===window.document||!window.document}function tz(t){if(!tk()&&"undefined"!=typeof matchMedia)return matchMedia(t)}function tP(t,e){return t===e||tt(e)&&e.indexOf(t)>-1}async function tC(t,e){try{await document.fonts.load(`${e??"400"} 36px '${t??"Verdana"}'`)}catch{}}function tM(t){return Math.floor(tn()*t.length)}function tO(t,e,i=!0){return t[void 0!==e&&i?e%t.length:tM(t)]}function tS(t,e,i,s,o){var a;let n;return a=tD(t,s??0),n=!0,o&&o!==r.bottom||(n=a.top<e.height+i.x),n&&(!o||o===r.left)&&(n=a.right>i.x),n&&(!o||o===r.right)&&(n=a.left<e.width+i.y),n&&(!o||o===r.top)&&(n=a.bottom>i.y),n}function tD(t,e){return{bottom:t.y+e,left:t.x-e,right:t.x+e,top:t.y-e}}function tT(t,...e){for(let i of e){if(null==i)continue;if(!J(i)){t=i;continue}let e=Array.isArray(i);for(let s in e&&(J(t)||!t||!Array.isArray(t))?t=[]:!e&&(J(t)||!t||Array.isArray(t))&&(t={}),i){if("__proto__"===s)continue;let e=i[s],o=t;o[s]=J(e)&&Array.isArray(e)?e.map(t=>tT(o[s],t)):tT(o[s],e)}}return t}function tE(t,e){return!!tq(e,e=>e.enable&&tP(t,e.mode))}function tR(t,e,i){tB(e,e=>{let s=e.mode;e.enable&&tP(t,s)&&tB(e.selectors,t=>{i(t,e)})})}function tI(t,e){if(e&&t)return tq(t,t=>(function(t,e){let i=tB(e,e=>t.matches(e));return tt(i)?i.some(t=>t):i})(e,t.selectors))}function tL(t){return{position:t.getPosition(),radius:t.getRadius(),mass:t.getMass(),velocity:t.velocity,factor:ts.create(th(t.options.bounce.horizontal.value),th(t.options.bounce.vertical.value))}}function tF(t,e){let{x:i,y:s}=t.velocity.sub(e.velocity),[o,a]=[t.position,e.position],{dx:n,dy:r}=tf(a,o);if(i*n+s*r<0)return;let l=-Math.atan2(r,n),c=t.mass,h=e.mass,d=t.velocity.rotate(l),u=e.velocity.rotate(l),p=tm(d,u,c,h),f=tm(u,d,c,h),y=p.rotate(-l),v=f.rotate(-l);t.velocity.x=y.x*t.factor.x,t.velocity.y=y.y*t.factor.y,e.velocity.x=v.x*e.factor.x,e.velocity.y=v.y*e.factor.y}function tB(t,e){return tt(t)?t.map((t,i)=>e(t,i)):e(t,0)}function tA(t,e,i){return tt(t)?tO(t,e,i):t}function tq(t,e){return tt(t)?t.find((t,i)=>e(t,i)):e(t,0)?t:void 0}function tV(t,e){let i=t.value,s=t.animation,n={delayTime:1e3*th(s.delay),enable:s.enable,value:th(t.value)*e,max:tu(i)*e,min:td(i)*e,loops:0,maxLoops:th(s.count),time:0};if(s.enable){switch(n.decay=1-th(s.decay),s.mode){case o.increase:n.status=a.increasing;break;case o.decrease:n.status=a.decreasing;break;case o.random:n.status=tn()>=.5?a.increasing:a.decreasing}let t=s.mode===o.auto;switch(s.startValue){case c.min:n.value=n.min,t&&(n.status=a.increasing);break;case c.max:n.value=n.max,t&&(n.status=a.decreasing);break;case c.random:default:n.value=tc(n),t&&(n.status=tn()>=.5?a.increasing:a.decreasing)}}return n.initialValue=n.value,n}function tU(t,e){if(t.mode!==l.percent){let{mode:e,...i}=t;return i}return"x"in t?{x:t.x/100*e.width,y:t.y/100*e.height}:{width:t.width/100*e.width,height:t.height/100*e.height}}function tH(t,e,i,s,o){if(t.destroyed||!e||!e.enable||(e.maxLoops??0)>0&&(e.loops??0)>(e.maxLoops??0))return;let r=(e.velocity??0)*o.factor,l=e.min,c=e.max,h=e.decay??1;if(e.time||(e.time=0),(e.delayTime??0)>0&&e.time<(e.delayTime??0)&&(e.time+=o.value),!((e.delayTime??0)>0)||!(e.time<(e.delayTime??0))){switch(e.status){case a.increasing:e.value>=c?(i?e.status=a.decreasing:e.value-=c,e.loops||(e.loops=0),e.loops++):e.value+=r;break;case a.decreasing:e.value<=l?(i?e.status=a.increasing:e.value+=c,e.loops||(e.loops=0),e.loops++):e.value-=r}e.velocity&&1!==h&&(e.velocity*=h),function(t,e,i,s,o){switch(e){case n.max:i>=o&&t.destroy();break;case n.min:i<=s&&t.destroy()}}(t,s,e.value,l,c),t.destroyed||(e.value=tr(e.value,l,c))}}let t$=function(t){let e=new Map;return(...i)=>{let s=JSON.stringify(i);if(e.has(s))return e.get(s);let o=t(...i);return e.set(s,o),o}}(function(t){let e=document.createElement("div").style,i={width:"100%",height:"100%",margin:"0",padding:"0",borderWidth:"0",position:"fixed",zIndex:t.toString(10),"z-index":t.toString(10),top:"0",left:"0"};for(let t in i){let s=i[t];e.setProperty(t,s)}return e});function tW(t,e,i,s=!0){if(!e)return;let o=Z(e)?{value:e}:e;if(Z(o.value))return function t(e,i,s,o=!0){if(!i)return;let a=Z(i)?{value:i}:i;if(Z(a.value))return a.value===W?tN():function(t,e){if(e){for(let i of t.colorManagers.values())if(e.startsWith(i.stringPrefix))return i.parseString(e)}}(e,a.value);if(tt(a.value))return t(e,{value:tO(a.value,s,o)});for(let t of e.colorManagers.values()){let e=t.handleColor(a);if(e)return e}}(t,o.value,i,s);if(tt(o.value))return tW(t,{value:tO(o.value,i,s)});for(let e of t.colorManagers.values()){let t=e.handleRangeColor(o);if(t)return t}}function tG(t,e,i,s=!0){let o=tW(t,e,i,s);return o?tj(o):void 0}function tj(t){let e=t.r/255,i=t.g/255,s=t.b/255,o=Math.max(e,i,s),a=Math.min(e,i,s),n={h:0,l:(o+a)*.5,s:0};return o!==a&&(n.s=n.l<.5?(o-a)/(o+a):(o-a)/(2-o-a),n.h=e===o?(i-s)/(o-a):n.h=i===o?2+(s-e)/(o-a):4+(e-i)/(o-a)),n.l*=100,n.s*=100,n.h*=60,n.h<0&&(n.h+=360),n.h>=360&&(n.h-=360),n}function tQ(t){let e=(t.h%360+360)%360,i=Math.max(0,Math.min(100,t.s)),s=Math.max(0,Math.min(100,t.l)),o=e/360,a=i/100,n=s/100;if(0===i){let t=Math.round(255*n);return{r:t,g:t,b:t}}let r=(t,e,i)=>(i<0&&i++,i>1&&i--,6*i<1)?t+(e-t)*6*i:2*i<1?e:3*i<2?t+(e-t)*(2/3-i)*6:t,l=n<.5?n*(1+a):n+a-n*a,c=2*n-l;return{r:Math.round(Math.min(255,255*r(c,l,o+.3333333333333333))),g:Math.round(Math.min(255,255*r(c,l,o))),b:Math.round(Math.min(255,255*r(c,l,o-.3333333333333333)))}}function tN(t){let e=t??0;return{b:Math.floor(tc(tp(e,256))),g:Math.floor(tc(tp(e,256))),r:Math.floor(tc(tp(e,256)))}}function tX(t,e){return`rgba(${t.r}, ${t.g}, ${t.b}, ${e??1})`}function tY(t,e){return`hsla(${t.h}, ${t.s}%, ${t.l}%, ${e??1})`}function tZ(t,e,i,s){let o=t,a=e;return void 0===o.r&&(o=tQ(t)),void 0===a.r&&(a=tQ(e)),{b:tl(o.b,a.b,i,s),g:tl(o.g,a.g,i,s),r:tl(o.r,a.r,i,s)}}function tK(t,e,i){if(i===W)return tN();if("mid"!==i)return i;{let i=t.getFillColor()??t.getStrokeColor(),s=e?.getFillColor()??e?.getStrokeColor();if(i&&s&&e)return tZ(i,s,t.getRadius(),e.getRadius());{let t=i??s;if(t)return tQ(t)}}}function tJ(t,e,i,s){let o=Z(e)?e:e.value;return o===W?s?tW(t,{value:o}):i?W:"mid":"mid"===o?"mid":tW(t,{value:o})}function t0(t){return void 0!==t?{h:t.h.value,s:t.s.value,l:t.l.value}:void 0}function t1(t,e,i){let s={h:{enable:!1,value:t.h},s:{enable:!1,value:t.s},l:{enable:!1,value:t.l}};return e&&(t2(s.h,e.h,i),t2(s.s,e.s,i),t2(s.l,e.l,i)),s}function t2(t,e,i){t.enable=e.enable,t.enable?(t.velocity=th(e.speed)/100*i,t.decay=1-th(e.decay),t.status=a.increasing,t.loops=0,t.maxLoops=th(e.count),t.time=0,t.delayTime=1e3*th(e.delay),e.sync||(t.velocity*=tn(),t.value*=tn()),t.initialValue=t.value,t.offset=tp(e.offset)):t.velocity=0}function t3(t,e,i,s){if(!t||!t.enable||(t.maxLoops??0)>0&&(t.loops??0)>(t.maxLoops??0)||(t.time||(t.time=0),(t.delayTime??0)>0&&t.time<(t.delayTime??0)&&(t.time+=s.value),(t.delayTime??0)>0&&t.time<(t.delayTime??0)))return;let o=t.offset?tc(t.offset):0,n=(t.velocity??0)*s.factor+3.6*o,r=t.decay??1,l=tu(e),c=td(e);i&&t.status!==a.increasing?(t.value-=n,t.value<0&&(t.loops||(t.loops=0),t.loops++,t.status=a.increasing)):(t.value+=n,t.value>l&&(t.loops||(t.loops=0),t.loops++,i?t.status=a.decreasing:t.value-=l)),t.velocity&&1!==r&&(t.velocity*=r),t.value=tr(t.value,c,l)}function t5(t,e){if(!t)return;let{h:i,s,l:o}=t,a={h:{min:0,max:360},s:{min:0,max:100},l:{min:0,max:100}};i&&t3(i,a.h,!1,e),s&&t3(s,a.s,!0,e),o&&t3(o,a.l,!0,e)}function t8(t,e,i){t.beginPath(),t.moveTo(e.x,e.y),t.lineTo(i.x,i.y),t.closePath()}function t6(t,e){t.clearRect(H.x,H.y,e.width,e.height)}function t4(t,e,i=!1){if(!e||!t)return;let s=t.style;if(!s)return;let o=new Set;for(let t in s)Object.prototype.hasOwnProperty.call(s,t)&&o.add(s[t]);for(let t in e)Object.prototype.hasOwnProperty.call(e,t)&&o.add(e[t]);for(let t of o){let o=e.getPropertyValue(t);o?s.setProperty(t,o,i?"important":""):s.removeProperty(t)}}!function(t){t.darken="darken",t.enlighten="enlighten"}(h||(h={}));class t9{constructor(t,e){this.container=t,this._applyPostDrawUpdaters=t=>{for(let e of this._postDrawUpdaters)e.afterDraw?.(t)},this._applyPreDrawUpdaters=(t,e,i,s,o,a)=>{for(let n of this._preDrawUpdaters){if(n.getColorStyles){let{fill:a,stroke:r}=n.getColorStyles(e,t,i,s);a&&(o.fill=a),r&&(o.stroke=r)}if(n.getTransformValues){let t=n.getTransformValues(e);for(let e in t)!function(t,e,i){let s=e[i];void 0!==s&&(t[i]=(t[i]??1)*s)}(a,t,e)}n.beforeDraw?.(e)}},this._applyResizePlugins=()=>{for(let t of this._resizePlugins)t.resize?.()},this._getPluginParticleColors=t=>{let e,i;for(let s of this._colorPlugins)if(!e&&s.particleFillColor&&(e=tG(this._engine,s.particleFillColor(t))),!i&&s.particleStrokeColor&&(i=tG(this._engine,s.particleStrokeColor(t))),e&&i)break;return[e,i]},this._initCover=async()=>{let t=this.container.actualOptions.backgroundMask.cover,e=t.color;if(e){let i=tW(this._engine,e);if(i){let e={...i,a:t.opacity};this._coverColorStyle=tX(e,e.a)}}else await new Promise((e,i)=>{if(!t.image)return;let s=document.createElement("img");s.addEventListener("load",()=>{this._coverImage={image:s,opacity:t.opacity},e()}),s.addEventListener("error",t=>{i(t.error)}),s.src=t.image})},this._initStyle=()=>{let t=this.element,e=this.container.actualOptions;if(t)for(let i in this._fullScreen?this._setFullScreenStyle():this._resetOriginalStyle(),e.style){if(!i||!e.style||!Object.prototype.hasOwnProperty.call(e.style,i))continue;let s=e.style[i];s&&t.style.setProperty(i,s,"important")}},this._initTrail=async()=>{let t=this.container.actualOptions.particles.move.trail,e=t.fill;if(!t.enable)return;let i=1/t.length;if(e.color){let t=tW(this._engine,e.color);if(!t)return;this._trailFill={color:{...t},opacity:i}}else await new Promise((t,s)=>{if(!e.image)return;let o=document.createElement("img");o.addEventListener("load",()=>{this._trailFill={image:o,opacity:i},t()}),o.addEventListener("error",t=>{s(t.error)}),o.src=e.image})},this._paintBase=t=>{this.draw(e=>(function(t,e,i){t.fillStyle=i??"rgba(0,0,0,0)",t.fillRect(H.x,H.y,e.width,e.height)})(e,this.size,t))},this._paintImage=(t,e)=>{this.draw(i=>(function(t,e,i,s){i&&(t.globalAlpha=s,t.drawImage(i,H.x,H.y,e.width,e.height),t.globalAlpha=1)})(i,this.size,t,e))},this._repairStyle=()=>{let t=this.element;t&&(this._safeMutationObserver(t=>t.disconnect()),this._initStyle(),this.initBackground(),this._safeMutationObserver(e=>{t&&t instanceof Node&&e.observe(t,{attributes:!0})}))},this._resetOriginalStyle=()=>{let t=this.element,e=this._originalStyle;t&&e&&t4(t,e,!0)},this._safeMutationObserver=t=>{this._mutationObserver&&t(this._mutationObserver)},this._setFullScreenStyle=()=>{let t=this.element;t&&t4(t,t$(this.container.actualOptions.fullScreen.zIndex),!0)},this._engine=e,this._standardSize={height:0,width:0};let i=t.retina.pixelRatio,s=this._standardSize;this.size={height:s.height*i,width:s.width*i},this._context=null,this._generated=!1,this._preDrawUpdaters=[],this._postDrawUpdaters=[],this._resizePlugins=[],this._colorPlugins=[]}get _fullScreen(){return this.container.actualOptions.fullScreen.enable}clear(){let t=this.container.actualOptions,e=t.particles.move.trail,i=this._trailFill;t.backgroundMask.enable?this.paint():e.enable&&e.length>0&&i?i.color?this._paintBase(tX(i.color,i.opacity)):i.image&&this._paintImage(i.image,i.opacity):t.clear&&this.draw(t=>{t6(t,this.size)})}destroy(){if(this.stop(),this._generated){let t=this.element;t?.remove(),this.element=void 0}else this._resetOriginalStyle();this._preDrawUpdaters=[],this._postDrawUpdaters=[],this._resizePlugins=[],this._colorPlugins=[]}draw(t){let e=this._context;if(e)return t(e)}drawAsync(t){let e=this._context;if(e)return t(e)}drawParticle(t,e){if(t.spawning||t.destroyed)return;let i=t.getRadius();if(i<=0)return;let s=t.getFillColor(),o=t.getStrokeColor()??s,[a,n]=this._getPluginParticleColors(t);a||(a=s),n||(n=o),(a||n)&&this.draw(s=>{let o=this.container,r=o.actualOptions,l=t.options.zIndex,c=1-t.zIndexFactor,h=c**l.opacityRate,d=t.bubble.opacity??t.opacity?.value??1,u=t.strokeOpacity??d,p=d*h,f={},y={fill:a?tY(a,p):void 0};y.stroke=n?tY(n,u*h):y.fill,this._applyPreDrawUpdaters(s,t,i,p,y,f),function(t){let{container:e,context:i,particle:s,delta:o,colorStyles:a,backgroundMask:n,composite:r,radius:l,opacity:c,shadow:h,transform:d}=t,u=s.getPosition(),p=s.rotation+(s.pathRotation?s.velocity.angle:0),f={sin:Math.sin(p),cos:Math.cos(p)},y=!!p,v={a:f.cos*(d.a??$.a),b:y?f.sin*(d.b??1):d.b??$.b,c:y?-f.sin*(d.c??1):d.c??$.c,d:f.cos*(d.d??$.d)};i.setTransform(v.a,v.b,v.c,v.d,u.x,u.y),n&&(i.globalCompositeOperation=r);let m=s.shadowColor;h.enable&&m&&(i.shadowBlur=h.blur,i.shadowColor=tX(m),i.shadowOffsetX=h.offset.x,i.shadowOffsetY=h.offset.y),a.fill&&(i.fillStyle=a.fill);let g=s.strokeWidth??0;i.lineWidth=g,a.stroke&&(i.strokeStyle=a.stroke);let b={container:e,context:i,particle:s,radius:l,opacity:c,delta:o,transformData:v,strokeWidth:g};(function(t){let{container:e,context:i,particle:s,radius:o,opacity:a,delta:n,strokeWidth:r,transformData:l}=t;if(!s.shape)return;let c=e.shapeDrawers.get(s.shape);c&&(i.beginPath(),c.draw({context:i,particle:s,radius:o,opacity:a,delta:n,pixelRatio:e.retina.pixelRatio,transformData:{...l}}),s.shapeClose&&i.closePath(),r>0&&i.stroke(),s.shapeFill&&i.fill())})(b),function(t){let{container:e,context:i,particle:s,radius:o,opacity:a,delta:n,transformData:r}=t;if(!s.shape)return;let l=e.shapeDrawers.get(s.shape);l?.afterDraw&&l.afterDraw({context:i,particle:s,radius:o,opacity:a,delta:n,pixelRatio:e.retina.pixelRatio,transformData:{...r}})}(b),function(t){let{container:e,context:i,particle:s,radius:o,opacity:a,delta:n,transformData:r}=t;if(!s.effect)return;let l=e.effectDrawers.get(s.effect);l&&l.draw({context:i,particle:s,radius:o,opacity:a,delta:n,pixelRatio:e.retina.pixelRatio,transformData:{...r}})}(b),i.globalCompositeOperation="source-over",i.resetTransform()}({container:o,context:s,particle:t,delta:e,colorStyles:y,backgroundMask:r.backgroundMask.enable,composite:r.backgroundMask.composite,radius:i*c**l.sizeRate,opacity:p,shadow:t.options.shadow,transform:f}),this._applyPostDrawUpdaters(t)})}drawParticlePlugin(t,e,i){this.draw(s=>{t.drawParticle&&t.drawParticle(s,e,i)})}drawPlugin(t,e){this.draw(i=>{t.draw&&t.draw(i,e)})}async init(){this._safeMutationObserver(t=>t.disconnect()),this._mutationObserver=function(t){if(!tk()&&"undefined"!=typeof MutationObserver)return new MutationObserver(t)}(t=>{for(let e of t)"attributes"===e.type&&"style"===e.attributeName&&this._repairStyle()}),this.resize(),this._initStyle(),await this._initCover();try{await this._initTrail()}catch(t){t_.error(t)}this.initBackground(),this._safeMutationObserver(t=>{this.element&&this.element instanceof Node&&t.observe(this.element,{attributes:!0})}),this.initUpdaters(),this.initPlugins(),this.paint()}initBackground(){let t=this.container.actualOptions.background,e=this.element;if(!e)return;let i=e.style;if(i){if(t.color){let e=tW(this._engine,t.color);i.backgroundColor=e?tX(e,t.opacity):""}else i.backgroundColor="";i.backgroundImage=t.image||"",i.backgroundPosition=t.position||"",i.backgroundRepeat=t.repeat||"",i.backgroundSize=t.size||""}}initPlugins(){for(let t of(this._resizePlugins=[],this.container.plugins.values()))t.resize&&this._resizePlugins.push(t),(t.particleFillColor??t.particleStrokeColor)&&this._colorPlugins.push(t)}initUpdaters(){for(let t of(this._preDrawUpdaters=[],this._postDrawUpdaters=[],this.container.particles.updaters))t.afterDraw&&this._postDrawUpdaters.push(t),(t.getColorStyles??t.getTransformValues??t.beforeDraw)&&this._preDrawUpdaters.push(t)}loadCanvas(t){this._generated&&this.element&&this.element.remove(),this._generated=t.dataset&&B in t.dataset?"true"===t.dataset[B]:this._generated,this.element=t,this.element.ariaHidden="true",this._originalStyle=function(t){let e=document.createElement("div").style;if(!t)return e;for(let i in t){let s=t[i];if(!Object.prototype.hasOwnProperty.call(t,i)||te(s))continue;let o=t.getPropertyValue?.(s);if(!o)continue;let a=t.getPropertyPriority?.(s);a?e.setProperty?.(s,o,a):e.setProperty?.(s,o)}return e}(this.element.style);let e=this._standardSize;e.height=t.offsetHeight,e.width=t.offsetWidth;let i=this.container.retina.pixelRatio,s=this.size;t.height=s.height=e.height*i,t.width=s.width=e.width*i,this._context=this.element.getContext("2d"),this._safeMutationObserver(t=>t.disconnect()),this.container.retina.init(),this.initBackground(),this._safeMutationObserver(t=>{this.element&&this.element instanceof Node&&t.observe(this.element,{attributes:!0})})}paint(){let t=this.container.actualOptions;this.draw(e=>{t.backgroundMask.enable&&t.backgroundMask.cover?(t6(e,this.size),this._coverImage?this._paintImage(this._coverImage.image,this._coverImage.opacity):this._coverColorStyle?this._paintBase(this._coverColorStyle):this._paintBase()):this._paintBase()})}resize(){if(!this.element)return!1;let t=this.container,e=t.canvas._standardSize,i={width:this.element.offsetWidth,height:this.element.offsetHeight},s=t.retina.pixelRatio,o={width:i.width*s,height:i.height*s};if(i.height===e.height&&i.width===e.width&&o.height===this.element.height&&o.width===this.element.width)return!1;let a={...e};e.height=i.height,e.width=i.width;let n=this.size;return this.element.width=n.width=o.width,this.element.height=n.height=o.height,this.container.started&&t.particles.setResizeFactor({width:e.width/a.width,height:e.height/a.height}),!0}stop(){this._safeMutationObserver(t=>t.disconnect()),this._mutationObserver=void 0,this.draw(t=>t6(t,this.size))}async windowResize(){if(!this.element||!this.resize())return;let t=this.container,e=t.updateActualOptions();t.particles.setDensity(),this._applyResizePlugins(),e&&await t.refresh()}}function t7(t,e,i,s,o){if(s){let s={passive:!0};Y(o)?s.capture=o:void 0!==o&&(s=o),t.addEventListener(e,i,s)}else t.removeEventListener(e,i,o)}!function(t){t.canvas="canvas",t.parent="parent",t.window="window"}(d||(d={}));class et{constructor(t){this.container=t,this._doMouseTouchClick=t=>{let e=this.container,i=e.actualOptions;if(this._canPush){let t=e.interactivity.mouse,s=t.position;if(!s)return;t.clickPosition={...s},t.clickTime=new Date().getTime(),tB(i.interactivity.events.onClick.mode,t=>this.container.handleClickMode(t))}"touchend"===t.type&&setTimeout(()=>this._mouseTouchFinish(),500)},this._handleThemeChange=t=>{let e=this.container,i=e.options,s=i.defaultThemes,o=t.matches?s.dark:s.light,a=i.themes.find(t=>t.name===o);a?.default.auto&&e.loadTheme(o)},this._handleVisibilityChange=()=>{let t=this.container,e=t.actualOptions;this._mouseTouchFinish(),e.pauseOnBlur&&(document?.hidden?(t.pageHidden=!0,t.pause()):(t.pageHidden=!1,t.animationStatus?t.play(!0):t.draw(!0)))},this._handleWindowResize=()=>{this._resizeTimeout&&(clearTimeout(this._resizeTimeout),delete this._resizeTimeout);let t=async()=>{let t=this.container.canvas;await t?.windowResize()};this._resizeTimeout=setTimeout(()=>void t(),1e3*this.container.actualOptions.interactivity.events.resize.delay)},this._manageInteractivityListeners=(t,e)=>{let i=this._handlers,s=this.container,o=s.actualOptions,a=s.interactivity.element;if(!a)return;let n=s.canvas.element;n&&(n.style.pointerEvents=a===n?"initial":"none"),(o.interactivity.events.onHover.enable||o.interactivity.events.onClick.enable)&&(t7(a,q,i.mouseMove,e),t7(a,"touchstart",i.touchStart,e),t7(a,"touchmove",i.touchMove,e),o.interactivity.events.onClick.enable?(t7(a,V,i.touchEndClick,e),t7(a,"pointerup",i.mouseUp,e),t7(a,"pointerdown",i.mouseDown,e)):t7(a,V,i.touchEnd,e),t7(a,t,i.mouseLeave,e),t7(a,"touchcancel",i.touchCancel,e))},this._manageListeners=t=>{let e=this._handlers,i=this.container,s=i.actualOptions.interactivity.detectsOn,o=i.canvas.element,a=A;s===d.window?(i.interactivity.element=window,a="pointerout"):s===d.parent&&o?i.interactivity.element=o.parentElement??o.parentNode:i.interactivity.element=o,this._manageMediaMatch(t),this._manageResize(t),this._manageInteractivityListeners(a,t),document&&t7(document,"visibilitychange",e.visibilityChange,t,!1)},this._manageMediaMatch=t=>{let e=this._handlers,i=tz("(prefers-color-scheme: dark)");if(i){if(void 0!==i.addEventListener){t7(i,"change",e.themeChange,t);return}void 0!==i.addListener&&(t?i.addListener(e.oldThemeChange):i.removeListener(e.oldThemeChange))}},this._manageResize=t=>{let e=this._handlers,i=this.container;if(!i.actualOptions.interactivity.events.resize)return;if("undefined"==typeof ResizeObserver){t7(window,"resize",e.resize,t);return}let s=i.canvas.element;this._resizeObserver&&!t?(s&&this._resizeObserver.unobserve(s),this._resizeObserver.disconnect(),delete this._resizeObserver):!this._resizeObserver&&t&&s&&(this._resizeObserver=new ResizeObserver(t=>{t.find(t=>t.target===s)&&this._handleWindowResize()}),this._resizeObserver.observe(s))},this._mouseDown=()=>{let{interactivity:t}=this.container;if(!t)return;let{mouse:e}=t;e.clicking=!0,e.downPosition=e.position},this._mouseTouchClick=t=>{let e=this.container,i=e.actualOptions,{mouse:s}=e.interactivity;s.inside=!0;let o=!1,a=s.position;if(a&&i.interactivity.events.onClick.enable){for(let t of e.plugins.values())if(t.clickPositionValid&&(o=t.clickPositionValid(a)))break;o||this._doMouseTouchClick(t),s.clicking=!1}},this._mouseTouchFinish=()=>{let t=this.container.interactivity;if(!t)return;let e=t.mouse;delete e.position,delete e.clickPosition,delete e.downPosition,t.status=A,e.inside=!1,e.clicking=!1},this._mouseTouchMove=t=>{let e;let i=this.container,s=i.actualOptions,o=i.interactivity,a=i.canvas.element;if(!o?.element)return;if(o.mouse.inside=!0,t.type.startsWith("pointer")){if(this._canPush=!0,o.element===window){if(a){let i=a.getBoundingClientRect();e={x:t.clientX-i.left,y:t.clientY-i.top}}}else if(s.interactivity.detectsOn===d.parent){let i=t.target,s=t.currentTarget;if(i&&s&&a){let o=i.getBoundingClientRect(),n=s.getBoundingClientRect(),r=a.getBoundingClientRect();e={x:t.offsetX+2*o.left-(n.left+r.left),y:t.offsetY+2*o.top-(n.top+r.top)}}else e={x:t.offsetX??t.clientX,y:t.offsetY??t.clientY}}else t.target===a&&(e={x:t.offsetX??t.clientX,y:t.offsetY??t.clientY})}else if(this._canPush="touchmove"!==t.type,a){let i=t.touches[t.touches.length-1],s=a.getBoundingClientRect();e={x:i.clientX-(s.left??0),y:i.clientY-(s.top??0)}}let n=i.retina.pixelRatio;e&&(e.x*=n,e.y*=n),o.mouse.position=e,o.status=q},this._touchEnd=t=>{for(let e of Array.from(t.changedTouches))this._touches.delete(e.identifier);this._mouseTouchFinish()},this._touchEndClick=t=>{for(let e of Array.from(t.changedTouches))this._touches.delete(e.identifier);this._mouseTouchClick(t)},this._touchStart=t=>{for(let e of Array.from(t.changedTouches))this._touches.set(e.identifier,performance.now());this._mouseTouchMove(t)},this._canPush=!0,this._touches=new Map,this._handlers={mouseDown:()=>this._mouseDown(),mouseLeave:()=>this._mouseTouchFinish(),mouseMove:t=>this._mouseTouchMove(t),mouseUp:t=>this._mouseTouchClick(t),touchStart:t=>this._touchStart(t),touchMove:t=>this._mouseTouchMove(t),touchEnd:t=>this._touchEnd(t),touchCancel:t=>this._touchEnd(t),touchEndClick:t=>this._touchEndClick(t),visibilityChange:()=>this._handleVisibilityChange(),themeChange:t=>this._handleThemeChange(t),oldThemeChange:t=>this._handleThemeChange(t),resize:()=>{this._handleWindowResize()}}}addListeners(){this._manageListeners(!0)}removeListeners(){this._manageListeners(!1)}}!function(t){t.configAdded="configAdded",t.containerInit="containerInit",t.particlesSetup="particlesSetup",t.containerStarted="containerStarted",t.containerStopped="containerStopped",t.containerDestroyed="containerDestroyed",t.containerPaused="containerPaused",t.containerPlay="containerPlay",t.containerBuilt="containerBuilt",t.particleAdded="particleAdded",t.particleDestroyed="particleDestroyed",t.particleRemoved="particleRemoved"}(u||(u={}));class ee{constructor(){this.value=""}static create(t,e){let i=new ee;return i.load(t),void 0!==e&&(Z(e)||tt(e)?i.load({value:e}):i.load(e)),i}load(t){!te(t)&&(te(t.value)||(this.value=t.value))}}class ei{constructor(){this.color=new ee,this.color.value="",this.image="",this.position="",this.repeat="",this.size="",this.opacity=1}load(t){te(t)||(void 0!==t.color&&(this.color=ee.create(this.color,t.color)),void 0!==t.image&&(this.image=t.image),void 0!==t.position&&(this.position=t.position),void 0!==t.repeat&&(this.repeat=t.repeat),void 0!==t.size&&(this.size=t.size),void 0===t.opacity||(this.opacity=t.opacity))}}class es{constructor(){this.opacity=1}load(t){te(t)||(void 0!==t.color&&(this.color=ee.create(this.color,t.color)),void 0!==t.image&&(this.image=t.image),void 0===t.opacity||(this.opacity=t.opacity))}}class eo{constructor(){this.composite="destination-out",this.cover=new es,this.enable=!1}load(t){if(!te(t)){if(void 0!==t.composite&&(this.composite=t.composite),void 0!==t.cover){let e=t.cover,i=Z(t.cover)?{color:t.cover}:t.cover;this.cover.load(void 0!==e.color||void 0!==e.image?e:{color:i})}void 0!==t.enable&&(this.enable=t.enable)}}}class ea{constructor(){this.enable=!0,this.zIndex=0}load(t){te(t)||(void 0!==t.enable&&(this.enable=t.enable),void 0===t.zIndex||(this.zIndex=t.zIndex))}}class en{constructor(){this.enable=!1,this.mode=[]}load(t){te(t)||(void 0!==t.enable&&(this.enable=t.enable),void 0===t.mode||(this.mode=t.mode))}}!function(t){t.circle="circle",t.rectangle="rectangle"}(p||(p={}));class er{constructor(){this.selectors=[],this.enable=!1,this.mode=[],this.type=p.circle}load(t){te(t)||(void 0!==t.selectors&&(this.selectors=t.selectors),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.mode&&(this.mode=t.mode),void 0===t.type||(this.type=t.type))}}class el{constructor(){this.enable=!1,this.force=2,this.smooth=10}load(t){te(t)||(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.force&&(this.force=t.force),void 0===t.smooth||(this.smooth=t.smooth))}}class ec{constructor(){this.enable=!1,this.mode=[],this.parallax=new el}load(t){te(t)||(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.mode&&(this.mode=t.mode),this.parallax.load(t.parallax))}}class eh{constructor(){this.delay=.5,this.enable=!0}load(t){te(t)||(void 0!==t.delay&&(this.delay=t.delay),void 0===t.enable||(this.enable=t.enable))}}class ed{constructor(){this.onClick=new en,this.onDiv=new er,this.onHover=new ec,this.resize=new eh}load(t){if(te(t))return;this.onClick.load(t.onClick);let e=t.onDiv;void 0!==e&&(this.onDiv=tB(e,t=>{let e=new er;return e.load(t),e})),this.onHover.load(t.onHover),this.resize.load(t.resize)}}class eu{constructor(t,e){this._engine=t,this._container=e}load(t){if(te(t)||!this._container)return;let e=this._engine.interactors.get(this._container);if(e)for(let i of e)i.loadModeOptions&&i.loadModeOptions(this,t)}}class ep{constructor(t,e){this.detectsOn=d.window,this.events=new ed,this.modes=new eu(t,e)}load(t){if(te(t))return;let e=t.detectsOn;void 0!==e&&(this.detectsOn=e),this.events.load(t.events),this.modes.load(t.modes)}}class ef{load(t){!te(t)&&(t.position&&(this.position={x:t.position.x??50,y:t.position.y??50,mode:t.position.mode??l.percent}),t.options&&(this.options=tT({},t.options)))}}!function(t){t.screen="screen",t.canvas="canvas"}(f||(f={}));class ey{constructor(){this.maxWidth=1/0,this.options={},this.mode=f.canvas}load(t){!te(t)&&(te(t.maxWidth)||(this.maxWidth=t.maxWidth),te(t.mode)||(t.mode===f.screen?this.mode=f.screen:this.mode=f.canvas),te(t.options)||(this.options=tT({},t.options)))}}!function(t){t.any="any",t.dark="dark",t.light="light"}(y||(y={}));class ev{constructor(){this.auto=!1,this.mode=y.any,this.value=!1}load(t){te(t)||(void 0!==t.auto&&(this.auto=t.auto),void 0!==t.mode&&(this.mode=t.mode),void 0===t.value||(this.value=t.value))}}class em{constructor(){this.name="",this.default=new ev}load(t){te(t)||(void 0!==t.name&&(this.name=t.name),this.default.load(t.default),void 0!==t.options&&(this.options=tT({},t.options)))}}class eg{constructor(){this.count=0,this.enable=!1,this.speed=1,this.decay=0,this.delay=0,this.sync=!1}load(t){te(t)||(void 0!==t.count&&(this.count=tp(t.count)),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.speed&&(this.speed=tp(t.speed)),void 0!==t.decay&&(this.decay=tp(t.decay)),void 0!==t.delay&&(this.delay=tp(t.delay)),void 0===t.sync||(this.sync=t.sync))}}class eb extends eg{constructor(){super(),this.mode=o.auto,this.startValue=c.random}load(t){super.load(t),te(t)||(void 0!==t.mode&&(this.mode=t.mode),void 0===t.startValue||(this.startValue=t.startValue))}}class ew extends eg{constructor(){super(),this.offset=0,this.sync=!0}load(t){super.load(t),te(t)||void 0===t.offset||(this.offset=tp(t.offset))}}class e_{constructor(){this.h=new ew,this.s=new ew,this.l=new ew}load(t){te(t)||(this.h.load(t.h),this.s.load(t.s),this.l.load(t.l))}}class ex extends ee{constructor(){super(),this.animation=new e_}static create(t,e){let i=new ex;return i.load(t),void 0!==e&&(Z(e)||tt(e)?i.load({value:e}):i.load(e)),i}load(t){if(super.load(t),te(t))return;let e=t.animation;void 0!==e&&(void 0!==e.enable?this.animation.h.load(e):this.animation.load(t.animation))}}!function(t){t.absorb="absorb",t.bounce="bounce",t.destroy="destroy"}(v||(v={}));class ek{constructor(){this.speed=2}load(t){te(t)||void 0===t.speed||(this.speed=t.speed)}}class ez{constructor(){this.enable=!0,this.retries=0}load(t){te(t)||(void 0!==t.enable&&(this.enable=t.enable),void 0===t.retries||(this.retries=t.retries))}}class eP{constructor(){this.value=0}load(t){!te(t)&&(te(t.value)||(this.value=tp(t.value)))}}class eC extends eP{constructor(){super(),this.animation=new eg}load(t){if(super.load(t),te(t))return;let e=t.animation;void 0!==e&&this.animation.load(e)}}class eM extends eC{constructor(){super(),this.animation=new eb}load(t){super.load(t)}}class eO extends eP{constructor(){super(),this.value=1}}class eS{constructor(){this.horizontal=new eO,this.vertical=new eO}load(t){te(t)||(this.horizontal.load(t.horizontal),this.vertical.load(t.vertical))}}class eD{constructor(){this.absorb=new ek,this.bounce=new eS,this.enable=!1,this.maxSpeed=50,this.mode=v.bounce,this.overlap=new ez}load(t){te(t)||(this.absorb.load(t.absorb),this.bounce.load(t.bounce),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.maxSpeed&&(this.maxSpeed=tp(t.maxSpeed)),void 0!==t.mode&&(this.mode=t.mode),this.overlap.load(t.overlap))}}class eT{constructor(){this.close=!0,this.fill=!0,this.options={},this.type=[]}load(t){if(te(t))return;let e=t.options;if(void 0!==e)for(let t in e){let i=e[t];i&&(this.options[t]=tT(this.options[t]??{},i))}void 0!==t.close&&(this.close=t.close),void 0!==t.fill&&(this.fill=t.fill),void 0!==t.type&&(this.type=t.type)}}class eE{constructor(){this.offset=0,this.value=90}load(t){te(t)||(void 0!==t.offset&&(this.offset=tp(t.offset)),void 0!==t.value&&(this.value=tp(t.value)))}}class eR{constructor(){this.distance=200,this.enable=!1,this.rotate={x:3e3,y:3e3}}load(t){if(!te(t)&&(void 0!==t.distance&&(this.distance=tp(t.distance)),void 0!==t.enable&&(this.enable=t.enable),t.rotate)){let e=t.rotate.x;void 0!==e&&(this.rotate.x=e);let i=t.rotate.y;void 0!==i&&(this.rotate.y=i)}}}class eI{constructor(){this.x=50,this.y=50,this.mode=l.percent,this.radius=0}load(t){te(t)||(void 0!==t.x&&(this.x=t.x),void 0!==t.y&&(this.y=t.y),void 0!==t.mode&&(this.mode=t.mode),void 0===t.radius||(this.radius=t.radius))}}class eL{constructor(){this.acceleration=9.81,this.enable=!1,this.inverse=!1,this.maxSpeed=50}load(t){te(t)||(void 0!==t.acceleration&&(this.acceleration=tp(t.acceleration)),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.inverse&&(this.inverse=t.inverse),void 0!==t.maxSpeed&&(this.maxSpeed=tp(t.maxSpeed)))}}class eF{constructor(){this.clamp=!0,this.delay=new eP,this.enable=!1,this.options={}}load(t){!te(t)&&(void 0!==t.clamp&&(this.clamp=t.clamp),this.delay.load(t.delay),void 0!==t.enable&&(this.enable=t.enable),this.generator=t.generator,t.options&&(this.options=tT(this.options,t.options)))}}class eB{load(t){te(t)||(void 0!==t.color&&(this.color=ee.create(this.color,t.color)),void 0===t.image||(this.image=t.image))}}class eA{constructor(){this.enable=!1,this.length=10,this.fill=new eB}load(t){te(t)||(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.fill&&this.fill.load(t.fill),void 0===t.length||(this.length=t.length))}}!function(t){t.bounce="bounce",t.none="none",t.out="out",t.destroy="destroy",t.split="split"}(m||(m={}));class eq{constructor(){this.default=m.out}load(t){te(t)||(void 0!==t.default&&(this.default=t.default),this.bottom=t.bottom??t.default,this.left=t.left??t.default,this.right=t.right??t.default,this.top=t.top??t.default)}}class eV{constructor(){this.acceleration=0,this.enable=!1}load(t){!te(t)&&(void 0!==t.acceleration&&(this.acceleration=tp(t.acceleration)),void 0!==t.enable&&(this.enable=t.enable),t.position&&(this.position=tT({},t.position)))}}class eU{constructor(){this.angle=new eE,this.attract=new eR,this.center=new eI,this.decay=0,this.distance={},this.direction=s.none,this.drift=0,this.enable=!1,this.gravity=new eL,this.path=new eF,this.outModes=new eq,this.random=!1,this.size=!1,this.speed=2,this.spin=new eV,this.straight=!1,this.trail=new eA,this.vibrate=!1,this.warp=!1}load(t){if(te(t))return;this.angle.load(K(t.angle)?{value:t.angle}:t.angle),this.attract.load(t.attract),this.center.load(t.center),void 0!==t.decay&&(this.decay=tp(t.decay)),void 0!==t.direction&&(this.direction=t.direction),void 0!==t.distance&&(this.distance=K(t.distance)?{horizontal:t.distance,vertical:t.distance}:{...t.distance}),void 0!==t.drift&&(this.drift=tp(t.drift)),void 0!==t.enable&&(this.enable=t.enable),this.gravity.load(t.gravity);let e=t.outModes;void 0!==e&&(J(e)?this.outModes.load(e):this.outModes.load({default:e})),this.path.load(t.path),void 0!==t.random&&(this.random=t.random),void 0!==t.size&&(this.size=t.size),void 0!==t.speed&&(this.speed=tp(t.speed)),this.spin.load(t.spin),void 0!==t.straight&&(this.straight=t.straight),this.trail.load(t.trail),void 0!==t.vibrate&&(this.vibrate=t.vibrate),void 0!==t.warp&&(this.warp=t.warp)}}class eH extends eb{constructor(){super(),this.destroy=n.none,this.speed=2}load(t){super.load(t),te(t)||void 0===t.destroy||(this.destroy=t.destroy)}}class e$ extends eM{constructor(){super(),this.animation=new eH,this.value=1}load(t){if(te(t))return;super.load(t);let e=t.animation;void 0!==e&&this.animation.load(e)}}class eW{constructor(){this.enable=!1,this.width=1920,this.height=1080}load(t){if(te(t))return;void 0!==t.enable&&(this.enable=t.enable);let e=t.width;void 0!==e&&(this.width=e);let i=t.height;void 0!==i&&(this.height=i)}}!function(t){t.delete="delete",t.wait="wait"}(g||(g={}));class eG{constructor(){this.mode=g.delete,this.value=0}load(t){te(t)||(void 0!==t.mode&&(this.mode=t.mode),void 0===t.value||(this.value=t.value))}}class ej{constructor(){this.density=new eW,this.limit=new eG,this.value=0}load(t){te(t)||(this.density.load(t.density),this.limit.load(t.limit),void 0===t.value||(this.value=t.value))}}class eQ{constructor(){this.blur=0,this.color=new ee,this.enable=!1,this.offset={x:0,y:0},this.color.value="#000"}load(t){!te(t)&&(void 0!==t.blur&&(this.blur=t.blur),this.color=ee.create(this.color,t.color),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.offset&&(void 0!==t.offset.x&&(this.offset.x=t.offset.x),void 0!==t.offset.y&&(this.offset.y=t.offset.y)))}}class eN{constructor(){this.close=!0,this.fill=!0,this.options={},this.type="circle"}load(t){if(te(t))return;let e=t.options;if(void 0!==e)for(let t in e){let i=e[t];i&&(this.options[t]=tT(this.options[t]??{},i))}void 0!==t.close&&(this.close=t.close),void 0!==t.fill&&(this.fill=t.fill),void 0!==t.type&&(this.type=t.type)}}class eX extends eb{constructor(){super(),this.destroy=n.none,this.speed=5}load(t){super.load(t),te(t)||void 0===t.destroy||(this.destroy=t.destroy)}}class eY extends eM{constructor(){super(),this.animation=new eX,this.value=3}load(t){if(super.load(t),te(t))return;let e=t.animation;void 0!==e&&this.animation.load(e)}}class eZ{constructor(){this.width=0}load(t){te(t)||(void 0!==t.color&&(this.color=ex.create(this.color,t.color)),void 0!==t.width&&(this.width=tp(t.width)),void 0!==t.opacity&&(this.opacity=tp(t.opacity)))}}class eK extends eP{constructor(){super(),this.opacityRate=1,this.sizeRate=1,this.velocityRate=1}load(t){super.load(t),te(t)||(void 0!==t.opacityRate&&(this.opacityRate=t.opacityRate),void 0!==t.sizeRate&&(this.sizeRate=t.sizeRate),void 0===t.velocityRate||(this.velocityRate=t.velocityRate))}}class eJ{constructor(t,e){this._engine=t,this._container=e,this.bounce=new eS,this.collisions=new eD,this.color=new ex,this.color.value="#fff",this.effect=new eT,this.groups={},this.move=new eU,this.number=new ej,this.opacity=new e$,this.reduceDuplicates=!1,this.shadow=new eQ,this.shape=new eN,this.size=new eY,this.stroke=new eZ,this.zIndex=new eK}load(t){if(te(t))return;if(void 0!==t.groups)for(let e of Object.keys(t.groups)){if(!Object.hasOwn(t.groups,e))continue;let i=t.groups[e];void 0!==i&&(this.groups[e]=tT(this.groups[e]??{},i))}void 0!==t.reduceDuplicates&&(this.reduceDuplicates=t.reduceDuplicates),this.bounce.load(t.bounce),this.color.load(ex.create(this.color,t.color)),this.effect.load(t.effect),this.move.load(t.move),this.number.load(t.number),this.opacity.load(t.opacity),this.shape.load(t.shape),this.size.load(t.size),this.shadow.load(t.shadow),this.zIndex.load(t.zIndex),this.collisions.load(t.collisions),void 0!==t.interactivity&&(this.interactivity=tT({},t.interactivity));let e=t.stroke;if(e&&(this.stroke=tB(e,t=>{let e=new eZ;return e.load(t),e})),this._container){let e=this._engine.updaters.get(this._container);if(e)for(let i of e)i.loadOptions&&i.loadOptions(this,t);let i=this._engine.interactors.get(this._container);if(i)for(let e of i)e.loadParticlesOptions&&e.loadParticlesOptions(this,t)}}}function e0(t,...e){for(let i of e)t.load(i)}function e1(t,e,...i){let s=new eJ(t,e);return e0(s,...i),s}class e2{constructor(t,e){this._findDefaultTheme=t=>this.themes.find(e=>e.default.value&&e.default.mode===t)??this.themes.find(t=>t.default.value&&t.default.mode===y.any),this._importPreset=t=>{this.load(this._engine.getPreset(t))},this._engine=t,this._container=e,this.autoPlay=!0,this.background=new ei,this.backgroundMask=new eo,this.clear=!0,this.defaultThemes={},this.delay=0,this.fullScreen=new ea,this.detectRetina=!0,this.duration=0,this.fpsLimit=120,this.interactivity=new ep(t,e),this.manualParticles=[],this.particles=e1(this._engine,this._container),this.pauseOnBlur=!0,this.pauseOnOutsideViewport=!0,this.responsive=[],this.smooth=!1,this.style={},this.themes=[],this.zLayers=100}load(t){if(te(t))return;void 0!==t.preset&&tB(t.preset,t=>this._importPreset(t)),void 0!==t.autoPlay&&(this.autoPlay=t.autoPlay),void 0!==t.clear&&(this.clear=t.clear),void 0!==t.key&&(this.key=t.key),void 0!==t.name&&(this.name=t.name),void 0!==t.delay&&(this.delay=tp(t.delay));let e=t.detectRetina;void 0!==e&&(this.detectRetina=e),void 0!==t.duration&&(this.duration=tp(t.duration));let i=t.fpsLimit;void 0!==i&&(this.fpsLimit=i),void 0!==t.pauseOnBlur&&(this.pauseOnBlur=t.pauseOnBlur),void 0!==t.pauseOnOutsideViewport&&(this.pauseOnOutsideViewport=t.pauseOnOutsideViewport),void 0!==t.zLayers&&(this.zLayers=t.zLayers),this.background.load(t.background);let s=t.fullScreen;Y(s)?this.fullScreen.enable=s:this.fullScreen.load(s),this.backgroundMask.load(t.backgroundMask),this.interactivity.load(t.interactivity),t.manualParticles&&(this.manualParticles=t.manualParticles.map(t=>{let e=new ef;return e.load(t),e})),this.particles.load(t.particles),this.style=tT(this.style,t.style),this._engine.loadOptions(this,t),void 0!==t.smooth&&(this.smooth=t.smooth);let o=this._engine.interactors.get(this._container);if(o)for(let e of o)e.loadOptions&&e.loadOptions(this,t);if(void 0!==t.responsive)for(let e of t.responsive){let t=new ey;t.load(e),this.responsive.push(t)}if(this.responsive.sort((t,e)=>t.maxWidth-e.maxWidth),void 0!==t.themes)for(let e of t.themes){let t=this.themes.find(t=>t.name===e.name);if(t)t.load(e);else{let t=new em;t.load(e),this.themes.push(t)}}this.defaultThemes.dark=this._findDefaultTheme(y.dark)?.name,this.defaultThemes.light=this._findDefaultTheme(y.light)?.name}setResponsive(t,e,i){this.load(i);let s=this.responsive.find(i=>i.mode===f.screen&&screen?i.maxWidth>screen.availWidth:i.maxWidth*e>t);return this.load(s?.options),s?.maxWidth}setTheme(t){if(t){let e=this.themes.find(e=>e.name===t);e&&this.load(e.options)}else{let t=tz("(prefers-color-scheme: dark)"),e=t?.matches,i=this._findDefaultTheme(e?y.dark:y.light);i&&this.load(i.options)}}}!function(t){t.external="external",t.particles="particles"}(b||(b={}));class e3{constructor(t,e){this.container=e,this._engine=t,this._interactors=[],this._externalInteractors=[],this._particleInteractors=[]}externalInteract(t){for(let e of this._externalInteractors)e.isEnabled()&&e.interact(t)}handleClickMode(t){for(let e of this._externalInteractors)e.handleClickMode?.(t)}async init(){for(let t of(this._interactors=await this._engine.getInteractors(this.container,!0),this._externalInteractors=[],this._particleInteractors=[],this._interactors)){switch(t.type){case b.external:this._externalInteractors.push(t);break;case b.particles:this._particleInteractors.push(t)}t.init()}}particlesInteract(t,e){for(let i of this._externalInteractors)i.clear(t,e);for(let i of this._particleInteractors)i.isEnabled(t)&&i.interact(t,e)}reset(t){for(let e of this._externalInteractors)e.isEnabled()&&e.reset(t);for(let e of this._particleInteractors)e.isEnabled(t)&&e.reset(t)}}function e5(t){if(!tP(t.outMode,t.checkModes))return;let e=2*t.radius;t.coord>t.maxCoord-e?t.setCb(-t.radius):t.coord<e&&t.setCb(t.radius)}!function(t){t.normal="normal",t.inside="inside",t.outside="outside"}(w||(w={}));class e8{constructor(t,e){this.container=e,this._calcPosition=(t,e,i,s=X)=>{for(let s of t.plugins.values()){let t=void 0!==s.particlePosition?s.particlePosition(e,this):void 0;if(t)return ti.create(t.x,t.y,i)}let o=function(t){return{x:t.position?.x??tn()*t.size.width,y:t.position?.y??tn()*t.size.height}}({size:t.canvas.size,position:e}),a=ti.create(o.x,o.y,i),n=this.getRadius(),r=this.options.move.outModes,l=e=>{e5({outMode:e,checkModes:[m.bounce],coord:a.x,maxCoord:t.canvas.size.width,setCb:t=>a.x+=t,radius:n})},c=e=>{e5({outMode:e,checkModes:[m.bounce],coord:a.y,maxCoord:t.canvas.size.height,setCb:t=>a.y+=t,radius:n})};return(l(r.left??r.default),l(r.right??r.default),c(r.top??r.default),c(r.bottom??r.default),this._checkOverlap(a,s))?this._calcPosition(t,void 0,i,s+1):a},this._calculateVelocity=()=>{let t=(function(t){let e=ts.origin;return e.length=1,e.angle=t,e})(this.direction).copy(),e=this.options.move;if(e.direction===s.inside||e.direction===s.outside)return t;let i=tv(th(e.angle.value)),o=tv(th(e.angle.offset)),a={left:o-.5*i,right:o+.5*i};return e.straight||(t.angle+=tc(tp(a.left,a.right))),e.random&&"number"==typeof e.speed&&(t.length*=tn()),t},this._checkOverlap=(t,e=X)=>{let i=this.options.collisions,s=this.getRadius();if(!i.enable)return!1;let o=i.overlap;if(o.enable)return!1;let a=o.retries;if(a>=0&&e>a)throw Error(`${U} particle is overlapping and can't be placed`);return!!this.container.particles.find(e=>ty(t,e.position)<s+e.getRadius())},this._getRollColor=t=>{if(!t||!this.roll||!this.backColor&&!this.roll.alter)return t;let e=this.roll.horizontal&&this.roll.vertical?2:1,i=this.roll.horizontal?.5*Math.PI:0;return Math.floor(((this.roll.angle??0)+i)/(Math.PI/e))%2?this.backColor?this.backColor:this.roll.alter?function(t,e,i){return{h:t.h,s:t.s,l:t.l+(e===h.darken?-1:1)*i}}(t,this.roll.alter.type,this.roll.alter.value):t:t},this._initPosition=t=>{let e=this.container,i=th(this.options.zIndex.value);this.position=this._calcPosition(e,t,tr(i,0,e.zLayers)),this.initialPosition=this.position.copy();let o=e.canvas.size;switch(this.moveCenter={...tU(this.options.move.center,o),radius:this.options.move.center.radius??0,mode:this.options.move.center.mode??l.percent},this.direction=function(t,e,i){if(K(t))return tv(t);switch(t){case s.top:return-(.5*Math.PI);case s.topRight:return-(.25*Math.PI);case s.right:return 0;case s.bottomRight:return .25*Math.PI;case s.bottom:return .5*Math.PI;case s.bottomLeft:return .75*Math.PI;case s.left:return Math.PI;case s.topLeft:return-(.75*Math.PI);case s.inside:return Math.atan2(i.y-e.y,i.x-e.x);case s.outside:return Math.atan2(e.y-i.y,e.x-i.x);default:return tn()*G}}(this.options.move.direction,this.position,this.moveCenter),this.options.move.direction){case s.inside:this.outType=w.inside;break;case s.outside:this.outType=w.outside}this.offset=ts.origin},this._engine=t}destroy(t){if(this.unbreakable||this.destroyed)return;this.destroyed=!0,this.bubble.inRange=!1,this.slow.inRange=!1;let e=this.container,i=this.pathGenerator,s=e.shapeDrawers.get(this.shape);for(let i of(s?.particleDestroy?.(this),e.plugins.values()))i.particleDestroyed?.(this,t);for(let i of e.particles.updaters)i.particleDestroyed?.(this,t);i?.reset(this),this._engine.dispatchEvent(u.particleDestroyed,{container:this.container,data:{particle:this}})}draw(t){let e=this.container,i=e.canvas;for(let s of e.plugins.values())i.drawParticlePlugin(s,this,t);i.drawParticle(this,t)}getFillColor(){return this._getRollColor(this.bubble.color??t0(this.color))}getMass(){return this.getRadius()**2*Math.PI*.5}getPosition(){return{x:this.position.x+this.offset.x,y:this.position.y+this.offset.y,z:this.position.z}}getRadius(){return this.bubble.radius??this.size.value}getStrokeColor(){return this._getRollColor(this.bubble.color??t0(this.strokeColor))}init(t,e,i,s){let o=this.container,a=this._engine;this.id=t,this.group=s,this.effectClose=!0,this.effectFill=!0,this.shapeClose=!0,this.shapeFill=!0,this.pathRotation=!1,this.lastPathTime=0,this.destroyed=!1,this.unbreakable=!1,this.isRotating=!1,this.rotation=0,this.misplaced=!1,this.retina={maxDistance:{}},this.outType=w.normal,this.ignoresResizeRatio=!0;let n=o.retina.pixelRatio,r=o.actualOptions,l=e1(this._engine,o,r.particles),{reduceDuplicates:c}=l,h=l.effect.type,d=l.shape.type;this.effect=tA(h,this.id,c),this.shape=tA(d,this.id,c);let u=l.effect,p=l.shape;if(i){if(i.effect?.type){let t=tA(i.effect.type,this.id,c);t&&(this.effect=t,u.load(i.effect))}if(i.shape?.type){let t=tA(i.shape.type,this.id,c);t&&(this.shape=t,p.load(i.shape))}}if(this.effect===W){let t=[...this.container.effectDrawers.keys()];this.effect=t[Math.floor(Math.random()*t.length)]}if(this.shape===W){let t=[...this.container.shapeDrawers.keys()];this.shape=t[Math.floor(Math.random()*t.length)]}this.effectData=function(t,e,i,s){let o=e.options[t];if(o)return tT({close:e.close,fill:e.fill},tA(o,i,s))}(this.effect,u,this.id,c),this.shapeData=function(t,e,i,s){let o=e.options[t];if(o)return tT({close:e.close,fill:e.fill},tA(o,i,s))}(this.shape,p,this.id,c),l.load(i);let f=this.effectData;f&&l.load(f.particles);let y=this.shapeData;y&&l.load(y.particles);let v=new ep(a,o);v.load(o.actualOptions.interactivity),v.load(l.interactivity),this.interactivity=v,this.effectFill=f?.fill??l.effect.fill,this.effectClose=f?.close??l.effect.close,this.shapeFill=y?.fill??l.shape.fill,this.shapeClose=y?.close??l.shape.close,this.options=l;let m=this.options.move.path;this.pathDelay=1e3*th(m.delay.value),m.generator&&(this.pathGenerator=this._engine.getPathGenerator(m.generator),this.pathGenerator&&o.addPath(m.generator,this.pathGenerator)&&this.pathGenerator.init(o)),o.retina.initParticle(this),this.size=tV(this.options.size,n),this.bubble={inRange:!1},this.slow={inRange:!1,factor:1},this._initPosition(e),this.initialVelocity=this._calculateVelocity(),this.velocity=this.initialVelocity.copy(),this.moveDecay=1-th(this.options.move.decay);let g=o.particles;g.setLastZIndex(this.position.z),this.zIndexFactor=this.position.z/o.zLayers,this.sides=24;let b=o.effectDrawers.get(this.effect);!b&&(b=this._engine.getEffectDrawer(this.effect))&&o.effectDrawers.set(this.effect,b),b?.loadEffect&&b.loadEffect(this);let _=o.shapeDrawers.get(this.shape);!_&&(_=this._engine.getShapeDrawer(this.shape))&&o.shapeDrawers.set(this.shape,_),_?.loadShape&&_.loadShape(this);let x=_?.getSidesCount;for(let t of(x&&(this.sides=x(this)),this.spawning=!1,this.shadowColor=tW(this._engine,this.options.shadow.color),g.updaters))t.init(this);for(let t of g.movers)t.init?.(this);for(let t of(b?.particleInit?.(o,this),_?.particleInit?.(o,this),o.plugins.values()))t.particleCreated?.(this)}isInsideCanvas(){let t=this.getRadius(),e=this.container.canvas.size,i=this.position;return i.x>=-t&&i.y>=-t&&i.y<=e.height+t&&i.x<=e.width+t}isVisible(){return!this.destroyed&&!this.spawning&&this.isInsideCanvas()}reset(){for(let t of this.container.particles.updaters)t.reset?.(this)}}class e6{constructor(t,e){this.position=t,this.particle=e}}!function(t){t.circle="circle",t.rectangle="rectangle"}(_||(_={}));class e4{constructor(t,e,i){this.position={x:t,y:e},this.type=i}}class e9 extends e4{constructor(t,e,i){super(t,e,_.circle),this.radius=i}contains(t){return ty(t,this.position)<=this.radius}intersects(t){let e=this.position,i=t.position,s={x:Math.abs(i.x-e.x),y:Math.abs(i.y-e.y)},o=this.radius;if(t instanceof e9||t.type===_.circle)return o+t.radius>Math.sqrt(s.x**2+s.y**2);if(t instanceof e7||t.type===_.rectangle){let{width:e,height:i}=t.size;return Math.pow(s.x-e,2)+Math.pow(s.y-i,2)<=o**2||s.x<=o+e&&s.y<=o+i||s.x<=e||s.y<=i}return!1}}class e7 extends e4{constructor(t,e,i,s){super(t,e,_.rectangle),this.size={height:s,width:i}}contains(t){let e=this.size.width,i=this.size.height,s=this.position;return t.x>=s.x&&t.x<=s.x+e&&t.y>=s.y&&t.y<=s.y+i}intersects(t){if(t instanceof e9)return t.intersects(this);let e=this.size.width,i=this.size.height,s=this.position,o=t.position,a=t instanceof e7?t.size:{width:0,height:0},n=a.width,r=a.height;return o.x<s.x+e&&o.x+n>s.x&&o.y<s.y+i&&o.y+r>s.y}}class it{constructor(t,e){this.rectangle=t,this.capacity=e,this._subdivide=()=>{let{x:t,y:e}=this.rectangle.position,{width:i,height:s}=this.rectangle.size,{capacity:o}=this;for(let a=0;a<4;a++){let n=a%2;this._subs.push(new it(new e7(t+.5*i*n,e+.5*s*(Math.round(.5*a)-n),.5*i,.5*s),o))}this._divided=!0},this._points=[],this._divided=!1,this._subs=[]}insert(t){return!!this.rectangle.contains(t.position)&&(this._points.length<this.capacity?(this._points.push(t),!0):(this._divided||this._subdivide(),this._subs.some(e=>e.insert(t))))}query(t,e){let i=[];if(!t.intersects(this.rectangle))return[];for(let s of this._points)!t.contains(s.position)&&ty(t.position,s.position)>s.particle.getRadius()&&(!e||e(s.particle))||i.push(s.particle);if(this._divided)for(let s of this._subs)i.push(...s.query(t,e));return i}queryCircle(t,e,i){return this.query(new e9(t.x,t.y,e),i)}queryRectangle(t,e,i){return this.query(new e7(t.x,t.y,e.width,e.height),i)}}let ie=t=>{let{height:e,width:i}=t;return new e7(-.25*i,-.25*e,1.5*i,1.5*e)};class ii{constructor(t,e){this._addToPool=(...t)=>{this._pool.push(...t)},this._applyDensity=(t,e,i)=>{let s=t.number;if(!t.number.density?.enable){void 0===i?this._limit=s.limit.value:s.limit&&this._groupLimits.set(i,s.limit.value);return}let o=this._initDensityFactor(s.density),a=s.value,n=s.limit.value>0?s.limit.value:a,r=Math.min(a,n)*o+e,l=Math.min(this.count,this.filter(t=>t.group===i).length);void 0===i?this._limit=s.limit.value*o:this._groupLimits.set(i,s.limit.value*o),l<r?this.push(Math.abs(r-l),void 0,t,i):l>r&&this.removeQuantity(l-r,i)},this._initDensityFactor=t=>{let e=this._container;if(!e.canvas.element||!t.enable)return 1;let i=e.canvas.element,s=e.retina.pixelRatio;return i.width*i.height/(t.height*t.width*s**2)},this._pushParticle=(t,e,i,s)=>{try{let o=this._pool.pop();o||(o=new e8(this._engine,this._container)),o.init(this._nextId,t,e,i);let a=!0;if(s&&(a=s(o)),!a)return;return this._array.push(o),this._zArray.push(o),this._nextId++,this._engine.dispatchEvent(u.particleAdded,{container:this._container,data:{particle:o}}),o}catch(t){t_.warning(`${U} adding particle: ${t}`)}},this._removeParticle=(t,e,i)=>{let s=this._array[t];if(!s||s.group!==e)return!1;let o=this._zArray.indexOf(s);return this._array.splice(t,1),this._zArray.splice(o,1),s.destroy(i),this._engine.dispatchEvent(u.particleRemoved,{container:this._container,data:{particle:s}}),this._addToPool(s),!0},this._engine=t,this._container=e,this._nextId=0,this._array=[],this._zArray=[],this._pool=[],this._limit=0,this._groupLimits=new Map,this._needsSort=!1,this._lastZIndex=0,this._interactionManager=new e3(t,e),this._pluginsInitialized=!1;let i=e.canvas.size;this.quadTree=new it(ie(i),4),this.movers=[],this.updaters=[]}get count(){return this._array.length}addManualParticles(){let t=this._container;t.actualOptions.manualParticles.forEach(e=>this.addParticle(e.position?tU(e.position,t.canvas.size):void 0,e.options))}addParticle(t,e,i,s){let o=this._container.actualOptions.particles.number.limit.mode,a=void 0===i?this._limit:this._groupLimits.get(i)??this._limit,n=this.count;if(a>0)switch(o){case g.delete:{let t=n+1-a;t>0&&this.removeQuantity(t);break}case g.wait:if(n>=a)return}return this._pushParticle(t,e,i,s)}clear(){this._array=[],this._zArray=[],this._pluginsInitialized=!1}destroy(){this._array=[],this._zArray=[],this.movers=[],this.updaters=[]}draw(t){let e=this._container,i=e.canvas;for(let s of(i.clear(),this.update(t),e.plugins.values()))i.drawPlugin(s,t);for(let e of this._zArray)e.draw(t)}filter(t){return this._array.filter(t)}find(t){return this._array.find(t)}get(t){return this._array[t]}handleClickMode(t){this._interactionManager.handleClickMode(t)}async init(){let t=this._container,e=t.actualOptions;this._lastZIndex=0,this._needsSort=!1,await this.initPlugins();let i=!1;for(let e of t.plugins.values())if(i=e.particlesInitialization?.()??i)break;if(this.addManualParticles(),!i){let t=e.particles,i=t.groups;for(let e in i){let s=i[e];for(let i=this.count,o=0;o<s.number?.value&&i<t.number.value;i++,o++)this.addParticle(void 0,s,e)}for(let e=this.count;e<t.number.value;e++)this.addParticle()}}async initPlugins(){if(this._pluginsInitialized)return;let t=this._container;for(let e of(this.movers=await this._engine.getMovers(t,!0),this.updaters=await this._engine.getUpdaters(t,!0),await this._interactionManager.init(),t.pathGenerators.values()))e.init(t)}push(t,e,i,s){for(let o=0;o<t;o++)this.addParticle(e?.position,i,s)}async redraw(){this.clear(),await this.init(),this.draw({value:0,factor:0})}remove(t,e,i){this.removeAt(this._array.indexOf(t),void 0,e,i)}removeAt(t,e=1,i,s){if(t<0||t>this.count)return;let o=0;for(let a=t;o<e&&a<this.count;a++)this._removeParticle(a,i,s)&&(a--,o++)}removeQuantity(t,e){this.removeAt(0,t,e)}setDensity(){let t=this._container.actualOptions,e=t.particles.groups;for(let t in e)this._applyDensity(e[t],0,t);this._applyDensity(t.particles,t.manualParticles.length)}setLastZIndex(t){this._lastZIndex=t,this._needsSort=this._needsSort||this._lastZIndex<t}setResizeFactor(t){this._resizeFactor=t}update(t){let e=this._container,i=new Set;for(let t of(this.quadTree=new it(ie(e.canvas.size),4),e.pathGenerators.values()))t.update();for(let i of e.plugins.values())i.update?.(t);let s=this._resizeFactor;for(let e of this._array){for(let i of(s&&!e.ignoresResizeRatio&&(e.position.x*=s.width,e.position.y*=s.height,e.initialPosition.x*=s.width,e.initialPosition.y*=s.height),e.ignoresResizeRatio=!1,this._interactionManager.reset(e),this._container.plugins.values())){if(e.destroyed)break;i.particleUpdate?.(e,t)}for(let i of this.movers)i.isEnabled(e)&&i.move(e,t);if(e.destroyed){i.add(e);continue}this.quadTree.insert(new e6(e.getPosition(),e))}if(i.size){let t=t=>!i.has(t);for(let e of(this._array=this.filter(t),this._zArray=this._zArray.filter(t),i))this._engine.dispatchEvent(u.particleRemoved,{container:this._container,data:{particle:e}});this._addToPool(...i)}for(let e of(this._interactionManager.externalInteract(t),this._array)){for(let i of this.updaters)i.update(e,t);e.destroyed||e.spawning||this._interactionManager.particlesInteract(e,t)}if(delete this._resizeFactor,this._needsSort){let t=this._zArray;t.sort((t,e)=>e.position.z-t.position.z||t.id-e.id),this._lastZIndex=t[t.length-1].position.z,this._needsSort=!1}}}class is{constructor(t){this.container=t,this.pixelRatio=1,this.reduceFactor=1}init(){let t=this.container,e=t.actualOptions;this.pixelRatio=!e.detectRetina||tk()?1:window.devicePixelRatio,this.reduceFactor=1;let i=this.pixelRatio,s=t.canvas;if(s.element){let t=s.element;s.size.width=t.offsetWidth*i,s.size.height=t.offsetHeight*i}let o=e.particles,a=o.move;this.maxSpeed=th(a.gravity.maxSpeed)*i,this.sizeAnimationSpeed=th(o.size.animation.speed)*i}initParticle(t){let e=t.options,i=this.pixelRatio,s=e.move,o=s.distance,a=t.retina;a.moveDrift=th(s.drift)*i,a.moveSpeed=th(s.speed)*i,a.sizeAnimationSpeed=th(e.size.animation.speed)*i;let n=a.maxDistance;n.horizontal=void 0!==o.horizontal?o.horizontal*i:void 0,n.vertical=void 0!==o.vertical?o.vertical*i:void 0,a.maxSpeed=th(s.gravity.maxSpeed)*i}}function io(t){return t&&!t.destroyed}function ia(t,e,...i){let s=new e2(t,e);return e0(s,...i),s}class ir{constructor(t,e,i){this._intersectionManager=t=>{if(io(this)&&this.actualOptions.pauseOnOutsideViewport)for(let e of t)e.target===this.interactivity.element&&(e.isIntersecting?this.play():this.pause())},this._nextFrame=t=>{try{if(!this._smooth&&void 0!==this._lastFrameTime&&t<this._lastFrameTime+1e3/this.fpsLimit){this.draw(!1);return}this._lastFrameTime??=t;let e=function(t,e=60,i=!1){return{value:t,factor:i?60/e:60*t/1e3}}(t-this._lastFrameTime,this.fpsLimit,this._smooth);if(this.addLifeTime(e.value),this._lastFrameTime=t,e.value>1e3){this.draw(!1);return}if(this.particles.draw(e),!this.alive()){this.destroy();return}this.animationStatus&&this.draw(!1)}catch(t){t_.error(`${U} in animation loop`,t)}},this._engine=t,this.id=Symbol(e),this.fpsLimit=120,this._smooth=!1,this._delay=0,this._duration=0,this._lifeTime=0,this._firstStart=!0,this.started=!1,this.destroyed=!1,this._paused=!0,this._lastFrameTime=0,this.zLayers=100,this.pageHidden=!1,this._clickHandlers=new Map,this._sourceOptions=i,this._initialSourceOptions=i,this.retina=new is(this),this.canvas=new t9(this,this._engine),this.particles=new ii(this._engine,this),this.pathGenerators=new Map,this.interactivity={mouse:{clicking:!1,inside:!1}},this.plugins=new Map,this.effectDrawers=new Map,this.shapeDrawers=new Map,this._options=ia(this._engine,this),this.actualOptions=ia(this._engine,this),this._eventListeners=new et(this),this._intersectionObserver=function(t){if(!tk()&&"undefined"!=typeof IntersectionObserver)return new IntersectionObserver(t)}(t=>this._intersectionManager(t)),this._engine.dispatchEvent(u.containerBuilt,{container:this})}get animationStatus(){return!this._paused&&!this.pageHidden&&io(this)}get options(){return this._options}get sourceOptions(){return this._sourceOptions}addClickHandler(t){if(!io(this))return;let e=this.interactivity.element;if(!e)return;let i=(e,i,s)=>{if(!io(this))return;let o=this.retina.pixelRatio,a={x:i.x*o,y:i.y*o};t(e,this.particles.quadTree.queryCircle(a,s*o))},s=!1,o=!1;for(let[t,a]of(this._clickHandlers.set("click",t=>{if(!io(this))return;let e={x:t.offsetX||t.clientX,y:t.offsetY||t.clientY};i(t,e,1)}),this._clickHandlers.set("touchstart",()=>{io(this)&&(s=!0,o=!1)}),this._clickHandlers.set("touchmove",()=>{io(this)&&(o=!0)}),this._clickHandlers.set("touchend",t=>{if(io(this)){if(s&&!o){let e=t.touches[t.touches.length-1];if(!e&&!(e=t.changedTouches[t.changedTouches.length-1]))return;let s=this.canvas.element,o=s?s.getBoundingClientRect():void 0;i(t,{x:e.clientX-(o?o.left:0),y:e.clientY-(o?o.top:0)},Math.max(e.radiusX,e.radiusY))}s=!1,o=!1}}),this._clickHandlers.set("touchcancel",()=>{io(this)&&(s=!1,o=!1)}),this._clickHandlers))e.addEventListener(t,a)}addLifeTime(t){this._lifeTime+=t}addPath(t,e,i=!1){return!(!io(this)||!i&&this.pathGenerators.has(t))&&(this.pathGenerators.set(t,e),!0)}alive(){return!this._duration||this._lifeTime<=this._duration}clearClickHandlers(){if(io(this)){for(let[t,e]of this._clickHandlers)this.interactivity.element?.removeEventListener(t,e);this._clickHandlers.clear()}}destroy(t=!0){if(io(this)){for(let t of(this.stop(),this.clearClickHandlers(),this.particles.destroy(),this.canvas.destroy(),this.effectDrawers.values()))t.destroy?.(this);for(let t of this.shapeDrawers.values())t.destroy?.(this);for(let t of this.effectDrawers.keys())this.effectDrawers.delete(t);for(let t of this.shapeDrawers.keys())this.shapeDrawers.delete(t);if(this._engine.clearPlugins(this),this.destroyed=!0,t){let t=this._engine.items,e=t.findIndex(t=>t===this);e>=0&&t.splice(e,1)}this._engine.dispatchEvent(u.containerDestroyed,{container:this})}}draw(t){var e;if(!io(this))return;let i=t,s=t=>{i&&(this._lastFrameTime=void 0,i=!1),this._nextFrame(t)};this._drawAnimationFrame=(e=t=>s(t),ta.nextFrame(e))}async export(t,e={}){for(let i of this.plugins.values()){if(!i.export)continue;let s=await i.export(t,e);if(s.supported)return s.blob}t_.error(`${U} - Export plugin with type ${t} not found`)}handleClickMode(t){if(io(this))for(let e of(this.particles.handleClickMode(t),this.plugins.values()))e.handleClickMode?.(t)}async init(){if(!io(this))return;for(let t of this._engine.getSupportedEffects()){let e=this._engine.getEffectDrawer(t);e&&this.effectDrawers.set(t,e)}for(let t of this._engine.getSupportedShapes()){let e=this._engine.getShapeDrawer(t);e&&this.shapeDrawers.set(t,e)}for(let[t,e]of(await this.particles.initPlugins(),this._options=ia(this._engine,this,this._initialSourceOptions,this.sourceOptions),this.actualOptions=ia(this._engine,this,this._options),await this._engine.getAvailablePlugins(this)))this.plugins.set(t,e);this.retina.init(),await this.canvas.init(),this.updateActualOptions(),this.canvas.initBackground(),this.canvas.resize();let{zLayers:t,duration:e,delay:i,fpsLimit:s,smooth:o}=this.actualOptions;for(let a of(this.zLayers=t,this._duration=1e3*th(e),this._delay=1e3*th(i),this._lifeTime=0,this.fpsLimit=s>0?s:120,this._smooth=o,this.effectDrawers.values()))await a.init?.(this);for(let t of this.shapeDrawers.values())await t.init?.(this);for(let t of this.plugins.values())await t.init?.();for(let t of(this._engine.dispatchEvent(u.containerInit,{container:this}),await this.particles.init(),this.particles.setDensity(),this.plugins.values()))t.particlesSetup?.();this._engine.dispatchEvent(u.particlesSetup,{container:this})}async loadTheme(t){io(this)&&(this._currentTheme=t,await this.refresh())}pause(){if(io(this)){if(void 0!==this._drawAnimationFrame){var t;t=this._drawAnimationFrame,ta.cancel(t),delete this._drawAnimationFrame}if(!this._paused){for(let t of this.plugins.values())t.pause?.();this.pageHidden||(this._paused=!0),this._engine.dispatchEvent(u.containerPaused,{container:this})}}}play(t){if(!io(this))return;let e=this._paused||t;if(this._firstStart&&!this.actualOptions.autoPlay){this._firstStart=!1;return}if(this._paused&&(this._paused=!1),e)for(let t of this.plugins.values())t.play&&t.play();this._engine.dispatchEvent(u.containerPlay,{container:this}),this.draw(e??!1)}async refresh(){if(io(this))return this.stop(),this.start()}async reset(t){if(io(this))return this._initialSourceOptions=t,this._sourceOptions=t,this._options=ia(this._engine,this,this._initialSourceOptions,this.sourceOptions),this.actualOptions=ia(this._engine,this,this._options),this.refresh()}async start(){io(this)&&!this.started&&(await this.init(),this.started=!0,await new Promise(t=>{let e=async()=>{for(let t of(this._eventListeners.addListeners(),this.interactivity.element instanceof HTMLElement&&this._intersectionObserver&&this._intersectionObserver.observe(this.interactivity.element),this.plugins.values()))await t.start?.();this._engine.dispatchEvent(u.containerStarted,{container:this}),this.play(),t()};this._delayTimeout=setTimeout(()=>void e(),this._delay)}))}stop(){if(io(this)&&this.started){for(let t of(this._delayTimeout&&(clearTimeout(this._delayTimeout),delete this._delayTimeout),this._firstStart=!0,this.started=!1,this._eventListeners.removeListeners(),this.pause(),this.particles.clear(),this.canvas.stop(),this.interactivity.element instanceof HTMLElement&&this._intersectionObserver&&this._intersectionObserver.unobserve(this.interactivity.element),this.plugins.values()))t.stop?.();for(let t of this.plugins.keys())this.plugins.delete(t);this._sourceOptions=this._options,this._engine.dispatchEvent(u.containerStopped,{container:this})}}updateActualOptions(){this.actualOptions.responsive=[];let t=this.actualOptions.setResponsive(this.canvas.size.width,this.retina.pixelRatio,this._options);return this.actualOptions.setTheme(this._currentTheme),this._responsiveMaxWidth!==t&&(this._responsiveMaxWidth=t,!0)}}class il{constructor(){this._listeners=new Map}addEventListener(t,e){this.removeEventListener(t,e);let i=this._listeners.get(t);i||(i=[],this._listeners.set(t,i)),i.push(e)}dispatchEvent(t,e){let i=this._listeners.get(t);i?.forEach(t=>t(e))}hasEventListener(t){return!!this._listeners.get(t)}removeAllEventListeners(t){t?this._listeners.delete(t):this._listeners=new Map}removeEventListener(t,e){let i=this._listeners.get(t);if(!i)return;let s=i.length,o=i.indexOf(e);o<0||(1===s?this._listeners.delete(t):i.splice(o,1))}}async function ic(t,e,i,s=!1){let o=e.get(t);return(!o||s)&&(o=await Promise.all([...i.values()].map(e=>e(t))),e.set(t,o)),o}async function ih(t){let e=tA(t.url,t.index);if(!e)return t.fallback;let i=await fetch(e);return i.ok?await i.json():(t_.error(`${U} ${i.status} while retrieving config file`),t.fallback)}let id=t=>{let e;if(t instanceof HTMLCanvasElement||t.tagName.toLowerCase()===N)(e=t).dataset[B]||(e.dataset[B]=Q);else{let i=t.getElementsByTagName(N);i.length?(e=i[0]).dataset[B]=Q:((e=document.createElement(N)).dataset[B]=j,t.appendChild(e))}let i="100%";return e.style.width||(e.style.width=i),e.style.height||(e.style.height=i),e},iu=(t,e)=>{let i=e??document.getElementById(t);return i||((i=document.createElement("div")).id=t,i.dataset[B]=j,document.body.append(i)),i};class ip{constructor(){this._configs=new Map,this._domArray=[],this._eventDispatcher=new il,this._initialized=!1,this.plugins=[],this.colorManagers=new Map,this.easingFunctions=new Map,this._initializers={interactors:new Map,movers:new Map,updaters:new Map},this.interactors=new Map,this.movers=new Map,this.updaters=new Map,this.presets=new Map,this.effectDrawers=new Map,this.shapeDrawers=new Map,this.pathGenerators=new Map}get configs(){let t={};for(let[e,i]of this._configs)t[e]=i;return t}get items(){return this._domArray}get version(){return"3.8.1"}async addColorManager(t,e=!0){this.colorManagers.set(t.key,t),await this.refresh(e)}addConfig(t){let e=t.key??t.name??"default";this._configs.set(e,t),this._eventDispatcher.dispatchEvent(u.configAdded,{data:{name:e,config:t}})}async addEasing(t,e,i=!0){this.getEasing(t)||(this.easingFunctions.set(t,e),await this.refresh(i))}async addEffect(t,e,i=!0){tB(t,t=>{this.getEffectDrawer(t)||this.effectDrawers.set(t,e)}),await this.refresh(i)}addEventListener(t,e){this._eventDispatcher.addEventListener(t,e)}async addInteractor(t,e,i=!0){this._initializers.interactors.set(t,e),await this.refresh(i)}async addMover(t,e,i=!0){this._initializers.movers.set(t,e),await this.refresh(i)}async addParticleUpdater(t,e,i=!0){this._initializers.updaters.set(t,e),await this.refresh(i)}async addPathGenerator(t,e,i=!0){this.getPathGenerator(t)||this.pathGenerators.set(t,e),await this.refresh(i)}async addPlugin(t,e=!0){this.getPlugin(t.id)||this.plugins.push(t),await this.refresh(e)}async addPreset(t,e,i=!1,s=!0){(i||!this.getPreset(t))&&this.presets.set(t,e),await this.refresh(s)}async addShape(t,e=!0){for(let e of t.validTypes)this.getShapeDrawer(e)||this.shapeDrawers.set(e,t);await this.refresh(e)}checkVersion(t){if(this.version!==t)throw Error(`The tsParticles version is different from the loaded plugins version. Engine version: ${this.version}. Plugin version: ${t}`)}clearPlugins(t){this.updaters.delete(t),this.movers.delete(t),this.interactors.delete(t)}dispatchEvent(t,e){this._eventDispatcher.dispatchEvent(t,e)}dom(){return this.items}domItem(t){return this.item(t)}async getAvailablePlugins(t){let e=new Map;for(let i of this.plugins)i.needsPlugin(t.actualOptions)&&e.set(i.id,await i.getPlugin(t));return e}getEasing(t){return this.easingFunctions.get(t)??(t=>t)}getEffectDrawer(t){return this.effectDrawers.get(t)}async getInteractors(t,e=!1){return ic(t,this.interactors,this._initializers.interactors,e)}async getMovers(t,e=!1){return ic(t,this.movers,this._initializers.movers,e)}getPathGenerator(t){return this.pathGenerators.get(t)}getPlugin(t){return this.plugins.find(e=>e.id===t)}getPreset(t){return this.presets.get(t)}getShapeDrawer(t){return this.shapeDrawers.get(t)}getSupportedEffects(){return this.effectDrawers.keys()}getSupportedShapes(){return this.shapeDrawers.keys()}async getUpdaters(t,e=!1){return ic(t,this.updaters,this._initializers.updaters,e)}init(){this._initialized||(this._initialized=!0)}item(t){let{items:e}=this,i=e[t];if(!i||i.destroyed){e.splice(t,1);return}return i}async load(t){let e=t.id??t.element?.id??`tsparticles${Math.floor(1e4*tn())}`,{index:i,url:s}=t,o=tA(s?await ih({fallback:t.options,url:s,index:i}):t.options,i),{items:a}=this,n=a.findIndex(t=>t.id.description===e),r=new ir(this,e,o);if(n>=0){let t=this.item(n),e=t?1:0;t&&!t.destroyed&&t.destroy(!1),a.splice(n,e,r)}else a.push(r);let l=id(iu(e,t.element));return r.canvas.loadCanvas(l),await r.start(),r}loadOptions(t,e){this.plugins.forEach(i=>i.loadOptions?.(t,e))}loadParticlesOptions(t,e,...i){let s=this.updaters.get(t);s&&s.forEach(t=>t.loadOptions?.(e,...i))}async refresh(t=!0){t&&await Promise.all(this.items.map(t=>t.refresh()))}removeEventListener(t,e){this._eventDispatcher.removeEventListener(t,e)}setOnClickHandler(t){let{items:e}=this;if(!e.length)throw Error(`${U} can only set click handlers after calling tsParticles.load()`);e.forEach(e=>e.addClickHandler(t))}}class iy{constructor(t){this.type=b.external,this.container=t}}class iv{constructor(t){this.type=b.particles,this.container=t}}(function(t){t.clockwise="clockwise",t.counterClockwise="counter-clockwise",t.random="random"})(x||(x={})),function(t){t.linear="linear",t.radial="radial",t.random="random"}(k||(k={})),function(t){t.easeInBack="ease-in-back",t.easeInCirc="ease-in-circ",t.easeInCubic="ease-in-cubic",t.easeInLinear="ease-in-linear",t.easeInQuad="ease-in-quad",t.easeInQuart="ease-in-quart",t.easeInQuint="ease-in-quint",t.easeInExpo="ease-in-expo",t.easeInSine="ease-in-sine",t.easeOutBack="ease-out-back",t.easeOutCirc="ease-out-circ",t.easeOutCubic="ease-out-cubic",t.easeOutLinear="ease-out-linear",t.easeOutQuad="ease-out-quad",t.easeOutQuart="ease-out-quart",t.easeOutQuint="ease-out-quint",t.easeOutExpo="ease-out-expo",t.easeOutSine="ease-out-sine",t.easeInOutBack="ease-in-out-back",t.easeInOutCirc="ease-in-out-circ",t.easeInOutCubic="ease-in-out-cubic",t.easeInOutLinear="ease-in-out-linear",t.easeInOutQuad="ease-in-out-quad",t.easeInOutQuart="ease-in-out-quart",t.easeInOutQuint="ease-in-out-quint",t.easeInOutExpo="ease-in-out-expo",t.easeInOutSine="ease-in-out-sine"}(z||(z={}));let im=function(){let t=new ip;return t.init(),t}();tk()||(window.tsParticles=im);class ig{constructor(){this.radius=0,this.mass=0}load(t){te(t)||(void 0!==t.mass&&(this.mass=t.mass),void 0===t.radius||(this.radius=t.radius))}}class ib extends eP{constructor(){super(),this.density=5,this.value=50,this.limit=new ig}load(t){te(t)||(super.load(t),void 0!==t.density&&(this.density=t.density),K(t.limit)?this.limit.radius=t.limit:this.limit.load(t.limit))}}class iw{constructor(){this.color=new ee,this.color.value="#000000",this.draggable=!1,this.opacity=1,this.destroy=!0,this.orbits=!1,this.size=new ib}load(t){te(t)||(void 0!==t.color&&(this.color=ee.create(this.color,t.color)),void 0!==t.draggable&&(this.draggable=t.draggable),this.name=t.name,void 0!==t.opacity&&(this.opacity=t.opacity),void 0!==t.position&&(this.position={},void 0!==t.position.x&&(this.position.x=tp(t.position.x)),void 0!==t.position.y&&(this.position.y=tp(t.position.y))),void 0!==t.size&&this.size.load(t.size),void 0!==t.destroy&&(this.destroy=t.destroy),void 0===t.orbits||(this.orbits=t.orbits))}}(P||(P={})).absorber="absorber";let i_=2*Math.PI;class ix{constructor(t,e,i,s,o){this._calcPosition=()=>{let t=tb({size:this._container.canvas.size,position:this.options.position});return ts.create(t.x,t.y)},this._updateParticlePosition=(t,e)=>{if(t.destroyed)return;let i=this._container,s=i.canvas.size;if(t.needsNewPosition){let e=tg({size:s});t.position.setTo(e),t.velocity.setTo(t.initialVelocity),t.absorberOrbit=void 0,t.needsNewPosition=!1}if(this.options.orbits){if(void 0===t.absorberOrbit&&(t.absorberOrbit=ts.origin,t.absorberOrbit.length=ty(t.getPosition(),this.position),t.absorberOrbit.angle=tn()*i_),t.absorberOrbit.length<=this.size&&!this.options.destroy){let e=Math.min(s.width,s.height);t.absorberOrbit.length=e*(1+(.2*tn()-.1))}void 0===t.absorberOrbitDirection&&(t.absorberOrbitDirection=t.velocity.x>=0?x.clockwise:x.counterClockwise);let o=t.absorberOrbit.length,a=t.absorberOrbit.angle,n=t.absorberOrbitDirection;t.velocity.setTo(ts.origin);let r={x:n===x.clockwise?Math.cos:Math.sin,y:n===x.clockwise?Math.sin:Math.cos};t.position.x=this.position.x+o*r.x(a),t.position.y=this.position.y+o*r.y(a),t.absorberOrbit.length-=e.length,t.absorberOrbit.angle+=(t.retina.moveSpeed??0)*i.retina.pixelRatio/100*i.retina.reduceFactor}else{let i=ts.origin;i.length=e.length,i.angle=e.angle,t.velocity.addTo(i)}},this._absorbers=t,this._container=e,this._engine=i,this.initialPosition=o?ts.create(o.x,o.y):void 0,s instanceof iw?this.options=s:(this.options=new iw,this.options.load(s)),this.dragging=!1,this.name=this.options.name,this.opacity=this.options.opacity,this.size=th(this.options.size.value)*e.retina.pixelRatio,this.mass=this.size*this.options.size.density*e.retina.reduceFactor;let a=this.options.size.limit;this.limit={radius:a.radius*e.retina.pixelRatio*e.retina.reduceFactor,mass:a.mass},this.color=tW(this._engine,this.options.color)??{b:0,g:0,r:0},this.position=this.initialPosition?.copy()??this._calcPosition()}attract(t){let e=this._container,i=this.options;if(i.draggable){let t=e.interactivity.mouse;t.clicking&&t.downPosition?ty(this.position,t.downPosition)<=this.size&&(this.dragging=!0):this.dragging=!1,this.dragging&&t.position&&(this.position.x=t.position.x,this.position.y=t.position.y)}let s=t.getPosition(),{dx:o,dy:a,distance:n}=tf(this.position,s),r=ts.create(o,a);if(r.length=this.mass/Math.pow(n,2)*e.retina.reduceFactor,n<this.size+t.getRadius()){let s=.033*t.getRadius()*e.retina.pixelRatio;this.size>t.getRadius()&&n<this.size-t.getRadius()||void 0!==t.absorberOrbit&&t.absorberOrbit.length<0?i.destroy?t.destroy():(t.needsNewPosition=!0,this._updateParticlePosition(t,r)):(i.destroy&&(t.size.value-=s),this._updateParticlePosition(t,r)),(this.limit.radius<=0||this.size<this.limit.radius)&&(this.size+=s),(this.limit.mass<=0||this.mass<this.limit.mass)&&(this.mass+=s*this.options.size.density*e.retina.reduceFactor)}else this._updateParticlePosition(t,r)}draw(t){t.translate(this.position.x,this.position.y),t.beginPath(),t.arc(H.x,H.y,this.size,0,i_,!1),t.closePath(),t.fillStyle=tX(this.color,this.opacity),t.fill()}resize(){let t=this.initialPosition;this.position=t&&tS(t,this._container.canvas.size,ts.origin)?t:this._calcPosition()}}class ik{constructor(t,e){this._container=t,this._engine=e,this.array=[],this.absorbers=[],this.interactivityAbsorbers=[],t.getAbsorber=t=>void 0===t||K(t)?this.array[t??0]:this.array.find(e=>e.name===t),t.addAbsorber=async(t,e)=>this.addAbsorber(t,e)}async addAbsorber(t,e){let i=new ix(this,this._container,this._engine,t,e);return this.array.push(i),Promise.resolve(i)}draw(t){for(let e of this.array)e.draw(t)}handleClickMode(t){let e=this.absorbers,i=this.interactivityAbsorbers;if(t===P.absorber){let t=tA(i)??tA(e),s=this._container.interactivity.mouse.clickPosition;this.addAbsorber(t,s)}}async init(){this.absorbers=this._container.actualOptions.absorbers,this.interactivityAbsorbers=this._container.actualOptions.interactivity.modes.absorbers;let t=tB(this.absorbers,async t=>{await this.addAbsorber(t)});t instanceof Array?await Promise.all(t):await t}particleUpdate(t){for(let e of this.array)if(e.attract(t),t.destroyed)break}removeAbsorber(t){let e=this.array.indexOf(t);e>=0&&this.array.splice(e,1)}resize(){for(let t of this.array)t.resize()}stop(){this.array=[]}}class iz{constructor(t){this.id="absorbers",this._engine=t}async getPlugin(t){return Promise.resolve(new ik(t,this._engine))}loadOptions(t,e){(this.needsPlugin(t)||this.needsPlugin(e))&&(e?.absorbers&&(t.absorbers=tB(e.absorbers,t=>{let e=new iw;return e.load(t),e})),t.interactivity.modes.absorbers=tB(e?.interactivity?.modes?.absorbers,t=>{let e=new iw;return e.load(t),e}))}needsPlugin(t){if(!t)return!1;let e=t.absorbers;return tt(e)?!!e.length:!!(e||t.interactivity?.events?.onClick?.mode&&tP(P.absorber,t.interactivity.events.onClick.mode))}}async function iP(t,e=!0){t.checkVersion("3.8.1"),await t.addPlugin(new iz(t),e)}class iC{load(t){te(t)||(void 0!==t.bottom&&(this.bottom=tp(t.bottom)),void 0!==t.left&&(this.left=tp(t.left)),void 0!==t.right&&(this.right=tp(t.right)),void 0!==t.top&&(this.top=tp(t.top)))}}!function(t){t.none="none",t.split="split"}(C||(C={}));class iM extends eP{constructor(){super(),this.value=3}}class iO extends eP{constructor(){super(),this.value={min:4,max:9}}}class iS{constructor(){this.count=1,this.factor=new iM,this.rate=new iO,this.sizeOffset=!0}load(t){!te(t)&&(void 0!==t.color&&(this.color=ee.create(this.color,t.color)),void 0!==t.count&&(this.count=t.count),this.factor.load(t.factor),this.rate.load(t.rate),this.particles=tB(t.particles,t=>tT({},t)),void 0!==t.sizeOffset&&(this.sizeOffset=t.sizeOffset),t.colorOffset&&(this.colorOffset=this.colorOffset??{},void 0!==t.colorOffset.h&&(this.colorOffset.h=t.colorOffset.h),void 0!==t.colorOffset.s&&(this.colorOffset.s=t.colorOffset.s),void 0!==t.colorOffset.l&&(this.colorOffset.l=t.colorOffset.l)))}}class iD{constructor(){this.bounds=new iC,this.mode=C.none,this.split=new iS}load(t){te(t)||(t.mode&&(this.mode=t.mode),t.bounds&&this.bounds.load(t.bounds),this.split.load(t.split))}}class iT{constructor(t,e){this.container=e,this.engine=t}init(t){let e=this.container,i=t.options.destroy;if(!i)return;t.splitCount=0;let s=i.bounds;t.destroyBounds||(t.destroyBounds={});let{bottom:o,left:a,right:n,top:r}=s,{destroyBounds:l}=t,c=e.canvas.size;o&&(l.bottom=th(o)*c.height/100),a&&(l.left=th(a)*c.width/100),n&&(l.right=th(n)*c.width/100),r&&(l.top=th(r)*c.height/100)}isEnabled(t){return!t.destroyed}loadOptions(t,...e){for(let i of(t.destroy||(t.destroy=new iD),e))t.destroy.load(i?.destroy)}particleDestroyed(t,e){if(e)return;let i=t.options.destroy;i&&i.mode===C.split&&function(t,e,i){let s=i.options.destroy;if(!s)return;let o=s.split;if(o.count>=0&&(void 0===i.splitCount||i.splitCount++>o.count))return;let a=th(o.rate.value),n=tA(o.particles);for(let s=0;s<a;s++)!function(t,e,i,s){let o=i.options.destroy;if(!o)return;let a=o.split,n=e1(t,e,i.options),r=th(a.factor.value),c=i.getFillColor();a.color?n.color.load(a.color):a.colorOffset&&c?n.color.load({value:{hsl:{h:c.h+th(a.colorOffset.h??0),s:c.s+th(a.colorOffset.s??0),l:c.l+th(a.colorOffset.l??0)}}}):n.color.load({value:{hsl:i.getFillColor()}}),n.move.load({center:{x:i.position.x,y:i.position.y,mode:l.precise}}),K(n.size.value)?n.size.value/=r:(n.size.value.min/=r,n.size.value.max/=r),n.load(s);let h=a.sizeOffset?tp(-i.size.value,i.size.value):0,d={x:i.position.x+tc(h),y:i.position.y+tc(h)};e.particles.addParticle(d,n,i.group,t=>!(t.size.value<.5)&&(t.velocity.length=tc(tp(i.velocity.length,t.velocity.length)),t.splitCount=(i.splitCount??0)+1,t.unbreakable=!0,setTimeout(()=>{t.unbreakable=!1},500),!0))}(t,e,i,n)}(this.engine,this.container,t)}update(t){if(!this.isEnabled(t))return;let e=t.getPosition(),i=t.destroyBounds;i&&(void 0!==i.bottom&&e.y>=i.bottom||void 0!==i.left&&e.x<=i.left||void 0!==i.right&&e.x>=i.right||void 0!==i.top&&e.y<=i.top)&&t.destroy()}}async function iE(t,e=!0){t.checkVersion("3.8.1"),await t.addParticleUpdater("destroy",e=>Promise.resolve(new iT(t,e)),e)}class iR{constructor(){this.wait=!1}load(t){te(t)||(void 0!==t.count&&(this.count=t.count),void 0!==t.delay&&(this.delay=tp(t.delay)),void 0!==t.duration&&(this.duration=tp(t.duration)),void 0===t.wait||(this.wait=t.wait))}}class iI{constructor(){this.quantity=1,this.delay=.1}load(t){te(t)||(void 0!==t.quantity&&(this.quantity=tp(t.quantity)),void 0!==t.delay&&(this.delay=tp(t.delay)))}}class iL{constructor(){this.color=!1,this.opacity=!1}load(t){te(t)||(void 0!==t.color&&(this.color=t.color),void 0===t.opacity||(this.opacity=t.opacity))}}class iF{constructor(){this.options={},this.replace=new iL,this.type="square"}load(t){te(t)||(void 0!==t.options&&(this.options=tT({},t.options??{})),this.replace.load(t.replace),void 0===t.type||(this.type=t.type))}}class iB{constructor(){this.mode=l.percent,this.height=0,this.width=0}load(t){te(t)||(void 0!==t.mode&&(this.mode=t.mode),void 0!==t.height&&(this.height=t.height),void 0===t.width||(this.width=t.width))}}class iA{constructor(){this.autoPlay=!0,this.fill=!0,this.life=new iR,this.rate=new iI,this.shape=new iF,this.startCount=0}load(t){te(t)||(void 0!==t.autoPlay&&(this.autoPlay=t.autoPlay),void 0!==t.size&&(this.size||(this.size=new iB),this.size.load(t.size)),void 0!==t.direction&&(this.direction=t.direction),this.domId=t.domId,void 0!==t.fill&&(this.fill=t.fill),this.life.load(t.life),this.name=t.name,this.particles=tB(t.particles,t=>tT({},t)),this.rate.load(t.rate),this.shape.load(t.shape),void 0!==t.position&&(this.position={},void 0!==t.position.x&&(this.position.x=tp(t.position.x)),void 0!==t.position.y&&(this.position.y=tp(t.position.y))),void 0!==t.spawnColor&&(void 0===this.spawnColor&&(this.spawnColor=new ex),this.spawnColor.load(t.spawnColor)),void 0===t.startCount||(this.startCount=t.startCount))}}(M||(M={})).emitter="emitter";let iq=1;function iV(t,e){t.color?t.color.value=e:t.color={value:e}}class iU{constructor(t,e,i,s,o){this.emitters=e,this.container=i,this._destroy=()=>{this._mutationObserver?.disconnect(),this._mutationObserver=void 0,this._resizeObserver?.disconnect(),this._resizeObserver=void 0,this.emitters.removeEmitter(this),this._engine.dispatchEvent("emitterDestroyed",{container:this.container,data:{emitter:this}})},this._prepareToDie=()=>{if(this._paused)return;let t=this.options.life?.duration!==void 0?th(this.options.life.duration):void 0;this.container.retina.reduceFactor&&(this._lifeCount>0||this._immortal)&&void 0!==t&&t>0&&(this._duration=1e3*t)},this._setColorAnimation=(t,e,i,s=iq)=>{let o=this.container;if(!t.enable)return e;let a=tc(t.offset),n=1e3*th(this.options.rate.delay)/o.retina.reduceFactor;return(e+th(t.speed??0)*o.fpsLimit/n+a*s)%i},this._engine=t,this._currentDuration=0,this._currentEmitDelay=0,this._currentSpawnDelay=0,this._initialPosition=o,s instanceof iA?this.options=s:(this.options=new iA,this.options.load(s)),this._spawnDelay=1e3*th(this.options.life.delay??0)/this.container.retina.reduceFactor,this.position=this._initialPosition??this._calcPosition(),this.name=this.options.name,this.fill=this.options.fill,this._firstSpawn=!this.options.life.wait,this._startParticlesAdded=!1;let a=tT({},this.options.particles);if(a??={},a.move??={},a.move.direction??=this.options.direction,this.options.spawnColor&&(this.spawnColor=tG(this._engine,this.options.spawnColor)),this._paused=!this.options.autoPlay,this._particlesOptions=a,this._size=this._calcSize(),this.size=tU(this._size,this.container.canvas.size),this._lifeCount=this.options.life.count??-1,this._immortal=this._lifeCount<=0,this.options.domId){let t=document.getElementById(this.options.domId);t&&(this._mutationObserver=new MutationObserver(()=>{this.resize()}),this._resizeObserver=new ResizeObserver(()=>{this.resize()}),this._mutationObserver.observe(t,{attributes:!0,attributeFilter:["style","width","height"]}),this._resizeObserver.observe(t))}let n=this.options.shape,r=this._engine.emitterShapeManager?.getShapeGenerator(n.type);r&&(this._shape=r.generate(this.position,this.size,this.fill,n.options)),this._engine.dispatchEvent("emitterCreated",{container:i,data:{emitter:this}}),this.play()}externalPause(){this._paused=!0,this.pause()}externalPlay(){this._paused=!1,this.play()}async init(){await this._shape?.init()}pause(){this._paused||delete this._emitDelay}play(){if(!this._paused&&this.container.retina.reduceFactor&&(this._lifeCount>0||this._immortal||!this.options.life.count)&&(this._firstSpawn||this._currentSpawnDelay>=(this._spawnDelay??0))){if(void 0===this._emitDelay){let t=th(this.options.rate.delay);this._emitDelay=1e3*t/this.container.retina.reduceFactor}(this._lifeCount>0||this._immortal)&&this._prepareToDie()}}resize(){let t=this._initialPosition;this.position=t&&tS(t,this.container.canvas.size,ts.origin)?t:this._calcPosition(),this._size=this._calcSize(),this.size=tU(this._size,this.container.canvas.size),this._shape?.resize(this.position,this.size)}update(t){!this._paused&&(this._firstSpawn&&(this._firstSpawn=!1,this._currentSpawnDelay=this._spawnDelay??0,this._currentEmitDelay=this._emitDelay??0),this._startParticlesAdded||(this._startParticlesAdded=!0,this._emitParticles(this.options.startCount)),void 0!==this._duration&&(this._currentDuration+=t.value,this._currentDuration>=this._duration&&(this.pause(),void 0!==this._spawnDelay&&delete this._spawnDelay,!this._immortal&&this._lifeCount--,this._lifeCount>0||this._immortal?(this.position=this._calcPosition(),this._shape?.resize(this.position,this.size),this._spawnDelay=1e3*th(this.options.life.delay??0)/this.container.retina.reduceFactor):this._destroy(),this._currentDuration-=this._duration,delete this._duration)),void 0!==this._spawnDelay&&(this._currentSpawnDelay+=t.value,this._currentSpawnDelay>=this._spawnDelay&&(this._engine.dispatchEvent("emitterPlay",{container:this.container}),this.play(),this._currentSpawnDelay-=this._currentSpawnDelay,delete this._spawnDelay)),void 0!==this._emitDelay&&(this._currentEmitDelay+=t.value,this._currentEmitDelay>=this._emitDelay&&(this._emit(),this._currentEmitDelay-=this._emitDelay)))}_calcPosition(){if(this.options.domId){let t=document.getElementById(this.options.domId);if(t){let e=t.getBoundingClientRect(),i=this.container.retina.pixelRatio;return{x:(e.x+.5*e.width)*i,y:(e.y+.5*e.height)*i}}}return tb({size:this.container.canvas.size,position:this.options.position})}_calcSize(){let t=this.container;if(this.options.domId){let e=document.getElementById(this.options.domId);if(e){let i=e.getBoundingClientRect();return{width:i.width*t.retina.pixelRatio,height:i.height*t.retina.pixelRatio,mode:l.precise}}}return this.options.size??(()=>{let t=new iB;return t.load({height:0,mode:l.percent,width:0}),t})()}_emit(){if(this._paused)return;let t=th(this.options.rate.quantity);this._emitParticles(t)}_emitParticles(t){let e=tA(this._particlesOptions);for(let i=0;i<t;i++){let t=tT({},e);if(this.spawnColor){let e=this.options.spawnColor?.animation;if(e){let t={h:360,s:100,l:100};this.spawnColor.h=this._setColorAnimation(e.h,this.spawnColor.h,t.h,3.6),this.spawnColor.s=this._setColorAnimation(e.s,this.spawnColor.s,t.s),this.spawnColor.l=this._setColorAnimation(e.l,this.spawnColor.l,t.l)}iV(t,this.spawnColor)}let i=this.options.shape,s=this.position;if(this._shape){let e=this._shape.randomPosition();if(e){s=e.position;let o=i.replace;o.color&&e.color&&iV(t,e.color),o.opacity&&(t.opacity?t.opacity.value=e.opacity:t.opacity={value:e.opacity})}else s=null}s&&this.container.particles.addParticle(s,t)}}}class iH{constructor(t,e){this.container=e,this._engine=t,this.array=[],this.emitters=[],this.interactivityEmitters={random:{count:1,enable:!1},value:[]},e.getEmitter=t=>void 0===t||K(t)?this.array[t??0]:this.array.find(e=>e.name===t),e.addEmitter=async(t,e)=>this.addEmitter(t,e),e.removeEmitter=t=>{let i=e.getEmitter(t);i&&this.removeEmitter(i)},e.playEmitter=t=>{let i=e.getEmitter(t);i&&i.externalPlay()},e.pauseEmitter=t=>{let i=e.getEmitter(t);i&&i.externalPause()}}async addEmitter(t,e){let i=new iA;i.load(t);let s=new iU(this._engine,this,this.container,i,e);return await s.init(),this.array.push(s),s}handleClickMode(t){let e;let i=this.emitters,s=this.interactivityEmitters;if(t!==M.emitter)return;if(s&&tt(s.value)){if(s.value.length>0&&s.random.enable){e=[];let t=[];for(let i=0;i<s.random.count;i++){let o=tM(s.value);if(t.includes(o)&&t.length<s.value.length){i--;continue}t.push(o),e.push(tO(s.value,o))}}else e=s.value}else e=s?.value;let o=e??i,a=this.container.interactivity.mouse.clickPosition;tB(o,async t=>{await this.addEmitter(t,a)})}async init(){if(this.emitters=this.container.actualOptions.emitters,this.interactivityEmitters=this.container.actualOptions.interactivity.modes.emitters,this.emitters){if(tt(this.emitters))for(let t of this.emitters)await this.addEmitter(t);else await this.addEmitter(this.emitters)}}pause(){for(let t of this.array)t.pause()}play(){for(let t of this.array)t.play()}removeEmitter(t){let e=this.array.indexOf(t);e>=0&&this.array.splice(e,1)}resize(){for(let t of this.array)t.resize()}stop(){this.array=[]}update(t){for(let e of this.array)e.update(t)}}class i${constructor(t){this._engine=t,this.id="emitters"}getPlugin(t){return Promise.resolve(new iH(this._engine,t))}loadOptions(t,e){if(!this.needsPlugin(t)&&!this.needsPlugin(e))return;e?.emitters&&(t.emitters=tB(e.emitters,t=>{let e=new iA;return e.load(t),e}));let i=e?.interactivity?.modes?.emitters;if(i){if(tt(i))t.interactivity.modes.emitters={random:{count:1,enable:!0},value:i.map(t=>{let e=new iA;return e.load(t),e})};else if(void 0!==i.value){if(tt(i.value))t.interactivity.modes.emitters={random:{count:i.random.count??1,enable:i.random.enable??!1},value:i.value.map(t=>{let e=new iA;return e.load(t),e})};else{let e=new iA;e.load(i.value),t.interactivity.modes.emitters={random:{count:i.random.count??1,enable:i.random.enable??!1},value:e}}}else(t.interactivity.modes.emitters={random:{count:1,enable:!1},value:new iA}).value.load(i)}}needsPlugin(t){if(!t)return!1;let e=t.emitters;return tt(e)&&!!e.length||void 0!==e||!!t.interactivity?.events?.onClick?.mode&&tP(M.emitter,t.interactivity.events.onClick.mode)}}let iW=new Map;class iG{constructor(t){this._engine=t}addShapeGenerator(t,e){this.getShapeGenerator(t)||iW.set(t,e)}getShapeGenerator(t){return iW.get(t)}getSupportedShapeGenerators(){return iW.keys()}}class ij{constructor(t,e,i,s){this.position=t,this.size=e,this.fill=i,this.options=s}resize(t,e){this.position=t,this.size=e}}async function iQ(t,e=!0){t.checkVersion("3.8.1"),t.emitterShapeManager||(t.emitterShapeManager=new iG(t)),t.addEmitterShapeGenerator||(t.addEmitterShapeGenerator=(e,i)=>{t.emitterShapeManager?.addShapeGenerator(e,i)});let i=new i$(t);await t.addPlugin(i,e)}let iN=2*Math.PI;class iX extends ij{constructor(t,e,i,s){super(t,e,i,s)}async init(){}randomPosition(){let t=this.size,e=this.fill,i=this.position,[s,o]=[.5*t.width,.5*t.height],a=((t,e)=>{let i=Math.atan(e/t*Math.tan(iN*(.25*tn()))),s=tn();return s<.25?i:s<.5?Math.PI-i:s<.75?Math.PI+i:-i})(s,o),n=s*o/Math.sqrt((o*Math.cos(a))**2+(s*Math.sin(a))**2),r=e?n*Math.sqrt(tn()):n;return{position:{x:i.x+r*Math.cos(a),y:i.y+r*Math.sin(a)}}}}class iY{generate(t,e,i,s){return new iX(t,e,i,s)}}async function iZ(t,e=!0){t.checkVersion("3.8.1"),t.addEmitterShapeGenerator?.("circle",new iY),await t.refresh(e)}!function(t){t[t.TopLeft=0]="TopLeft",t[t.TopRight=1]="TopRight",t[t.BottomRight=2]="BottomRight",t[t.BottomLeft=3]="BottomLeft"}(O||(O={}));class iK extends ij{constructor(t,e,i,s){super(t,e,i,s)}async init(){}randomPosition(){let t=this.fill,e=this.position,i=this.size;if(t)return{position:{x:e.x+i.width*(tn()-.5),y:e.y+i.height*(tn()-.5)}};{let t=.5*i.width,s=.5*i.height,o=Math.floor(4*tn()),a=(tn()-.5)*2;switch(o){case O.TopLeft:return{position:{x:e.x+a*t,y:e.y-s}};case O.TopRight:return{position:{x:e.x-t,y:e.y+a*s}};case O.BottomRight:return{position:{x:e.x+a*t,y:e.y+s}};case O.BottomLeft:default:return{position:{x:e.x+t,y:e.y+a*s}}}}}}class iJ{generate(t,e,i,s){return new iK(t,e,i,s)}}async function i0(t,e=!0){t.checkVersion("3.8.1"),t.addEmitterShapeGenerator?.("square",new iJ),await t.refresh(e)}class i1{constructor(){this.delay=1,this.pauseOnStop=!1,this.quantity=1}load(t){te(t)||(void 0!==t.delay&&(this.delay=t.delay),void 0!==t.quantity&&(this.quantity=t.quantity),void 0!==t.particles&&(this.particles=tT({},t.particles)),void 0===t.pauseOnStop||(this.pauseOnStop=t.pauseOnStop))}}let i2="trail";class i3 extends iy{constructor(t){super(t),this._delay=0}clear(){}init(){}interact(t){let e=this.container,{interactivity:i}=e;if(!e.retina.reduceFactor)return;let s=e.actualOptions.interactivity.modes.trail;if(!s)return;let o=1e3*s.delay/this.container.retina.reduceFactor;if(this._delay<o&&(this._delay+=t.value),this._delay<o)return;let a=!(s.pauseOnStop&&(i.mouse.position===this._lastPosition||i.mouse.position?.x===this._lastPosition?.x&&i.mouse.position?.y===this._lastPosition?.y)),n=e.interactivity.mouse.position;n?this._lastPosition={...n}:delete this._lastPosition,a&&e.particles.push(s.quantity,e.interactivity.mouse,s.particles),this._delay-=o}isEnabled(t){let e=this.container,i=e.actualOptions,s=e.interactivity.mouse,o=(t?.interactivity??i.interactivity).events;return s.clicking&&s.inside&&!!s.position&&tP(i2,o.onClick.mode)||s.inside&&!!s.position&&tP(i2,o.onHover.mode)}loadModeOptions(t,...e){for(let i of(t.trail||(t.trail=new i1),e))t.trail.load(i?.trail)}reset(){}}async function i5(t,e=!0){t.checkVersion("3.8.1"),await t.addInteractor("externalTrail",t=>Promise.resolve(new i3(t)),e)}!function(t){t.both="both",t.horizontal="horizontal",t.vertical="vertical"}(S||(S={}));let i8=2*Math.PI;class i6{constructor(){this.enable=!1,this.value=0}load(t){te(t)||(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.value&&(this.value=tp(t.value)))}}class i4{constructor(){this.darken=new i6,this.enable=!1,this.enlighten=new i6,this.mode=S.vertical,this.speed=25}load(t){te(t)||(void 0!==t.backColor&&(this.backColor=ee.create(this.backColor,t.backColor)),this.darken.load(t.darken),void 0!==t.enable&&(this.enable=t.enable),this.enlighten.load(t.enlighten),void 0!==t.mode&&(this.mode=t.mode),void 0!==t.speed&&(this.speed=tp(t.speed)))}}class i9{constructor(t){this._engine=t}getTransformValues(t){let e=t.roll?.enable&&t.roll,i=e&&e.horizontal,s=e&&e.vertical;return{a:i?Math.cos(e.angle):void 0,d:s?Math.sin(e.angle):void 0}}init(t){!function(t,e){let i=e.options.roll;if(!i?.enable){e.roll={enable:!1,horizontal:!1,vertical:!1,angle:0,speed:0};return}if(e.roll={enable:i.enable,horizontal:i.mode===S.horizontal||i.mode===S.both,vertical:i.mode===S.vertical||i.mode===S.both,angle:tn()*i8,speed:th(i.speed)/360},i.backColor)e.backColor=tG(t,i.backColor);else if(i.darken.enable&&i.enlighten.enable){let t=tn()>=.5?h.darken:h.enlighten;e.roll.alter={type:t,value:th(t===h.darken?i.darken.value:i.enlighten.value)}}else i.darken.enable?e.roll.alter={type:h.darken,value:th(i.darken.value)}:i.enlighten.enable&&(e.roll.alter={type:h.enlighten,value:th(i.enlighten.value)})}(this._engine,t)}isEnabled(t){let e=t.options.roll;return!t.destroyed&&!t.spawning&&!!e?.enable}loadOptions(t,...e){for(let i of(t.roll||(t.roll=new i4),e))t.roll.load(i?.roll)}update(t,e){this.isEnabled(t)&&function(t,e){let i=t.options.roll,s=t.roll;if(!s||!i?.enable)return;let o=s.speed*e.factor;s.angle+=o,s.angle>i8&&(s.angle-=i8)}(t,e)}}async function i7(t,e=!0){t.checkVersion("3.8.1"),await t.addParticleUpdater("roll",()=>Promise.resolve(new i9(t)),e)}let st=2*Math.PI;class se{init(t){let e=t.options.move.gravity;t.gravity={enable:e.enable,acceleration:th(e.acceleration),inverse:e.inverse},function(t){let e=t.container,i=t.options.move.spin;if(!i.enable)return;let s=i.position??{x:50,y:50},o={x:.01*s.x*e.canvas.size.width,y:.01*s.y*e.canvas.size.height},a=ty(t.getPosition(),o),n=th(i.acceleration);t.retina.spinAcceleration=n*e.retina.pixelRatio,t.spin={center:o,direction:t.velocity.x>=0?x.clockwise:x.counterClockwise,angle:tn()*st,radius:a,acceleration:t.retina.spinAcceleration}}(t)}isEnabled(t){return!t.destroyed&&t.options.move.enable}move(t,e){let i=t.options,s=i.move;if(!s.enable)return;let o=t.container,a=o.retina.pixelRatio;t.retina.moveSpeed??=th(s.speed)*a,t.retina.moveDrift??=th(t.options.move.drift)*a;let n=t.slow.inRange?t.slow.factor:1,r=t.retina.moveSpeed*o.retina.reduceFactor,l=t.retina.moveDrift,c=tu(i.size.value)*a,h=r*(s.size?t.getRadius()/c:1)*n*(e.factor||1)/2,d=t.retina.maxSpeed??o.retina.maxSpeed;s.spin.enable?function(t,e){let i=t.container;if(!t.spin)return;let s=t.spin.direction===x.clockwise,o={x:s?Math.cos:Math.sin,y:s?Math.sin:Math.cos};t.position.x=t.spin.center.x+t.spin.radius*o.x(t.spin.angle),t.position.y=t.spin.center.y+t.spin.radius*o.y(t.spin.angle),t.spin.radius+=t.spin.acceleration;let a=Math.max(i.canvas.size.width,i.canvas.size.height),n=.5*a;t.spin.radius>n?(t.spin.radius=n,t.spin.acceleration*=-1):t.spin.radius<0&&(t.spin.radius=0,t.spin.acceleration*=-1),t.spin.angle+=.01*e*(1-t.spin.radius/a)}(t,h):function(t,e,i,s,o,a){(function(t,e){let i=t.options.move.path;if(!i.enable)return;if(t.lastPathTime<=t.pathDelay){t.lastPathTime+=e.value;return}let s=t.pathGenerator?.generate(t,e);s&&t.velocity.addTo(s),i.clamp&&(t.velocity.x=tr(t.velocity.x,-1,1),t.velocity.y=tr(t.velocity.y,-1,1)),t.lastPathTime-=t.pathDelay})(t,a);let n=t.gravity,r=n?.enable&&n.inverse?-1:1;o&&i&&(t.velocity.x+=o*a.factor/(60*i)),n?.enable&&i&&(t.velocity.y+=r*(n.acceleration*a.factor)/(60*i));let l=t.moveDecay;t.velocity.multTo(l);let c=t.velocity.mult(i);n?.enable&&s>0&&(!n.inverse&&c.y>=0&&c.y>=s||n.inverse&&c.y<=0&&c.y<=-s)&&(c.y=r*s,i&&(t.velocity.y=c.y/i));let h=t.options.zIndex,d=(1-t.zIndexFactor)**h.velocityRate;c.multTo(d);let{position:u}=t;u.addTo(c),e.vibrate&&(u.x+=Math.sin(u.x*Math.cos(u.y)),u.y+=Math.cos(u.y*Math.sin(u.x)))}(t,s,h,d,l,e),function(t){let e=t.initialPosition,{dx:i,dy:s}=tf(e,t.position),o=Math.abs(i),a=Math.abs(s),{maxDistance:n}=t.retina,r=n.horizontal,l=n.vertical;if(r||l){if((r&&o>=r||l&&a>=l)&&!t.misplaced)t.misplaced=!!r&&o>r||!!l&&a>l,r&&(t.velocity.x=.5*t.velocity.y-t.velocity.x),l&&(t.velocity.y=.5*t.velocity.x-t.velocity.y);else if((!r||o<r)&&(!l||a<l)&&t.misplaced)t.misplaced=!1;else if(t.misplaced){let i=t.position,s=t.velocity;r&&(i.x<e.x&&s.x<0||i.x>e.x&&s.x>0)&&(s.x*=-tn()),l&&(i.y<e.y&&s.y<0||i.y>e.y&&s.y>0)&&(s.y*=-tn())}}}(t)}}async function si(t,e=!0){t.checkVersion("3.8.1"),await t.addMover("base",()=>Promise.resolve(new se),e)}let ss=2*Math.PI,so={x:0,y:0};class sa{constructor(){this.validTypes=["circle"]}draw(t){!function(t){let{context:e,particle:i,radius:s}=t;i.circleRange||(i.circleRange={min:0,max:ss});let o=i.circleRange;e.arc(so.x,so.y,s,o.min,o.max,!1)}(t)}getSidesCount(){return 12}particleInit(t,e){let i=e.shapeData,s=i?.angle??{max:360,min:0};e.circleRange=J(s)?{min:tv(s.min),max:tv(s.max)}:{min:0,max:tv(s)}}}async function sn(t,e=!0){t.checkVersion("3.8.1"),await t.addShape(new sa,e)}class sr{constructor(t,e){this._container=t,this._engine=e}init(t){let e=tG(this._engine,t.options.color,t.id,t.options.reduceDuplicates);e&&(t.color=t1(e,t.options.color.animation,this._container.retina.reduceFactor))}isEnabled(t){let{h:e,s:i,l:s}=t.options.color.animation,{color:o}=t;return!t.destroyed&&!t.spawning&&(o?.h.value!==void 0&&e.enable||o?.s.value!==void 0&&i.enable||o?.l.value!==void 0&&s.enable)}update(t,e){t5(t.color,e)}}async function sl(t,e=!0){t.checkVersion("3.8.1"),await t.addParticleUpdater("color",e=>Promise.resolve(new sr(e,t)),e)}!function(t){t[t.r=1]="r",t[t.g=2]="g",t[t.b=3]="b",t[t.a=4]="a"}(D||(D={}));let sc=/^#?([a-f\d])([a-f\d])([a-f\d])([a-f\d])?$/i,sh=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})?$/i;class sd{constructor(){this.key="hex",this.stringPrefix="#"}handleColor(t){return this._parseString(t.value)}handleRangeColor(t){return this._parseString(t.value)}parseString(t){return this._parseString(t)}_parseString(t){if("string"!=typeof t||!t?.startsWith(this.stringPrefix))return;let e=t.replace(sc,(t,e,i,s,o)=>e+e+i+i+s+s+(void 0!==o?o+o:"")),i=sh.exec(e);return i?{a:void 0!==i[D.a]?parseInt(i[D.a],16)/255:1,b:parseInt(i[D.b],16),g:parseInt(i[D.g],16),r:parseInt(i[D.r],16)}:void 0}}async function su(t,e=!0){t.checkVersion("3.8.1"),await t.addColorManager(new sd,e)}!function(t){t[t.h=1]="h",t[t.s=2]="s",t[t.l=3]="l",t[t.a=5]="a"}(T||(T={}));class sp{constructor(){this.key="hsl",this.stringPrefix="hsl"}handleColor(t){let e=t.value.hsl??t.value;if(void 0!==e.h&&void 0!==e.s&&void 0!==e.l)return tQ(e)}handleRangeColor(t){let e=t.value.hsl??t.value;if(void 0!==e.h&&void 0!==e.l)return tQ({h:th(e.h),l:th(e.l),s:th(e.s)})}parseString(t){if(!t.startsWith("hsl"))return;let e=/hsla?\(\s*(\d+)\s*[\s,]\s*(\d+)%\s*[\s,]\s*(\d+)%\s*([\s,]\s*(0|1|0?\.\d+|(\d{1,3})%)\s*)?\)/i.exec(t);return e?function(t){let e=tQ(t);return{a:t.a,b:e.b,g:e.g,r:e.r}}({a:e.length>4?tw(e[T.a]):1,h:parseInt(e[T.h],10),l:parseInt(e[T.l],10),s:parseInt(e[T.s],10)}):void 0}}async function sf(t,e=!0){t.checkVersion("3.8.1"),await t.addColorManager(new sp,e)}class sy{constructor(t){this.container=t}init(t){let e=t.options.opacity;t.opacity=tV(e,1);let i=e.animation;i.enable&&(t.opacity.velocity=th(i.speed)/100*this.container.retina.reduceFactor,i.sync||(t.opacity.velocity*=tn()))}isEnabled(t){return!t.destroyed&&!t.spawning&&!!t.opacity&&t.opacity.enable&&((t.opacity.maxLoops??0)<=0||(t.opacity.maxLoops??0)>0&&(t.opacity.loops??0)<(t.opacity.maxLoops??0))}reset(t){t.opacity&&(t.opacity.time=0,t.opacity.loops=0)}update(t,e){this.isEnabled(t)&&t.opacity&&tH(t,t.opacity,!0,t.options.opacity.animation.destroy,e)}}async function sv(t,e=!0){t.checkVersion("3.8.1"),await t.addParticleUpdater("opacity",t=>Promise.resolve(new sy(t)),e)}class sm{constructor(t){this.container=t,this.modes=[m.bounce,m.split]}update(t,e,i,s){if(!this.modes.includes(s))return;let o=this.container,a=!1;for(let s of o.plugins.values())if(void 0!==s.particleBounce&&(a=s.particleBounce(t,i,e)),a)break;if(a)return;let n=t.getPosition(),l=t.offset,c=t.getRadius(),h=tD(n,c),d=o.canvas.size;(function(t){if(t.outMode!==m.bounce&&t.outMode!==m.split||t.direction!==r.left&&t.direction!==r.right)return;t.bounds.right<0&&t.direction===r.left?t.particle.position.x=t.size+t.offset.x:t.bounds.left>t.canvasSize.width&&t.direction===r.right&&(t.particle.position.x=t.canvasSize.width-t.size-t.offset.x);let e=t.particle.velocity.x,i=!1;if(t.direction===r.right&&t.bounds.right>=t.canvasSize.width&&e>0||t.direction===r.left&&t.bounds.left<=0&&e<0){let e=th(t.particle.options.bounce.horizontal.value);t.particle.velocity.x*=-e,i=!0}if(!i)return;let s=t.offset.x+t.size;t.bounds.right>=t.canvasSize.width&&t.direction===r.right?t.particle.position.x=t.canvasSize.width-s:t.bounds.left<=0&&t.direction===r.left&&(t.particle.position.x=s),t.outMode===m.split&&t.particle.destroy()})({particle:t,outMode:s,direction:e,bounds:h,canvasSize:d,offset:l,size:c}),function(t){if(t.outMode!==m.bounce&&t.outMode!==m.split||t.direction!==r.bottom&&t.direction!==r.top)return;t.bounds.bottom<0&&t.direction===r.top?t.particle.position.y=t.size+t.offset.y:t.bounds.top>t.canvasSize.height&&t.direction===r.bottom&&(t.particle.position.y=t.canvasSize.height-t.size-t.offset.y);let e=t.particle.velocity.y,i=!1;if(t.direction===r.bottom&&t.bounds.bottom>=t.canvasSize.height&&e>0||t.direction===r.top&&t.bounds.top<=0&&e<0){let e=th(t.particle.options.bounce.vertical.value);t.particle.velocity.y*=-e,i=!0}if(!i)return;let s=t.offset.y+t.size;t.bounds.bottom>=t.canvasSize.height&&t.direction===r.bottom?t.particle.position.y=t.canvasSize.height-s:t.bounds.top<=0&&t.direction===r.top&&(t.particle.position.y=s),t.outMode===m.split&&t.particle.destroy()}({particle:t,outMode:s,direction:e,bounds:h,canvasSize:d,offset:l,size:c})}}class sg{constructor(t){this.container=t,this.modes=[m.destroy]}update(t,e,i,s){if(!this.modes.includes(s))return;let o=this.container;switch(t.outType){case w.normal:case w.outside:if(tS(t.position,o.canvas.size,ts.origin,t.getRadius(),e))return;break;case w.inside:{let{dx:e,dy:i}=tf(t.position,t.moveCenter),{x:s,y:o}=t.velocity;if(s<0&&e>t.moveCenter.radius||o<0&&i>t.moveCenter.radius||s>=0&&e<-t.moveCenter.radius||o>=0&&i<-t.moveCenter.radius)return}}o.particles.remove(t,t.group,!0)}}class sb{constructor(t){this.container=t,this.modes=[m.none]}update(t,e,i,s){if(!this.modes.includes(s)||((t.options.move.distance.horizontal&&(e===r.left||e===r.right))??(t.options.move.distance.vertical&&(e===r.top||e===r.bottom))))return;let o=t.options.move.gravity,a=this.container,n=a.canvas.size,l=t.getRadius();if(o.enable){let i=t.position;(!o.inverse&&i.y>n.height+l&&e===r.bottom||o.inverse&&i.y<-l&&e===r.top)&&a.particles.remove(t)}else{if(t.velocity.y>0&&t.position.y<=n.height+l||t.velocity.y<0&&t.position.y>=-l||t.velocity.x>0&&t.position.x<=n.width+l||t.velocity.x<0&&t.position.x>=-l)return;tS(t.position,a.canvas.size,ts.origin,l,e)||a.particles.remove(t)}}}class sw{constructor(t){this.container=t,this.modes=[m.out]}update(t,e,i,s){if(!this.modes.includes(s))return;let o=this.container;if(t.outType===w.inside){let{x:e,y:i}=t.velocity,s=ts.origin;s.length=t.moveCenter.radius,s.angle=t.velocity.angle+Math.PI,s.addTo(ts.create(t.moveCenter));let{dx:a,dy:n}=tf(t.position,s);if(e<=0&&a>=0||i<=0&&n>=0||e>=0&&a<=0||i>=0&&n<=0)return;t.position.x=Math.floor(tc({min:0,max:o.canvas.size.width})),t.position.y=Math.floor(tc({min:0,max:o.canvas.size.height}));let{dx:r,dy:l}=tf(t.position,t.moveCenter);t.direction=Math.atan2(-l,-r),t.velocity.angle=t.direction}else{if(tS(t.position,o.canvas.size,ts.origin,t.getRadius(),e))return;switch(t.outType){case w.outside:{t.position.x=Math.floor(tc({min:-t.moveCenter.radius,max:t.moveCenter.radius}))+t.moveCenter.x,t.position.y=Math.floor(tc({min:-t.moveCenter.radius,max:t.moveCenter.radius}))+t.moveCenter.y;let{dx:e,dy:i}=tf(t.position,t.moveCenter);t.moveCenter.radius&&(t.direction=Math.atan2(i,e),t.velocity.angle=t.direction);break}case w.normal:{let i=t.options.move.warp,s=o.canvas.size,a={bottom:s.height+t.getRadius()+t.offset.y,left:-t.getRadius()-t.offset.x,right:s.width+t.getRadius()+t.offset.x,top:-t.getRadius()-t.offset.y},n=t.getRadius(),l=tD(t.position,n);e===r.right&&l.left>s.width+t.offset.x?(t.position.x=a.left,t.initialPosition.x=t.position.x,i||(t.position.y=tn()*s.height,t.initialPosition.y=t.position.y)):e===r.left&&l.right<-t.offset.x&&(t.position.x=a.right,t.initialPosition.x=t.position.x,i||(t.position.y=tn()*s.height,t.initialPosition.y=t.position.y)),e===r.bottom&&l.top>s.height+t.offset.y?(i||(t.position.x=tn()*s.width,t.initialPosition.x=t.position.x),t.position.y=a.top,t.initialPosition.y=t.position.y):e===r.top&&l.bottom<-t.offset.y&&(i||(t.position.x=tn()*s.width,t.initialPosition.x=t.position.x),t.position.y=a.bottom,t.initialPosition.y=t.position.y)}}}}}let s_=(t,e)=>t.default===e||t.bottom===e||t.left===e||t.right===e||t.top===e;class sx{constructor(t){this._addUpdaterIfMissing=(t,e,i)=>{let s=t.options.move.outModes;!this.updaters.has(e)&&s_(s,e)&&this.updaters.set(e,i(this.container))},this._updateOutMode=(t,e,i,s)=>{for(let o of this.updaters.values())o.update(t,s,e,i)},this.container=t,this.updaters=new Map}init(t){this._addUpdaterIfMissing(t,m.bounce,t=>new sm(t)),this._addUpdaterIfMissing(t,m.out,t=>new sw(t)),this._addUpdaterIfMissing(t,m.destroy,t=>new sg(t)),this._addUpdaterIfMissing(t,m.none,t=>new sb(t))}isEnabled(t){return!t.destroyed&&!t.spawning}update(t,e){let i=t.options.move.outModes;this._updateOutMode(t,e,i.bottom??i.default,r.bottom),this._updateOutMode(t,e,i.left??i.default,r.left),this._updateOutMode(t,e,i.right??i.default,r.right),this._updateOutMode(t,e,i.top??i.default,r.top)}}async function sk(t,e=!0){t.checkVersion("3.8.1"),await t.addParticleUpdater("outModes",t=>Promise.resolve(new sx(t)),e)}!function(t){t[t.r=1]="r",t[t.g=2]="g",t[t.b=3]="b",t[t.a=5]="a"}(E||(E={}));class sz{constructor(){this.key="rgb",this.stringPrefix="rgb"}handleColor(t){let e=t.value.rgb??t.value;if(void 0!==e.r)return e}handleRangeColor(t){let e=t.value.rgb??t.value;if(void 0!==e.r)return{r:th(e.r),g:th(e.g),b:th(e.b)}}parseString(t){if(!t.startsWith(this.stringPrefix))return;let e=/rgba?\(\s*(\d{1,3})\s*[\s,]\s*(\d{1,3})\s*[\s,]\s*(\d{1,3})\s*([\s,]\s*(0|1|0?\.\d+|(\d{1,3})%)\s*)?\)/i.exec(t);return e?{a:e.length>4?tw(e[E.a]):1,b:parseInt(e[E.b],10),g:parseInt(e[E.g],10),r:parseInt(e[E.r],10)}:void 0}}async function sP(t,e=!0){t.checkVersion("3.8.1"),await t.addColorManager(new sz,e)}class sC{init(t){let e=t.container,i=t.options.size.animation;i.enable&&(t.size.velocity=(t.retina.sizeAnimationSpeed??e.retina.sizeAnimationSpeed)/100*e.retina.reduceFactor,i.sync||(t.size.velocity*=tn()))}isEnabled(t){return!t.destroyed&&!t.spawning&&t.size.enable&&((t.size.maxLoops??0)<=0||(t.size.maxLoops??0)>0&&(t.size.loops??0)<(t.size.maxLoops??0))}reset(t){t.size.loops=0}update(t,e){this.isEnabled(t)&&tH(t,t.size,!0,t.options.size.animation.destroy,e)}}async function sM(t,e=!0){t.checkVersion("3.8.1"),await t.addParticleUpdater("size",()=>Promise.resolve(new sC),e)}async function sO(t,e=!0){t.checkVersion("3.8.1"),await su(t,!1),await sf(t,!1),await sP(t,!1),await si(t,!1),await sn(t,!1),await sl(t,!1),await sv(t,!1),await sk(t,!1),await sM(t,!1),await t.refresh(e)}async function sS(t,e=!0){t.checkVersion("3.8.1"),await t.addEasing(z.easeInQuad,t=>t**2,!1),await t.addEasing(z.easeOutQuad,t=>1-(1-t)**2,!1),await t.addEasing(z.easeInOutQuad,t=>t<.5?2*t**2:1-(-2*t+2)**2/2,!1),await t.refresh(e)}let sD='"Twemoji Mozilla", Apple Color Emoji, "Segoe UI Emoji", "Noto Color Emoji", "EmojiOne Color"';class sT{constructor(){this.validTypes=["emoji"],this._emojiShapeDict=new Map}destroy(){for(let[t,e]of this._emojiShapeDict)e instanceof ImageBitmap&&e?.close(),this._emojiShapeDict.delete(t)}draw(t){let e=t.particle.emojiDataKey;if(!e)return;let i=this._emojiShapeDict.get(e);i&&function(t,e){let{context:i,opacity:s}=t,o=i.globalAlpha;if(!e)return;let a=e.width,n=.5*a;i.globalAlpha=s,i.drawImage(e,-n,-n,a,a),i.globalAlpha=o}(t,i)}async init(t){let e=t.actualOptions,{validTypes:i}=this;if(!i.find(t=>tP(t,e.particles.shape.type)))return;let s=[tC(sD)],o=i.map(t=>e.particles.shape.options[t]).find(t=>!!t);o&&tB(o,t=>{t.font&&s.push(tC(t.font))}),await Promise.all(s)}particleDestroy(t){t.emojiDataKey=void 0}particleInit(t,e){let i;let s=e.shapeData;if(!s?.value)return;let o=tA(s.value,e.randomIndexData);if(!o)return;let a="string"==typeof o?{font:s.font??sD,padding:s.padding??0,value:o}:{font:sD,padding:0,...s,...o},n=a.font,r=a.value,l=`${r}_${n}`;if(this._emojiShapeDict.has(l)){e.emojiDataKey=l;return}let c=2*a.padding,h=tu(e.size.value),d=h+c,u=2*d;if("undefined"!=typeof OffscreenCanvas){let t=new OffscreenCanvas(u,u),e=t.getContext("2d");if(!e)return;e.font=`400 ${2*h}px ${n}`,e.textBaseline="middle",e.textAlign="center",e.fillText(r,d,d),i=t.transferToImageBitmap()}else{let t=document.createElement("canvas");t.width=u,t.height=u;let e=t.getContext("2d");if(!e)return;e.font=`400 ${2*h}px ${n}`,e.textBaseline="middle",e.textAlign="center",e.fillText(r,d,d),i=t}this._emojiShapeDict.set(l,i),e.emojiDataKey=l}}async function sE(t,e=!0){t.checkVersion("3.8.1"),await t.addShape(new sT,e)}function sR(t,e,i,s,o,a){let n=e.actualOptions.interactivity.modes.attract;if(n)for(let r of e.particles.quadTree.query(o,a)){let{dx:e,dy:o,distance:a}=tf(r.position,i),l=n.speed*n.factor,c=tr(t.getEasing(n.easing)(1-a/s)*l,1,n.maxSpeed),h=ts.create(a?e/a*c:l,a?o/a*c:l);r.position.subFrom(h)}}class sI{constructor(){this.distance=200,this.duration=.4,this.easing=z.easeOutQuad,this.factor=1,this.maxSpeed=50,this.speed=1}load(t){te(t)||(void 0!==t.distance&&(this.distance=t.distance),void 0!==t.duration&&(this.duration=t.duration),void 0!==t.easing&&(this.easing=t.easing),void 0!==t.factor&&(this.factor=t.factor),void 0!==t.maxSpeed&&(this.maxSpeed=t.maxSpeed),void 0===t.speed||(this.speed=t.speed))}}let sL="attract";class sF extends iy{constructor(t,e){super(e),this._engine=t,e.attract||(e.attract={particles:[]}),this.handleClickMode=t=>{let i=this.container.actualOptions.interactivity.modes.attract;if(i&&t===sL){for(let t of(e.attract||(e.attract={particles:[]}),e.attract.clicking=!0,e.attract.count=0,e.attract.particles))this.isEnabled(t)&&t.velocity.setTo(t.initialVelocity);e.attract.particles=[],e.attract.finish=!1,setTimeout(()=>{e.destroyed||(e.attract||(e.attract={particles:[]}),e.attract.clicking=!1)},1e3*i.duration)}}}clear(){}init(){let t=this.container,e=t.actualOptions.interactivity.modes.attract;e&&(t.retina.attractModeDistance=e.distance*t.retina.pixelRatio)}interact(){let t=this.container,e=t.actualOptions,i=t.interactivity.status===q,s=e.interactivity.events,{enable:o,mode:a}=s.onHover,{enable:n,mode:r}=s.onClick;i&&o&&tP(sL,a)?function(t,e,i){let s=e.interactivity.mouse.position,o=e.retina.attractModeDistance;o&&!(o<0)&&s&&sR(t,e,s,o,new e9(s.x,s.y,o),t=>i(t))}(this._engine,this.container,t=>this.isEnabled(t)):n&&tP(sL,r)&&function(t,e,i){e.attract||(e.attract={particles:[]});let{attract:s}=e;if(s.finish||(s.count||(s.count=0),s.count++,s.count!==e.particles.count||(s.finish=!0)),s.clicking){let s=e.interactivity.mouse.clickPosition,o=e.retina.attractModeDistance;if(!o||o<0||!s)return;sR(t,e,s,o,new e9(s.x,s.y,o),t=>i(t))}else!1===s.clicking&&(s.particles=[])}(this._engine,this.container,t=>this.isEnabled(t))}isEnabled(t){let e=this.container,i=e.actualOptions,s=e.interactivity.mouse,o=(t?.interactivity??i.interactivity).events;if((!s.position||!o.onHover.enable)&&(!s.clickPosition||!o.onClick.enable))return!1;let a=o.onHover.mode,n=o.onClick.mode;return tP(sL,a)||tP(sL,n)}loadModeOptions(t,...e){for(let i of(t.attract||(t.attract=new sI),e))t.attract.load(i?.attract)}reset(){}}async function sB(t,e=!0){t.checkVersion("3.8.1"),await t.addInteractor("externalAttract",e=>Promise.resolve(new sF(t,e)),e)}let sA=.5*Math.PI;function sq(t,e,i,s,o){for(let a of t.particles.quadTree.query(s,o))s instanceof e9?tF(tL(a),{position:e,radius:i,mass:i**2*sA,velocity:ts.origin,factor:ts.origin}):s instanceof e7&&function(t,e){let i=tD(t.getPosition(),t.getRadius()),s=t.options.bounce,o=tx({pSide:{min:i.left,max:i.right},pOtherSide:{min:i.top,max:i.bottom},rectSide:{min:e.left,max:e.right},rectOtherSide:{min:e.top,max:e.bottom},velocity:t.velocity.x,factor:th(s.horizontal.value)});o.bounced&&(void 0!==o.velocity&&(t.velocity.x=o.velocity),void 0!==o.position&&(t.position.x=o.position));let a=tx({pSide:{min:i.top,max:i.bottom},pOtherSide:{min:i.left,max:i.right},rectSide:{min:e.top,max:e.bottom},rectOtherSide:{min:e.left,max:e.right},velocity:t.velocity.y,factor:th(s.vertical.value)});a.bounced&&(void 0!==a.velocity&&(t.velocity.y=a.velocity),void 0!==a.position&&(t.position.y=a.position))}(a,tD(e,i))}class sV{constructor(){this.distance=200}load(t){te(t)||void 0===t.distance||(this.distance=t.distance)}}let sU="bounce";class sH extends iy{constructor(t){super(t)}clear(){}init(){let t=this.container,e=t.actualOptions.interactivity.modes.bounce;e&&(t.retina.bounceModeDistance=e.distance*t.retina.pixelRatio)}interact(){let t=this.container,e=t.actualOptions.interactivity.events,i=t.interactivity.status===q,s=e.onHover.enable,o=e.onHover.mode,a=e.onDiv;if(i&&s&&tP(sU,o))!function(t,e){let i=t.retina.pixelRatio,s=t.interactivity.mouse.position,o=t.retina.bounceModeDistance;o&&!(o<0)&&s&&sq(t,s,o,new e9(s.x,s.y,o+10*i),e)}(this.container,t=>this.isEnabled(t));else{var n,r;n=this.container,r=t=>this.isEnabled(t),tR(sU,a,(t,e)=>(function(t,e,i,s){let o=document.querySelectorAll(e);o.length&&o.forEach(e=>{let o=t.retina.pixelRatio,a={x:(e.offsetLeft+.5*e.offsetWidth)*o,y:(e.offsetTop+.5*e.offsetHeight)*o},n=.5*e.offsetWidth*o,r=10*o,l=i.type===p.circle?new e9(a.x,a.y,n+r):new e7(e.offsetLeft*o-r,e.offsetTop*o-r,e.offsetWidth*o+2*r,e.offsetHeight*o+2*r);s(a,n,l)})})(n,t,e,(t,e,i)=>sq(n,t,e,i,r)))}}isEnabled(t){let e=this.container,i=e.actualOptions,s=e.interactivity.mouse,o=(t?.interactivity??i.interactivity).events,a=o.onDiv;return!!s.position&&o.onHover.enable&&tP(sU,o.onHover.mode)||tE(sU,a)}loadModeOptions(t,...e){for(let i of(t.bounce||(t.bounce=new sV),e))t.bounce.load(i?.bounce)}reset(){}}async function s$(t,e=!0){t.checkVersion("3.8.1"),await t.addInteractor("externalBounce",t=>Promise.resolve(new sH(t)),e)}class sW{constructor(){this.distance=200,this.duration=.4,this.mix=!1}load(t){if(!te(t)){if(void 0!==t.distance&&(this.distance=t.distance),void 0!==t.duration&&(this.duration=t.duration),void 0!==t.mix&&(this.mix=t.mix),void 0!==t.opacity&&(this.opacity=t.opacity),void 0!==t.color){let e=tt(this.color)?void 0:this.color;this.color=tB(t.color,t=>ee.create(e,t))}void 0!==t.size&&(this.size=t.size)}}}class sG extends sW{constructor(){super(),this.selectors=[]}load(t){super.load(t),te(t)||void 0===t.selectors||(this.selectors=t.selectors)}}class sj extends sW{load(t){super.load(t),te(t)||(this.divs=tB(t.divs,t=>{let e=new sG;return e.load(t),e}))}}function sQ(t,e,i,s){return e>=i?tr(t+(e-i)*s,t,e):e<i?tr(t-(i-e)*s,e,t):void 0}!function(t){t.color="color",t.opacity="opacity",t.size="size"}(R||(R={}));let sN="bubble";class sX extends iy{constructor(t,e){super(t),this._clickBubble=()=>{let t=this.container,e=t.actualOptions,i=t.interactivity.mouse.clickPosition,s=e.interactivity.modes.bubble;if(!s||!i)return;t.bubble||(t.bubble={});let o=t.retina.bubbleModeDistance;if(!o||o<0)return;let a=t.particles.quadTree.queryCircle(i,o,t=>this.isEnabled(t)),{bubble:n}=t;for(let e of a){if(!n.clicking)continue;e.bubble.inRange=!n.durationEnd;let a=ty(e.getPosition(),i),r=(new Date().getTime()-(t.interactivity.mouse.clickTime??0))/1e3;r>s.duration&&(n.durationEnd=!0),r>2*s.duration&&(n.clicking=!1,n.durationEnd=!1);let l={bubbleObj:{optValue:t.retina.bubbleModeSize,value:e.bubble.radius},particlesObj:{optValue:tu(e.options.size.value)*t.retina.pixelRatio,value:e.size.value},type:R.size};this._process(e,a,r,l);let c={bubbleObj:{optValue:s.opacity,value:e.bubble.opacity},particlesObj:{optValue:tu(e.options.opacity.value),value:e.opacity?.value??1},type:R.opacity};this._process(e,a,r,c),!n.durationEnd&&a<=o?this._hoverBubbleColor(e,a):delete e.bubble.color}},this._hoverBubble=()=>{let t=this.container,e=t.interactivity.mouse.position,i=t.retina.bubbleModeDistance;if(i&&!(i<0)&&e)for(let s of t.particles.quadTree.queryCircle(e,i,t=>this.isEnabled(t))){s.bubble.inRange=!0;let o=ty(s.getPosition(),e),a=1-o/i;o<=i?a>=0&&t.interactivity.status===q&&(this._hoverBubbleSize(s,a),this._hoverBubbleOpacity(s,a),this._hoverBubbleColor(s,a)):this.reset(s),t.interactivity.status===A&&this.reset(s)}},this._hoverBubbleColor=(t,e,i)=>{let s=this.container.actualOptions,o=i??s.interactivity.modes.bubble;if(o){if(!t.bubble.finalColor){let e=o.color;if(!e)return;let i=tA(e);t.bubble.finalColor=tG(this._engine,i)}if(t.bubble.finalColor){if(o.mix){t.bubble.color=void 0;let i=t.getFillColor();t.bubble.color=i?tj(tZ(i,t.bubble.finalColor,1-e,e)):t.bubble.finalColor}else t.bubble.color=t.bubble.finalColor}}},this._hoverBubbleOpacity=(t,e,i)=>{let s=this.container.actualOptions,o=i?.opacity??s.interactivity.modes.bubble?.opacity;if(!o)return;let a=t.options.opacity.value,n=sQ(t.opacity?.value??1,o,tu(a),e);void 0!==n&&(t.bubble.opacity=n)},this._hoverBubbleSize=(t,e,i)=>{let s=this.container,o=i?.size?i.size*s.retina.pixelRatio:s.retina.bubbleModeSize;if(void 0===o)return;let a=tu(t.options.size.value)*s.retina.pixelRatio,n=sQ(t.size.value,o,a,e);void 0!==n&&(t.bubble.radius=n)},this._process=(t,e,i,s)=>{let o=this.container,a=s.bubbleObj.optValue,n=o.actualOptions.interactivity.modes.bubble;if(!n||void 0===a)return;let r=n.duration,l=o.retina.bubbleModeDistance,c=s.particlesObj.optValue,h=s.bubbleObj.value,d=s.particlesObj.value??0,u=s.type;if(l&&!(l<0)&&a!==c){if(o.bubble||(o.bubble={}),o.bubble.durationEnd)h&&(u===R.size&&delete t.bubble.radius,u===R.opacity&&delete t.bubble.opacity);else if(e<=l){if((h??d)!==a){let e=d-i*(d-a)/r;u===R.size&&(t.bubble.radius=e),u===R.opacity&&(t.bubble.opacity=e)}}else u===R.size&&delete t.bubble.radius,u===R.opacity&&delete t.bubble.opacity}},this._singleSelectorHover=(t,e,i)=>{let s=this.container,o=document.querySelectorAll(e),a=s.actualOptions.interactivity.modes.bubble;a&&o.length&&o.forEach(e=>{let o=s.retina.pixelRatio,n={x:(e.offsetLeft+.5*e.offsetWidth)*o,y:(e.offsetTop+.5*e.offsetHeight)*o},r=.5*e.offsetWidth*o,l=i.type===p.circle?new e9(n.x,n.y,r):new e7(e.offsetLeft*o,e.offsetTop*o,e.offsetWidth*o,e.offsetHeight*o);for(let i of s.particles.quadTree.query(l,t=>this.isEnabled(t))){if(!l.contains(i.getPosition()))continue;i.bubble.inRange=!0;let s=tI(a.divs,e);i.bubble.div&&i.bubble.div===e||(this.clear(i,t,!0),i.bubble.div=e),this._hoverBubbleSize(i,1,s),this._hoverBubbleOpacity(i,1,s),this._hoverBubbleColor(i,1,s)}})},this._engine=e,t.bubble||(t.bubble={}),this.handleClickMode=e=>{e===sN&&(t.bubble||(t.bubble={}),t.bubble.clicking=!0)}}clear(t,e,i){(!t.bubble.inRange||i)&&(delete t.bubble.div,delete t.bubble.opacity,delete t.bubble.radius,delete t.bubble.color)}init(){let t=this.container,e=t.actualOptions.interactivity.modes.bubble;e&&(t.retina.bubbleModeDistance=e.distance*t.retina.pixelRatio,void 0!==e.size&&(t.retina.bubbleModeSize=e.size*t.retina.pixelRatio))}interact(t){let e=this.container.actualOptions.interactivity.events,i=e.onHover,s=e.onClick,o=i.enable,a=i.mode,n=s.enable,r=s.mode,l=e.onDiv;o&&tP(sN,a)?this._hoverBubble():n&&tP(sN,r)?this._clickBubble():tR(sN,l,(e,i)=>this._singleSelectorHover(t,e,i))}isEnabled(t){let e=this.container,i=e.actualOptions,s=e.interactivity.mouse,{onClick:o,onDiv:a,onHover:n}=(t?.interactivity??i.interactivity).events,r=tE(sN,a);return(!!r||!!n.enable&&!!s.position||!!o.enable&&!!s.clickPosition)&&(tP(sN,n.mode)||tP(sN,o.mode)||r)}loadModeOptions(t,...e){for(let i of(t.bubble||(t.bubble=new sj),e))t.bubble.load(i?.bubble)}reset(t){t.bubble.inRange=!1}}async function sY(t,e=!0){t.checkVersion("3.8.1"),await t.addInteractor("externalBubble",e=>Promise.resolve(new sX(e,t)),e)}class sZ{constructor(){this.opacity=.5}load(t){te(t)||void 0===t.opacity||(this.opacity=t.opacity)}}class sK{constructor(){this.distance=80,this.links=new sZ,this.radius=60}load(t){te(t)||(void 0!==t.distance&&(this.distance=t.distance),this.links.load(t.links),void 0===t.radius||(this.radius=t.radius))}}class sJ extends iy{constructor(t){super(t)}clear(){}init(){let t=this.container,e=t.actualOptions.interactivity.modes.connect;e&&(t.retina.connectModeDistance=e.distance*t.retina.pixelRatio,t.retina.connectModeRadius=e.radius*t.retina.pixelRatio)}interact(){let t=this.container;if(t.actualOptions.interactivity.events.onHover.enable&&"pointermove"===t.interactivity.status){let e=t.interactivity.mouse.position,{connectModeDistance:i,connectModeRadius:s}=t.retina;if(!i||i<0||!s||s<0||!e)return;let o=Math.abs(s),a=t.particles.quadTree.queryCircle(e,o,t=>this.isEnabled(t));a.forEach((e,s)=>{let o=e.getPosition();for(let n of a.slice(s+1)){let s=n.getPosition(),a=Math.abs(i),r=Math.abs(o.x-s.x),l=Math.abs(o.y-s.y);r<a&&l<a&&function(t,e,i){t.canvas.draw(s=>{var o;let a=function(t,e,i,s){let o=t.actualOptions.interactivity.modes.connect;if(o)return function(t,e,i,s){let o=Math.floor(i.getRadius()/e.getRadius()),a=e.getFillColor(),n=i.getFillColor();if(!a||!n)return;let r=e.getPosition(),l=i.getPosition(),c=tZ(a,n,e.getRadius(),i.getRadius()),h=t.createLinearGradient(r.x,r.y,l.x,l.y);return h.addColorStop(0,tY(a,s)),h.addColorStop(tr(o,0,1),tX(c,s)),h.addColorStop(1,tY(n,s)),h}(e,i,s,o.links.opacity)}(t,s,e,i);if(!a)return;let n=e.getPosition(),r=i.getPosition();o=e.retina.linksWidth??0,t8(s,n,r),s.lineWidth=o,s.strokeStyle=a,s.stroke()})}(t,e,n)}})}}isEnabled(t){let e=this.container,i=e.interactivity.mouse,s=(t?.interactivity??e.actualOptions.interactivity).events;return!!s.onHover.enable&&!!i.position&&tP("connect",s.onHover.mode)}loadModeOptions(t,...e){for(let i of(t.connect||(t.connect=new sK),e))t.connect.load(i?.connect)}reset(){}}async function s0(t,e=!0){t.checkVersion("3.8.1"),await t.addInteractor("externalConnect",t=>Promise.resolve(new sJ(t)),e)}class s1{constructor(){this.blink=!1,this.consent=!1,this.opacity=1}load(t){te(t)||(void 0!==t.blink&&(this.blink=t.blink),void 0!==t.color&&(this.color=ee.create(this.color,t.color)),void 0!==t.consent&&(this.consent=t.consent),void 0===t.opacity||(this.opacity=t.opacity))}}class s2{constructor(){this.distance=100,this.links=new s1}load(t){te(t)||(void 0!==t.distance&&(this.distance=t.distance),this.links.load(t.links))}}class s3 extends iy{constructor(t,e){super(t),this._engine=e}clear(){}init(){let t=this.container,e=t.actualOptions.interactivity.modes.grab;e&&(t.retina.grabModeDistance=e.distance*t.retina.pixelRatio)}interact(){let t=this.container,e=t.actualOptions.interactivity;if(!e.modes.grab||!e.events.onHover.enable||t.interactivity.status!==q)return;let i=t.interactivity.mouse.position;if(!i)return;let s=t.retina.grabModeDistance;if(s&&!(s<0))for(let o of t.particles.quadTree.queryCircle(i,s,t=>this.isEnabled(t))){let a=ty(o.getPosition(),i);if(a>s)continue;let n=e.modes.grab.links,r=n.opacity,l=r-a*r/s;if(l<=0)continue;let c=n.color??o.options.links?.color;if(!t.particles.grabLineColor&&c){let i=e.modes.grab.links;t.particles.grabLineColor=tJ(this._engine,c,i.blink,i.consent)}let h=tK(o,void 0,t.particles.grabLineColor);h&&function(t,e,i,s,o){t.canvas.draw(t=>{var a;let n=e.getPosition();a=e.retina.linksWidth??0,t8(t,n,o),t.strokeStyle=tX(i,s),t.lineWidth=a,t.stroke()})}(t,o,h,l,i)}}isEnabled(t){let e=this.container,i=e.interactivity.mouse,s=(t?.interactivity??e.actualOptions.interactivity).events;return s.onHover.enable&&!!i.position&&tP("grab",s.onHover.mode)}loadModeOptions(t,...e){for(let i of(t.grab||(t.grab=new s2),e))t.grab.load(i?.grab)}reset(){}}async function s5(t,e=!0){t.checkVersion("3.8.1"),await t.addInteractor("externalGrab",e=>Promise.resolve(new s3(e,t)),e)}class s8 extends iy{constructor(t){super(t),this.handleClickMode=t=>{if("pause"!==t)return;let e=this.container;e.animationStatus?e.pause():e.play()}}clear(){}init(){}interact(){}isEnabled(){return!0}reset(){}}async function s6(t,e=!0){t.checkVersion("3.8.1"),await t.addInteractor("externalPause",t=>Promise.resolve(new s8(t)),e)}class s4{constructor(){this.default=!0,this.groups=[],this.quantity=4}load(t){if(te(t))return;void 0!==t.default&&(this.default=t.default),void 0!==t.groups&&(this.groups=t.groups.map(t=>t)),this.groups.length||(this.default=!0);let e=t.quantity;void 0!==e&&(this.quantity=tp(e))}}class s9 extends iy{constructor(t){super(t),this.handleClickMode=t=>{if("push"!==t)return;let e=this.container,i=e.actualOptions.interactivity.modes.push;if(!i)return;let s=th(i.quantity);if(s<=0)return;let o=tO([void 0,...i.groups]),a=void 0!==o?e.actualOptions.particles.groups[o]:void 0;e.particles.push(s,e.interactivity.mouse,a,o)}}clear(){}init(){}interact(){}isEnabled(){return!0}loadModeOptions(t,...e){for(let i of(t.push||(t.push=new s4),e))t.push.load(i?.push)}reset(){}}async function s7(t,e=!0){t.checkVersion("3.8.1"),await t.addInteractor("externalPush",t=>Promise.resolve(new s9(t)),e)}class ot{constructor(){this.quantity=2}load(t){if(te(t))return;let e=t.quantity;void 0!==e&&(this.quantity=tp(e))}}class oe extends iy{constructor(t){super(t),this.handleClickMode=t=>{let e=this.container,i=e.actualOptions;if(!i.interactivity.modes.remove||"remove"!==t)return;let s=th(i.interactivity.modes.remove.quantity);e.particles.removeQuantity(s)}}clear(){}init(){}interact(){}isEnabled(){return!0}loadModeOptions(t,...e){for(let i of(t.remove||(t.remove=new ot),e))t.remove.load(i?.remove)}reset(){}}async function oi(t,e=!0){t.checkVersion("3.8.1"),await t.addInteractor("externalRemove",t=>Promise.resolve(new oe(t)),e)}class os{constructor(){this.distance=200,this.duration=.4,this.factor=100,this.speed=1,this.maxSpeed=50,this.easing=z.easeOutQuad}load(t){te(t)||(void 0!==t.distance&&(this.distance=t.distance),void 0!==t.duration&&(this.duration=t.duration),void 0!==t.easing&&(this.easing=t.easing),void 0!==t.factor&&(this.factor=t.factor),void 0!==t.speed&&(this.speed=t.speed),void 0===t.maxSpeed||(this.maxSpeed=t.maxSpeed))}}class oo extends os{constructor(){super(),this.selectors=[]}load(t){super.load(t),te(t)||void 0===t.selectors||(this.selectors=t.selectors)}}class oa extends os{load(t){super.load(t),te(t)||(this.divs=tB(t.divs,t=>{let e=new oo;return e.load(t),e}))}}let on="repulse";class or extends iy{constructor(t,e){super(e),this._clickRepulse=()=>{let t=this.container,e=t.actualOptions.interactivity.modes.repulse;if(!e)return;let i=t.repulse??{particles:[]};if(i.finish||(i.count||(i.count=0),i.count++,i.count!==t.particles.count||(i.finish=!0)),i.clicking){let s=t.retina.repulseModeDistance;if(!s||s<0)return;let o=Math.pow(s/6,3),a=t.interactivity.mouse.clickPosition;if(void 0===a)return;let n=new e9(a.x,a.y,o);for(let s of t.particles.quadTree.query(n,t=>this.isEnabled(t))){let{dx:t,dy:n,distance:r}=tf(a,s.position),l=r**2,c=-o*e.speed/l;if(l<=o){i.particles.push(s);let e=ts.create(t,n);e.length=c,s.velocity.setTo(e)}}}else if(!1===i.clicking){for(let t of i.particles)t.velocity.setTo(t.initialVelocity);i.particles=[]}},this._hoverRepulse=()=>{let t=this.container,e=t.interactivity.mouse.position,i=t.retina.repulseModeDistance;i&&!(i<0)&&e&&this._processRepulse(e,i,new e9(e.x,e.y,i))},this._processRepulse=(t,e,i,s)=>{let o=this.container,a=o.particles.quadTree.query(i,t=>this.isEnabled(t)),n=o.actualOptions.interactivity.modes.repulse;if(!n)return;let{easing:r,speed:l,factor:c,maxSpeed:h}=n,d=this._engine.getEasing(r),u=(s?.speed??l)*c;for(let i of a){let{dx:s,dy:o,distance:a}=tf(i.position,t),n=tr(d(1-a/e)*u,0,h),r=ts.create(a?s/a*n:u,a?o/a*n:u);i.position.addTo(r)}},this._singleSelectorRepulse=(t,e)=>{let i=this.container,s=i.actualOptions.interactivity.modes.repulse;if(!s)return;let o=document.querySelectorAll(t);o.length&&o.forEach(t=>{let o=i.retina.pixelRatio,a={x:(t.offsetLeft+.5*t.offsetWidth)*o,y:(t.offsetTop+.5*t.offsetHeight)*o},n=.5*t.offsetWidth*o,r=e.type===p.circle?new e9(a.x,a.y,n):new e7(t.offsetLeft*o,t.offsetTop*o,t.offsetWidth*o,t.offsetHeight*o),l=tI(s.divs,t);this._processRepulse(a,n,r,l)})},this._engine=t,e.repulse||(e.repulse={particles:[]}),this.handleClickMode=t=>{let i=this.container.actualOptions.interactivity.modes.repulse;if(!i||t!==on)return;e.repulse||(e.repulse={particles:[]});let s=e.repulse;for(let t of(s.clicking=!0,s.count=0,e.repulse.particles))this.isEnabled(t)&&t.velocity.setTo(t.initialVelocity);s.particles=[],s.finish=!1,setTimeout(()=>{e.destroyed||(s.clicking=!1)},1e3*i.duration)}}clear(){}init(){let t=this.container,e=t.actualOptions.interactivity.modes.repulse;e&&(t.retina.repulseModeDistance=e.distance*t.retina.pixelRatio)}interact(){let t=this.container,e=t.actualOptions,i=t.interactivity.status===q,s=e.interactivity.events,o=s.onHover,a=o.enable,n=o.mode,r=s.onClick,l=r.enable,c=r.mode,h=s.onDiv;i&&a&&tP(on,n)?this._hoverRepulse():l&&tP(on,c)?this._clickRepulse():tR(on,h,(t,e)=>this._singleSelectorRepulse(t,e))}isEnabled(t){let e=this.container,i=e.actualOptions,s=e.interactivity.mouse,o=(t?.interactivity??i.interactivity).events,a=o.onDiv,n=o.onHover,r=o.onClick,l=tE(on,a);if(!(l||n.enable&&s.position||r.enable&&s.clickPosition))return!1;let c=n.mode,h=r.mode;return tP(on,c)||tP(on,h)||l}loadModeOptions(t,...e){for(let i of(t.repulse||(t.repulse=new oa),e))t.repulse.load(i?.repulse)}reset(){}}async function ol(t,e=!0){t.checkVersion("3.8.1"),await t.addInteractor("externalRepulse",e=>Promise.resolve(new or(t,e)),e)}class oc{constructor(){this.factor=3,this.radius=200}load(t){te(t)||(void 0!==t.factor&&(this.factor=t.factor),void 0===t.radius||(this.radius=t.radius))}}class oh extends iy{constructor(t){super(t)}clear(t,e,i){(!t.slow.inRange||i)&&(t.slow.factor=1)}init(){let t=this.container,e=t.actualOptions.interactivity.modes.slow;e&&(t.retina.slowModeRadius=e.radius*t.retina.pixelRatio)}interact(){}isEnabled(t){let e=this.container,i=e.interactivity.mouse,s=(t?.interactivity??e.actualOptions.interactivity).events;return s.onHover.enable&&!!i.position&&tP("slow",s.onHover.mode)}loadModeOptions(t,...e){for(let i of(t.slow||(t.slow=new oc),e))t.slow.load(i?.slow)}reset(t){t.slow.inRange=!1;let e=this.container,i=e.actualOptions,s=e.interactivity.mouse.position,o=e.retina.slowModeRadius,a=i.interactivity.modes.slow;if(!a||!o||o<0||!s)return;let n=ty(s,t.getPosition()),r=a.factor,{slow:l}=t;n>o||(l.inRange=!0,l.factor=n/o/r)}}async function od(t,e=!0){t.checkVersion("3.8.1"),await t.addInteractor("externalSlow",t=>Promise.resolve(new oh(t)),e)}let ou=/(#(?:[0-9a-f]{2}){2,4}|(#[0-9a-f]{3})|(rgb|hsl)a?\((-?\d+%?[,\s]+){2,3}\s*[\d.]+%?\))|currentcolor/gi;async function op(t){return new Promise(e=>{t.loading=!0;let i=new Image;t.element=i,i.addEventListener("load",()=>{t.loading=!1,e()}),i.addEventListener("error",()=>{t.element=void 0,t.error=!0,t.loading=!1,t_.error(`${U} loading image: ${t.source}`),e()}),i.src=t.source})}async function of(t){if("svg"!==t.type){await op(t);return}t.loading=!0;let e=await fetch(t.source);e.ok?t.svgData=await e.text():(t_.error(`${U} Image not found`),t.error=!0),t.loading=!1}let oy=[0,4,2,1],ov=[8,8,4,2];class om{constructor(t){this.pos=0,this.data=new Uint8ClampedArray(t)}getString(t){let e=this.data.slice(this.pos,this.pos+t);return this.pos+=e.length,e.reduce((t,e)=>t+String.fromCharCode(e),"")}nextByte(){return this.data[this.pos++]}nextTwoBytes(){return this.pos+=2,this.data[this.pos-2]+(this.data[this.pos-1]<<8)}readSubBlocks(){let t="",e=0;do{e=this.data[this.pos++];for(let i=e;--i>=0;t+=String.fromCharCode(this.data[this.pos++]));}while(0!==e);return t}readSubBlocksBin(){let t=this.data[this.pos],e=0;for(let i=0;0!==t;i+=t+1,t=this.data[this.pos+i])e+=t;let i=new Uint8Array(e);t=this.data[this.pos++];for(let e=0;0!==t;t=this.data[this.pos++])for(let s=t;--s>=0;i[e++]=this.data[this.pos++]);return i}skipSubBlocks(){for(;0!==this.data[this.pos];this.pos+=this.data[this.pos]+1);this.pos++}}(function(t){t[t.Replace=0]="Replace",t[t.Combine=1]="Combine",t[t.RestoreBackground=2]="RestoreBackground",t[t.RestorePrevious=3]="RestorePrevious",t[t.UndefinedA=4]="UndefinedA",t[t.UndefinedB=5]="UndefinedB",t[t.UndefinedC=6]="UndefinedC",t[t.UndefinedD=7]="UndefinedD"})(I||(I={})),function(t){t[t.Extension=33]="Extension",t[t.ApplicationExtension=255]="ApplicationExtension",t[t.GraphicsControlExtension=249]="GraphicsControlExtension",t[t.PlainTextExtension=1]="PlainTextExtension",t[t.CommentExtension=254]="CommentExtension",t[t.Image=44]="Image",t[t.EndOfFile=59]="EndOfFile"}(L||(L={}));let og={x:0,y:0};function ob(t,e){let i=[];for(let s=0;s<e;s++)i.push({r:t.data[t.pos],g:t.data[t.pos+1],b:t.data[t.pos+2]}),t.pos+=3;return i}async function ow(t,e,i,s,o,a){let n=e.frames[s(!0)];n.left=t.nextTwoBytes(),n.top=t.nextTwoBytes(),n.width=t.nextTwoBytes(),n.height=t.nextTwoBytes();let r=t.nextByte(),l=(128&r)==128;n.sortFlag=(32&r)==32,n.reserved=(24&r)>>>3,l&&(n.localColorTable=ob(t,1<<(7&r)+1));let c=t=>{let{r:s,g:a,b:r}=(l?n.localColorTable:e.globalColorTable)[t];return t!==o(null)?{r:s,g:a,b:r,a:255}:{r:s,g:a,b:r,a:i?~~((s+a+r)/3):0}},h=(()=>{try{return new ImageData(n.width,n.height,{colorSpace:"srgb"})}catch(t){if(t instanceof DOMException&&"IndexSizeError"===t.name)return null;throw t}})();if(null==h)throw EvalError("GIF frame size is to large");let d=t.nextByte(),u=t.readSubBlocksBin(),p=1<<d,f=(t,e)=>{let i=t>>>3,s=7&t;return(u[i]+(u[i+1]<<8)+(u[i+2]<<16)&(1<<e)-1<<s)>>>s};if((64&r)==64){for(let i=0,o=d+1,r=0,l=[[0]],u=0;u<4;u++){if(oy[u]<n.height){let t=0,e=0,s=!1;for(;!s;){let a=i;if(i=f(r,o),r+=o+1,i===p){o=d+1,l.length=p+2;for(let t=0;t<l.length;t++)l[t]=t<p?[t]:[]}else{for(let s of(i>=l.length?l.push(l[a].concat(l[a][0])):a!==p&&l.push(l[a].concat(l[i][0])),l[i])){let{r:i,g:o,b:a,a:r}=c(s);h.data.set([i,o,a,r],oy[u]*n.width+ov[u]*e+t%(4*n.width)),t+=4}l.length===1<<o&&o<12&&o++}t===4*n.width*(e+1)&&(e++,oy[u]+ov[u]*e>=n.height&&(s=!0))}}a?.(t.pos/(t.data.length-1),s(!1)+1,h,{x:n.left,y:n.top},{width:e.width,height:e.height})}n.image=h,n.bitmap=await createImageBitmap(h)}else{let i=0,o=d+1,r=0,l=-4,u=!1,y=[[0]];for(;!u;){let t=i;if(i=f(r,o),r+=o,i===p){o=d+1,y.length=p+2;for(let t=0;t<y.length;t++)y[t]=t<p?[t]:[]}else{if(i===p+1){u=!0;break}for(let e of(i>=y.length?y.push(y[t].concat(y[t][0])):t!==p&&y.push(y[t].concat(y[i][0])),y[i])){let{r:t,g:i,b:s,a:o}=c(e);h.data.set([t,i,s,o],l+=4)}y.length>=1<<o&&o<12&&o++}}n.image=h,n.bitmap=await createImageBitmap(h),a?.((t.pos+1)/t.data.length,s(!1)+1,n.image,{x:n.left,y:n.top},{width:e.width,height:e.height})}}async function o_(t,e,i,s,o,a){switch(t.nextByte()){case L.EndOfFile:return!0;case L.Image:await ow(t,e,i,s,o,a);break;case L.Extension:!function(t,e,i,s){switch(t.nextByte()){case L.GraphicsControlExtension:{let o=e.frames[i(!1)];t.pos++;let a=t.nextByte();o.GCreserved=(224&a)>>>5,o.disposalMethod=(28&a)>>>2,o.userInputDelayFlag=(2&a)==2,o.delayTime=10*t.nextTwoBytes();let n=t.nextByte();(1&a)==1&&s(n),t.pos++;break}case L.ApplicationExtension:{t.pos++;let i={identifier:t.getString(8),authenticationCode:t.getString(3),data:t.readSubBlocksBin()};e.applicationExtensions.push(i);break}case L.CommentExtension:e.comments.push([i(!1),t.readSubBlocks()]);break;case L.PlainTextExtension:if(0===e.globalColorTable.length)throw EvalError("plain text extension without global color table");t.pos++,e.frames[i(!1)].plainTextData={left:t.nextTwoBytes(),top:t.nextTwoBytes(),width:t.nextTwoBytes(),height:t.nextTwoBytes(),charSize:{width:t.nextTwoBytes(),height:t.nextTwoBytes()},foregroundColor:t.nextByte(),backgroundColor:t.nextByte(),text:t.readSubBlocks()};break;default:t.skipSubBlocks()}}(t,e,s,o);break;default:throw EvalError("undefined block found")}return!1}async function ox(t,e,i){i||(i=!1);let s=await fetch(t);if(!s.ok&&404===s.status)throw EvalError("file not found");let o=await s.arrayBuffer(),a={width:0,height:0,totalTime:0,colorRes:0,pixelAspectRatio:0,frames:[],sortFlag:!1,globalColorTable:[],backgroundImage:new ImageData(1,1,{colorSpace:"srgb"}),comments:[],applicationExtensions:[]},n=new om(new Uint8ClampedArray(o));if("GIF89a"!==n.getString(6))throw Error("not a supported GIF file");a.width=n.nextTwoBytes(),a.height=n.nextTwoBytes();let r=n.nextByte(),l=(128&r)==128;a.colorRes=(112&r)>>>4,a.sortFlag=(8&r)==8;let c=n.nextByte();a.pixelAspectRatio=n.nextByte(),0!==a.pixelAspectRatio&&(a.pixelAspectRatio=(a.pixelAspectRatio+15)/64),l&&(a.globalColorTable=ob(n,1<<(7&r)+1));let h=(()=>{try{return new ImageData(a.width,a.height,{colorSpace:"srgb"})}catch(t){if(t instanceof DOMException&&"IndexSizeError"===t.name)return null;throw t}})();if(null==h)throw Error("GIF frame size is to large");let{r:d,g:u,b:p}=a.globalColorTable[c];h.data.set(l?[d,u,p,255]:[0,0,0,0]);for(let t=4;t<h.data.length;t*=2)h.data.copyWithin(t,0,t);a.backgroundImage=h;let f=-1,y=!0,v=-1,m=t=>(t&&(y=!0),f),g=t=>(null!=t&&(v=t),v);try{do y&&(a.frames.push({left:0,top:0,width:0,height:0,disposalMethod:I.Replace,image:new ImageData(1,1,{colorSpace:"srgb"}),plainTextData:null,userInputDelayFlag:!1,delayTime:0,sortFlag:!1,localColorTable:[],reserved:0,GCreserved:0}),f++,v=-1,y=!1);while(!await o_(n,a,i,m,g,e));for(let t of(a.frames.length--,a.frames)){if(t.userInputDelayFlag&&0===t.delayTime){a.totalTime=1/0;break}a.totalTime+=t.delayTime}return a}catch(t){if(t instanceof EvalError)throw Error(`error while parsing frame ${f} "${t.message}"`);throw t}}async function ok(t){if("gif"!==t.type){await op(t);return}t.loading=!0;try{t.gifData=await ox(t.source),t.gifLoopCount=function(t){for(let e of t.applicationExtensions)if(e.identifier+e.authenticationCode==="NETSCAPE2.0")return e.data[1]+(e.data[2]<<8);return NaN}(t.gifData)??0,t.gifLoopCount||(t.gifLoopCount=1/0)}catch{t.error=!0}t.loading=!1}class oz{constructor(t){this.validTypes=["image","images"],this.loadImageShape=async t=>{if(!this._engine.loadImage)throw Error(`${U} image shape not initialized`);await this._engine.loadImage({gif:t.gif,name:t.name,replaceColor:t.replaceColor??!1,src:t.src})},this._engine=t}addImage(t){this._engine.images||(this._engine.images=[]),this._engine.images.push(t)}draw(t){let{context:e,radius:i,particle:s,opacity:o}=t,a=s.image,n=a?.element;if(a){if(e.globalAlpha=o,a.gif&&a.gifData)!function(t){let{context:e,radius:i,particle:s,delta:o}=t,a=s.image;if(!a?.gifData||!a.gif)return;let n=new OffscreenCanvas(a.gifData.width,a.gifData.height),r=n.getContext("2d");if(!r)throw Error("could not create offscreen canvas context");r.imageSmoothingQuality="low",r.imageSmoothingEnabled=!1,r.clearRect(og.x,og.y,n.width,n.height),void 0===s.gifLoopCount&&(s.gifLoopCount=a.gifLoopCount??0);let l=s.gifFrame??0,c={x:-(.5*a.gifData.width),y:-(.5*a.gifData.height)},h=a.gifData.frames[l];if(void 0===s.gifTime&&(s.gifTime=0),h.bitmap){switch(e.scale(i/a.gifData.width,i/a.gifData.height),h.disposalMethod){case I.UndefinedA:case I.UndefinedB:case I.UndefinedC:case I.UndefinedD:case I.Replace:r.drawImage(h.bitmap,h.left,h.top),e.drawImage(n,c.x,c.y),r.clearRect(og.x,og.y,n.width,n.height);break;case I.Combine:r.drawImage(h.bitmap,h.left,h.top),e.drawImage(n,c.x,c.y);break;case I.RestoreBackground:r.drawImage(h.bitmap,h.left,h.top),e.drawImage(n,c.x,c.y),r.clearRect(og.x,og.y,n.width,n.height),a.gifData.globalColorTable.length?r.putImageData(a.gifData.backgroundImage,c.x,c.y):r.putImageData(a.gifData.frames[0].image,c.x+h.left,c.y+h.top);break;case I.RestorePrevious:{let t=r.getImageData(og.x,og.y,n.width,n.height);r.drawImage(h.bitmap,h.left,h.top),e.drawImage(n,c.x,c.y),r.clearRect(og.x,og.y,n.width,n.height),r.putImageData(t,og.x,og.y)}}if(s.gifTime+=o.value,s.gifTime>h.delayTime){if(s.gifTime-=h.delayTime,++l>=a.gifData.frames.length){if(--s.gifLoopCount<=0)return;l=0,r.clearRect(og.x,og.y,n.width,n.height)}s.gifFrame=l}e.scale(a.gifData.width/i,a.gifData.height/i)}}(t);else if(n){let t=a.ratio,s={x:-i,y:-i},o=2*i;e.drawImage(n,s.x,s.y,o,o/t)}e.globalAlpha=1}}getSidesCount(){return 12}async init(t){let e=t.actualOptions;if(e.preload&&this._engine.loadImage)for(let t of e.preload)await this._engine.loadImage(t)}loadShape(t){if("image"!==t.shape&&"images"!==t.shape)return;this._engine.images||(this._engine.images=[]);let e=t.shapeData;e&&(this._engine.images.find(t=>t.name===e.name||t.source===e.src)||this.loadImageShape(e).then(()=>{this.loadShape(t)}))}particleInit(t,e){if("image"!==e.shape&&"images"!==e.shape)return;this._engine.images||(this._engine.images=[]);let i=this._engine.images,s=e.shapeData;if(!s)return;let o=e.getFillColor(),a=i.find(t=>t.name===s.name||t.source===s.src);if(!a)return;let n=s.replaceColor??a.replaceColor;if(a.loading){setTimeout(()=>{this.particleInit(t,e)});return}(async()=>{let t;(t=a.svgData&&o?await function(t,e,i,s){let o=function(t,e,i){let{svgData:s}=t;if(!s)return"";let o=tY(e,i);if(s.includes("fill"))return s.replace(ou,()=>o);let a=s.indexOf(">");return`${s.substring(0,a)} fill="${o}"${s.substring(a)}`}(t,i,s.opacity?.value??1),a={color:i,gif:e.gif,data:{...t,svgData:o},loaded:!1,ratio:e.width/e.height,replaceColor:e.replaceColor,source:e.src};return new Promise(e=>{let i=new Blob([o],{type:"image/svg+xml"}),s=URL||window.URL||window.webkitURL||window,n=s.createObjectURL(i),r=new Image;r.addEventListener("load",()=>{a.loaded=!0,a.element=r,e(a),s.revokeObjectURL(n)});let l=async()=>{s.revokeObjectURL(n);let i={...t,error:!1,loading:!0};await op(i),a.loaded=!0,a.element=i.element,e(a)};r.addEventListener("error",()=>void l()),r.src=n})}(a,s,o,e):{color:o,data:a,element:a.element,gif:a.gif,gifData:a.gifData,gifLoopCount:a.gifLoopCount,loaded:!0,ratio:s.width&&s.height?s.width/s.height:a.ratio??1,replaceColor:n,source:s.src}).ratio||(t.ratio=1);let i=s.fill??e.shapeFill,r=s.close??e.shapeClose;e.image=t,e.shapeFill=i,e.shapeClose=r})()}}class oP{constructor(){this.src="",this.gif=!1}load(t){te(t)||(void 0!==t.gif&&(this.gif=t.gif),void 0!==t.height&&(this.height=t.height),void 0!==t.name&&(this.name=t.name),void 0!==t.replaceColor&&(this.replaceColor=t.replaceColor),void 0!==t.src&&(this.src=t.src),void 0===t.width||(this.width=t.width))}}class oC{constructor(t){this.id="imagePreloader",this._engine=t}async getPlugin(){return await Promise.resolve(),{}}loadOptions(t,e){if(!e?.preload)return;t.preload||(t.preload=[]);let i=t.preload;for(let t of e.preload){let e=i.find(e=>e.name===t.name||e.src===t.src);if(e)e.load(t);else{let e=new oP;e.load(t),i.push(e)}}}needsPlugin(){return!0}}async function oM(t,e=!0){t.checkVersion("3.8.1"),t.loadImage||(t.loadImage=async e=>{if(!e.name&&!e.src)throw Error(`${U} no image source provided`);if(t.images||(t.images=[]),!t.images.find(t=>t.name===e.name||t.source===e.src))try{let i;let s={gif:e.gif??!1,name:e.name??e.src,source:e.src,type:e.src.substring(e.src.length-3),error:!1,loading:!0,replaceColor:e.replaceColor,ratio:e.width&&e.height?e.width/e.height:void 0};t.images.push(s),i=e.gif?ok:e.replaceColor?of:op,await i(s)}catch{throw Error(`${U} ${e.name??e.src} not found`)}});let i=new oC(t);await t.addPlugin(i,e),await t.addShape(new oz(t),e)}class oO extends eP{constructor(){super(),this.sync=!1}load(t){te(t)||(super.load(t),void 0===t.sync||(this.sync=t.sync))}}class oS extends eP{constructor(){super(),this.sync=!1}load(t){te(t)||(super.load(t),void 0===t.sync||(this.sync=t.sync))}}class oD{constructor(){this.count=0,this.delay=new oO,this.duration=new oS}load(t){te(t)||(void 0!==t.count&&(this.count=t.count),this.delay.load(t.delay),this.duration.load(t.duration))}}class oT{constructor(t){this.container=t}init(t){let e=this.container,i=t.options.life;i&&(t.life={delay:e.retina.reduceFactor?th(i.delay.value)*(i.delay.sync?1:tn())/e.retina.reduceFactor*1e3:0,delayTime:0,duration:e.retina.reduceFactor?th(i.duration.value)*(i.duration.sync?1:tn())/e.retina.reduceFactor*1e3:0,time:0,count:i.count},t.life.duration<=0&&(t.life.duration=-1),t.life.count<=0&&(t.life.count=-1),t.life&&(t.spawning=t.life.delay>0))}isEnabled(t){return!t.destroyed}loadOptions(t,...e){for(let i of(t.life||(t.life=new oD),e))t.life.load(i?.life)}update(t,e){this.isEnabled(t)&&t.life&&function(t,e,i){if(!t.life)return;let s=t.life,o=!1;if(t.spawning){if(s.delayTime+=e.value,!(s.delayTime>=t.life.delay))return;o=!0,t.spawning=!1,s.delayTime=0,s.time=0}if(-1===s.duration||t.spawning||(o?s.time=0:s.time+=e.value,s.time<s.duration))return;if(s.time=0,t.life.count>0&&t.life.count--,0===t.life.count){t.destroy();return}let a=tp(0,i.width),n=tp(0,i.width);t.position.x=tc(a),t.position.y=tc(n),t.spawning=!0,s.delayTime=0,s.time=0,t.reset();let r=t.options.life;r&&(s.delay=1e3*th(r.delay.value),s.duration=1e3*th(r.duration.value))}(t,e,this.container.canvas.size)}}async function oE(t,e=!0){t.checkVersion("3.8.1"),await t.addParticleUpdater("life",async t=>Promise.resolve(new oT(t)),e)}class oR{constructor(){this.validTypes=["line"]}draw(t){!function(t){let{context:e,particle:i,radius:s}=t,o=i.shapeData;e.moveTo(-s,0),e.lineTo(s,0),e.lineCap=o?.cap??"butt"}(t)}getSidesCount(){return 1}}async function oI(t,e=!0){t.checkVersion("3.8.1"),await t.addShape(new oR,e)}class oL{init(){}isEnabled(t){return!tk()&&!t.destroyed&&t.container.actualOptions.interactivity.events.onHover.parallax.enable}move(t){let e=t.container,i=e.actualOptions.interactivity.events.onHover.parallax;if(tk()||!i.enable)return;let s=i.force,o=e.interactivity.mouse.position;if(!o)return;let a=e.canvas.size,n={x:.5*a.width,y:.5*a.height},r=i.smooth,l=t.getRadius()/s,c={x:(o.x-n.x)*l,y:(o.y-n.y)*l},{offset:h}=t;h.x+=(c.x-h.x)/r,h.y+=(c.y-h.y)/r}}async function oF(t,e=!0){t.checkVersion("3.8.1"),await t.addMover("parallax",()=>Promise.resolve(new oL),e)}class oB extends iv{constructor(t){super(t)}clear(){}init(){}interact(t){let e=this.container;void 0===t.attractDistance&&(t.attractDistance=th(t.options.move.attract.distance)*e.retina.pixelRatio);let i=t.attractDistance,s=t.getPosition();for(let o of e.particles.quadTree.queryCircle(s,i)){if(t===o||!o.options.move.attract.enable||o.destroyed||o.spawning)continue;let{dx:e,dy:i}=tf(s,o.getPosition()),a=t.options.move.attract.rotate,n=e/(1e3*a.x),r=i/(1e3*a.y),l=o.size.value/t.size.value,c=1/l;t.velocity.x-=n*l,t.velocity.y-=r*l,o.velocity.x+=n*c,o.velocity.y+=r*c}}isEnabled(t){return t.options.move.attract.enable}reset(){}}async function oA(t,e=!0){t.checkVersion("3.8.1"),await t.addInteractor("particlesAttract",t=>Promise.resolve(new oB(t)),e)}function oq(t,e,i,s,o,a){let n=tr(t.options.collisions.absorb.speed*o.factor/10,0,s);t.size.value+=.5*n,i.size.value-=n,s<=a&&(i.size.value=0,i.destroy())}let oV=t=>{void 0===t.collisionMaxSpeed&&(t.collisionMaxSpeed=th(t.options.collisions.maxSpeed)),t.velocity.length>t.collisionMaxSpeed&&(t.velocity.length=t.collisionMaxSpeed)};function oU(t,e){tF(tL(t),tL(e)),oV(t),oV(e)}class oH extends iv{constructor(t){super(t)}clear(){}init(){}interact(t,e){if(t.destroyed||t.spawning)return;let i=this.container,s=t.getPosition(),o=t.getRadius();for(let a of i.particles.quadTree.queryCircle(s,2*o)){if(t===a||!a.options.collisions.enable||t.options.collisions.mode!==a.options.collisions.mode||a.destroyed||a.spawning)continue;let n=a.getPosition(),r=a.getRadius();!(Math.abs(Math.round(s.z)-Math.round(n.z))>o+r)&&(ty(s,n)>o+r||function(t,e,i,s){switch(t.options.collisions.mode){case v.absorb:!function(t,e,i,s){let o=t.getRadius(),a=e.getRadius();void 0===o&&void 0!==a?t.destroy():void 0!==o&&void 0===a?e.destroy():void 0!==o&&void 0!==a&&(o>=a?oq(t,o,e,a,i,s):oq(e,a,t,o,i,s))}(t,e,i,s);break;case v.bounce:oU(t,e);break;case v.destroy:t.unbreakable||e.unbreakable||oU(t,e),void 0===t.getRadius()&&void 0!==e.getRadius()?t.destroy():void 0!==t.getRadius()&&void 0===e.getRadius()?e.destroy():void 0!==t.getRadius()&&void 0!==e.getRadius()&&(t.getRadius()>=e.getRadius()?e:t).destroy()}}(t,a,e,i.retina.pixelRatio))}}isEnabled(t){return t.options.collisions.enable}reset(){}}async function o$(t,e=!0){t.checkVersion("3.8.1"),await t.addInteractor("particlesCollisions",t=>Promise.resolve(new oH(t)),e)}class oW extends e9{constructor(t,e,i,s){super(t,e,i),this.canvasSize=s,this.canvasSize={...s}}contains(t){let{width:e,height:i}=this.canvasSize,{x:s,y:o}=t;return super.contains(t)||super.contains({x:s-e,y:o})||super.contains({x:s-e,y:o-i})||super.contains({x:s,y:o-i})}intersects(t){if(super.intersects(t))return!0;let e={x:t.position.x-this.canvasSize.width,y:t.position.y-this.canvasSize.height};if(void 0!==t.radius){let i=new e9(e.x,e.y,2*t.radius);return super.intersects(i)}if(void 0!==t.size){let i=new e7(e.x,e.y,2*t.size.width,2*t.size.height);return super.intersects(i)}return!1}}class oG{constructor(){this.blur=5,this.color=new ee,this.color.value="#000",this.enable=!1}load(t){te(t)||(void 0!==t.blur&&(this.blur=t.blur),this.color=ee.create(this.color,t.color),void 0===t.enable||(this.enable=t.enable))}}class oj{constructor(){this.enable=!1,this.frequency=1}load(t){te(t)||(void 0!==t.color&&(this.color=ee.create(this.color,t.color)),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.frequency&&(this.frequency=t.frequency),void 0===t.opacity||(this.opacity=t.opacity))}}class oQ{constructor(){this.blink=!1,this.color=new ee,this.color.value="#fff",this.consent=!1,this.distance=100,this.enable=!1,this.frequency=1,this.opacity=1,this.shadow=new oG,this.triangles=new oj,this.width=1,this.warp=!1}load(t){te(t)||(void 0!==t.id&&(this.id=t.id),void 0!==t.blink&&(this.blink=t.blink),this.color=ee.create(this.color,t.color),void 0!==t.consent&&(this.consent=t.consent),void 0!==t.distance&&(this.distance=t.distance),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.frequency&&(this.frequency=t.frequency),void 0!==t.opacity&&(this.opacity=t.opacity),this.shadow.load(t.shadow),this.triangles.load(t.triangles),void 0!==t.width&&(this.width=t.width),void 0===t.warp||(this.warp=t.warp))}}let oN={x:0,y:0};class oX extends iv{constructor(t,e){super(t),this._setColor=t=>{if(!t.options.links)return;let e=this._linkContainer,i=t.options.links,s=void 0===i.id?e.particles.linksColor:e.particles.linksColors.get(i.id);if(s)return;let o=i.color;s=tJ(this._engine,o,i.blink,i.consent),void 0===i.id?e.particles.linksColor=s:e.particles.linksColors.set(i.id,s)},this._linkContainer=t,this._engine=e}clear(){}init(){this._linkContainer.particles.linksColor=void 0,this._linkContainer.particles.linksColors=new Map}interact(t){let e;if(!t.options.links)return;t.links=[];let i=t.getPosition(),s=this.container,o=s.canvas.size;if(i.x<oN.x||i.y<oN.y||i.x>o.width||i.y>o.height)return;let a=t.options.links,n=a.opacity,r=t.retina.linksDistance??0,l=a.warp;for(let c of(e=l?new oW(i.x,i.y,r,o):new e9(i.x,i.y,r),s.particles.quadTree.query(e))){let e=c.options.links;if(t===c||!e?.enable||a.id!==e.id||c.spawning||c.destroyed||!c.links||t.links.some(t=>t.destination===c)||c.links.some(e=>e.destination===t))continue;let s=c.getPosition();if(s.x<oN.x||s.y<oN.y||s.x>o.width||s.y>o.height)continue;let h=function(t,e,i,s,o){let{dx:a,dy:n,distance:r}=tf(t,e);if(!o||r<=i)return r;let l={x:Math.abs(a),y:Math.abs(n)},c={x:Math.min(l.x,s.width-l.x),y:Math.min(l.y,s.height-l.y)};return Math.sqrt(c.x**2+c.y**2)}(i,s,r,o,l&&e.warp);if(h>r)continue;let d=(1-h/r)*n;this._setColor(t),t.links.push({destination:c,opacity:d})}}isEnabled(t){return!!t.options.links?.enable}loadParticlesOptions(t,...e){for(let i of(t.links||(t.links=new oQ),e))t.links.load(i?.links)}reset(){}}async function oY(t,e=!0){await t.addInteractor("particlesLinks",async e=>Promise.resolve(new oX(e,t)),e)}function oZ(t,e){var i;let s=((i=t.map(t=>t.id)).sort((t,e)=>t-e),i.join("_")),o=e.get(s);return void 0===o&&(o=tn(),e.set(s,o)),o}class oK{constructor(t,e){this._drawLinkLine=(t,e)=>{let i=t.options.links;if(!i?.enable)return;let s=this._container,o=s.actualOptions,a=e.destination,n=t.getPosition(),r=a.getPosition(),l=e.opacity;s.canvas.draw(e=>{let c;let h=t.options.twinkle?.lines;if(h?.enable){let t=h.frequency,e=tW(this._engine,h.color);tn()<t&&e&&(c=e,l=th(h.opacity))}if(c||(c=tK(t,a,void 0!==i.id?s.particles.linksColors.get(i.id):s.particles.linksColor)),!c)return;let d=t.retina.linksWidth??0,u=t.retina.linksDistance??0,{backgroundMask:p}=o;!function(t){let e=!1,{begin:i,end:s,engine:o,maxDistance:a,context:n,canvasSize:r,width:l,backgroundMask:c,colorLine:h,opacity:d,links:u}=t;if(ty(i,s)<=a)t8(n,i,s),e=!0;else if(u.warp){let t,o;let l=tf(i,{x:s.x-r.width,y:s.y});if(l.distance<=a){let e=i.y-l.dy/l.dx*i.x;t={x:0,y:e},o={x:r.width,y:e}}else{let e=tf(i,{x:s.x,y:s.y-r.height});if(e.distance<=a){let s=-(i.y-e.dy/e.dx*i.x)/(e.dy/e.dx);t={x:s,y:0},o={x:s,y:r.height}}else{let e=tf(i,{x:s.x-r.width,y:s.y-r.height});if(e.distance<=a){let s=i.y-e.dy/e.dx*i.x;o={x:(t={x:-s/(e.dy/e.dx),y:s}).x+r.width,y:t.y+r.height}}}}t&&o&&(t8(n,i,t),t8(n,s,o),e=!0)}if(!e)return;n.lineWidth=l,c.enable&&(n.globalCompositeOperation=c.composite),n.strokeStyle=tX(h,d);let{shadow:p}=u;if(p.enable){let t=tW(o,p.color);t&&(n.shadowBlur=p.blur,n.shadowColor=tX(t))}n.stroke()}({context:e,width:d,begin:n,end:r,engine:this._engine,maxDistance:u,canvasSize:s.canvas.size,links:i,backgroundMask:p,colorLine:c,opacity:l})})},this._drawLinkTriangle=(t,e,i)=>{let s=t.options.links;if(!s?.enable)return;let o=s.triangles;if(!o.enable)return;let a=this._container,n=a.actualOptions,r=e.destination,l=i.destination,c=o.opacity??(e.opacity+i.opacity)*.5;c<=0||a.canvas.draw(e=>{let i=t.getPosition(),h=r.getPosition(),d=l.getPosition(),u=t.retina.linksDistance??0;if(ty(i,h)>u||ty(d,h)>u||ty(d,i)>u)return;let p=tW(this._engine,o.color);p||(p=tK(t,r,void 0!==s.id?a.particles.linksColors.get(s.id):a.particles.linksColor)),p&&function(t){let{context:e,pos1:i,pos2:s,pos3:o,backgroundMask:a,colorTriangle:n,opacityTriangle:r}=t;e.beginPath(),e.moveTo(i.x,i.y),e.lineTo(s.x,s.y),e.lineTo(o.x,o.y),e.closePath(),a.enable&&(e.globalCompositeOperation=a.composite),e.fillStyle=tX(n,r),e.fill()}({context:e,pos1:i,pos2:h,pos3:d,backgroundMask:n.backgroundMask,colorTriangle:p,opacityTriangle:c})})},this._drawTriangles=(t,e,i,s)=>{let o=i.destination;if(!(t.links?.triangles.enable&&o.options.links?.triangles.enable))return;let a=o.links?.filter(t=>{let e=this._getLinkFrequency(o,t.destination);return o.options.links&&e<=o.options.links.frequency&&s.findIndex(e=>e.destination===t.destination)>=0});if(a?.length)for(let s of a){let a=s.destination;this._getTriangleFrequency(e,o,a)>t.links.triangles.frequency||this._drawLinkTriangle(e,i,s)}},this._getLinkFrequency=(t,e)=>oZ([t,e],this._freqs.links),this._getTriangleFrequency=(t,e,i)=>oZ([t,e,i],this._freqs.triangles),this._container=t,this._engine=e,this._freqs={links:new Map,triangles:new Map}}drawParticle(t,e){let{links:i,options:s}=e;if(!i?.length)return;let o=i.filter(t=>s.links&&(s.links.frequency>=1||this._getLinkFrequency(e,t.destination)<=s.links.frequency));for(let t of o)this._drawTriangles(s,e,t,o),t.opacity>0&&(e.retina.linksWidth??0)>0&&this._drawLinkLine(e,t)}async init(){this._freqs.links=new Map,this._freqs.triangles=new Map,await Promise.resolve()}particleCreated(t){if(t.links=[],!t.options.links)return;let e=this._container.retina.pixelRatio,{retina:i}=t,{distance:s,width:o}=t.options.links;i.linksDistance=s*e,i.linksWidth=o*e}particleDestroyed(t){t.links=[]}}class oJ{constructor(t){this.id="links",this._engine=t}getPlugin(t){return Promise.resolve(new oK(t,this._engine))}loadOptions(){}needsPlugin(){return!0}}async function o0(t,e=!0){let i=new oJ(t);await t.addPlugin(i,e)}async function o1(t,e=!0){t.checkVersion("3.8.1"),await oY(t,e),await o0(t,e)}let o2={x:0,y:0};class o3{draw(t){let{particle:e,radius:i}=t;!function(t,e,i){let{context:s}=t,o=i.count.numerator*i.count.denominator,a=i.count.numerator/i.count.denominator,n=Math.PI-tv(180*(a-2)/a);if(s){s.beginPath(),s.translate(e.x,e.y),s.moveTo(o2.x,o2.y);for(let t=0;t<o;t++)s.lineTo(i.length,o2.y),s.translate(i.length,o2.y),s.rotate(n)}}(t,this.getCenter(e,i),this.getSidesData(e,i))}getSidesCount(t){let e=t.shapeData;return Math.round(th(e?.sides??5))}}class o5 extends o3{constructor(){super(...arguments),this.validTypes=["polygon"]}getCenter(t,e){return{x:-e/(t.sides/3.5),y:-e/.76}}getSidesData(t,e){let i=t.sides;return{count:{denominator:1,numerator:i},length:2.66*e/(i/3)}}}class o8 extends o3{constructor(){super(...arguments),this.validTypes=["triangle"]}getCenter(t,e){return{x:-e,y:e/1.66}}getSidesCount(){return 3}getSidesData(t,e){return{count:{denominator:2,numerator:3},length:2*e}}}async function o6(t,e=!0){t.checkVersion("3.8.1"),await t.addShape(new o5,e)}async function o4(t,e=!0){t.checkVersion("3.8.1"),await t.addShape(new o8,e)}async function o9(t,e=!0){t.checkVersion("3.8.1"),await o6(t,e),await o4(t,e)}class o7{constructor(){this.enable=!1,this.speed=0,this.decay=0,this.sync=!1}load(t){te(t)||(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.speed&&(this.speed=tp(t.speed)),void 0!==t.decay&&(this.decay=tp(t.decay)),void 0===t.sync||(this.sync=t.sync))}}class at extends eP{constructor(){super(),this.animation=new o7,this.direction=x.clockwise,this.path=!1,this.value=0}load(t){te(t)||(super.load(t),void 0!==t.direction&&(this.direction=t.direction),this.animation.load(t.animation),void 0===t.path||(this.path=t.path))}}let ae=2*Math.PI;class ai{constructor(t){this.container=t}init(t){let e=t.options.rotate;if(!e)return;t.rotate={enable:e.animation.enable,value:tv(th(e.value)),min:0,max:ae},t.pathRotation=e.path;let i=e.direction;switch(i===x.random&&(i=Math.floor(2*tn())>0?x.counterClockwise:x.clockwise),i){case x.counterClockwise:case"counterClockwise":t.rotate.status=a.decreasing;break;case x.clockwise:t.rotate.status=a.increasing}let s=e.animation;s.enable&&(t.rotate.decay=1-th(s.decay),t.rotate.velocity=th(s.speed)/360*this.container.retina.reduceFactor,s.sync||(t.rotate.velocity*=tn())),t.rotation=t.rotate.value}isEnabled(t){let e=t.options.rotate;return!!e&&!t.destroyed&&!t.spawning&&(!!e.value||e.animation.enable||e.path)}loadOptions(t,...e){for(let i of(t.rotate||(t.rotate=new at),e))t.rotate.load(i?.rotate)}update(t,e){this.isEnabled(t)&&(t.isRotating=!!t.rotate,t.rotate&&(tH(t,t.rotate,!1,n.none,e),t.rotation=t.rotate.value))}}async function as(t,e=!0){t.checkVersion("3.8.1"),await t.addParticleUpdater("rotate",t=>Promise.resolve(new ai(t)),e)}let ao=Math.sqrt(2);class aa{constructor(){this.validTypes=["edge","square"]}draw(t){!function(t){let{context:e,radius:i}=t,s=i/ao,o=2*s;e.rect(-s,-s,o,o)}(t)}getSidesCount(){return 4}}async function an(t,e=!0){t.checkVersion("3.8.1"),await t.addShape(new aa,e)}let ar={x:0,y:0};class al{constructor(){this.validTypes=["star"]}draw(t){!function(t){let{context:e,particle:i,radius:s}=t,o=i.sides,a=i.starInset??2;e.moveTo(ar.x,ar.y-s);for(let t=0;t<o;t++)e.rotate(Math.PI/o),e.lineTo(ar.x,ar.y-s*a),e.rotate(Math.PI/o),e.lineTo(ar.x,ar.y-s)}(t)}getSidesCount(t){let e=t.shapeData;return Math.round(th(e?.sides??5))}particleInit(t,e){let i=e.shapeData;e.starInset=th(i?.inset??2)}}async function ac(t,e=!0){t.checkVersion("3.8.1"),await t.addShape(new al,e)}class ah{constructor(t,e){this._container=t,this._engine=e}init(t){let e=this._container,i=t.options,s=tA(i.stroke,t.id,i.reduceDuplicates);t.strokeWidth=th(s.width)*e.retina.pixelRatio,t.strokeOpacity=th(s.opacity??1),t.strokeAnimation=s.color?.animation;let o=tG(this._engine,s.color)??t.getFillColor();o&&(t.strokeColor=t1(o,t.strokeAnimation,e.retina.reduceFactor))}isEnabled(t){let e=t.strokeAnimation,{strokeColor:i}=t;return!t.destroyed&&!t.spawning&&!!e&&(i?.h.value!==void 0&&i.h.enable||i?.s.value!==void 0&&i.s.enable||i?.l.value!==void 0&&i.l.enable)}update(t,e){this.isEnabled(t)&&t5(t.strokeColor,e)}}async function ad(t,e=!0){t.checkVersion("3.8.1"),await t.addParticleUpdater("strokeColor",e=>Promise.resolve(new ah(e,t)),e)}async function au(t,e=!0){t.checkVersion("3.8.1"),await oF(t,!1),await sB(t,!1),await s$(t,!1),await sY(t,!1),await s0(t,!1),await s5(t,!1),await s6(t,!1),await s7(t,!1),await oi(t,!1),await ol(t,!1),await od(t,!1),await oA(t,!1),await o$(t,!1),await o1(t,!1),await sS(t,!1),await sE(t,!1),await oM(t,!1),await oI(t,!1),await o9(t,!1),await an(t,!1),await ac(t,!1),await oE(t,!1),await as(t,!1),await ad(t,!1),await sO(t,e)}class ap{constructor(){this.validTypes=["text","character","char","multiline-text"]}draw(t){!function(t){let{context:e,particle:i,radius:s,opacity:o}=t,a=i.shapeData;if(!a)return;let n=a.value;if(void 0===n)return;void 0===i.text&&(i.text=tA(n,i.randomIndexData));let r=i.text,l=a.style??"",c=a.weight??"400",h=2*Math.round(s),d=a.font??"Verdana",u=i.shapeFill,p=r?.split("\n");if(p){e.font=`${l} ${c} ${h}px "${d}"`,e.globalAlpha=o;for(let t=0;t<p.length;t++)(function(t,e,i,s,o,a){let n={x:-(e.length*i*.5),y:.5*i},r=2*i;a?t.fillText(e,n.x,n.y+r*o):t.strokeText(e,n.x,n.y+r*o)})(e,p[t],s,0,t,u);e.globalAlpha=1}}(t)}async init(t){let e=t.actualOptions,{validTypes:i}=this;if(i.find(t=>tP(t,e.particles.shape.type))){let t=i.map(t=>e.particles.shape.options[t]).find(t=>!!t),s=[];tB(t,t=>{s.push(tC(t.font,t.weight))}),await Promise.all(s)}}particleInit(t,e){if(!e.shape||!this.validTypes.includes(e.shape))return;let i=e.shapeData;if(void 0===i)return;let s=i.value;void 0!==s&&(e.text=tA(s,e.randomIndexData))}}async function af(t,e=!0){t.checkVersion("3.8.1"),await t.addShape(new ap,e)}!function(t){t.clockwise="clockwise",t.counterClockwise="counter-clockwise",t.random="random"}(F||(F={}));class ay{constructor(){this.enable=!1,this.speed=0,this.decay=0,this.sync=!1}load(t){te(t)||(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.speed&&(this.speed=tp(t.speed)),void 0!==t.decay&&(this.decay=tp(t.decay)),void 0===t.sync||(this.sync=t.sync))}}class av extends eP{constructor(){super(),this.animation=new ay,this.direction=F.clockwise,this.enable=!1,this.value=0}load(t){super.load(t),te(t)||(this.animation.load(t.animation),void 0!==t.direction&&(this.direction=t.direction),void 0===t.enable||(this.enable=t.enable))}}let am=2*Math.PI;class ag{constructor(t){this.container=t}getTransformValues(t){let e=t.tilt?.enable&&t.tilt;return{b:e?Math.cos(e.value)*e.cosDirection:void 0,c:e?Math.sin(e.value)*e.sinDirection:void 0}}init(t){let e=t.options.tilt;if(!e)return;t.tilt={enable:e.enable,value:tv(th(e.value)),sinDirection:tn()>=.5?1:-1,cosDirection:tn()>=.5?1:-1,min:0,max:am};let i=e.direction;switch(i===F.random&&(i=Math.floor(2*tn())>0?F.counterClockwise:F.clockwise),i){case F.counterClockwise:case"counterClockwise":t.tilt.status=a.decreasing;break;case F.clockwise:t.tilt.status=a.increasing}let s=t.options.tilt?.animation;s?.enable&&(t.tilt.decay=1-th(s.decay),t.tilt.velocity=th(s.speed)/360*this.container.retina.reduceFactor,s.sync||(t.tilt.velocity*=tn()))}isEnabled(t){let e=t.options.tilt?.animation;return!t.destroyed&&!t.spawning&&!!e?.enable}loadOptions(t,...e){for(let i of(t.tilt||(t.tilt=new av),e))t.tilt.load(i?.tilt)}async update(t,e){this.isEnabled(t)&&t.tilt&&(tH(t,t.tilt,!1,n.none,e),await Promise.resolve())}}async function ab(t,e=!0){t.checkVersion("3.8.1"),await t.addParticleUpdater("tilt",t=>Promise.resolve(new ag(t)),e)}class aw{constructor(){this.enable=!1,this.frequency=.05,this.opacity=1}load(t){te(t)||(void 0!==t.color&&(this.color=ee.create(this.color,t.color)),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.frequency&&(this.frequency=t.frequency),void 0!==t.opacity&&(this.opacity=tp(t.opacity)))}}class a_{constructor(){this.lines=new aw,this.particles=new aw}load(t){te(t)||(this.lines.load(t.lines),this.particles.load(t.particles))}}class ax{constructor(t){this._engine=t}getColorStyles(t,e,i,s){let o=t.options.twinkle;if(!o)return{};let a=o.particles,n=a.enable&&tn()<a.frequency,r=t.options.zIndex,l=(1-t.zIndexFactor)**r.opacityRate,c=n?th(a.opacity)*l:s,h=tG(this._engine,a.color),d=h?tY(h,c):void 0,u={},p=n&&d;return u.fill=p?d:void 0,u.stroke=p?d:void 0,u}async init(){await Promise.resolve()}isEnabled(t){let e=t.options.twinkle;return!!e&&e.particles.enable}loadOptions(t,...e){for(let i of(t.twinkle||(t.twinkle=new a_),e))t.twinkle.load(i?.twinkle)}async update(){await Promise.resolve()}}async function ak(t,e=!0){t.checkVersion("3.8.1"),await t.addParticleUpdater("twinkle",()=>Promise.resolve(new ax(t)),e)}class az{constructor(){this.angle=50,this.move=10}load(t){te(t)||(void 0!==t.angle&&(this.angle=tp(t.angle)),void 0!==t.move&&(this.move=tp(t.move)))}}class aP{constructor(){this.distance=5,this.enable=!1,this.speed=new az}load(t){if(!te(t)&&(void 0!==t.distance&&(this.distance=tp(t.distance)),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.speed)){if(K(t.speed))this.speed.load({angle:t.speed});else{let e=t.speed;void 0!==e.min?this.speed.load({angle:e}):this.speed.load(t.speed)}}}}let aC=2*Math.PI,aM=2*Math.PI;class aO{constructor(t){this.container=t}init(t){let e=t.options.wobble;e?.enable?t.wobble={angle:tn()*aM,angleSpeed:th(e.speed.angle)/360,moveSpeed:th(e.speed.move)/10}:t.wobble={angle:0,angleSpeed:0,moveSpeed:0},t.retina.wobbleDistance=th(e?.distance??0)*this.container.retina.pixelRatio}isEnabled(t){return!t.destroyed&&!t.spawning&&!!t.options.wobble?.enable}loadOptions(t,...e){for(let i of(t.wobble||(t.wobble=new aP),e))t.wobble.load(i?.wobble)}update(t,e){this.isEnabled(t)&&function(t,e){let{wobble:i}=t.options,{wobble:s}=t;if(!i?.enable||!s)return;let o=s.angleSpeed*e.factor,a=s.moveSpeed*e.factor*((t.retina.wobbleDistance??0)*e.factor)/(1e3/60),{position:n}=t;s.angle+=o,s.angle>aC&&(s.angle-=aC),n.x+=a*Math.cos(s.angle),n.y+=a*Math.abs(Math.sin(s.angle))}(t,e)}}async function aS(t,e=!0){t.checkVersion("3.8.1"),await t.addParticleUpdater("wobble",t=>Promise.resolve(new aO(t)),e)}async function aD(t,e=!0){t.checkVersion("3.8.1"),await iE(t,!1),await i7(t,!1),await ab(t,!1),await ak(t,!1),await aS(t,!1),await af(t,!1),await i5(t,!1),await iP(t,!1),await iQ(t,!1),await iZ(t,!1),await i0(t,!1),await au(t,e)}}};