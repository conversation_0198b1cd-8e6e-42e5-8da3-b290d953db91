"use strict";exports.id=310,exports.ids=[310],exports.modules={2627:function(e,t,r){var n=this&&this.__assign||function(){return(n=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},o=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),u=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&o(t,e,r);return i(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.ImageUrlBuilder=void 0;var s=u(r(80501)),a=["clip","crop","fill","fillmax","max","scale","min"],c=["top","bottom","left","right","center","focalpoint","entropy"],l=["format"];t.default=function(e){if(e&&"config"in e&&"function"==typeof e.config){var t=e.config(),r=t.apiHost,n=t.projectId,o=t.dataset,i=r||"https://api.sanity.io";return new f(null,{baseUrl:i.replace(/^https:\/\/api\./,"https://cdn."),projectId:n,dataset:o})}if(e&&"clientConfig"in e&&"object"==typeof e.clientConfig){var u=e.clientConfig,r=u.apiHost,n=u.projectId,o=u.dataset,i=r||"https://api.sanity.io";return new f(null,{baseUrl:i.replace(/^https:\/\/api\./,"https://cdn."),projectId:n,dataset:o})}return new f(null,e||{})};var f=function(){function e(e,t){this.options=e?n(n({},e.options||{}),t||{}):n({},t||{})}return e.prototype.withOptions=function(t){var r=t.baseUrl||this.options.baseUrl,o={baseUrl:r};for(var i in t)t.hasOwnProperty(i)&&(o[function(e){for(var t=s.SPEC_NAME_TO_URL_NAME_MAPPINGS,r=0;r<t.length;r++){var n=t[r],o=n[0],i=n[1];if(e===o||e===i)return o}return e}(i)]=t[i]);return new e(this,n({baseUrl:r},o))},e.prototype.image=function(e){return this.withOptions({source:e})},e.prototype.dataset=function(e){return this.withOptions({dataset:e})},e.prototype.projectId=function(e){return this.withOptions({projectId:e})},e.prototype.bg=function(e){return this.withOptions({bg:e})},e.prototype.dpr=function(e){return this.withOptions(e&&1!==e?{dpr:e}:{})},e.prototype.width=function(e){return this.withOptions({width:e})},e.prototype.height=function(e){return this.withOptions({height:e})},e.prototype.focalPoint=function(e,t){return this.withOptions({focalPoint:{x:e,y:t}})},e.prototype.maxWidth=function(e){return this.withOptions({maxWidth:e})},e.prototype.minWidth=function(e){return this.withOptions({minWidth:e})},e.prototype.maxHeight=function(e){return this.withOptions({maxHeight:e})},e.prototype.minHeight=function(e){return this.withOptions({minHeight:e})},e.prototype.size=function(e,t){return this.withOptions({width:e,height:t})},e.prototype.blur=function(e){return this.withOptions({blur:e})},e.prototype.sharpen=function(e){return this.withOptions({sharpen:e})},e.prototype.rect=function(e,t,r,n){return this.withOptions({rect:{left:e,top:t,width:r,height:n}})},e.prototype.format=function(e){return this.withOptions({format:e})},e.prototype.invert=function(e){return this.withOptions({invert:e})},e.prototype.orientation=function(e){return this.withOptions({orientation:e})},e.prototype.quality=function(e){return this.withOptions({quality:e})},e.prototype.forceDownload=function(e){return this.withOptions({download:e})},e.prototype.flipHorizontal=function(){return this.withOptions({flipHorizontal:!0})},e.prototype.flipVertical=function(){return this.withOptions({flipVertical:!0})},e.prototype.ignoreImageParams=function(){return this.withOptions({ignoreImageParams:!0})},e.prototype.fit=function(e){if(-1===a.indexOf(e))throw Error('Invalid fit mode "'.concat(e,'"'));return this.withOptions({fit:e})},e.prototype.crop=function(e){if(-1===c.indexOf(e))throw Error('Invalid crop mode "'.concat(e,'"'));return this.withOptions({crop:e})},e.prototype.saturation=function(e){return this.withOptions({saturation:e})},e.prototype.auto=function(e){if(-1===l.indexOf(e))throw Error('Invalid auto mode "'.concat(e,'"'));return this.withOptions({auto:e})},e.prototype.pad=function(e){return this.withOptions({pad:e})},e.prototype.vanityName=function(e){return this.withOptions({vanityName:e})},e.prototype.frame=function(e){if(1!==e)throw Error('Invalid frame value "'.concat(e,'"'));return this.withOptions({frame:e})},e.prototype.url=function(){return(0,s.default)(this.options)},e.prototype.toString=function(){return this.url()},e}();t.ImageUrlBuilder=f},25e3:function(e,t,r){var n=(this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}})(r(2627));e.exports=n.default},4622:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});var r="image-Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000-jpg";t.default=function(e){var t=e.split("-"),n=t[1],o=t[2],i=t[3];if(!n||!o||!i)throw Error("Malformed asset _ref '".concat(e,"'. Expected an id like \"").concat(r,'".'));var u=o.split("x"),s=u[0],a=u[1],c=+s,l=+a;if(!(isFinite(c)&&isFinite(l)))throw Error("Malformed asset _ref '".concat(e,"'. Expected an id like \"").concat(r,'".'));return{id:n,width:c,height:l,format:i}}},85782:function(e,t){var r=this&&this.__assign||function(){return(r=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function n(e){var t=e.split("/").slice(-1);return"image-".concat(t[0]).replace(/\.([a-z]+)$/,"-$1")}Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t;if(!e)return null;if("string"==typeof e&&/^https?:\/\//.test("".concat(e)))t={asset:{_ref:n(e)}};else if("string"==typeof e)t={asset:{_ref:e}};else if(e&&"string"==typeof e._ref)t={asset:e};else if(e&&"string"==typeof e._id)t={asset:{_ref:e._id||""}};else if(e&&e.asset&&"string"==typeof e.asset.url)t={asset:{_ref:n(e.asset.url)}};else{if("object"!=typeof e.asset)return null;t=r({},e)}return e.crop&&(t.crop=e.crop),e.hotspot&&(t.hotspot=e.hotspot),function(e){if(e.crop&&e.hotspot)return e;var t=r({},e);return t.crop||(t.crop={left:0,top:0,bottom:0,right:0}),t.hotspot||(t.hotspot={x:.5,y:.5,height:1,width:1}),t}(t)}},80501:function(e,t,r){var n=this&&this.__assign||function(){return(n=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},o=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.parseSource=t.SPEC_NAME_TO_URL_NAME_MAPPINGS=void 0;var i=o(r(4622)),u=o(r(85782));t.parseSource=u.default,t.SPEC_NAME_TO_URL_NAME_MAPPINGS=[["width","w"],["height","h"],["format","fm"],["download","dl"],["blur","blur"],["sharpen","sharp"],["invert","invert"],["orientation","or"],["minHeight","min-h"],["maxHeight","max-h"],["minWidth","min-w"],["maxWidth","max-w"],["quality","q"],["fit","fit"],["crop","crop"],["saturation","sat"],["auto","auto"],["dpr","dpr"],["pad","pad"],["frame","frame"]],t.default=function(e){var r=n({},e||{}),o=r.source;delete r.source;var s=(0,u.default)(o);if(!s)throw Error("Unable to resolve image URL from source (".concat(JSON.stringify(o),")"));var a=s.asset._ref||s.asset._id||"",c=(0,i.default)(a),l=Math.round(s.crop.left*c.width),f=Math.round(s.crop.top*c.height),p={left:l,top:f,width:Math.round(c.width-s.crop.right*c.width-l),height:Math.round(c.height-s.crop.bottom*c.height-f)},d=s.hotspot.height*c.height/2,h=s.hotspot.width*c.width/2,v=s.hotspot.x*c.width,b=s.hotspot.y*c.height;return r.rect||r.focalPoint||r.ignoreImageParams||r.crop||(r=n(n({},r),function(e,t){var r,n=t.width,o=t.height;if(!(n&&o))return{width:n,height:o,rect:e.crop};var i=e.crop,u=e.hotspot,s=n/o;if(i.width/i.height>s){var a=Math.round(i.height),c=Math.round(a*s),l=Math.max(0,Math.round(i.top)),f=Math.max(0,Math.round(Math.round((u.right-u.left)/2+u.left)-c/2));f<i.left?f=i.left:f+c>i.left+i.width&&(f=i.left+i.width-c),r={left:f,top:l,width:c,height:a}}else{var c=i.width,a=Math.round(c/s),f=Math.max(0,Math.round(i.left)),p=Math.max(0,Math.round(Math.round((u.bottom-u.top)/2+u.top)-a/2));p<i.top?p=i.top:p+a>i.top+i.height&&(p=i.top+i.height-a),r={left:f,top:p,width:c,height:a}}return{width:n,height:o,rect:r}}({crop:p,hotspot:{left:v-h,top:b-d,right:v+h,bottom:b+d}},r))),function(e){var r=(e.baseUrl||"https://cdn.sanity.io").replace(/\/+$/,""),n=e.vanityName?"/".concat(e.vanityName):"",o="".concat(e.asset.id,"-").concat(e.asset.width,"x").concat(e.asset.height,".").concat(e.asset.format).concat(n),i="".concat(r,"/images/").concat(e.projectId,"/").concat(e.dataset,"/").concat(o),u=[];if(e.rect){var s=e.rect,a=s.left,c=s.top,l=s.width,f=s.height;(0!==a||0!==c||f!==e.asset.height||l!==e.asset.width)&&u.push("rect=".concat(a,",").concat(c,",").concat(l,",").concat(f))}e.bg&&u.push("bg=".concat(e.bg)),e.focalPoint&&(u.push("fp-x=".concat(e.focalPoint.x)),u.push("fp-y=".concat(e.focalPoint.y)));var p=[e.flipHorizontal&&"h",e.flipVertical&&"v"].filter(Boolean).join("");return(p&&u.push("flip=".concat(p)),t.SPEC_NAME_TO_URL_NAME_MAPPINGS.forEach(function(t){var r=t[0],n=t[1];void 0!==e[r]?u.push("".concat(n,"=").concat(encodeURIComponent(e[r]))):void 0!==e[n]&&u.push("".concat(n,"=").concat(encodeURIComponent(e[n])))}),0===u.length)?i:"".concat(i,"?").concat(u.join("&"))}(n(n({},r),{asset:c}))}},48950:(e,t,r)=>{var n=function(){var e=function(t,r){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(t,"__esModule",{value:!0}),t.AsyncSubject=void 0;var o=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._value=null,t._hasValue=!1,t._isComplete=!1,t}return n(t,e),t.prototype._checkFinalizedStatuses=function(e){var t=this.hasError,r=this._hasValue,n=this._value,o=this.thrownError,i=this.isStopped,u=this._isComplete;t?e.error(o):(i||u)&&(r&&e.next(n),e.complete())},t.prototype.next=function(e){this.isStopped||(this._value=e,this._hasValue=!0)},t.prototype.complete=function(){var t=this._hasValue,r=this._value;this._isComplete||(this._isComplete=!0,t&&e.prototype.next.call(this,r),e.prototype.complete.call(this))},t}(r(13712).Subject);t.AsyncSubject=o},63038:(e,t,r)=>{var n=function(){var e=function(t,r){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(t,"__esModule",{value:!0}),t.BehaviorSubject=void 0;var o=function(e){function t(t){var r=e.call(this)||this;return r._value=t,r}return n(t,e),Object.defineProperty(t.prototype,"value",{get:function(){return this.getValue()},enumerable:!1,configurable:!0}),t.prototype._subscribe=function(t){var r=e.prototype._subscribe.call(this,t);return r.closed||t.next(this._value),r},t.prototype.getValue=function(){var e=this.hasError,t=this.thrownError,r=this._value;if(e)throw t;return this._throwIfClosed(),r},t.prototype.next=function(t){e.prototype.next.call(this,this._value=t)},t}(r(13712).Subject);t.BehaviorSubject=o},39557:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.observeNotification=t.Notification=t.NotificationKind=void 0;var n=r(69021),o=r(99975),i=r(9456),u=r(45321);!function(e){e.NEXT="N",e.ERROR="E",e.COMPLETE="C"}(t.NotificationKind||(t.NotificationKind={}));var s=function(){function e(e,t,r){this.kind=e,this.value=t,this.error=r,this.hasValue="N"===e}return e.prototype.observe=function(e){return a(this,e)},e.prototype.do=function(e,t,r){var n=this.kind,o=this.value,i=this.error;return"N"===n?null==e?void 0:e(o):"E"===n?null==t?void 0:t(i):null==r?void 0:r()},e.prototype.accept=function(e,t,r){return u.isFunction(null==e?void 0:e.next)?this.observe(e):this.do(e,t,r)},e.prototype.toObservable=function(){var e=this.kind,t=this.value,r=this.error,u="N"===e?o.of(t):"E"===e?i.throwError(function(){return r}):"C"===e?n.EMPTY:0;if(!u)throw TypeError("Unexpected notification kind "+e);return u},e.createNext=function(t){return new e("N",t)},e.createError=function(t){return new e("E",void 0,t)},e.createComplete=function(){return e.completeNotification},e.completeNotification=new e("C"),e}();function a(e,t){var r,n,o,i=e.kind,u=e.value,s=e.error;if("string"!=typeof i)throw TypeError('Invalid notification, missing "kind"');"N"===i?null===(r=t.next)||void 0===r||r.call(t,u):"E"===i?null===(n=t.error)||void 0===n||n.call(t,s):null===(o=t.complete)||void 0===o||o.call(t)}t.Notification=s,t.observeNotification=a},56635:(e,t)=>{function r(e,t,r){return{kind:e,value:t,error:r}}Object.defineProperty(t,"__esModule",{value:!0}),t.createNotification=t.nextNotification=t.errorNotification=t.COMPLETE_NOTIFICATION=void 0,t.COMPLETE_NOTIFICATION=r("C",void 0,void 0),t.errorNotification=function(e){return r("E",void 0,e)},t.nextNotification=function(e){return r("N",e,void 0)},t.createNotification=r},77207:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Observable=void 0;var n=r(27924),o=r(65107),i=r(19284),u=r(3349),s=r(15720),a=r(45321),c=r(60392),l=function(){function e(e){e&&(this._subscribe=e)}return e.prototype.lift=function(t){var r=new e;return r.source=this,r.operator=t,r},e.prototype.subscribe=function(e,t,r){var i,u=this,s=(i=e)&&i instanceof n.Subscriber||i&&a.isFunction(i.next)&&a.isFunction(i.error)&&a.isFunction(i.complete)&&o.isSubscription(i)?e:new n.SafeSubscriber(e,t,r);return c.errorContext(function(){var e=u.operator,t=u.source;s.add(e?e.call(s,t):t?u._subscribe(s):u._trySubscribe(s))}),s},e.prototype._trySubscribe=function(e){try{return this._subscribe(e)}catch(t){e.error(t)}},e.prototype.forEach=function(e,t){var r=this;return new(t=f(t))(function(t,o){var i=new n.SafeSubscriber({next:function(t){try{e(t)}catch(e){o(e),i.unsubscribe()}},error:o,complete:t});r.subscribe(i)})},e.prototype._subscribe=function(e){var t;return null===(t=this.source)||void 0===t?void 0:t.subscribe(e)},e.prototype[i.observable]=function(){return this},e.prototype.pipe=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return u.pipeFromArray(e)(this)},e.prototype.toPromise=function(e){var t=this;return new(e=f(e))(function(e,r){var n;t.subscribe(function(e){return n=e},function(e){return r(e)},function(){return e(n)})})},e.create=function(t){return new e(t)},e}();function f(e){var t;return null!==(t=null!=e?e:s.config.Promise)&&void 0!==t?t:Promise}t.Observable=l},62837:(e,t,r)=>{var n=function(){var e=function(t,r){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(t,"__esModule",{value:!0}),t.ReplaySubject=void 0;var o=r(13712),i=r(46655),u=function(e){function t(t,r,n){void 0===t&&(t=1/0),void 0===r&&(r=1/0),void 0===n&&(n=i.dateTimestampProvider);var o=e.call(this)||this;return o._bufferSize=t,o._windowTime=r,o._timestampProvider=n,o._buffer=[],o._infiniteTimeWindow=!0,o._infiniteTimeWindow=r===1/0,o._bufferSize=Math.max(1,t),o._windowTime=Math.max(1,r),o}return n(t,e),t.prototype.next=function(t){var r=this.isStopped,n=this._buffer,o=this._infiniteTimeWindow,i=this._timestampProvider,u=this._windowTime;!r&&(n.push(t),o||n.push(i.now()+u)),this._trimBuffer(),e.prototype.next.call(this,t)},t.prototype._subscribe=function(e){this._throwIfClosed(),this._trimBuffer();for(var t=this._innerSubscribe(e),r=this._infiniteTimeWindow,n=this._buffer.slice(),o=0;o<n.length&&!e.closed;o+=r?1:2)e.next(n[o]);return this._checkFinalizedStatuses(e),t},t.prototype._trimBuffer=function(){var e=this._bufferSize,t=this._timestampProvider,r=this._buffer,n=this._infiniteTimeWindow,o=(n?1:2)*e;if(e<1/0&&o<r.length&&r.splice(0,r.length-o),!n){for(var i=t.now(),u=0,s=1;s<r.length&&r[s]<=i;s+=2)u=s;u&&r.splice(0,u+1)}},t}(o.Subject);t.ReplaySubject=u},72665:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Scheduler=void 0;var n=r(46655),o=function(){function e(t,r){void 0===r&&(r=e.now),this.schedulerActionCtor=t,this.now=r}return e.prototype.schedule=function(e,t,r){return void 0===t&&(t=0),new this.schedulerActionCtor(this,e).schedule(r,t)},e.now=n.dateTimestampProvider.now,e}();t.Scheduler=o},13712:(e,t,r)=>{var n=function(){var e=function(t,r){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),o=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(t,"__esModule",{value:!0}),t.AnonymousSubject=t.Subject=void 0;var i=r(77207),u=r(65107),s=r(97619),a=r(46674),c=r(60392),l=function(e){function t(){var t=e.call(this)||this;return t.closed=!1,t.currentObservers=null,t.observers=[],t.isStopped=!1,t.hasError=!1,t.thrownError=null,t}return n(t,e),t.prototype.lift=function(e){var t=new f(this,this);return t.operator=e,t},t.prototype._throwIfClosed=function(){if(this.closed)throw new s.ObjectUnsubscribedError},t.prototype.next=function(e){var t=this;c.errorContext(function(){var r,n;if(t._throwIfClosed(),!t.isStopped){t.currentObservers||(t.currentObservers=Array.from(t.observers));try{for(var i=o(t.currentObservers),u=i.next();!u.done;u=i.next())u.value.next(e)}catch(e){r={error:e}}finally{try{u&&!u.done&&(n=i.return)&&n.call(i)}finally{if(r)throw r.error}}}})},t.prototype.error=function(e){var t=this;c.errorContext(function(){if(t._throwIfClosed(),!t.isStopped){t.hasError=t.isStopped=!0,t.thrownError=e;for(var r=t.observers;r.length;)r.shift().error(e)}})},t.prototype.complete=function(){var e=this;c.errorContext(function(){if(e._throwIfClosed(),!e.isStopped){e.isStopped=!0;for(var t=e.observers;t.length;)t.shift().complete()}})},t.prototype.unsubscribe=function(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null},Object.defineProperty(t.prototype,"observed",{get:function(){var e;return(null===(e=this.observers)||void 0===e?void 0:e.length)>0},enumerable:!1,configurable:!0}),t.prototype._trySubscribe=function(t){return this._throwIfClosed(),e.prototype._trySubscribe.call(this,t)},t.prototype._subscribe=function(e){return this._throwIfClosed(),this._checkFinalizedStatuses(e),this._innerSubscribe(e)},t.prototype._innerSubscribe=function(e){var t=this,r=this.hasError,n=this.isStopped,o=this.observers;return r||n?u.EMPTY_SUBSCRIPTION:(this.currentObservers=null,o.push(e),new u.Subscription(function(){t.currentObservers=null,a.arrRemove(o,e)}))},t.prototype._checkFinalizedStatuses=function(e){var t=this.hasError,r=this.thrownError,n=this.isStopped;t?e.error(r):n&&e.complete()},t.prototype.asObservable=function(){var e=new i.Observable;return e.source=this,e},t.create=function(e,t){return new f(e,t)},t}(i.Observable);t.Subject=l;var f=function(e){function t(t,r){var n=e.call(this)||this;return n.destination=t,n.source=r,n}return n(t,e),t.prototype.next=function(e){var t,r;null===(r=null===(t=this.destination)||void 0===t?void 0:t.next)||void 0===r||r.call(t,e)},t.prototype.error=function(e){var t,r;null===(r=null===(t=this.destination)||void 0===t?void 0:t.error)||void 0===r||r.call(t,e)},t.prototype.complete=function(){var e,t;null===(t=null===(e=this.destination)||void 0===e?void 0:e.complete)||void 0===t||t.call(e)},t.prototype._subscribe=function(e){var t,r;return null!==(r=null===(t=this.source)||void 0===t?void 0:t.subscribe(e))&&void 0!==r?r:u.EMPTY_SUBSCRIPTION},t}(l);t.AnonymousSubject=f},27924:(e,t,r)=>{var n=function(){var e=function(t,r){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(t,"__esModule",{value:!0}),t.EMPTY_OBSERVER=t.SafeSubscriber=t.Subscriber=void 0;var o=r(45321),i=r(65107),u=r(15720),s=r(67108),a=r(23965),c=r(56635),l=r(67248),f=r(60392),p=function(e){function r(r){var n=e.call(this)||this;return n.isStopped=!1,r?(n.destination=r,i.isSubscription(r)&&r.add(n)):n.destination=t.EMPTY_OBSERVER,n}return n(r,e),r.create=function(e,t,r){return new b(e,t,r)},r.prototype.next=function(e){this.isStopped?m(c.nextNotification(e),this):this._next(e)},r.prototype.error=function(e){this.isStopped?m(c.errorNotification(e),this):(this.isStopped=!0,this._error(e))},r.prototype.complete=function(){this.isStopped?m(c.COMPLETE_NOTIFICATION,this):(this.isStopped=!0,this._complete())},r.prototype.unsubscribe=function(){this.closed||(this.isStopped=!0,e.prototype.unsubscribe.call(this),this.destination=null)},r.prototype._next=function(e){this.destination.next(e)},r.prototype._error=function(e){try{this.destination.error(e)}finally{this.unsubscribe()}},r.prototype._complete=function(){try{this.destination.complete()}finally{this.unsubscribe()}},r}(i.Subscription);t.Subscriber=p;var d=Function.prototype.bind;function h(e,t){return d.call(e,t)}var v=function(){function e(e){this.partialObserver=e}return e.prototype.next=function(e){var t=this.partialObserver;if(t.next)try{t.next(e)}catch(e){y(e)}},e.prototype.error=function(e){var t=this.partialObserver;if(t.error)try{t.error(e)}catch(e){y(e)}else y(e)},e.prototype.complete=function(){var e=this.partialObserver;if(e.complete)try{e.complete()}catch(e){y(e)}},e}(),b=function(e){function t(t,r,n){var i,s,a=e.call(this)||this;return o.isFunction(t)||!t?i={next:null!=t?t:void 0,error:null!=r?r:void 0,complete:null!=n?n:void 0}:a&&u.config.useDeprecatedNextContext?((s=Object.create(t)).unsubscribe=function(){return a.unsubscribe()},i={next:t.next&&h(t.next,s),error:t.error&&h(t.error,s),complete:t.complete&&h(t.complete,s)}):i=t,a.destination=new v(i),a}return n(t,e),t}(p);function y(e){u.config.useDeprecatedSynchronousErrorHandling?f.captureError(e):s.reportUnhandledError(e)}function m(e,t){var r=u.config.onStoppedNotification;r&&l.timeoutProvider.setTimeout(function(){return r(e,t)})}t.SafeSubscriber=b,t.EMPTY_OBSERVER={closed:!0,next:a.noop,error:function(e){throw e},complete:a.noop}},65107:(e,t,r)=>{var n=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},o=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),u=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)u.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return u},i=function(e,t){for(var r=0,n=t.length,o=e.length;r<n;r++,o++)e[o]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.isSubscription=t.EMPTY_SUBSCRIPTION=t.Subscription=void 0;var u=r(45321),s=r(70661),a=r(46674),c=function(){var e;function t(e){this.initialTeardown=e,this.closed=!1,this._parentage=null,this._finalizers=null}return t.prototype.unsubscribe=function(){if(!this.closed){this.closed=!0;var e,t,r,a,c,f=this._parentage;if(f){if(this._parentage=null,Array.isArray(f))try{for(var p=n(f),d=p.next();!d.done;d=p.next())d.value.remove(this)}catch(t){e={error:t}}finally{try{d&&!d.done&&(t=p.return)&&t.call(p)}finally{if(e)throw e.error}}else f.remove(this)}var h=this.initialTeardown;if(u.isFunction(h))try{h()}catch(e){c=e instanceof s.UnsubscriptionError?e.errors:[e]}var v=this._finalizers;if(v){this._finalizers=null;try{for(var b=n(v),y=b.next();!y.done;y=b.next()){var m=y.value;try{l(m)}catch(e){c=null!=c?c:[],e instanceof s.UnsubscriptionError?c=i(i([],o(c)),o(e.errors)):c.push(e)}}}catch(e){r={error:e}}finally{try{y&&!y.done&&(a=b.return)&&a.call(b)}finally{if(r)throw r.error}}}if(c)throw new s.UnsubscriptionError(c)}},t.prototype.add=function(e){var r;if(e&&e!==this){if(this.closed)l(e);else{if(e instanceof t){if(e.closed||e._hasParent(this))return;e._addParent(this)}(this._finalizers=null!==(r=this._finalizers)&&void 0!==r?r:[]).push(e)}}},t.prototype._hasParent=function(e){var t=this._parentage;return t===e||Array.isArray(t)&&t.includes(e)},t.prototype._addParent=function(e){var t=this._parentage;this._parentage=Array.isArray(t)?(t.push(e),t):t?[t,e]:e},t.prototype._removeParent=function(e){var t=this._parentage;t===e?this._parentage=null:Array.isArray(t)&&a.arrRemove(t,e)},t.prototype.remove=function(e){var r=this._finalizers;r&&a.arrRemove(r,e),e instanceof t&&e._removeParent(this)},t.EMPTY=((e=new t).closed=!0,e),t}();function l(e){u.isFunction(e)?e():e.unsubscribe()}t.Subscription=c,t.EMPTY_SUBSCRIPTION=c.EMPTY,t.isSubscription=function(e){return e instanceof c||e&&"closed"in e&&u.isFunction(e.remove)&&u.isFunction(e.add)&&u.isFunction(e.unsubscribe)}},15720:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.config=void 0,t.config={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1}},92547:(e,t,r)=>{var n=function(){var e=function(t,r){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(t,"__esModule",{value:!0}),t.ConnectableObservable=void 0;var o=r(77207),i=r(65107),u=r(1328),s=r(6802),a=r(81692),c=function(e){function t(t,r){var n=e.call(this)||this;return n.source=t,n.subjectFactory=r,n._subject=null,n._refCount=0,n._connection=null,a.hasLift(t)&&(n.lift=t.lift),n}return n(t,e),t.prototype._subscribe=function(e){return this.getSubject().subscribe(e)},t.prototype.getSubject=function(){var e=this._subject;return(!e||e.isStopped)&&(this._subject=this.subjectFactory()),this._subject},t.prototype._teardown=function(){this._refCount=0;var e=this._connection;this._subject=this._connection=null,null==e||e.unsubscribe()},t.prototype.connect=function(){var e=this,t=this._connection;if(!t){t=this._connection=new i.Subscription;var r=this.getSubject();t.add(this.source.subscribe(s.createOperatorSubscriber(r,void 0,function(){e._teardown(),r.complete()},function(t){e._teardown(),r.error(t)},function(){return e._teardown()}))),t.closed&&(this._connection=null,t=i.Subscription.EMPTY)}return t},t.prototype.refCount=function(){return u.refCount()(this)},t}(o.Observable);t.ConnectableObservable=c},35194:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.combineLatestInit=t.combineLatest=void 0;var n=r(77207),o=r(60771),i=r(90526),u=r(30711),s=r(47052),a=r(1972),c=r(19342),l=r(6802),f=r(92671);function p(e,t,r){return void 0===r&&(r=u.identity),function(n){d(t,function(){for(var o=e.length,u=Array(o),s=o,a=o,c=function(o){d(t,function(){var c=i.from(e[o],t),f=!1;c.subscribe(l.createOperatorSubscriber(n,function(e){u[o]=e,!f&&(f=!0,a--),a||n.next(r(u.slice()))},function(){--s||n.complete()}))},n)},f=0;f<o;f++)c(f)},n)}}function d(e,t,r){e?f.executeSchedule(r,e,t):t()}t.combineLatest=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=a.popScheduler(e),l=a.popResultSelector(e),f=o.argsArgArrayOrObject(e),d=f.args,h=f.keys;if(0===d.length)return i.from([],r);var v=new n.Observable(p(d,r,h?function(e){return c.createObject(h,e)}:u.identity));return l?v.pipe(s.mapOneOrManyArgs(l)):v},t.combineLatestInit=p},97302:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.concat=void 0;var n=r(72169),o=r(1972),i=r(90526);t.concat=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return n.concatAll()(i.from(e,o.popScheduler(e)))}},69021:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.empty=t.EMPTY=void 0;var n=r(77207);t.EMPTY=new n.Observable(function(e){return e.complete()}),t.empty=function(e){return e?new n.Observable(function(t){return e.schedule(function(){return t.complete()})}):t.EMPTY}},90526:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.from=void 0;var n=r(22423),o=r(93836);t.from=function(e,t){return t?n.scheduled(e,t):o.innerFrom(e)}},2603:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.fromSubscribable=void 0;var n=r(77207);t.fromSubscribable=function(e){return new n.Observable(function(t){return e.subscribe(t)})}},93836:(e,t,r)=>{var n=function(e,t){var r,n,o,i,u={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(r)throw TypeError("Generator is already executing.");for(;u;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return u.label++,{value:i[1],done:!1};case 5:u.label++,n=i[1],i=[0];continue;case 7:i=u.ops.pop(),u.trys.pop();continue;default:if(!(o=(o=u.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){u=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){u.label=i[1];break}if(6===i[0]&&u.label<o[1]){u.label=o[1],o=i;break}if(o&&u.label<o[2]){u.label=o[2],u.ops.push(i);break}o[2]&&u.ops.pop(),u.trys.pop();continue}i=t.call(e,u)}catch(e){i=[6,e],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}},o=function(e){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var t,r=e[Symbol.asyncIterator];return r?r.call(e):(e="function"==typeof i?i(e):e[Symbol.iterator](),t={},n("next"),n("throw"),n("return"),t[Symbol.asyncIterator]=function(){return this},t);function n(r){t[r]=e[r]&&function(t){return new Promise(function(n,o){(function(e,t,r,n){Promise.resolve(n).then(function(t){e({value:t,done:r})},t)})(n,o,(t=e[r](t)).done,t.value)})}}},i=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(t,"__esModule",{value:!0}),t.fromReadableStreamLike=t.fromAsyncIterable=t.fromIterable=t.fromPromise=t.fromArrayLike=t.fromInteropObservable=t.innerFrom=void 0;var u=r(58219),s=r(65256),a=r(77207),c=r(7389),l=r(62131),f=r(81897),p=r(10623),d=r(28874),h=r(45321),v=r(67108),b=r(19284);function y(e){return new a.Observable(function(t){var r=e[b.observable]();if(h.isFunction(r.subscribe))return r.subscribe(t);throw TypeError("Provided object does not correctly implement Symbol.observable")})}function m(e){return new a.Observable(function(t){for(var r=0;r<e.length&&!t.closed;r++)t.next(e[r]);t.complete()})}function g(e){return new a.Observable(function(t){e.then(function(e){t.closed||(t.next(e),t.complete())},function(e){return t.error(e)}).then(null,v.reportUnhandledError)})}function _(e){return new a.Observable(function(t){var r,n;try{for(var o=i(e),u=o.next();!u.done;u=o.next()){var s=u.value;if(t.next(s),t.closed)return}}catch(e){r={error:e}}finally{try{u&&!u.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}t.complete()})}function w(e){return new a.Observable(function(t){(function(e,t){var r,i,u,s,a,c,l,f;return a=this,c=void 0,l=void 0,f=function(){var a;return n(this,function(n){switch(n.label){case 0:n.trys.push([0,5,6,11]),r=o(e),n.label=1;case 1:return[4,r.next()];case 2:if((i=n.sent()).done)return[3,4];if(a=i.value,t.next(a),t.closed)return[2];n.label=3;case 3:return[3,1];case 4:return[3,11];case 5:return u={error:n.sent()},[3,11];case 6:if(n.trys.push([6,,9,10]),!(i&&!i.done&&(s=r.return)))return[3,8];return[4,s.call(r)];case 7:n.sent(),n.label=8;case 8:return[3,10];case 9:if(u)throw u.error;return[7];case 10:return[7];case 11:return t.complete(),[2]}})},new(l||(l=Promise))(function(e,t){function r(e){try{o(f.next(e))}catch(e){t(e)}}function n(e){try{o(f.throw(e))}catch(e){t(e)}}function o(t){var o;t.done?e(t.value):((o=t.value)instanceof l?o:new l(function(e){e(o)})).then(r,n)}o((f=f.apply(a,c||[])).next())})})(e,t).catch(function(e){return t.error(e)})})}function O(e){return w(d.readableStreamLikeToAsyncGenerator(e))}t.innerFrom=function(e){if(e instanceof a.Observable)return e;if(null!=e){if(c.isInteropObservable(e))return y(e);if(u.isArrayLike(e))return m(e);if(s.isPromise(e))return g(e);if(l.isAsyncIterable(e))return w(e);if(p.isIterable(e))return _(e);if(d.isReadableStreamLike(e))return O(e)}throw f.createInvalidObservableTypeError(e)},t.fromInteropObservable=y,t.fromArrayLike=m,t.fromPromise=g,t.fromIterable=_,t.fromAsyncIterable=w,t.fromReadableStreamLike=O},31063:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.interval=void 0;var n=r(49658),o=r(21329);t.interval=function(e,t){return void 0===e&&(e=0),void 0===t&&(t=n.asyncScheduler),e<0&&(e=0),o.timer(e,e,t)}},99975:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.of=void 0;var n=r(1972),o=r(90526);t.of=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=n.popScheduler(e);return o.from(e,r)}},18539:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.onErrorResumeNext=void 0;var n=r(77207),o=r(61268),i=r(6802),u=r(23965),s=r(93836);t.onErrorResumeNext=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=o.argsOrArgArray(e);return new n.Observable(function(e){var t=0,n=function(){if(t<r.length){var o=void 0;try{o=s.innerFrom(r[t++])}catch(e){n();return}var a=new i.OperatorSubscriber(e,void 0,u.noop,u.noop);o.subscribe(a),a.add(n)}else e.complete()};n()})}},78695:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.raceInit=t.race=void 0;var n=r(77207),o=r(93836),i=r(61268),u=r(6802);function s(e){return function(t){for(var r=[],n=function(n){r.push(o.innerFrom(e[n]).subscribe(u.createOperatorSubscriber(t,function(e){if(r){for(var o=0;o<r.length;o++)o!==n&&r[o].unsubscribe();r=null}t.next(e)})))},i=0;r&&!t.closed&&i<e.length;i++)n(i)}}t.race=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return 1===(e=i.argsOrArgArray(e)).length?o.innerFrom(e[0]):new n.Observable(s(e))},t.raceInit=s},9456:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.throwError=void 0;var n=r(77207),o=r(45321);t.throwError=function(e,t){var r=o.isFunction(e)?e:function(){return e},i=function(e){return e.error(r())};return new n.Observable(t?function(e){return t.schedule(i,0,e)}:i)}},21329:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.timer=void 0;var n=r(77207),o=r(49658),i=r(77796),u=r(20593);t.timer=function(e,t,r){void 0===e&&(e=0),void 0===r&&(r=o.async);var s=-1;return null!=t&&(i.isScheduler(t)?r=t:s=t),new n.Observable(function(t){var n=u.isValidDate(e)?+e-r.now():e;n<0&&(n=0);var o=0;return r.schedule(function(){t.closed||(t.next(o++),0<=s?this.schedule(void 0,s):t.complete())},n)})}},68483:(e,t,r)=>{var n=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),u=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)u.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return u},o=function(e,t){for(var r=0,n=t.length,o=e.length;r<n;r++,o++)e[o]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.zip=void 0;var i=r(77207),u=r(93836),s=r(61268),a=r(69021),c=r(6802),l=r(1972);t.zip=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=l.popResultSelector(e),f=s.argsOrArgArray(e);return f.length?new i.Observable(function(e){var t=f.map(function(){return[]}),i=f.map(function(){return!1});e.add(function(){t=i=null});for(var s=function(s){u.innerFrom(f[s]).subscribe(c.createOperatorSubscriber(e,function(u){if(t[s].push(u),t.every(function(e){return e.length})){var a=t.map(function(e){return e.shift()});e.next(r?r.apply(void 0,o([],n(a))):a),t.some(function(e,t){return!e.length&&i[t]})&&e.complete()}},function(){i[s]=!0,t[s].length||e.complete()}))},a=0;!e.closed&&a<f.length;a++)s(a);return function(){t=i=null}}):a.EMPTY}},6802:(e,t,r)=>{var n=function(){var e=function(t,r){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(t,"__esModule",{value:!0}),t.OperatorSubscriber=t.createOperatorSubscriber=void 0;var o=r(27924);t.createOperatorSubscriber=function(e,t,r,n,o){return new i(e,t,r,n,o)};var i=function(e){function t(t,r,n,o,i,u){var s=e.call(this,t)||this;return s.onFinalize=i,s.shouldUnsubscribe=u,s._next=r?function(e){try{r(e)}catch(e){t.error(e)}}:e.prototype._next,s._error=o?function(e){try{o(e)}catch(e){t.error(e)}finally{this.unsubscribe()}}:e.prototype._error,s._complete=n?function(){try{n()}catch(e){t.error(e)}finally{this.unsubscribe()}}:e.prototype._complete,s}return n(t,e),t.prototype.unsubscribe=function(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){var r=this.closed;e.prototype.unsubscribe.call(this),r||null===(t=this.onFinalize)||void 0===t||t.call(this)}},t}(o.Subscriber);t.OperatorSubscriber=i},78085:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.audit=void 0;var n=r(81692),o=r(93836),i=r(6802);t.audit=function(e){return n.operate(function(t,r){var n=!1,u=null,s=null,a=!1,c=function(){if(null==s||s.unsubscribe(),s=null,n){n=!1;var e=u;u=null,r.next(e)}a&&r.complete()},l=function(){s=null,a&&r.complete()};t.subscribe(i.createOperatorSubscriber(r,function(t){n=!0,u=t,s||o.innerFrom(e(t)).subscribe(s=i.createOperatorSubscriber(r,c,l))},function(){a=!0,n&&s&&!s.closed||r.complete()}))})}},13008:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.auditTime=void 0;var n=r(49658),o=r(78085),i=r(21329);t.auditTime=function(e,t){return void 0===t&&(t=n.asyncScheduler),o.audit(function(){return i.timer(e,t)})}},22542:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.buffer=void 0;var n=r(81692),o=r(23965),i=r(6802),u=r(93836);t.buffer=function(e){return n.operate(function(t,r){var n=[];return t.subscribe(i.createOperatorSubscriber(r,function(e){return n.push(e)},function(){r.next(n),r.complete()})),u.innerFrom(e).subscribe(i.createOperatorSubscriber(r,function(){var e=n;n=[],r.next(e)},o.noop)),function(){n=null}})}},49187:(e,t,r)=>{var n=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(t,"__esModule",{value:!0}),t.bufferCount=void 0;var o=r(81692),i=r(6802),u=r(46674);t.bufferCount=function(e,t){return void 0===t&&(t=null),t=null!=t?t:e,o.operate(function(r,o){var s=[],a=0;r.subscribe(i.createOperatorSubscriber(o,function(r){var i,c,l,f,p=null;a++%t==0&&s.push([]);try{for(var d=n(s),h=d.next();!h.done;h=d.next()){var v=h.value;v.push(r),e<=v.length&&(p=null!=p?p:[]).push(v)}}catch(e){i={error:e}}finally{try{h&&!h.done&&(c=d.return)&&c.call(d)}finally{if(i)throw i.error}}if(p)try{for(var b=n(p),y=b.next();!y.done;y=b.next()){var v=y.value;u.arrRemove(s,v),o.next(v)}}catch(e){l={error:e}}finally{try{y&&!y.done&&(f=b.return)&&f.call(b)}finally{if(l)throw l.error}}},function(){var e,t;try{for(var r=n(s),i=r.next();!i.done;i=r.next()){var u=i.value;o.next(u)}}catch(t){e={error:t}}finally{try{i&&!i.done&&(t=r.return)&&t.call(r)}finally{if(e)throw e.error}}o.complete()},void 0,function(){s=null}))})}},65283:(e,t,r)=>{var n=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(t,"__esModule",{value:!0}),t.bufferTime=void 0;var o=r(65107),i=r(81692),u=r(6802),s=r(46674),a=r(49658),c=r(1972),l=r(92671);t.bufferTime=function(e){for(var t,r,f=[],p=1;p<arguments.length;p++)f[p-1]=arguments[p];var d=null!==(t=c.popScheduler(f))&&void 0!==t?t:a.asyncScheduler,h=null!==(r=f[0])&&void 0!==r?r:null,v=f[1]||1/0;return i.operate(function(t,r){var i=[],a=!1,c=function(e){var t=e.buffer;e.subs.unsubscribe(),s.arrRemove(i,e),r.next(t),a&&f()},f=function(){if(i){var t=new o.Subscription;r.add(t);var n={buffer:[],subs:t};i.push(n),l.executeSchedule(t,d,function(){return c(n)},e)}};null!==h&&h>=0?l.executeSchedule(r,d,f,h,!0):a=!0,f();var p=u.createOperatorSubscriber(r,function(e){var t,r,o=i.slice();try{for(var u=n(o),s=u.next();!s.done;s=u.next()){var a=s.value,l=a.buffer;l.push(e),v<=l.length&&c(a)}}catch(e){t={error:e}}finally{try{s&&!s.done&&(r=u.return)&&r.call(u)}finally{if(t)throw t.error}}},function(){for(;null==i?void 0:i.length;)r.next(i.shift().buffer);null==p||p.unsubscribe(),r.complete(),r.unsubscribe()},void 0,function(){return i=null});t.subscribe(p)})}},15790:(e,t,r)=>{var n=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(t,"__esModule",{value:!0}),t.bufferToggle=void 0;var o=r(65107),i=r(81692),u=r(93836),s=r(6802),a=r(23965),c=r(46674);t.bufferToggle=function(e,t){return i.operate(function(r,i){var l=[];u.innerFrom(e).subscribe(s.createOperatorSubscriber(i,function(e){var r=[];l.push(r);var n=new o.Subscription;n.add(u.innerFrom(t(e)).subscribe(s.createOperatorSubscriber(i,function(){c.arrRemove(l,r),i.next(r),n.unsubscribe()},a.noop)))},a.noop)),r.subscribe(s.createOperatorSubscriber(i,function(e){var t,r;try{for(var o=n(l),i=o.next();!i.done;i=o.next())i.value.push(e)}catch(e){t={error:e}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(t)throw t.error}}},function(){for(;l.length>0;)i.next(l.shift());i.complete()}))})}},26658:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.bufferWhen=void 0;var n=r(81692),o=r(23965),i=r(6802),u=r(93836);t.bufferWhen=function(e){return n.operate(function(t,r){var n=null,s=null,a=function(){null==s||s.unsubscribe();var t=n;n=[],t&&r.next(t),u.innerFrom(e()).subscribe(s=i.createOperatorSubscriber(r,a,o.noop))};a(),t.subscribe(i.createOperatorSubscriber(r,function(e){return null==n?void 0:n.push(e)},function(){n&&r.next(n),r.complete()},void 0,function(){return n=s=null}))})}},25611:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.catchError=void 0;var n=r(93836),o=r(6802),i=r(81692);t.catchError=function e(t){return i.operate(function(r,i){var u,s=null,a=!1;s=r.subscribe(o.createOperatorSubscriber(i,void 0,void 0,function(o){u=n.innerFrom(t(o,e(t)(r))),s?(s.unsubscribe(),s=null,u.subscribe(i)):a=!0})),a&&(s.unsubscribe(),s=null,u.subscribe(i))})}},93132:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.combineAll=void 0;var n=r(66277);t.combineAll=n.combineLatestAll},92436:(e,t,r)=>{var n=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),u=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)u.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return u},o=function(e,t){for(var r=0,n=t.length,o=e.length;r<n;r++,o++)e[o]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.combineLatest=void 0;var i=r(35194),u=r(81692),s=r(61268),a=r(47052),c=r(3349),l=r(1972);t.combineLatest=function e(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var f=l.popResultSelector(t);return f?c.pipe(e.apply(void 0,o([],n(t))),a.mapOneOrManyArgs(f)):u.operate(function(e,r){i.combineLatestInit(o([e],n(s.argsOrArgArray(t))))(r)})}},66277:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.combineLatestAll=void 0;var n=r(35194),o=r(55147);t.combineLatestAll=function(e){return o.joinAllInternals(n.combineLatest,e)}},61388:(e,t,r)=>{var n=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),u=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)u.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return u},o=function(e,t){for(var r=0,n=t.length,o=e.length;r<n;r++,o++)e[o]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.combineLatestWith=void 0;var i=r(92436);t.combineLatestWith=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return i.combineLatest.apply(void 0,o([],n(e)))}},17920:(e,t,r)=>{var n=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),u=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)u.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return u},o=function(e,t){for(var r=0,n=t.length,o=e.length;r<n;r++,o++)e[o]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.concat=void 0;var i=r(81692),u=r(72169),s=r(1972),a=r(90526);t.concat=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=s.popScheduler(e);return i.operate(function(t,i){u.concatAll()(a.from(o([t],n(e)),r)).subscribe(i)})}},72169:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.concatAll=void 0;var n=r(21683);t.concatAll=function(){return n.mergeAll(1)}},72224:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.concatMap=void 0;var n=r(28266),o=r(45321);t.concatMap=function(e,t){return o.isFunction(t)?n.mergeMap(e,t,1):n.mergeMap(e,1)}},49785:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.concatMapTo=void 0;var n=r(72224),o=r(45321);t.concatMapTo=function(e,t){return o.isFunction(t)?n.concatMap(function(){return e},t):n.concatMap(function(){return e})}},54360:(e,t,r)=>{var n=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),u=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)u.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return u},o=function(e,t){for(var r=0,n=t.length,o=e.length;r<n;r++,o++)e[o]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.concatWith=void 0;var i=r(17920);t.concatWith=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return i.concat.apply(void 0,o([],n(e)))}},55188:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.connect=void 0;var n=r(13712),o=r(93836),i=r(81692),u=r(2603),s={connector:function(){return new n.Subject}};t.connect=function(e,t){void 0===t&&(t=s);var r=t.connector;return i.operate(function(t,n){var i=r();o.innerFrom(e(u.fromSubscribable(i))).subscribe(n),n.add(t.subscribe(i))})}},47061:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.count=void 0;var n=r(33178);t.count=function(e){return n.reduce(function(t,r,n){return!e||e(r,n)?t+1:t},0)}},8051:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.debounce=void 0;var n=r(81692),o=r(23965),i=r(6802),u=r(93836);t.debounce=function(e){return n.operate(function(t,r){var n=!1,s=null,a=null,c=function(){if(null==a||a.unsubscribe(),a=null,n){n=!1;var e=s;s=null,r.next(e)}};t.subscribe(i.createOperatorSubscriber(r,function(t){null==a||a.unsubscribe(),n=!0,s=t,a=i.createOperatorSubscriber(r,c,o.noop),u.innerFrom(e(t)).subscribe(a)},function(){c(),r.complete()},void 0,function(){s=a=null}))})}},75134:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.debounceTime=void 0;var n=r(49658),o=r(81692),i=r(6802);t.debounceTime=function(e,t){return void 0===t&&(t=n.asyncScheduler),o.operate(function(r,n){var o=null,u=null,s=null,a=function(){if(o){o.unsubscribe(),o=null;var e=u;u=null,n.next(e)}};function c(){var r=s+e,i=t.now();if(i<r){o=this.schedule(void 0,r-i),n.add(o);return}a()}r.subscribe(i.createOperatorSubscriber(n,function(r){u=r,s=t.now(),o||(o=t.schedule(c,e),n.add(o))},function(){a(),n.complete()},void 0,function(){u=o=null}))})}},96107:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.defaultIfEmpty=void 0;var n=r(81692),o=r(6802);t.defaultIfEmpty=function(e){return n.operate(function(t,r){var n=!1;t.subscribe(o.createOperatorSubscriber(r,function(e){n=!0,r.next(e)},function(){n||r.next(e),r.complete()}))})}},54781:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.delay=void 0;var n=r(49658),o=r(20141),i=r(21329);t.delay=function(e,t){void 0===t&&(t=n.asyncScheduler);var r=i.timer(e,t);return o.delayWhen(function(){return r})}},20141:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.delayWhen=void 0;var n=r(97302),o=r(68987),i=r(38347),u=r(55177),s=r(28266),a=r(93836);t.delayWhen=function e(t,r){return r?function(u){return n.concat(r.pipe(o.take(1),i.ignoreElements()),u.pipe(e(t)))}:s.mergeMap(function(e,r){return a.innerFrom(t(e,r)).pipe(o.take(1),u.mapTo(e))})}},68598:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.dematerialize=void 0;var n=r(39557),o=r(81692),i=r(6802);t.dematerialize=function(){return o.operate(function(e,t){e.subscribe(i.createOperatorSubscriber(t,function(e){return n.observeNotification(e,t)}))})}},84772:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.distinct=void 0;var n=r(81692),o=r(6802),i=r(23965),u=r(93836);t.distinct=function(e,t){return n.operate(function(r,n){var s=new Set;r.subscribe(o.createOperatorSubscriber(n,function(t){var r=e?e(t):t;s.has(r)||(s.add(r),n.next(t))})),t&&u.innerFrom(t).subscribe(o.createOperatorSubscriber(n,function(){return s.clear()},i.noop))})}},7884:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.distinctUntilChanged=void 0;var n=r(30711),o=r(81692),i=r(6802);function u(e,t){return e===t}t.distinctUntilChanged=function(e,t){return void 0===t&&(t=n.identity),e=null!=e?e:u,o.operate(function(r,n){var o,u=!0;r.subscribe(i.createOperatorSubscriber(n,function(r){var i=t(r);(u||!e(o,i))&&(u=!1,o=i,n.next(r))}))})}},97319:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.distinctUntilKeyChanged=void 0;var n=r(7884);t.distinctUntilKeyChanged=function(e,t){return n.distinctUntilChanged(function(r,n){return t?t(r[e],n[e]):r[e]===n[e]})}},23099:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.elementAt=void 0;var n=r(81816),o=r(54610),i=r(25232),u=r(96107),s=r(68987);t.elementAt=function(e,t){if(e<0)throw new n.ArgumentOutOfRangeError;var r=arguments.length>=2;return function(a){return a.pipe(o.filter(function(t,r){return r===e}),s.take(1),r?u.defaultIfEmpty(t):i.throwIfEmpty(function(){return new n.ArgumentOutOfRangeError}))}}},48467:(e,t,r)=>{var n=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),u=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)u.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return u},o=function(e,t){for(var r=0,n=t.length,o=e.length;r<n;r++,o++)e[o]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.endWith=void 0;var i=r(97302),u=r(99975);t.endWith=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return function(t){return i.concat(t,u.of.apply(void 0,o([],n(e))))}}},18913:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.every=void 0;var n=r(81692),o=r(6802);t.every=function(e,t){return n.operate(function(r,n){var i=0;r.subscribe(o.createOperatorSubscriber(n,function(o){e.call(t,o,i++,r)||(n.next(!1),n.complete())},function(){n.next(!0),n.complete()}))})}},30896:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.exhaust=void 0;var n=r(26137);t.exhaust=n.exhaustAll},26137:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.exhaustAll=void 0;var n=r(95824),o=r(30711);t.exhaustAll=function(){return n.exhaustMap(o.identity)}},95824:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.exhaustMap=void 0;var n=r(77360),o=r(93836),i=r(81692),u=r(6802);t.exhaustMap=function e(t,r){return r?function(i){return i.pipe(e(function(e,i){return o.innerFrom(t(e,i)).pipe(n.map(function(t,n){return r(e,t,i,n)}))}))}:i.operate(function(e,r){var n=0,i=null,s=!1;e.subscribe(u.createOperatorSubscriber(r,function(e){i||(i=u.createOperatorSubscriber(r,void 0,function(){i=null,s&&r.complete()}),o.innerFrom(t(e,n++)).subscribe(i))},function(){s=!0,i||r.complete()}))})}},48016:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.expand=void 0;var n=r(81692),o=r(70146);t.expand=function(e,t,r){return void 0===t&&(t=1/0),t=1>(t||0)?1/0:t,n.operate(function(n,i){return o.mergeInternals(n,i,e,t,void 0,!0,r)})}},54610:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.filter=void 0;var n=r(81692),o=r(6802);t.filter=function(e,t){return n.operate(function(r,n){var i=0;r.subscribe(o.createOperatorSubscriber(n,function(r){return e.call(t,r,i++)&&n.next(r)}))})}},98982:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.finalize=void 0;var n=r(81692);t.finalize=function(e){return n.operate(function(t,r){try{t.subscribe(r)}finally{r.add(e)}})}},72019:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createFind=t.find=void 0;var n=r(81692),o=r(6802);function i(e,t,r){var n="index"===r;return function(r,i){var u=0;r.subscribe(o.createOperatorSubscriber(i,function(o){var s=u++;e.call(t,o,s,r)&&(i.next(n?s:o),i.complete())},function(){i.next(n?-1:void 0),i.complete()}))}}t.find=function(e,t){return n.operate(i(e,t,"value"))},t.createFind=i},67311:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.findIndex=void 0;var n=r(81692),o=r(72019);t.findIndex=function(e,t){return n.operate(o.createFind(e,t,"index"))}},37564:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.first=void 0;var n=r(63464),o=r(54610),i=r(68987),u=r(96107),s=r(25232),a=r(30711);t.first=function(e,t){var r=arguments.length>=2;return function(c){return c.pipe(e?o.filter(function(t,r){return e(t,r,c)}):a.identity,i.take(1),r?u.defaultIfEmpty(t):s.throwIfEmpty(function(){return new n.EmptyError}))}}},27917:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.flatMap=void 0;var n=r(28266);t.flatMap=n.mergeMap},73392:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.groupBy=void 0;var n=r(77207),o=r(93836),i=r(13712),u=r(81692),s=r(6802);t.groupBy=function(e,t,r,a){return u.operate(function(u,c){t&&"function"!=typeof t?(r=t.duration,l=t.element,a=t.connector):l=t;var l,f=new Map,p=function(e){f.forEach(e),e(c)},d=function(e){return p(function(t){return t.error(e)})},h=0,v=!1,b=new s.OperatorSubscriber(c,function(t){try{var u=e(t),p=f.get(u);if(!p){f.set(u,p=a?a():new i.Subject);var y,m,g=(y=p,(m=new n.Observable(function(e){h++;var t=y.subscribe(e);return function(){t.unsubscribe(),0==--h&&v&&b.unsubscribe()}})).key=u,m);if(c.next(g),r){var _=s.createOperatorSubscriber(p,function(){p.complete(),null==_||_.unsubscribe()},void 0,void 0,function(){return f.delete(u)});b.add(o.innerFrom(r(g)).subscribe(_))}}p.next(l?l(t):t)}catch(e){d(e)}},function(){return p(function(e){return e.complete()})},d,function(){return f.clear()},function(){return v=!0,0===h});u.subscribe(b)})}},38347:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ignoreElements=void 0;var n=r(81692),o=r(6802),i=r(23965);t.ignoreElements=function(){return n.operate(function(e,t){e.subscribe(o.createOperatorSubscriber(t,i.noop))})}},38367:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isEmpty=void 0;var n=r(81692),o=r(6802);t.isEmpty=function(){return n.operate(function(e,t){e.subscribe(o.createOperatorSubscriber(t,function(){t.next(!1),t.complete()},function(){t.next(!0),t.complete()}))})}},55147:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.joinAllInternals=void 0;var n=r(30711),o=r(47052),i=r(3349),u=r(28266),s=r(71596);t.joinAllInternals=function(e,t){return i.pipe(s.toArray(),u.mergeMap(function(t){return e(t)}),t?o.mapOneOrManyArgs(t):n.identity)}},96032:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.last=void 0;var n=r(63464),o=r(54610),i=r(13019),u=r(25232),s=r(96107),a=r(30711);t.last=function(e,t){var r=arguments.length>=2;return function(c){return c.pipe(e?o.filter(function(t,r){return e(t,r,c)}):a.identity,i.takeLast(1),r?s.defaultIfEmpty(t):u.throwIfEmpty(function(){return new n.EmptyError}))}}},77360:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.map=void 0;var n=r(81692),o=r(6802);t.map=function(e,t){return n.operate(function(r,n){var i=0;r.subscribe(o.createOperatorSubscriber(n,function(r){n.next(e.call(t,r,i++))}))})}},55177:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.mapTo=void 0;var n=r(77360);t.mapTo=function(e){return n.map(function(){return e})}},60687:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.materialize=void 0;var n=r(39557),o=r(81692),i=r(6802);t.materialize=function(){return o.operate(function(e,t){e.subscribe(i.createOperatorSubscriber(t,function(e){t.next(n.Notification.createNext(e))},function(){t.next(n.Notification.createComplete()),t.complete()},function(e){t.next(n.Notification.createError(e)),t.complete()}))})}},86696:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.max=void 0;var n=r(33178),o=r(45321);t.max=function(e){return n.reduce(o.isFunction(e)?function(t,r){return e(t,r)>0?t:r}:function(e,t){return e>t?e:t})}},52846:(e,t,r)=>{var n=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),u=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)u.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return u},o=function(e,t){for(var r=0,n=t.length,o=e.length;r<n;r++,o++)e[o]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.merge=void 0;var i=r(81692),u=r(21683),s=r(1972),a=r(90526);t.merge=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=s.popScheduler(e),c=s.popNumber(e,1/0);return i.operate(function(t,i){u.mergeAll(c)(a.from(o([t],n(e)),r)).subscribe(i)})}},21683:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.mergeAll=void 0;var n=r(28266),o=r(30711);t.mergeAll=function(e){return void 0===e&&(e=1/0),n.mergeMap(o.identity,e)}},70146:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.mergeInternals=void 0;var n=r(93836),o=r(92671),i=r(6802);t.mergeInternals=function(e,t,r,u,s,a,c,l){var f=[],p=0,d=0,h=!1,v=function(){!h||f.length||p||t.complete()},b=function(e){return p<u?y(e):f.push(e)},y=function(e){a&&t.next(e),p++;var l=!1;n.innerFrom(r(e,d++)).subscribe(i.createOperatorSubscriber(t,function(e){null==s||s(e),a?b(e):t.next(e)},function(){l=!0},void 0,function(){if(l)try{for(p--;f.length&&p<u;)!function(){var e=f.shift();c?o.executeSchedule(t,c,function(){return y(e)}):y(e)}();v()}catch(e){t.error(e)}}))};return e.subscribe(i.createOperatorSubscriber(t,b,function(){h=!0,v()})),function(){null==l||l()}}},28266:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.mergeMap=void 0;var n=r(77360),o=r(93836),i=r(81692),u=r(70146),s=r(45321);t.mergeMap=function e(t,r,a){return(void 0===a&&(a=1/0),s.isFunction(r))?e(function(e,i){return n.map(function(t,n){return r(e,t,i,n)})(o.innerFrom(t(e,i)))},a):("number"==typeof r&&(a=r),i.operate(function(e,r){return u.mergeInternals(e,r,t,a)}))}},60095:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.mergeMapTo=void 0;var n=r(28266),o=r(45321);t.mergeMapTo=function(e,t,r){return(void 0===r&&(r=1/0),o.isFunction(t))?n.mergeMap(function(){return e},t,r):("number"==typeof t&&(r=t),n.mergeMap(function(){return e},r))}},839:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.mergeScan=void 0;var n=r(81692),o=r(70146);t.mergeScan=function(e,t,r){return void 0===r&&(r=1/0),n.operate(function(n,i){var u=t;return o.mergeInternals(n,i,function(t,r){return e(u,t,r)},r,function(e){u=e},!1,void 0,function(){return u=null})})}},60914:(e,t,r)=>{var n=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),u=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)u.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return u},o=function(e,t){for(var r=0,n=t.length,o=e.length;r<n;r++,o++)e[o]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.mergeWith=void 0;var i=r(52846);t.mergeWith=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return i.merge.apply(void 0,o([],n(e)))}},82834:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.min=void 0;var n=r(33178),o=r(45321);t.min=function(e){return n.reduce(o.isFunction(e)?function(t,r){return 0>e(t,r)?t:r}:function(e,t){return e<t?e:t})}},86760:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.multicast=void 0;var n=r(92547),o=r(45321),i=r(55188);t.multicast=function(e,t){var r=o.isFunction(e)?e:function(){return e};return o.isFunction(t)?i.connect(t,{connector:r}):function(e){return new n.ConnectableObservable(e,r)}}},4819:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.observeOn=void 0;var n=r(92671),o=r(81692),i=r(6802);t.observeOn=function(e,t){return void 0===t&&(t=0),o.operate(function(r,o){r.subscribe(i.createOperatorSubscriber(o,function(r){return n.executeSchedule(o,e,function(){return o.next(r)},t)},function(){return n.executeSchedule(o,e,function(){return o.complete()},t)},function(r){return n.executeSchedule(o,e,function(){return o.error(r)},t)}))})}},9769:(e,t,r)=>{var n=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),u=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)u.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return u},o=function(e,t){for(var r=0,n=t.length,o=e.length;r<n;r++,o++)e[o]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.onErrorResumeNext=t.onErrorResumeNextWith=void 0;var i=r(61268),u=r(18539);function s(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=i.argsOrArgArray(e);return function(e){return u.onErrorResumeNext.apply(void 0,o([e],n(r)))}}t.onErrorResumeNextWith=s,t.onErrorResumeNext=s},72528:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.pairwise=void 0;var n=r(81692),o=r(6802);t.pairwise=function(){return n.operate(function(e,t){var r,n=!1;e.subscribe(o.createOperatorSubscriber(t,function(e){var o=r;r=e,n&&t.next([o,e]),n=!0}))})}},74684:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.partition=void 0;var n=r(18400),o=r(54610);t.partition=function(e,t){return function(r){return[o.filter(e,t)(r),o.filter(n.not(e,t))(r)]}}},23287:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.pluck=void 0;var n=r(77360);t.pluck=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=e.length;if(0===r)throw Error("list of properties cannot be empty.");return n.map(function(t){for(var n=t,o=0;o<r;o++){var i=null==n?void 0:n[e[o]];if(void 0===i)return;n=i}return n})}},99941:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.publish=void 0;var n=r(13712),o=r(86760),i=r(55188);t.publish=function(e){return e?function(t){return i.connect(e)(t)}:function(e){return o.multicast(new n.Subject)(e)}}},44799:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.publishBehavior=void 0;var n=r(63038),o=r(92547);t.publishBehavior=function(e){return function(t){var r=new n.BehaviorSubject(e);return new o.ConnectableObservable(t,function(){return r})}}},38617:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.publishLast=void 0;var n=r(48950),o=r(92547);t.publishLast=function(){return function(e){var t=new n.AsyncSubject;return new o.ConnectableObservable(e,function(){return t})}}},49274:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.publishReplay=void 0;var n=r(62837),o=r(86760),i=r(45321);t.publishReplay=function(e,t,r,u){r&&!i.isFunction(r)&&(u=r);var s=i.isFunction(r)?r:void 0;return function(r){return o.multicast(new n.ReplaySubject(e,t,u),s)(r)}}},18385:(e,t,r)=>{var n=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),u=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)u.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return u},o=function(e,t){for(var r=0,n=t.length,o=e.length;r<n;r++,o++)e[o]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.race=void 0;var i=r(61268),u=r(98077);t.race=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return u.raceWith.apply(void 0,o([],n(i.argsOrArgArray(e))))}},98077:(e,t,r)=>{var n=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),u=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)u.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return u},o=function(e,t){for(var r=0,n=t.length,o=e.length;r<n;r++,o++)e[o]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.raceWith=void 0;var i=r(78695),u=r(81692),s=r(30711);t.raceWith=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return e.length?u.operate(function(t,r){i.raceInit(o([t],n(e)))(r)}):s.identity}},33178:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.reduce=void 0;var n=r(70615),o=r(81692);t.reduce=function(e,t){return o.operate(n.scanInternals(e,t,arguments.length>=2,!1,!0))}},1328:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.refCount=void 0;var n=r(81692),o=r(6802);t.refCount=function(){return n.operate(function(e,t){var r=null;e._refCount++;var n=o.createOperatorSubscriber(t,void 0,void 0,void 0,function(){if(!e||e._refCount<=0||0<--e._refCount){r=null;return}var n=e._connection,o=r;r=null,n&&(!o||n===o)&&n.unsubscribe(),t.unsubscribe()});e.subscribe(n),n.closed||(r=e.connect())})}},11895:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.repeat=void 0;var n=r(69021),o=r(81692),i=r(6802),u=r(93836),s=r(21329);t.repeat=function(e){var t,r,a=1/0;return null!=e&&("object"==typeof e?(a=void 0===(t=e.count)?1/0:t,r=e.delay):a=e),a<=0?function(){return n.EMPTY}:o.operate(function(e,t){var n,o=0,c=function(){if(null==n||n.unsubscribe(),n=null,null!=r){var e="number"==typeof r?s.timer(r):u.innerFrom(r(o)),a=i.createOperatorSubscriber(t,function(){a.unsubscribe(),l()});e.subscribe(a)}else l()},l=function(){var r=!1;n=e.subscribe(i.createOperatorSubscriber(t,void 0,function(){++o<a?n?c():r=!0:t.complete()})),r&&c()};l()})}},77407:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.repeatWhen=void 0;var n=r(93836),o=r(13712),i=r(81692),u=r(6802);t.repeatWhen=function(e){return i.operate(function(t,r){var i,s,a=!1,c=!1,l=!1,f=function(){return l&&c&&(r.complete(),!0)},p=function(){l=!1,i=t.subscribe(u.createOperatorSubscriber(r,void 0,function(){l=!0,f()||(s||(s=new o.Subject,n.innerFrom(e(s)).subscribe(u.createOperatorSubscriber(r,function(){i?p():a=!0},function(){c=!0,f()}))),s).next()})),a&&(i.unsubscribe(),i=null,a=!1,p())};p()})}},60560:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.retry=void 0;var n=r(81692),o=r(6802),i=r(30711),u=r(21329),s=r(93836);t.retry=function(e){void 0===e&&(e=1/0);var t,r=(t=e&&"object"==typeof e?e:{count:e}).count,a=void 0===r?1/0:r,c=t.delay,l=t.resetOnSuccess,f=void 0!==l&&l;return a<=0?i.identity:n.operate(function(e,t){var r,n=0,i=function(){var l=!1;r=e.subscribe(o.createOperatorSubscriber(t,function(e){f&&(n=0),t.next(e)},void 0,function(e){if(n++<a){var f=function(){r?(r.unsubscribe(),r=null,i()):l=!0};if(null!=c){var p="number"==typeof c?u.timer(c):s.innerFrom(c(e,n)),d=o.createOperatorSubscriber(t,function(){d.unsubscribe(),f()},function(){t.complete()});p.subscribe(d)}else f()}else t.error(e)})),l&&(r.unsubscribe(),r=null,i())};i()})}},85796:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.retryWhen=void 0;var n=r(93836),o=r(13712),i=r(81692),u=r(6802);t.retryWhen=function(e){return i.operate(function(t,r){var i,s,a=!1,c=function(){i=t.subscribe(u.createOperatorSubscriber(r,void 0,void 0,function(t){s||(s=new o.Subject,n.innerFrom(e(s)).subscribe(u.createOperatorSubscriber(r,function(){return i?c():a=!0}))),s&&s.next(t)})),a&&(i.unsubscribe(),i=null,a=!1,c())};c()})}},57270:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.sample=void 0;var n=r(93836),o=r(81692),i=r(23965),u=r(6802);t.sample=function(e){return o.operate(function(t,r){var o=!1,s=null;t.subscribe(u.createOperatorSubscriber(r,function(e){o=!0,s=e})),n.innerFrom(e).subscribe(u.createOperatorSubscriber(r,function(){if(o){o=!1;var e=s;s=null,r.next(e)}},i.noop))})}},49067:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.sampleTime=void 0;var n=r(49658),o=r(57270),i=r(31063);t.sampleTime=function(e,t){return void 0===t&&(t=n.asyncScheduler),o.sample(i.interval(e,t))}},32629:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.scan=void 0;var n=r(81692),o=r(70615);t.scan=function(e,t){return n.operate(o.scanInternals(e,t,arguments.length>=2,!0))}},70615:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.scanInternals=void 0;var n=r(6802);t.scanInternals=function(e,t,r,o,i){return function(u,s){var a=r,c=t,l=0;u.subscribe(n.createOperatorSubscriber(s,function(t){var r=l++;c=a?e(c,t,r):(a=!0,t),o&&s.next(c)},i&&function(){a&&s.next(c),s.complete()}))}}},5657:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.sequenceEqual=void 0;var n=r(81692),o=r(6802),i=r(93836);function u(){return{buffer:[],complete:!1}}t.sequenceEqual=function(e,t){return void 0===t&&(t=function(e,t){return e===t}),n.operate(function(r,n){var s=u(),a=u(),c=function(e){n.next(e),n.complete()},l=function(e,r){var i=o.createOperatorSubscriber(n,function(n){var o=r.buffer,i=r.complete;0===o.length?i?c(!1):e.buffer.push(n):t(n,o.shift())||c(!1)},function(){e.complete=!0;var t=r.complete,n=r.buffer;t&&c(0===n.length),null==i||i.unsubscribe()});return i};r.subscribe(l(s,a)),i.innerFrom(e).subscribe(l(a,s))})}},33589:(e,t,r)=>{var n=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),u=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)u.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return u},o=function(e,t){for(var r=0,n=t.length,o=e.length;r<n;r++,o++)e[o]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.share=void 0;var i=r(93836),u=r(13712),s=r(27924),a=r(81692);function c(e,t){for(var r=[],u=2;u<arguments.length;u++)r[u-2]=arguments[u];if(!0===t){e();return}if(!1!==t){var a=new s.SafeSubscriber({next:function(){a.unsubscribe(),e()}});return i.innerFrom(t.apply(void 0,o([],n(r)))).subscribe(a)}}t.share=function(e){void 0===e&&(e={});var t=e.connector,r=void 0===t?function(){return new u.Subject}:t,n=e.resetOnError,o=void 0===n||n,l=e.resetOnComplete,f=void 0===l||l,p=e.resetOnRefCountZero,d=void 0===p||p;return function(e){var t,n,u,l=0,p=!1,h=!1,v=function(){null==n||n.unsubscribe(),n=void 0},b=function(){v(),t=u=void 0,p=h=!1},y=function(){var e=t;b(),null==e||e.unsubscribe()};return a.operate(function(e,a){l++,h||p||v();var m=u=null!=u?u:r();a.add(function(){0!=--l||h||p||(n=c(y,d))}),m.subscribe(a),!t&&l>0&&(t=new s.SafeSubscriber({next:function(e){return m.next(e)},error:function(e){h=!0,v(),n=c(b,o,e),m.error(e)},complete:function(){p=!0,v(),n=c(b,f),m.complete()}}),i.innerFrom(e).subscribe(t))})(e)}}},9482:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.shareReplay=void 0;var n=r(62837),o=r(33589);t.shareReplay=function(e,t,r){var i,u,s,a,c=!1;return e&&"object"==typeof e?(a=void 0===(i=e.bufferSize)?1/0:i,t=void 0===(u=e.windowTime)?1/0:u,c=void 0!==(s=e.refCount)&&s,r=e.scheduler):a=null!=e?e:1/0,o.share({connector:function(){return new n.ReplaySubject(a,t,r)},resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:c})}},92616:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.single=void 0;var n=r(63464),o=r(54132),i=r(88734),u=r(81692),s=r(6802);t.single=function(e){return u.operate(function(t,r){var u,a=!1,c=!1,l=0;t.subscribe(s.createOperatorSubscriber(r,function(n){c=!0,(!e||e(n,l++,t))&&(a&&r.error(new o.SequenceError("Too many matching values")),a=!0,u=n)},function(){a?(r.next(u),r.complete()):r.error(c?new i.NotFoundError("No matching values"):new n.EmptyError)}))})}},21427:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.skip=void 0;var n=r(54610);t.skip=function(e){return n.filter(function(t,r){return e<=r})}},71267:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.skipLast=void 0;var n=r(30711),o=r(81692),i=r(6802);t.skipLast=function(e){return e<=0?n.identity:o.operate(function(t,r){var n=Array(e),o=0;return t.subscribe(i.createOperatorSubscriber(r,function(t){var i=o++;if(i<e)n[i]=t;else{var u=i%e,s=n[u];n[u]=t,r.next(s)}})),function(){n=null}})}},41027:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.skipUntil=void 0;var n=r(81692),o=r(6802),i=r(93836),u=r(23965);t.skipUntil=function(e){return n.operate(function(t,r){var n=!1,s=o.createOperatorSubscriber(r,function(){null==s||s.unsubscribe(),n=!0},u.noop);i.innerFrom(e).subscribe(s),t.subscribe(o.createOperatorSubscriber(r,function(e){return n&&r.next(e)}))})}},47762:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.skipWhile=void 0;var n=r(81692),o=r(6802);t.skipWhile=function(e){return n.operate(function(t,r){var n=!1,i=0;t.subscribe(o.createOperatorSubscriber(r,function(t){return(n||(n=!e(t,i++)))&&r.next(t)}))})}},42754:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.startWith=void 0;var n=r(97302),o=r(1972),i=r(81692);t.startWith=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=o.popScheduler(e);return i.operate(function(t,o){(r?n.concat(e,t,r):n.concat(e,t)).subscribe(o)})}},17035:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.subscribeOn=void 0;var n=r(81692);t.subscribeOn=function(e,t){return void 0===t&&(t=0),n.operate(function(r,n){n.add(e.schedule(function(){return r.subscribe(n)},t))})}},67193:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.switchAll=void 0;var n=r(52112),o=r(30711);t.switchAll=function(){return n.switchMap(o.identity)}},52112:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.switchMap=void 0;var n=r(93836),o=r(81692),i=r(6802);t.switchMap=function(e,t){return o.operate(function(r,o){var u=null,s=0,a=!1,c=function(){return a&&!u&&o.complete()};r.subscribe(i.createOperatorSubscriber(o,function(r){null==u||u.unsubscribe();var a=0,l=s++;n.innerFrom(e(r,l)).subscribe(u=i.createOperatorSubscriber(o,function(e){return o.next(t?t(r,e,l,a++):e)},function(){u=null,c()}))},function(){a=!0,c()}))})}},5897:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.switchMapTo=void 0;var n=r(52112),o=r(45321);t.switchMapTo=function(e,t){return o.isFunction(t)?n.switchMap(function(){return e},t):n.switchMap(function(){return e})}},32821:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.switchScan=void 0;var n=r(52112),o=r(81692);t.switchScan=function(e,t){return o.operate(function(r,o){var i=t;return n.switchMap(function(t,r){return e(i,t,r)},function(e,t){return i=t,t})(r).subscribe(o),function(){i=null}})}},68987:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.take=void 0;var n=r(69021),o=r(81692),i=r(6802);t.take=function(e){return e<=0?function(){return n.EMPTY}:o.operate(function(t,r){var n=0;t.subscribe(i.createOperatorSubscriber(r,function(t){++n<=e&&(r.next(t),e<=n&&r.complete())}))})}},13019:(e,t,r)=>{var n=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(t,"__esModule",{value:!0}),t.takeLast=void 0;var o=r(69021),i=r(81692),u=r(6802);t.takeLast=function(e){return e<=0?function(){return o.EMPTY}:i.operate(function(t,r){var o=[];t.subscribe(u.createOperatorSubscriber(r,function(t){o.push(t),e<o.length&&o.shift()},function(){var e,t;try{for(var i=n(o),u=i.next();!u.done;u=i.next()){var s=u.value;r.next(s)}}catch(t){e={error:t}}finally{try{u&&!u.done&&(t=i.return)&&t.call(i)}finally{if(e)throw e.error}}r.complete()},void 0,function(){o=null}))})}},54507:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.takeUntil=void 0;var n=r(81692),o=r(6802),i=r(93836),u=r(23965);t.takeUntil=function(e){return n.operate(function(t,r){i.innerFrom(e).subscribe(o.createOperatorSubscriber(r,function(){return r.complete()},u.noop)),r.closed||t.subscribe(r)})}},35930:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.takeWhile=void 0;var n=r(81692),o=r(6802);t.takeWhile=function(e,t){return void 0===t&&(t=!1),n.operate(function(r,n){var i=0;r.subscribe(o.createOperatorSubscriber(n,function(r){var o=e(r,i++);(o||t)&&n.next(r),o||n.complete()}))})}},53439:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.tap=void 0;var n=r(45321),o=r(81692),i=r(6802),u=r(30711);t.tap=function(e,t,r){var s=n.isFunction(e)||t||r?{next:e,error:t,complete:r}:e;return s?o.operate(function(e,t){null===(r=s.subscribe)||void 0===r||r.call(s);var r,n=!0;e.subscribe(i.createOperatorSubscriber(t,function(e){var r;null===(r=s.next)||void 0===r||r.call(s,e),t.next(e)},function(){var e;n=!1,null===(e=s.complete)||void 0===e||e.call(s),t.complete()},function(e){var r;n=!1,null===(r=s.error)||void 0===r||r.call(s,e),t.error(e)},function(){var e,t;n&&(null===(e=s.unsubscribe)||void 0===e||e.call(s)),null===(t=s.finalize)||void 0===t||t.call(s)}))}):u.identity}},83432:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.throttle=void 0;var n=r(81692),o=r(6802),i=r(93836);t.throttle=function(e,t){return n.operate(function(r,n){var u=null!=t?t:{},s=u.leading,a=void 0===s||s,c=u.trailing,l=void 0!==c&&c,f=!1,p=null,d=null,h=!1,v=function(){null==d||d.unsubscribe(),d=null,l&&(m(),h&&n.complete())},b=function(){d=null,h&&n.complete()},y=function(t){return d=i.innerFrom(e(t)).subscribe(o.createOperatorSubscriber(n,v,b))},m=function(){if(f){f=!1;var e=p;p=null,n.next(e),h||y(e)}};r.subscribe(o.createOperatorSubscriber(n,function(e){f=!0,p=e,d&&!d.closed||(a?m():y(e))},function(){h=!0,l&&f&&d&&!d.closed||n.complete()}))})}},46165:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.throttleTime=void 0;var n=r(49658),o=r(83432),i=r(21329);t.throttleTime=function(e,t,r){void 0===t&&(t=n.asyncScheduler);var u=i.timer(e,t);return o.throttle(function(){return u},r)}},25232:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.throwIfEmpty=void 0;var n=r(63464),o=r(81692),i=r(6802);function u(){return new n.EmptyError}t.throwIfEmpty=function(e){return void 0===e&&(e=u),o.operate(function(t,r){var n=!1;t.subscribe(i.createOperatorSubscriber(r,function(e){n=!0,r.next(e)},function(){return n?r.complete():r.error(e())}))})}},51740:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TimeInterval=t.timeInterval=void 0;var n=r(49658),o=r(81692),i=r(6802);t.timeInterval=function(e){return void 0===e&&(e=n.asyncScheduler),o.operate(function(t,r){var n=e.now();t.subscribe(i.createOperatorSubscriber(r,function(t){var o=e.now(),i=o-n;n=o,r.next(new u(t,i))}))})};var u=function(e,t){this.value=e,this.interval=t};t.TimeInterval=u},88513:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.timeout=t.TimeoutError=void 0;var n=r(49658),o=r(20593),i=r(81692),u=r(93836),s=r(18359),a=r(6802),c=r(92671);function l(e){throw new t.TimeoutError(e)}t.TimeoutError=s.createErrorClass(function(e){return function(t){void 0===t&&(t=null),e(this),this.message="Timeout has occurred",this.name="TimeoutError",this.info=t}}),t.timeout=function(e,t){var r=o.isValidDate(e)?{first:e}:"number"==typeof e?{each:e}:e,s=r.first,f=r.each,p=r.with,d=void 0===p?l:p,h=r.scheduler,v=void 0===h?null!=t?t:n.asyncScheduler:h,b=r.meta,y=void 0===b?null:b;if(null==s&&null==f)throw TypeError("No timeout provided.");return i.operate(function(e,t){var r,n,o=null,i=0,l=function(e){n=c.executeSchedule(t,v,function(){try{r.unsubscribe(),u.innerFrom(d({meta:y,lastValue:o,seen:i})).subscribe(t)}catch(e){t.error(e)}},e)};r=e.subscribe(a.createOperatorSubscriber(t,function(e){null==n||n.unsubscribe(),i++,t.next(o=e),f>0&&l(f)},void 0,void 0,function(){(null==n?void 0:n.closed)||null==n||n.unsubscribe(),o=null})),i||l(null!=s?"number"==typeof s?s:+s-v.now():f)})}},67981:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.timeoutWith=void 0;var n=r(49658),o=r(20593),i=r(88513);t.timeoutWith=function(e,t,r){var u,s,a;if(r=null!=r?r:n.async,o.isValidDate(e)?u=e:"number"==typeof e&&(s=e),t)a=function(){return t};else throw TypeError("No observable provided to switch to");if(null==u&&null==s)throw TypeError("No timeout provided.");return i.timeout({first:u,each:s,scheduler:r,with:a})}},31958:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.timestamp=void 0;var n=r(46655),o=r(77360);t.timestamp=function(e){return void 0===e&&(e=n.dateTimestampProvider),o.map(function(t){return{value:t,timestamp:e.now()}})}},71596:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.toArray=void 0;var n=r(33178),o=r(81692),i=function(e,t){return e.push(t),e};t.toArray=function(){return o.operate(function(e,t){n.reduce(i,[])(e).subscribe(t)})}},68384:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.window=void 0;var n=r(13712),o=r(81692),i=r(6802),u=r(23965),s=r(93836);t.window=function(e){return o.operate(function(t,r){var o=new n.Subject;r.next(o.asObservable());var a=function(e){o.error(e),r.error(e)};return t.subscribe(i.createOperatorSubscriber(r,function(e){return null==o?void 0:o.next(e)},function(){o.complete(),r.complete()},a)),s.innerFrom(e).subscribe(i.createOperatorSubscriber(r,function(){o.complete(),r.next(o=new n.Subject)},u.noop,a)),function(){null==o||o.unsubscribe(),o=null}})}},50117:(e,t,r)=>{var n=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(t,"__esModule",{value:!0}),t.windowCount=void 0;var o=r(13712),i=r(81692),u=r(6802);t.windowCount=function(e,t){void 0===t&&(t=0);var r=t>0?t:e;return i.operate(function(t,i){var s=[new o.Subject],a=0;i.next(s[0].asObservable()),t.subscribe(u.createOperatorSubscriber(i,function(t){try{for(var u,c,l=n(s),f=l.next();!f.done;f=l.next())f.value.next(t)}catch(e){u={error:e}}finally{try{f&&!f.done&&(c=l.return)&&c.call(l)}finally{if(u)throw u.error}}var p=a-e+1;if(p>=0&&p%r==0&&s.shift().complete(),++a%r==0){var d=new o.Subject;s.push(d),i.next(d.asObservable())}},function(){for(;s.length>0;)s.shift().complete();i.complete()},function(e){for(;s.length>0;)s.shift().error(e);i.error(e)},function(){s=null}))})}},4077:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.windowTime=void 0;var n=r(13712),o=r(49658),i=r(65107),u=r(81692),s=r(6802),a=r(46674),c=r(1972),l=r(92671);t.windowTime=function(e){for(var t,r,f=[],p=1;p<arguments.length;p++)f[p-1]=arguments[p];var d=null!==(t=c.popScheduler(f))&&void 0!==t?t:o.asyncScheduler,h=null!==(r=f[0])&&void 0!==r?r:null,v=f[1]||1/0;return u.operate(function(t,r){var o=[],u=!1,c=function(e){var t=e.window,r=e.subs;t.complete(),r.unsubscribe(),a.arrRemove(o,e),u&&f()},f=function(){if(o){var t=new i.Subscription;r.add(t);var u=new n.Subject,s={window:u,subs:t,seen:0};o.push(s),r.next(u.asObservable()),l.executeSchedule(t,d,function(){return c(s)},e)}};null!==h&&h>=0?l.executeSchedule(r,d,f,h,!0):u=!0,f();var p=function(e){return o.slice().forEach(e)},b=function(e){p(function(t){return e(t.window)}),e(r),r.unsubscribe()};return t.subscribe(s.createOperatorSubscriber(r,function(e){p(function(t){t.window.next(e),v<=++t.seen&&c(t)})},function(){return b(function(e){return e.complete()})},function(e){return b(function(t){return t.error(e)})})),function(){o=null}})}},85588:(e,t,r)=>{var n=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(t,"__esModule",{value:!0}),t.windowToggle=void 0;var o=r(13712),i=r(65107),u=r(81692),s=r(93836),a=r(6802),c=r(23965),l=r(46674);t.windowToggle=function(e,t){return u.operate(function(r,u){var f=[],p=function(e){for(;0<f.length;)f.shift().error(e);u.error(e)};s.innerFrom(e).subscribe(a.createOperatorSubscriber(u,function(e){var r,n=new o.Subject;f.push(n);var d=new i.Subscription;try{r=s.innerFrom(t(e))}catch(e){p(e);return}u.next(n.asObservable()),d.add(r.subscribe(a.createOperatorSubscriber(u,function(){l.arrRemove(f,n),n.complete(),d.unsubscribe()},c.noop,p)))},c.noop)),r.subscribe(a.createOperatorSubscriber(u,function(e){var t,r,o=f.slice();try{for(var i=n(o),u=i.next();!u.done;u=i.next())u.value.next(e)}catch(e){t={error:e}}finally{try{u&&!u.done&&(r=i.return)&&r.call(i)}finally{if(t)throw t.error}}},function(){for(;0<f.length;)f.shift().complete();u.complete()},p,function(){for(;0<f.length;)f.shift().unsubscribe()}))})}},3412:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.windowWhen=void 0;var n=r(13712),o=r(81692),i=r(6802),u=r(93836);t.windowWhen=function(e){return o.operate(function(t,r){var o,s,a=function(e){o.error(e),r.error(e)},c=function(){var t;null==s||s.unsubscribe(),null==o||o.complete(),o=new n.Subject,r.next(o.asObservable());try{t=u.innerFrom(e())}catch(e){a(e);return}t.subscribe(s=i.createOperatorSubscriber(r,c,c,a))};c(),t.subscribe(i.createOperatorSubscriber(r,function(e){return o.next(e)},function(){o.complete(),r.complete()},a,function(){null==s||s.unsubscribe(),o=null}))})}},89099:(e,t,r)=>{var n=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),u=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)u.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return u},o=function(e,t){for(var r=0,n=t.length,o=e.length;r<n;r++,o++)e[o]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.withLatestFrom=void 0;var i=r(81692),u=r(6802),s=r(93836),a=r(30711),c=r(23965),l=r(1972);t.withLatestFrom=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=l.popResultSelector(e);return i.operate(function(t,i){for(var l=e.length,f=Array(l),p=e.map(function(){return!1}),d=!1,h=function(t){s.innerFrom(e[t]).subscribe(u.createOperatorSubscriber(i,function(e){f[t]=e,!d&&!p[t]&&(p[t]=!0,(d=p.every(a.identity))&&(p=null))},c.noop))},v=0;v<l;v++)h(v);t.subscribe(u.createOperatorSubscriber(i,function(e){if(d){var t=o([e],n(f));i.next(r?r.apply(void 0,o([],n(t))):t)}}))})}},14597:(e,t,r)=>{var n=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),u=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)u.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return u},o=function(e,t){for(var r=0,n=t.length,o=e.length;r<n;r++,o++)e[o]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.zip=void 0;var i=r(68483),u=r(81692);t.zip=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return u.operate(function(t,r){i.zip.apply(void 0,o([t],n(e))).subscribe(r)})}},21070:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.zipAll=void 0;var n=r(68483),o=r(55147);t.zipAll=function(e){return o.joinAllInternals(n.zip,e)}},26513:(e,t,r)=>{var n=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),u=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)u.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return u},o=function(e,t){for(var r=0,n=t.length,o=e.length;r<n;r++,o++)e[o]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.zipWith=void 0;var i=r(14597);t.zipWith=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return i.zip.apply(void 0,o([],n(e)))}},61370:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.scheduleArray=void 0;var n=r(77207);t.scheduleArray=function(e,t){return new n.Observable(function(r){var n=0;return t.schedule(function(){n===e.length?r.complete():(r.next(e[n++]),r.closed||this.schedule())})})}},72145:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.scheduleAsyncIterable=void 0;var n=r(77207),o=r(92671);t.scheduleAsyncIterable=function(e,t){if(!e)throw Error("Iterable cannot be null");return new n.Observable(function(r){o.executeSchedule(r,t,function(){var n=e[Symbol.asyncIterator]();o.executeSchedule(r,t,function(){n.next().then(function(e){e.done?r.complete():r.next(e.value)})},0,!0)})})}},71757:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.scheduleIterable=void 0;var n=r(77207),o=r(25887),i=r(45321),u=r(92671);t.scheduleIterable=function(e,t){return new n.Observable(function(r){var n;return u.executeSchedule(r,t,function(){n=e[o.iterator](),u.executeSchedule(r,t,function(){var e,t,o;try{t=(e=n.next()).value,o=e.done}catch(e){r.error(e);return}o?r.complete():r.next(t)},0,!0)}),function(){return i.isFunction(null==n?void 0:n.return)&&n.return()}})}},97806:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.scheduleObservable=void 0;var n=r(93836),o=r(4819),i=r(17035);t.scheduleObservable=function(e,t){return n.innerFrom(e).pipe(i.subscribeOn(t),o.observeOn(t))}},55230:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.schedulePromise=void 0;var n=r(93836),o=r(4819),i=r(17035);t.schedulePromise=function(e,t){return n.innerFrom(e).pipe(i.subscribeOn(t),o.observeOn(t))}},98412:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.scheduleReadableStreamLike=void 0;var n=r(72145),o=r(28874);t.scheduleReadableStreamLike=function(e,t){return n.scheduleAsyncIterable(o.readableStreamLikeToAsyncGenerator(e),t)}},22423:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.scheduled=void 0;var n=r(97806),o=r(55230),i=r(61370),u=r(71757),s=r(72145),a=r(7389),c=r(65256),l=r(58219),f=r(10623),p=r(62131),d=r(81897),h=r(28874),v=r(98412);t.scheduled=function(e,t){if(null!=e){if(a.isInteropObservable(e))return n.scheduleObservable(e,t);if(l.isArrayLike(e))return i.scheduleArray(e,t);if(c.isPromise(e))return o.schedulePromise(e,t);if(p.isAsyncIterable(e))return s.scheduleAsyncIterable(e,t);if(f.isIterable(e))return u.scheduleIterable(e,t);if(h.isReadableStreamLike(e))return v.scheduleReadableStreamLike(e,t)}throw d.createInvalidObservableTypeError(e)}},79806:(e,t,r)=>{var n=function(){var e=function(t,r){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(t,"__esModule",{value:!0}),t.Action=void 0;var o=function(e){function t(t,r){return e.call(this)||this}return n(t,e),t.prototype.schedule=function(e,t){return void 0===t&&(t=0),this},t}(r(65107).Subscription);t.Action=o},81732:(e,t,r)=>{var n=function(){var e=function(t,r){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(t,"__esModule",{value:!0}),t.AsyncAction=void 0;var o=r(79806),i=r(99708),u=r(46674),s=function(e){function t(t,r){var n=e.call(this,t,r)||this;return n.scheduler=t,n.work=r,n.pending=!1,n}return n(t,e),t.prototype.schedule=function(e,t){if(void 0===t&&(t=0),this.closed)return this;this.state=e;var r,n=this.id,o=this.scheduler;return null!=n&&(this.id=this.recycleAsyncId(o,n,t)),this.pending=!0,this.delay=t,this.id=null!==(r=this.id)&&void 0!==r?r:this.requestAsyncId(o,this.id,t),this},t.prototype.requestAsyncId=function(e,t,r){return void 0===r&&(r=0),i.intervalProvider.setInterval(e.flush.bind(e,this),r)},t.prototype.recycleAsyncId=function(e,t,r){if(void 0===r&&(r=0),null!=r&&this.delay===r&&!1===this.pending)return t;null!=t&&i.intervalProvider.clearInterval(t)},t.prototype.execute=function(e,t){if(this.closed)return Error("executing a cancelled action");this.pending=!1;var r=this._execute(e,t);if(r)return r;!1===this.pending&&null!=this.id&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))},t.prototype._execute=function(e,t){var r,n=!1;try{this.work(e)}catch(e){n=!0,r=e||Error("Scheduled action threw falsy error")}if(n)return this.unsubscribe(),r},t.prototype.unsubscribe=function(){if(!this.closed){var t=this.id,r=this.scheduler,n=r.actions;this.work=this.state=this.scheduler=null,this.pending=!1,u.arrRemove(n,this),null!=t&&(this.id=this.recycleAsyncId(r,t,null)),this.delay=null,e.prototype.unsubscribe.call(this)}},t}(o.Action);t.AsyncAction=s},37477:(e,t,r)=>{var n=function(){var e=function(t,r){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();Object.defineProperty(t,"__esModule",{value:!0}),t.AsyncScheduler=void 0;var o=r(72665),i=function(e){function t(t,r){void 0===r&&(r=o.Scheduler.now);var n=e.call(this,t,r)||this;return n.actions=[],n._active=!1,n}return n(t,e),t.prototype.flush=function(e){var t,r=this.actions;if(this._active){r.push(e);return}this._active=!0;do if(t=e.execute(e.state,e.delay))break;while(e=r.shift());if(this._active=!1,t){for(;e=r.shift();)e.unsubscribe();throw t}},t}(o.Scheduler);t.AsyncScheduler=i},49658:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.async=t.asyncScheduler=void 0;var n=r(81732),o=r(37477);t.asyncScheduler=new o.AsyncScheduler(n.AsyncAction),t.async=t.asyncScheduler},46655:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.dateTimestampProvider=void 0,t.dateTimestampProvider={now:function(){return(t.dateTimestampProvider.delegate||Date).now()},delegate:void 0}},99708:(e,t)=>{var r=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),u=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)u.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return u},n=function(e,t){for(var r=0,n=t.length,o=e.length;r<n;r++,o++)e[o]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.intervalProvider=void 0,t.intervalProvider={setInterval:function(e,o){for(var i=[],u=2;u<arguments.length;u++)i[u-2]=arguments[u];var s=t.intervalProvider.delegate;return(null==s?void 0:s.setInterval)?s.setInterval.apply(s,n([e,o],r(i))):setInterval.apply(void 0,n([e,o],r(i)))},clearInterval:function(e){var r=t.intervalProvider.delegate;return((null==r?void 0:r.clearInterval)||clearInterval)(e)},delegate:void 0}},67248:(e,t)=>{var r=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),u=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)u.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return u},n=function(e,t){for(var r=0,n=t.length,o=e.length;r<n;r++,o++)e[o]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.timeoutProvider=void 0,t.timeoutProvider={setTimeout:function(e,o){for(var i=[],u=2;u<arguments.length;u++)i[u-2]=arguments[u];var s=t.timeoutProvider.delegate;return(null==s?void 0:s.setTimeout)?s.setTimeout.apply(s,n([e,o],r(i))):setTimeout.apply(void 0,n([e,o],r(i)))},clearTimeout:function(e){var r=t.timeoutProvider.delegate;return((null==r?void 0:r.clearTimeout)||clearTimeout)(e)},delegate:void 0}},25887:(e,t)=>{function r(){return"function"==typeof Symbol&&Symbol.iterator?Symbol.iterator:"@@iterator"}Object.defineProperty(t,"__esModule",{value:!0}),t.iterator=t.getSymbolIterator=void 0,t.getSymbolIterator=r,t.iterator=r()},19284:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.observable=void 0,t.observable="function"==typeof Symbol&&Symbol.observable||"@@observable"},81816:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ArgumentOutOfRangeError=void 0;var n=r(18359);t.ArgumentOutOfRangeError=n.createErrorClass(function(e){return function(){e(this),this.name="ArgumentOutOfRangeError",this.message="argument out of range"}})},63464:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.EmptyError=void 0;var n=r(18359);t.EmptyError=n.createErrorClass(function(e){return function(){e(this),this.name="EmptyError",this.message="no elements in sequence"}})},88734:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NotFoundError=void 0;var n=r(18359);t.NotFoundError=n.createErrorClass(function(e){return function(t){e(this),this.name="NotFoundError",this.message=t}})},97619:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ObjectUnsubscribedError=void 0;var n=r(18359);t.ObjectUnsubscribedError=n.createErrorClass(function(e){return function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"}})},54132:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SequenceError=void 0;var n=r(18359);t.SequenceError=n.createErrorClass(function(e){return function(t){e(this),this.name="SequenceError",this.message=t}})},70661:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.UnsubscriptionError=void 0;var n=r(18359);t.UnsubscriptionError=n.createErrorClass(function(e){return function(t){e(this),this.message=t?t.length+" errors occurred during unsubscription:\n"+t.map(function(e,t){return t+1+") "+e.toString()}).join("\n  "):"",this.name="UnsubscriptionError",this.errors=t}})},1972:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.popNumber=t.popScheduler=t.popResultSelector=void 0;var n=r(45321),o=r(77796);function i(e){return e[e.length-1]}t.popResultSelector=function(e){return n.isFunction(i(e))?e.pop():void 0},t.popScheduler=function(e){return o.isScheduler(i(e))?e.pop():void 0},t.popNumber=function(e,t){return"number"==typeof i(e)?e.pop():t}},60771:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.argsArgArrayOrObject=void 0;var r=Array.isArray,n=Object.getPrototypeOf,o=Object.prototype,i=Object.keys;t.argsArgArrayOrObject=function(e){if(1===e.length){var t=e[0];if(r(t))return{args:t,keys:null};if(t&&"object"==typeof t&&n(t)===o){var u=i(t);return{args:u.map(function(e){return t[e]}),keys:u}}}return{args:e,keys:null}}},61268:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.argsOrArgArray=void 0;var r=Array.isArray;t.argsOrArgArray=function(e){return 1===e.length&&r(e[0])?e[0]:e}},46674:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.arrRemove=void 0,t.arrRemove=function(e,t){if(e){var r=e.indexOf(t);0<=r&&e.splice(r,1)}}},18359:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createErrorClass=void 0,t.createErrorClass=function(e){var t=e(function(e){Error.call(e),e.stack=Error().stack});return t.prototype=Object.create(Error.prototype),t.prototype.constructor=t,t}},19342:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createObject=void 0,t.createObject=function(e,t){return e.reduce(function(e,r,n){return e[r]=t[n],e},{})}},60392:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.captureError=t.errorContext=void 0;var n=r(15720),o=null;t.errorContext=function(e){if(n.config.useDeprecatedSynchronousErrorHandling){var t=!o;if(t&&(o={errorThrown:!1,error:null}),e(),t){var r=o,i=r.errorThrown,u=r.error;if(o=null,i)throw u}}else e()},t.captureError=function(e){n.config.useDeprecatedSynchronousErrorHandling&&o&&(o.errorThrown=!0,o.error=e)}},92671:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.executeSchedule=void 0,t.executeSchedule=function(e,t,r,n,o){void 0===n&&(n=0),void 0===o&&(o=!1);var i=t.schedule(function(){r(),o?e.add(this.schedule(null,n)):this.unsubscribe()},n);if(e.add(i),!o)return i}},30711:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.identity=void 0,t.identity=function(e){return e}},58219:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isArrayLike=void 0,t.isArrayLike=function(e){return e&&"number"==typeof e.length&&"function"!=typeof e}},62131:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isAsyncIterable=void 0;var n=r(45321);t.isAsyncIterable=function(e){return Symbol.asyncIterator&&n.isFunction(null==e?void 0:e[Symbol.asyncIterator])}},20593:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isValidDate=void 0,t.isValidDate=function(e){return e instanceof Date&&!isNaN(e)}},45321:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isFunction=void 0,t.isFunction=function(e){return"function"==typeof e}},7389:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isInteropObservable=void 0;var n=r(19284),o=r(45321);t.isInteropObservable=function(e){return o.isFunction(e[n.observable])}},10623:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isIterable=void 0;var n=r(25887),o=r(45321);t.isIterable=function(e){return o.isFunction(null==e?void 0:e[n.iterator])}},65256:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isPromise=void 0;var n=r(45321);t.isPromise=function(e){return n.isFunction(null==e?void 0:e.then)}},28874:(e,t,r)=>{var n=function(e,t){var r,n,o,i,u={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(r)throw TypeError("Generator is already executing.");for(;u;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return u.label++,{value:i[1],done:!1};case 5:u.label++,n=i[1],i=[0];continue;case 7:i=u.ops.pop(),u.trys.pop();continue;default:if(!(o=(o=u.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){u=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){u.label=i[1];break}if(6===i[0]&&u.label<o[1]){u.label=o[1],o=i;break}if(o&&u.label<o[2]){u.label=o[2],u.ops.push(i);break}o[2]&&u.ops.pop(),u.trys.pop();continue}i=t.call(e,u)}catch(e){i=[6,e],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}},o=function(e){return this instanceof o?(this.v=e,this):new o(e)},i=function(e,t,r){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var n,i=r.apply(e,t||[]),u=[];return n={},s("next"),s("throw"),s("return"),n[Symbol.asyncIterator]=function(){return this},n;function s(e){i[e]&&(n[e]=function(t){return new Promise(function(r,n){u.push([e,t,r,n])>1||a(e,t)})})}function a(e,t){try{var r;(r=i[e](t)).value instanceof o?Promise.resolve(r.value.v).then(c,l):f(u[0][2],r)}catch(e){f(u[0][3],e)}}function c(e){a("next",e)}function l(e){a("throw",e)}function f(e,t){e(t),u.shift(),u.length&&a(u[0][0],u[0][1])}};Object.defineProperty(t,"__esModule",{value:!0}),t.isReadableStreamLike=t.readableStreamLikeToAsyncGenerator=void 0;var u=r(45321);t.readableStreamLikeToAsyncGenerator=function(e){return i(this,arguments,function(){var t,r,i;return n(this,function(n){switch(n.label){case 0:t=e.getReader(),n.label=1;case 1:n.trys.push([1,,9,10]),n.label=2;case 2:return[4,o(t.read())];case 3:if(i=(r=n.sent()).value,!r.done)return[3,5];return[4,o(void 0)];case 4:return[2,n.sent()];case 5:return[4,o(i)];case 6:return[4,n.sent()];case 7:return n.sent(),[3,2];case 8:return[3,10];case 9:return t.releaseLock(),[7];case 10:return[2]}})})},t.isReadableStreamLike=function(e){return u.isFunction(null==e?void 0:e.getReader)}},77796:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isScheduler=void 0;var n=r(45321);t.isScheduler=function(e){return e&&n.isFunction(e.schedule)}},81692:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.operate=t.hasLift=void 0;var n=r(45321);function o(e){return n.isFunction(null==e?void 0:e.lift)}t.hasLift=o,t.operate=function(e){return function(t){if(o(t))return t.lift(function(t){try{return e(t,this)}catch(e){this.error(e)}});throw TypeError("Unable to lift unknown Observable type")}}},47052:(e,t,r)=>{var n=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),u=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)u.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return u},o=function(e,t){for(var r=0,n=t.length,o=e.length;r<n;r++,o++)e[o]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.mapOneOrManyArgs=void 0;var i=r(77360),u=Array.isArray;t.mapOneOrManyArgs=function(e){return i.map(function(t){return u(t)?e.apply(void 0,o([],n(t))):e(t)})}},23965:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.noop=void 0,t.noop=function(){}},18400:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.not=void 0,t.not=function(e,t){return function(r,n){return!e.call(t,r,n)}}},3349:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.pipeFromArray=t.pipe=void 0;var n=r(30711);function o(e){return 0===e.length?n.identity:1===e.length?e[0]:function(t){return e.reduce(function(e,t){return t(e)},t)}}t.pipe=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return o(e)},t.pipeFromArray=o},67108:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.reportUnhandledError=void 0;var n=r(15720),o=r(67248);t.reportUnhandledError=function(e){o.timeoutProvider.setTimeout(function(){var t=n.config.onUnhandledError;if(t)t(e);else throw e})}},81897:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createInvalidObservableTypeError=void 0,t.createInvalidObservableTypeError=function(e){return TypeError("You provided "+(null!==e&&"object"==typeof e?"an invalid object":"'"+e+"'")+" where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.")}},85798:(e,t,r)=>{t.Tj=t.jE=t.pb=t.vp=void 0,r(78085),r(13008),r(22542),r(49187),r(65283),r(15790),r(26658),r(25611),r(93132),r(66277),r(92436);var n=r(61388);Object.defineProperty(t,"vp",{enumerable:!0,get:function(){return n.combineLatestWith}}),r(17920),r(72169),r(72224),r(49785),r(54360),r(55188),r(47061),r(8051),r(75134),r(96107),r(54781),r(20141),r(68598),r(84772),r(7884),r(97319),r(23099),r(48467),r(18913),r(30896),r(26137),r(95824),r(48016);var o=r(54610);Object.defineProperty(t,"pb",{enumerable:!0,get:function(){return o.filter}});var i=r(98982);Object.defineProperty(t,"jE",{enumerable:!0,get:function(){return i.finalize}}),r(72019),r(67311),r(37564),r(73392),r(38347),r(38367),r(96032);var u=r(77360);Object.defineProperty(t,"Tj",{enumerable:!0,get:function(){return u.map}}),r(55177),r(60687),r(86696),r(52846),r(21683),r(27917),r(28266),r(60095),r(839),r(60914),r(82834),r(86760),r(4819),r(9769),r(72528),r(74684),r(23287),r(99941),r(44799),r(38617),r(49274),r(18385),r(98077),r(33178),r(11895),r(77407),r(60560),r(85796),r(1328),r(57270),r(49067),r(32629),r(5657),r(33589),r(9482),r(92616),r(21427),r(71267),r(41027),r(47762),r(42754),r(17035),r(67193),r(52112),r(5897),r(32821),r(68987),r(13019),r(54507),r(35930),r(53439),r(83432),r(46165),r(25232),r(51740),r(88513),r(67981),r(31958),r(71596),r(68384),r(50117),r(4077),r(85588),r(3412),r(89099),r(14597),r(21070),r(26513)},20872:(e,t,r)=>{r.d(t,{C:()=>u,Q:()=>c});var n={0:8203,1:8204,2:8205,3:8290,4:8291,5:8288,6:65279,7:8289,8:119155,9:119156,a:119157,b:119158,c:119159,d:119160,e:119161,f:119162},o={0:8203,1:8204,2:8205,3:65279},i=[,,,,].fill(String.fromCodePoint(o[0])).join("");function u(e,t,r="auto"){let n;return!0===r||"auto"===r&&(!(!Number.isNaN(Number(e))||/[a-z]/i.test(e)&&!/\d+(?:[-:\/]\d+){2}(?:T\d+(?:[-:\/]\d+){1,2}(\.\d+)?Z?)?/.test(e))&&Date.parse(e)||function(e){try{new URL(e,e.startsWith("/")?"https://acme.com":void 0)}catch{return!1}return!0}(e))?e:`${e}${n=JSON.stringify(t),`${i}${Array.from(n).map(e=>{let t=e.charCodeAt(0);if(t>255)throw Error(`Only ASCII edit info can be encoded. Error attempting to encode ${n} on character ${e} (${t})`);return Array.from(t.toString(4).padStart(4,"0")).map(e=>String.fromCodePoint(o[e])).join("")}).join("")}`}`}Object.fromEntries(Object.entries(o).map(e=>e.reverse())),Object.fromEntries(Object.entries(n).map(e=>e.reverse()));var s=`${Object.values(n).map(e=>`\\u{${e.toString(16)}}`).join("")}`,a=RegExp(`[${s}]{4,}`,"gu");function c(e){var t,r;return e&&JSON.parse({cleaned:(t=JSON.stringify(e)).replace(a,""),encoded:(null==(r=t.match(a))?void 0:r[0])||""}.cleaned)}},3709:(e,t,r)=>{let n,o;r.d(t,{UU:()=>nc});let i=!(typeof navigator>"u")&&"ReactNative"===navigator.product,u={timeout:i?6e4:12e4},s=function(e){let t={...u,..."string"==typeof e?{url:e}:e};if(t.timeout=function e(t){if(!1===t||0===t)return!1;if(t.connect||t.socket)return t;let r=Number(t);return isNaN(r)?e(u.timeout):{connect:r,socket:r}}(t.timeout),t.query){let{url:e,searchParams:r}=function(e){let t=e.indexOf("?");if(-1===t)return{url:e,searchParams:new URLSearchParams};let r=e.slice(0,t),n=e.slice(t+1);if(!i)return{url:r,searchParams:new URLSearchParams(n)};if("function"!=typeof decodeURIComponent)throw Error("Broken `URLSearchParams` implementation, and `decodeURIComponent` is not defined");let o=new URLSearchParams;for(let e of n.split("&")){let[t,r]=e.split("=");t&&o.append(a(t),a(r||""))}return{url:r,searchParams:o}}(t.url);for(let[n,o]of Object.entries(t.query)){if(void 0!==o){if(Array.isArray(o))for(let e of o)r.append(n,e);else r.append(n,o)}let i=r.toString();i&&(t.url=`${e}?${i}`)}}return t.method=t.body&&!t.method?"POST":(t.method||"GET").toUpperCase(),t};function a(e){return decodeURIComponent(e.replace(/\+/g," "))}let c=/^https?:\/\//i,l=function(e){if(!c.test(e.url))throw Error(`"${e.url}" is not a valid URL`)},f=["request","response","progress","error","abort"],p=["processOptions","validateOptions","interceptRequest","finalizeOptions","onRequest","onResponse","onError","onReturn","onHeaders"];typeof navigator>"u"||navigator.product;var d,h,v=function(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}(function(){if(h)return d;h=1;var e=function(e){return e.replace(/^\s+|\s+$/g,"")};return d=function(t){if(!t)return{};for(var r,n={},o=e(t).split("\n"),i=0;i<o.length;i++){var u=o[i],s=u.indexOf(":"),a=e(u.slice(0,s)).toLowerCase(),c=e(u.slice(s+1));typeof n[a]>"u"?n[a]=c:(r=n[a],"[object Array]"===Object.prototype.toString.call(r)?n[a].push(c):n[a]=[n[a],c])}return n}}());class b{onabort;onerror;onreadystatechange;ontimeout;readyState=0;response;responseText="";responseType="";status;statusText;withCredentials;#e;#t;#r;#n={};#o;#i={};#u;open(e,t,r){this.#e=e,this.#t=t,this.#r="",this.readyState=1,this.onreadystatechange?.(),this.#o=void 0}abort(){this.#o&&this.#o.abort()}getAllResponseHeaders(){return this.#r}setRequestHeader(e,t){this.#n[e]=t}setInit(e,t=!0){this.#i=e,this.#u=t}send(e){let t="arraybuffer"!==this.responseType,r={...this.#i,method:this.#e,headers:this.#n,body:e};"function"==typeof AbortController&&this.#u&&(this.#o=new AbortController,"u">typeof EventTarget&&this.#o.signal instanceof EventTarget&&(r.signal=this.#o.signal)),"u">typeof document&&(r.credentials=this.withCredentials?"include":"omit"),fetch(this.#t,r).then(e=>(e.headers.forEach((e,t)=>{this.#r+=`${t}: ${e}\r
`}),this.status=e.status,this.statusText=e.statusText,this.readyState=3,this.onreadystatechange?.(),t?e.text():e.arrayBuffer())).then(e=>{"string"==typeof e?this.responseText=e:this.response=e,this.readyState=4,this.onreadystatechange?.()}).catch(e=>{"AbortError"!==e.name?this.onerror?.(e):this.onabort?.()})}}let y="function"==typeof XMLHttpRequest?"xhr":"fetch",m="xhr"===y?XMLHttpRequest:b,g=(e,t)=>{let r=e.options,n=e.applyMiddleware("finalizeOptions",r),o={},i=e.applyMiddleware("interceptRequest",void 0,{adapter:y,context:e});if(i){let e=setTimeout(t,0,null,i);return{abort:()=>clearTimeout(e)}}let u=new m;u instanceof b&&"object"==typeof n.fetch&&u.setInit(n.fetch,n.useAbortSignal??!0);let s=n.headers,a=n.timeout,c=!1,l=!1,f=!1;if(u.onerror=e=>{h(u instanceof b?e instanceof Error?e:Error(`Request error while attempting to reach is ${n.url}`,{cause:e}):Error(`Request error while attempting to reach is ${n.url}${e.lengthComputable?`(${e.loaded} of ${e.total} bytes transferred)`:""}`))},u.ontimeout=e=>{h(Error(`Request timeout while attempting to reach ${n.url}${e.lengthComputable?`(${e.loaded} of ${e.total} bytes transferred)`:""}`))},u.onabort=()=>{d(!0),c=!0},u.onreadystatechange=function(){a&&(d(),o.socket=setTimeout(()=>p("ESOCKETTIMEDOUT"),a.socket)),!c&&u&&4===u.readyState&&0!==u.status&&function(){if(!(c||l||f)){if(0===u.status)return void h(Error("Unknown XHR error"));d(),l=!0,t(null,{body:u.response||(""===u.responseType||"text"===u.responseType?u.responseText:""),url:n.url,method:n.method,headers:v(u.getAllResponseHeaders()),statusCode:u.status,statusMessage:u.statusText})}}()},u.open(n.method,n.url,!0),u.withCredentials=!!n.withCredentials,s&&u.setRequestHeader)for(let e in s)s.hasOwnProperty(e)&&u.setRequestHeader(e,s[e]);return n.rawBody&&(u.responseType="arraybuffer"),e.applyMiddleware("onRequest",{options:n,adapter:y,request:u,context:e}),u.send(n.body||null),a&&(o.connect=setTimeout(()=>p("ETIMEDOUT"),a.connect)),{abort:function(){c=!0,u&&u.abort()}};function p(t){f=!0,u.abort();let r=Error("ESOCKETTIMEDOUT"===t?`Socket timed out on request to ${n.url}`:`Connection timed out on request to ${n.url}`);r.code=t,e.channels.error.publish(r)}function d(e){(e||c||u&&u.readyState>=2&&o.connect)&&clearTimeout(o.connect),o.socket&&clearTimeout(o.socket)}function h(e){if(l)return;d(!0),l=!0,u=null;let r=e||Error(`Network error while attempting to reach ${n.url}`);r.isNetworkError=!0,r.request=n,t(r)}},_=(e=[],t=g)=>(function e(t,r){let n=[],o=p.reduce((e,t)=>(e[t]=e[t]||[],e),{processOptions:[s],validateOptions:[l]});function i(e){let t;let n=f.reduce((e,t)=>(e[t]=function(){let e=Object.create(null),t=0;return{publish:function(t){for(let r in e)e[r](t)},subscribe:function(r){let n=t++;return e[n]=r,function(){delete e[n]}}}}(),e),{}),i=function(e,t,...r){let n="onError"===e,i=t;for(let t=0;t<o[e].length&&(i=(0,o[e][t])(i,...r),!n||i);t++);return i},u=i("processOptions",e);i("validateOptions",u);let s={options:u,channels:n,applyMiddleware:i},a=n.request.subscribe(e=>{t=r(e,(t,r)=>((e,t,r)=>{let o=e,u=t;if(!o)try{u=i("onResponse",t,r)}catch(e){u=null,o=e}(o=o&&i("onError",o,r))?n.error.publish(o):u&&n.response.publish(u)})(t,r,e))});n.abort.subscribe(()=>{a(),t&&t.abort()});let c=i("onReturn",n,s);return c===n&&n.request.publish(s),c}return i.use=function(e){if(!e)throw Error("Tried to add middleware that resolved to falsey value");if("function"==typeof e)throw Error("Tried to add middleware that was a function. It probably expects you to pass options to it.");if(e.onReturn&&o.onReturn.length>0)throw Error("Tried to add new middleware with `onReturn` handler, but another handler has already been registered for this event");return p.forEach(t=>{e[t]&&o[t].push(e[t])}),n.push(e),i},i.clone=()=>e(n,r),t.forEach(i.use),i})(e,t);var w,O,S,j,x,E={exports:{}};x||(x=1,function(e,t){let r;t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let r="color: "+this.color;t.splice(1,0,r,"color: inherit");let n=0,o=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(n++,"%c"===e&&(o=n))}),t.splice(o,0,r)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch{}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch{}return!e&&"u">typeof process&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){let e;return"u">typeof window&&!!window.process&&("renderer"===window.process.type||!!window.process.__nwjs)||!("u">typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("u">typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"u">typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"u">typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"u">typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch{}}(),t.destroy=(r=!1,()=>{r||(r=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=(j?S:(j=1,S=function(e){function t(e){let n,o,i,u=null;function s(...e){if(!s.enabled)return;let r=Number(new Date),o=r-(n||r);s.diff=o,s.prev=n,s.curr=r,n=r,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let i=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(r,n)=>{if("%%"===r)return"%";i++;let o=t.formatters[n];if("function"==typeof o){let t=e[i];r=o.call(s,t),e.splice(i,1),i--}return r}),t.formatArgs.call(s,e),(s.log||t.log).apply(s,e)}return s.namespace=e,s.useColors=t.useColors(),s.color=t.selectColor(e),s.extend=r,s.destroy=t.destroy,Object.defineProperty(s,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==u?u:(o!==t.namespaces&&(o=t.namespaces,i=t.enabled(e)),i),set:e=>{u=e}}),"function"==typeof t.init&&t.init(s),s}function r(e,r){let n=t(this.namespace+(typeof r>"u"?":":r)+e);return n.log=this.log,n}function n(e,t){let r=0,n=0,o=-1,i=0;for(;r<e.length;)if(n<t.length&&(t[n]===e[r]||"*"===t[n]))"*"===t[n]?(o=n,i=r):r++,n++;else{if(-1===o)return!1;n=o+1,r=++i}for(;n<t.length&&"*"===t[n];)n++;return n===t.length}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){let e=[...t.names,...t.skips.map(e=>"-"+e)].join(",");return t.enable(""),e},t.enable=function(e){for(let r of(t.save(e),t.namespaces=e,t.names=[],t.skips=[],("string"==typeof e?e:"").trim().replace(" ",",").split(",").filter(Boolean)))"-"===r[0]?t.skips.push(r.slice(1)):t.names.push(r)},t.enabled=function(e){for(let r of t.skips)if(n(e,r))return!1;for(let r of t.names)if(n(e,r))return!0;return!1},t.humanize=function(){if(O)return w;function e(e,t,r,n){return Math.round(e/r)+" "+n+(t>=1.5*r?"s":"")}return O=1,w=function(t,r){r=r||{};var n,o,i=typeof t;if("string"===i&&t.length>0)return function(e){if(!((e=String(e)).length>100)){var t=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(t){var r=parseFloat(t[1]);switch((t[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*r;case"weeks":case"week":case"w":return 6048e5*r;case"days":case"day":case"d":return 864e5*r;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*r;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*r;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*r;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return r;default:return}}}}(t);if("number"===i&&isFinite(t))return r.long?(o=Math.abs(t))>=864e5?e(t,o,864e5,"day"):o>=36e5?e(t,o,36e5,"hour"):o>=6e4?e(t,o,6e4,"minute"):o>=1e3?e(t,o,1e3,"second"):t+" ms":(n=Math.abs(t))>=864e5?Math.round(t/864e5)+"d":n>=36e5?Math.round(t/36e5)+"h":n>=6e4?Math.round(t/6e4)+"m":n>=1e3?Math.round(t/1e3)+"s":t+"ms";throw Error("val is not a non-empty string or a valid number. val="+JSON.stringify(t))}}(),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(r=>{t[r]=e[r]}),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let r=0;for(let t=0;t<e.length;t++)r=(r<<5)-r+e.charCodeAt(t)|0;return t.colors[Math.abs(r)%t.colors.length]},t.enable(t.load()),t}))(t);let{formatters:n}=e.exports;n.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}}(E,E.exports)),E.exports,Object.prototype.hasOwnProperty;let P=typeof Buffer>"u"?()=>!1:e=>Buffer.isBuffer(e);function C(e){return"[object Object]"===Object.prototype.toString.call(e)}let M=["boolean","string","number"],I={};"u">typeof globalThis?I=globalThis:"u">typeof window?I=window:"u">typeof global?I=global:"u">typeof self&&(I=self);var T=I;let A=(e={})=>{let t=e.implementation||Promise;if(!t)throw Error("`Promise` is not available in global scope, and no implementation was passed");return{onReturn:(r,n)=>new t((t,o)=>{let i=n.options.cancelToken;i&&i.promise.then(e=>{r.abort.publish(e),o(e)}),r.error.subscribe(o),r.response.subscribe(r=>{t(e.onlyBody?r.body:r)}),setTimeout(()=>{try{r.request.publish(n)}catch(e){o(e)}},0)})}};class R{__CANCEL__=!0;message;constructor(e){this.message=e}toString(){return"Cancel"+(this.message?`: ${this.message}`:"")}}class q{promise;reason;constructor(e){if("function"!=typeof e)throw TypeError("executor must be a function.");let t=null;this.promise=new Promise(e=>{t=e}),e(e=>{this.reason||(this.reason=new R(e),t(this.reason))})}static source=()=>{let e;return{token:new q(t=>{e=t}),cancel:e}}}A.Cancel=R,A.CancelToken=q,A.isCancel=e=>!(!e||!e?.__CANCEL__);var F=(e,t,r)=>("GET"===r.method||"HEAD"===r.method)&&(e.isNetworkError||!1);function $(e){return 100*Math.pow(2,e)+100*Math.random()}let k=(e={})=>(e=>{let t=e.maxRetries||5,r=e.retryDelay||$,n=e.shouldRetry;return{onError:(e,o)=>{var i;let u=o.options,s=u.maxRetries||t,a=u.retryDelay||r,c=u.shouldRetry||n,l=u.attemptNumber||0;if(null!==(i=u.body)&&"object"==typeof i&&"function"==typeof i.pipe||!c(e,l,u)||l>=s)return e;let f=Object.assign({},o,{options:Object.assign({},u,{attemptNumber:l+1})});return setTimeout(()=>o.channels.request.publish(f),a(l)),null}}})({shouldRetry:F,...e});k.shouldRetry=F;var N=function(e,t){return(N=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)};function U(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}N(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}function D(e,t){var r,n,o,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},u=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return u.next=s(0),u.throw=s(1),u.return=s(2),"function"==typeof Symbol&&(u[Symbol.iterator]=function(){return this}),u;function s(s){return function(a){return function(s){if(r)throw TypeError("Generator is already executing.");for(;u&&(u=0,s[0]&&(i=0)),i;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return i.label++,{value:s[1],done:!1};case 5:i.label++,n=s[1],s=[0];continue;case 7:s=i.ops.pop(),i.trys.pop();continue;default:if(!(o=(o=i.trys).length>0&&o[o.length-1])&&(6===s[0]||2===s[0])){i=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){i.label=s[1];break}if(6===s[0]&&i.label<o[1]){i.label=o[1],o=s;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(s);break}o[2]&&i.ops.pop(),i.trys.pop();continue}s=t.call(e,i)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,a])}}}function L(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function z(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),u=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)u.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return u}function W(e,t,r){if(r||2==arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))}function V(e){return this instanceof V?(this.v=e,this):new V(e)}function B(e){return"function"==typeof e}function H(e){var t=e(function(e){Error.call(e),e.stack=Error().stack});return t.prototype=Object.create(Error.prototype),t.prototype.constructor=t,t}Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError;var Y=H(function(e){return function(t){e(this),this.message=t?t.length+" errors occurred during unsubscription:\n"+t.map(function(e,t){return t+1+") "+e.toString()}).join("\n  "):"",this.name="UnsubscriptionError",this.errors=t}});function G(e,t){if(e){var r=e.indexOf(t);0<=r&&e.splice(r,1)}}var J=function(){var e;function t(e){this.initialTeardown=e,this.closed=!1,this._parentage=null,this._finalizers=null}return t.prototype.unsubscribe=function(){if(!this.closed){this.closed=!0;var e,t,r,n,o,i=this._parentage;if(i){if(this._parentage=null,Array.isArray(i))try{for(var u=L(i),s=u.next();!s.done;s=u.next())s.value.remove(this)}catch(t){e={error:t}}finally{try{s&&!s.done&&(t=u.return)&&t.call(u)}finally{if(e)throw e.error}}else i.remove(this)}var a=this.initialTeardown;if(B(a))try{a()}catch(e){o=e instanceof Y?e.errors:[e]}var c=this._finalizers;if(c){this._finalizers=null;try{for(var l=L(c),f=l.next();!f.done;f=l.next()){var p=f.value;try{X(p)}catch(e){o=null!=o?o:[],e instanceof Y?o=W(W([],z(o)),z(e.errors)):o.push(e)}}}catch(e){r={error:e}}finally{try{f&&!f.done&&(n=l.return)&&n.call(l)}finally{if(r)throw r.error}}}if(o)throw new Y(o)}},t.prototype.add=function(e){var r;if(e&&e!==this){if(this.closed)X(e);else{if(e instanceof t){if(e.closed||e._hasParent(this))return;e._addParent(this)}(this._finalizers=null!==(r=this._finalizers)&&void 0!==r?r:[]).push(e)}}},t.prototype._hasParent=function(e){var t=this._parentage;return t===e||Array.isArray(t)&&t.includes(e)},t.prototype._addParent=function(e){var t=this._parentage;this._parentage=Array.isArray(t)?(t.push(e),t):t?[t,e]:e},t.prototype._removeParent=function(e){var t=this._parentage;t===e?this._parentage=null:Array.isArray(t)&&G(t,e)},t.prototype.remove=function(e){var r=this._finalizers;r&&G(r,e),e instanceof t&&e._removeParent(this)},t.EMPTY=((e=new t).closed=!0,e),t}(),K=J.EMPTY;function Q(e){return e instanceof J||e&&"closed"in e&&B(e.remove)&&B(e.add)&&B(e.unsubscribe)}function X(e){B(e)?e():e.unsubscribe()}var Z={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1},ee={setTimeout:function(e,t){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];var o=ee.delegate;return(null==o?void 0:o.setTimeout)?o.setTimeout.apply(o,W([e,t],z(r))):setTimeout.apply(void 0,W([e,t],z(r)))},clearTimeout:function(e){var t=ee.delegate;return((null==t?void 0:t.clearTimeout)||clearTimeout)(e)},delegate:void 0};function et(e){ee.setTimeout(function(){var t=Z.onUnhandledError;if(t)t(e);else throw e})}function er(){}var en=eo("C",void 0,void 0);function eo(e,t,r){return{kind:e,value:t,error:r}}var ei=null;function eu(e){if(Z.useDeprecatedSynchronousErrorHandling){var t=!ei;if(t&&(ei={errorThrown:!1,error:null}),e(),t){var r=ei,n=r.errorThrown,o=r.error;if(ei=null,n)throw o}}else e()}var es=function(e){function t(t){var r=e.call(this)||this;return r.isStopped=!1,t?(r.destination=t,Q(t)&&t.add(r)):r.destination=eh,r}return U(t,e),t.create=function(e,t,r){return new ef(e,t,r)},t.prototype.next=function(e){this.isStopped?ed(eo("N",e,void 0),this):this._next(e)},t.prototype.error=function(e){this.isStopped?ed(eo("E",void 0,e),this):(this.isStopped=!0,this._error(e))},t.prototype.complete=function(){this.isStopped?ed(en,this):(this.isStopped=!0,this._complete())},t.prototype.unsubscribe=function(){this.closed||(this.isStopped=!0,e.prototype.unsubscribe.call(this),this.destination=null)},t.prototype._next=function(e){this.destination.next(e)},t.prototype._error=function(e){try{this.destination.error(e)}finally{this.unsubscribe()}},t.prototype._complete=function(){try{this.destination.complete()}finally{this.unsubscribe()}},t}(J),ea=Function.prototype.bind;function ec(e,t){return ea.call(e,t)}var el=function(){function e(e){this.partialObserver=e}return e.prototype.next=function(e){var t=this.partialObserver;if(t.next)try{t.next(e)}catch(e){ep(e)}},e.prototype.error=function(e){var t=this.partialObserver;if(t.error)try{t.error(e)}catch(e){ep(e)}else ep(e)},e.prototype.complete=function(){var e=this.partialObserver;if(e.complete)try{e.complete()}catch(e){ep(e)}},e}(),ef=function(e){function t(t,r,n){var o,i,u=e.call(this)||this;return B(t)||!t?o={next:null!=t?t:void 0,error:null!=r?r:void 0,complete:null!=n?n:void 0}:u&&Z.useDeprecatedNextContext?((i=Object.create(t)).unsubscribe=function(){return u.unsubscribe()},o={next:t.next&&ec(t.next,i),error:t.error&&ec(t.error,i),complete:t.complete&&ec(t.complete,i)}):o=t,u.destination=new el(o),u}return U(t,e),t}(es);function ep(e){Z.useDeprecatedSynchronousErrorHandling?Z.useDeprecatedSynchronousErrorHandling&&ei&&(ei.errorThrown=!0,ei.error=e):et(e)}function ed(e,t){var r=Z.onStoppedNotification;r&&ee.setTimeout(function(){return r(e,t)})}var eh={closed:!0,next:er,error:function(e){throw e},complete:er},ev="function"==typeof Symbol&&Symbol.observable||"@@observable";function eb(e){return e}var ey=function(){function e(e){e&&(this._subscribe=e)}return e.prototype.lift=function(t){var r=new e;return r.source=this,r.operator=t,r},e.prototype.subscribe=function(e,t,r){var n,o=this,i=(n=e)&&n instanceof es||n&&B(n.next)&&B(n.error)&&B(n.complete)&&Q(n)?e:new ef(e,t,r);return eu(function(){var e=o.operator,t=o.source;i.add(e?e.call(i,t):t?o._subscribe(i):o._trySubscribe(i))}),i},e.prototype._trySubscribe=function(e){try{return this._subscribe(e)}catch(t){e.error(t)}},e.prototype.forEach=function(e,t){var r=this;return new(t=em(t))(function(t,n){var o=new ef({next:function(t){try{e(t)}catch(e){n(e),o.unsubscribe()}},error:n,complete:t});r.subscribe(o)})},e.prototype._subscribe=function(e){var t;return null===(t=this.source)||void 0===t?void 0:t.subscribe(e)},e.prototype[ev]=function(){return this},e.prototype.pipe=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return(0===e.length?eb:1===e.length?e[0]:function(t){return e.reduce(function(e,t){return t(e)},t)})(this)},e.prototype.toPromise=function(e){var t=this;return new(e=em(e))(function(e,r){var n;t.subscribe(function(e){return n=e},function(e){return r(e)},function(){return e(n)})})},e.create=function(t){return new e(t)},e}();function em(e){var t;return null!==(t=null!=e?e:Z.Promise)&&void 0!==t?t:Promise}var eg=function(e){return e&&"number"==typeof e.length&&"function"!=typeof e};function e_(e){return B(null==e?void 0:e.then)}function ew(e){return Symbol.asyncIterator&&B(null==e?void 0:e[Symbol.asyncIterator])}function eO(e){return TypeError("You provided "+(null!==e&&"object"==typeof e?"an invalid object":"'"+e+"'")+" where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.")}var eS="function"==typeof Symbol&&Symbol.iterator?Symbol.iterator:"@@iterator";function ej(e){return B(null==e?void 0:e[eS])}function ex(e){return function(e,t,r){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var n,o=r.apply(e,t||[]),i=[];return n=Object.create(("function"==typeof AsyncIterator?AsyncIterator:Object).prototype),u("next"),u("throw"),u("return",function(e){return function(t){return Promise.resolve(t).then(e,c)}}),n[Symbol.asyncIterator]=function(){return this},n;function u(e,t){o[e]&&(n[e]=function(t){return new Promise(function(r,n){i.push([e,t,r,n])>1||s(e,t)})},t&&(n[e]=t(n[e])))}function s(e,t){try{var r;(r=o[e](t)).value instanceof V?Promise.resolve(r.value.v).then(a,c):l(i[0][2],r)}catch(e){l(i[0][3],e)}}function a(e){s("next",e)}function c(e){s("throw",e)}function l(e,t){e(t),i.shift(),i.length&&s(i[0][0],i[0][1])}}(this,arguments,function(){var t,r,n;return D(this,function(o){switch(o.label){case 0:t=e.getReader(),o.label=1;case 1:o.trys.push([1,,9,10]),o.label=2;case 2:return[4,V(t.read())];case 3:if(n=(r=o.sent()).value,!r.done)return[3,5];return[4,V(void 0)];case 4:return[2,o.sent()];case 5:return[4,V(n)];case 6:return[4,o.sent()];case 7:return o.sent(),[3,2];case 8:return[3,10];case 9:return t.releaseLock(),[7];case 10:return[2]}})})}function eE(e){return B(null==e?void 0:e.getReader)}function eP(e){if(e instanceof ey)return e;if(null!=e){if(B(e[ev]))return new ey(function(t){var r=e[ev]();if(B(r.subscribe))return r.subscribe(t);throw TypeError("Provided object does not correctly implement Symbol.observable")});if(eg(e))return new ey(function(t){for(var r=0;r<e.length&&!t.closed;r++)t.next(e[r]);t.complete()});if(e_(e))return new ey(function(t){e.then(function(e){t.closed||(t.next(e),t.complete())},function(e){return t.error(e)}).then(null,et)});if(ew(e))return eC(e);if(ej(e))return new ey(function(t){var r,n;try{for(var o=L(e),i=o.next();!i.done;i=o.next()){var u=i.value;if(t.next(u),t.closed)return}}catch(e){r={error:e}}finally{try{i&&!i.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}t.complete()});if(eE(e))return eC(ex(e))}throw eO(e)}function eC(e){return new ey(function(t){(function(e,t){var r,n,o,i,u,s,a,c;return u=this,s=void 0,a=void 0,c=function(){var u;return D(this,function(s){switch(s.label){case 0:s.trys.push([0,5,6,11]),r=function(e){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var t,r=e[Symbol.asyncIterator];return r?r.call(e):(e=L(e),t={},n("next"),n("throw"),n("return"),t[Symbol.asyncIterator]=function(){return this},t);function n(r){t[r]=e[r]&&function(t){return new Promise(function(n,o){(function(e,t,r,n){Promise.resolve(n).then(function(t){e({value:t,done:r})},t)})(n,o,(t=e[r](t)).done,t.value)})}}}(e),s.label=1;case 1:return[4,r.next()];case 2:if((n=s.sent()).done)return[3,4];if(u=n.value,t.next(u),t.closed)return[2];s.label=3;case 3:return[3,1];case 4:return[3,11];case 5:return o={error:s.sent()},[3,11];case 6:if(s.trys.push([6,,9,10]),!(n&&!n.done&&(i=r.return)))return[3,8];return[4,i.call(r)];case 7:s.sent(),s.label=8;case 8:return[3,10];case 9:if(o)throw o.error;return[7];case 10:return[7];case 11:return t.complete(),[2]}})},new(a||(a=Promise))(function(e,t){function r(e){try{o(c.next(e))}catch(e){t(e)}}function n(e){try{o(c.throw(e))}catch(e){t(e)}}function o(t){var o;t.done?e(t.value):((o=t.value)instanceof a?o:new a(function(e){e(o)})).then(r,n)}o((c=c.apply(u,s||[])).next())})})(e,t).catch(function(e){return t.error(e)})})}function eM(e){return new ey(function(t){eP(e()).subscribe(t)})}function eI(e){return e[e.length-1]}function eT(e){var t;return(t=eI(e))&&B(t.schedule)?e.pop():void 0}function eA(e,t,r,n,o){void 0===n&&(n=0),void 0===o&&(o=!1);var i=t.schedule(function(){r(),o?e.add(this.schedule(null,n)):this.unsubscribe()},n);if(e.add(i),!o)return i}function eR(e){return function(t){if(B(null==t?void 0:t.lift))return t.lift(function(t){try{return e(t,this)}catch(e){this.error(e)}});throw TypeError("Unable to lift unknown Observable type")}}function eq(e,t,r,n,o){return new eF(e,t,r,n,o)}var eF=function(e){function t(t,r,n,o,i,u){var s=e.call(this,t)||this;return s.onFinalize=i,s.shouldUnsubscribe=u,s._next=r?function(e){try{r(e)}catch(e){t.error(e)}}:e.prototype._next,s._error=o?function(e){try{o(e)}catch(e){t.error(e)}finally{this.unsubscribe()}}:e.prototype._error,s._complete=n?function(){try{n()}catch(e){t.error(e)}finally{this.unsubscribe()}}:e.prototype._complete,s}return U(t,e),t.prototype.unsubscribe=function(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){var r=this.closed;e.prototype.unsubscribe.call(this),r||null===(t=this.onFinalize)||void 0===t||t.call(this)}},t}(es);function e$(e,t){return void 0===t&&(t=0),eR(function(r,n){r.subscribe(eq(n,function(r){return eA(n,e,function(){return n.next(r)},t)},function(){return eA(n,e,function(){return n.complete()},t)},function(r){return eA(n,e,function(){return n.error(r)},t)}))})}function ek(e,t){return void 0===t&&(t=0),eR(function(r,n){n.add(e.schedule(function(){return r.subscribe(n)},t))})}function eN(e,t){if(!e)throw Error("Iterable cannot be null");return new ey(function(r){eA(r,t,function(){var n=e[Symbol.asyncIterator]();eA(r,t,function(){n.next().then(function(e){e.done?r.complete():r.next(e.value)})},0,!0)})})}function eU(e,t){return t?function(e,t){if(null!=e){if(B(e[ev]))return eP(e).pipe(ek(t),e$(t));if(eg(e))return new ey(function(r){var n=0;return t.schedule(function(){n===e.length?r.complete():(r.next(e[n++]),r.closed||this.schedule())})});if(e_(e))return eP(e).pipe(ek(t),e$(t));if(ew(e))return eN(e,t);if(ej(e))return new ey(function(r){var n;return eA(r,t,function(){n=e[eS](),eA(r,t,function(){var e,t,o;try{t=(e=n.next()).value,o=e.done}catch(e){r.error(e);return}o?r.complete():r.next(t)},0,!0)}),function(){return B(null==n?void 0:n.return)&&n.return()}});if(eE(e))return eN(ex(e),t)}throw eO(e)}(e,t):eP(e)}function eD(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=eT(e);return eU(e,r)}function eL(e,t){return eR(function(r,n){var o=0;r.subscribe(eq(n,function(r){n.next(e.call(t,r,o++))}))})}function ez(e,t,r){return(void 0===r&&(r=1/0),B(t))?ez(function(r,n){return eL(function(e,o){return t(r,e,n,o)})(eP(e(r,n)))},r):("number"==typeof t&&(r=t),eR(function(t,n){var o,i,u,s,a,c,l,f,p;return o=r,u=[],s=0,a=0,c=!1,l=function(){!c||u.length||s||n.complete()},f=function(e){return s<o?p(e):u.push(e)},p=function(t){s++;var r=!1;eP(e(t,a++)).subscribe(eq(n,function(e){i?f(e):n.next(e)},function(){r=!0},void 0,function(){if(r)try{for(s--;u.length&&s<o;)!function(){var e=u.shift();p(e)}();l()}catch(e){n.error(e)}}))},t.subscribe(eq(n,f,function(){c=!0,l()})),function(){}}))}var eW=H(function(e){return function(){e(this),this.name="EmptyError",this.message="no elements in sequence"}});function eV(e,t){var r="object"==typeof t;return new Promise(function(n,o){var i,u=!1;e.subscribe({next:function(e){i=e,u=!0},error:o,complete:function(){u?n(i):r?n(t.defaultValue):o(new eW)}})})}var eB=H(function(e){return function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"}}),eH=function(e){function t(){var t=e.call(this)||this;return t.closed=!1,t.currentObservers=null,t.observers=[],t.isStopped=!1,t.hasError=!1,t.thrownError=null,t}return U(t,e),t.prototype.lift=function(e){var t=new eY(this,this);return t.operator=e,t},t.prototype._throwIfClosed=function(){if(this.closed)throw new eB},t.prototype.next=function(e){var t=this;eu(function(){var r,n;if(t._throwIfClosed(),!t.isStopped){t.currentObservers||(t.currentObservers=Array.from(t.observers));try{for(var o=L(t.currentObservers),i=o.next();!i.done;i=o.next())i.value.next(e)}catch(e){r={error:e}}finally{try{i&&!i.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}}})},t.prototype.error=function(e){var t=this;eu(function(){if(t._throwIfClosed(),!t.isStopped){t.hasError=t.isStopped=!0,t.thrownError=e;for(var r=t.observers;r.length;)r.shift().error(e)}})},t.prototype.complete=function(){var e=this;eu(function(){if(e._throwIfClosed(),!e.isStopped){e.isStopped=!0;for(var t=e.observers;t.length;)t.shift().complete()}})},t.prototype.unsubscribe=function(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null},Object.defineProperty(t.prototype,"observed",{get:function(){var e;return(null===(e=this.observers)||void 0===e?void 0:e.length)>0},enumerable:!1,configurable:!0}),t.prototype._trySubscribe=function(t){return this._throwIfClosed(),e.prototype._trySubscribe.call(this,t)},t.prototype._subscribe=function(e){return this._throwIfClosed(),this._checkFinalizedStatuses(e),this._innerSubscribe(e)},t.prototype._innerSubscribe=function(e){var t=this,r=this.hasError,n=this.isStopped,o=this.observers;return r||n?K:(this.currentObservers=null,o.push(e),new J(function(){t.currentObservers=null,G(o,e)}))},t.prototype._checkFinalizedStatuses=function(e){var t=this.hasError,r=this.thrownError,n=this.isStopped;t?e.error(r):n&&e.complete()},t.prototype.asObservable=function(){var e=new ey;return e.source=this,e},t.create=function(e,t){return new eY(e,t)},t}(ey),eY=function(e){function t(t,r){var n=e.call(this)||this;return n.destination=t,n.source=r,n}return U(t,e),t.prototype.next=function(e){var t,r;null===(r=null===(t=this.destination)||void 0===t?void 0:t.next)||void 0===r||r.call(t,e)},t.prototype.error=function(e){var t,r;null===(r=null===(t=this.destination)||void 0===t?void 0:t.error)||void 0===r||r.call(t,e)},t.prototype.complete=function(){var e,t;null===(t=null===(e=this.destination)||void 0===e?void 0:e.complete)||void 0===t||t.call(e)},t.prototype._subscribe=function(e){var t,r;return null!==(r=null===(t=this.source)||void 0===t?void 0:t.subscribe(e))&&void 0!==r?r:K},t}(eH),eG={now:function(){return(eG.delegate||Date).now()},delegate:void 0},eJ=function(e){function t(t,r,n){void 0===t&&(t=1/0),void 0===r&&(r=1/0),void 0===n&&(n=eG);var o=e.call(this)||this;return o._bufferSize=t,o._windowTime=r,o._timestampProvider=n,o._buffer=[],o._infiniteTimeWindow=!0,o._infiniteTimeWindow=r===1/0,o._bufferSize=Math.max(1,t),o._windowTime=Math.max(1,r),o}return U(t,e),t.prototype.next=function(t){var r=this.isStopped,n=this._buffer,o=this._infiniteTimeWindow,i=this._timestampProvider,u=this._windowTime;!r&&(n.push(t),o||n.push(i.now()+u)),this._trimBuffer(),e.prototype.next.call(this,t)},t.prototype._subscribe=function(e){this._throwIfClosed(),this._trimBuffer();for(var t=this._innerSubscribe(e),r=this._infiniteTimeWindow,n=this._buffer.slice(),o=0;o<n.length&&!e.closed;o+=r?1:2)e.next(n[o]);return this._checkFinalizedStatuses(e),t},t.prototype._trimBuffer=function(){var e=this._bufferSize,t=this._timestampProvider,r=this._buffer,n=this._infiniteTimeWindow,o=(n?1:2)*e;if(e<1/0&&o<r.length&&r.splice(0,r.length-o),!n){for(var i=t.now(),u=0,s=1;s<r.length&&r[s]<=i;s+=2)u=s;u&&r.splice(0,u+1)}},t}(eH);function eK(e){void 0===e&&(e={});var t=e.connector,r=void 0===t?function(){return new eH}:t,n=e.resetOnError,o=void 0===n||n,i=e.resetOnComplete,u=void 0===i||i,s=e.resetOnRefCountZero,a=void 0===s||s;return function(e){var t,n,i,s=0,c=!1,l=!1,f=function(){null==n||n.unsubscribe(),n=void 0},p=function(){f(),t=i=void 0,c=l=!1},d=function(){var e=t;p(),null==e||e.unsubscribe()};return eR(function(e,h){s++,l||c||f();var v=i=null!=i?i:r();h.add(function(){0!=--s||l||c||(n=eQ(d,a))}),v.subscribe(h),!t&&s>0&&(t=new ef({next:function(e){return v.next(e)},error:function(e){l=!0,f(),n=eQ(p,o,e),v.error(e)},complete:function(){c=!0,f(),n=eQ(p,u),v.complete()}}),eP(e).subscribe(t))})(e)}}function eQ(e,t){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];if(!0===t){e();return}if(!1!==t){var o=new ef({next:function(){o.unsubscribe(),e()}});return eP(t.apply(void 0,W([],z(r)))).subscribe(o)}}function eX(e){return eR(function(t,r){var n,o=null,i=!1;o=t.subscribe(eq(r,void 0,void 0,function(u){n=eP(e(u,eX(e)(t))),o?(o.unsubscribe(),o=null,n.subscribe(r)):i=!0})),i&&(o.unsubscribe(),o=null,n.subscribe(r))})}function eZ(e){return void 0===e&&(e=1/0),ez(eb,e)}function e0(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return eZ(1)(eU(e,eT(e)))}var e1=function(e){function t(t,r){return e.call(this)||this}return U(t,e),t.prototype.schedule=function(e,t){return void 0===t&&(t=0),this},t}(J),e2={setInterval:function(e,t){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];var o=e2.delegate;return(null==o?void 0:o.setInterval)?o.setInterval.apply(o,W([e,t],z(r))):setInterval.apply(void 0,W([e,t],z(r)))},clearInterval:function(e){var t=e2.delegate;return((null==t?void 0:t.clearInterval)||clearInterval)(e)},delegate:void 0},e6=function(e){function t(t,r){var n=e.call(this,t,r)||this;return n.scheduler=t,n.work=r,n.pending=!1,n}return U(t,e),t.prototype.schedule=function(e,t){if(void 0===t&&(t=0),this.closed)return this;this.state=e;var r,n=this.id,o=this.scheduler;return null!=n&&(this.id=this.recycleAsyncId(o,n,t)),this.pending=!0,this.delay=t,this.id=null!==(r=this.id)&&void 0!==r?r:this.requestAsyncId(o,this.id,t),this},t.prototype.requestAsyncId=function(e,t,r){return void 0===r&&(r=0),e2.setInterval(e.flush.bind(e,this),r)},t.prototype.recycleAsyncId=function(e,t,r){if(void 0===r&&(r=0),null!=r&&this.delay===r&&!1===this.pending)return t;null!=t&&e2.clearInterval(t)},t.prototype.execute=function(e,t){if(this.closed)return Error("executing a cancelled action");this.pending=!1;var r=this._execute(e,t);if(r)return r;!1===this.pending&&null!=this.id&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))},t.prototype._execute=function(e,t){var r,n=!1;try{this.work(e)}catch(e){n=!0,r=e||Error("Scheduled action threw falsy error")}if(n)return this.unsubscribe(),r},t.prototype.unsubscribe=function(){if(!this.closed){var t=this.id,r=this.scheduler,n=r.actions;this.work=this.state=this.scheduler=null,this.pending=!1,G(n,this),null!=t&&(this.id=this.recycleAsyncId(r,t,null)),this.delay=null,e.prototype.unsubscribe.call(this)}},t}(e1),e3=function(){function e(t,r){void 0===r&&(r=e.now),this.schedulerActionCtor=t,this.now=r}return e.prototype.schedule=function(e,t,r){return void 0===t&&(t=0),new this.schedulerActionCtor(this,e).schedule(r,t)},e.now=eG.now,e}(),e8=new(function(e){function t(t,r){void 0===r&&(r=e3.now);var n=e.call(this,t,r)||this;return n.actions=[],n._active=!1,n}return U(t,e),t.prototype.flush=function(e){var t,r=this.actions;if(this._active){r.push(e);return}this._active=!0;do if(t=e.execute(e.state,e.delay))break;while(e=r.shift());if(this._active=!1,t){for(;e=r.shift();)e.unsubscribe();throw t}},t}(e3))(e6);function e9(e,t){var r=B(e)?e:function(){return e},n=function(e){return e.error(r())};return new ey(t?function(e){return t.schedule(n,0,e)}:n)}var e7=new ey(function(e){return e.complete()});function e5(e,t){var r="object"==typeof t;return new Promise(function(n,o){var i=new ef({next:function(e){n(e),i.unsubscribe()},error:o,complete:function(){r?n(t.defaultValue):o(new eW)}});e.subscribe(i)})}var e4=r(20872),te=r(85798);let tt=/_key\s*==\s*['"](.*)['"]/,tr=/^\d*:\d*$/,tn="drafts.",to="versions.";function ti(e){return e.startsWith(tn)}function tu(e){return e.startsWith(to)}function ts(e,t){if("drafts"===t||"published"===t)throw Error('Version can not be "published" or "drafts"');return`${to}${t}.${tc(e)}`}function ta(e){if(!tu(e))return;let[t,r,...n]=e.split(".");return r}function tc(e){return tu(e)?e.split(".").slice(2).join("."):ti(e)?e.slice(tn.length):e}var tl=r(55511);let tf=e=>{!n||n.length<e?(n=Buffer.allocUnsafe(128*e),tl.randomFillSync(n),o=0):o+e>n.length&&(tl.randomFillSync(n),o=0),o+=e},tp=e=>(tf(e|=0),n.subarray(o-e,o)),td=(e,t,r)=>{let n=(2<<31-Math.clz32(e.length-1|1))-1,o=Math.ceil(1.6*n*t/e.length);return (i=t)=>{let u="";for(;;){let t=r(o),s=o;for(;s--;)if((u+=e[t[s]&n]||"").length===i)return u}}};class th extends Error{response;statusCode=400;responseBody;details;constructor(e){let t=tb(e);super(t.message),Object.assign(this,t)}}class tv extends Error{response;statusCode=500;responseBody;details;constructor(e){let t=tb(e);super(t.message),Object.assign(this,t)}}function tb(e){let t=e.body,r={response:e,statusCode:e.statusCode,responseBody:-1!==(e.headers["content-type"]||"").toLowerCase().indexOf("application/json")?JSON.stringify(t,null,2):t,message:"",details:void 0};if(t.error&&t.message)return r.message=`${t.error} - ${t.message}`,r;if(ty(t)&&ty(t.error)&&"mutationError"===t.error.type&&"string"==typeof t.error.description||ty(t)&&ty(t.error)&&"actionError"===t.error.type&&"string"==typeof t.error.description){let e=t.error.items||[],n=e.slice(0,5).map(e=>e.error?.description).filter(Boolean),o=n.length?`:
- ${n.join(`
- `)}`:"";return e.length>5&&(o+=`
...and ${e.length-5} more`),r.message=`${t.error.description}${o}`,r.details=t.error,r}return t.error&&t.error.description?(r.message=t.error.description,r.details=t.error):r.message=t.error||t.message||function(e){let t=e.statusMessage?` ${e.statusMessage}`:"";return`${e.method}-request to ${e.url} resulted in HTTP ${e.statusCode}${t}`}(e),r}function ty(e){return"object"==typeof e&&null!==e&&!Array.isArray(e)}class tm extends Error{projectId;addOriginUrl;constructor({projectId:e}){super("CorsOriginError"),this.name="CorsOriginError",this.projectId=e;let t=new URL(`https://sanity.io/manage/project/${e}/api`);if("u">typeof location){let{origin:e}=location;t.searchParams.set("cors","add"),t.searchParams.set("origin",e),this.addOriginUrl=t,this.message=`The current origin is not allowed to connect to the Live Content API. Add it here: ${t}`}else this.message=`The current origin is not allowed to connect to the Live Content API. Change your configuration here: ${t}`}}let tg={onResponse:e=>{if(e.statusCode>=500)throw new tv(e);if(e.statusCode>=400)throw new th(e);return e}};function t_(e){return _([k({shouldRetry:tw}),...e,function(){let e={};return{onResponse:t=>{let r=t.headers["x-sanity-warning"];for(let t of Array.isArray(r)?r:[r])!t||e[t]||(e[t]=!0,console.warn(t));return t}}}(),{processOptions:e=>{let t=e.body;return!t||"function"==typeof t.pipe||P(t)||-1===M.indexOf(typeof t)&&!Array.isArray(t)&&!function(e){if(!1===C(e))return!1;let t=e.constructor;if(void 0===t)return!0;let r=t.prototype;return!(!1===C(r)||!1===r.hasOwnProperty("isPrototypeOf"))}(t)?e:Object.assign({},e,{body:JSON.stringify(e.body),headers:Object.assign({},e.headers,{"Content-Type":"application/json"})})}},{onResponse:e=>{let t=e.headers["content-type"]||"",r=-1!==t.indexOf("application/json");return e.body&&t&&r?Object.assign({},e,{body:function(e){try{return JSON.parse(e)}catch(e){throw e.message=`Failed to parsed response body as JSON: ${e.message}`,e}}(e.body)}):e},processOptions:e=>Object.assign({},e,{headers:Object.assign({Accept:"application/json"},e.headers)})},{onRequest:e=>{if("xhr"!==e.adapter)return;let t=e.request,r=e.context;function n(e){return t=>{let n=t.lengthComputable?t.loaded/t.total*100:-1;r.channels.progress.publish({stage:e,percent:n,total:t.total,loaded:t.loaded,lengthComputable:t.lengthComputable})}}"upload"in t&&"onprogress"in t.upload&&(t.upload.onprogress=n("upload")),"onprogress"in t&&(t.onprogress=n("download"))}},tg,function(e={}){let t=e.implementation||T.Observable;if(!t)throw Error("`Observable` is not available in global scope, and no implementation was passed");return{onReturn:(e,r)=>new t(t=>(e.error.subscribe(e=>t.error(e)),e.progress.subscribe(e=>t.next(Object.assign({type:"progress"},e))),e.response.subscribe(e=>{t.next(Object.assign({type:"response"},e)),t.complete()}),e.request.publish(r),()=>e.abort.publish()))}}({implementation:ey})])}function tw(e,t,r){if(0===r.maxRetries)return!1;let n="GET"===r.method||"HEAD"===r.method,o=(r.uri||r.url).startsWith("/data/query"),i=e.response&&(429===e.response.statusCode||502===e.response.statusCode||503===e.response.statusCode);return(!!n||!!o)&&!!i||k.shouldRetry(e,t,r)}function tO(e){return"https://www.sanity.io/help/"+e}let tS=["image","file"],tj=["before","after","replace"],tx=e=>{if(!/^(~[a-z0-9]{1}[-\w]{0,63}|[a-z0-9]{1}[-\w]{0,63})$/.test(e))throw Error("Datasets can only contain lowercase characters, numbers, underscores and dashes, and start with tilde, and be maximum 64 characters")},tE=e=>{if(!/^[-a-z0-9]+$/i.test(e))throw Error("`projectId` can only contain only a-z, 0-9 and dashes")},tP=e=>{if(-1===tS.indexOf(e))throw Error(`Invalid asset type: ${e}. Must be one of ${tS.join(", ")}`)},tC=(e,t)=>{if(null===t||"object"!=typeof t||Array.isArray(t))throw Error(`${e}() takes an object of properties`)},tM=(e,t)=>{if("string"!=typeof t||!/^[a-z0-9_][a-z0-9_.-]{0,127}$/i.test(t)||t.includes(".."))throw Error(`${e}(): "${t}" is not a valid document ID`)},tI=(e,t)=>{if(!t._id)throw Error(`${e}() requires that the document contains an ID ("_id" property)`);tM(e,t._id)},tT=(e,t)=>{if("string"!=typeof t)throw Error(`\`${e}()\`: \`${t}\` is not a valid document type`)},tA=(e,t)=>{if(!t._type)throw Error(`\`${e}()\` requires that the document contains a type (\`_type\` property)`);tT(e,t._type)},tR=(e,t)=>{if(t._id&&t._id!==e)throw Error(`The provided document ID (\`${t._id}\`) does not match the generated version ID (\`${e}\`)`)},tq=(e,t,r)=>{let n="insert(at, selector, items)";if(-1===tj.indexOf(e)){let e=tj.map(e=>`"${e}"`).join(", ");throw Error(`${n} takes an "at"-argument which is one of: ${e}`)}if("string"!=typeof t)throw Error(`${n} takes a "selector"-argument which must be a string`);if(!Array.isArray(r))throw Error(`${n} takes an "items"-argument which must be an array`)},tF=e=>{if(!e.dataset)throw Error("`dataset` must be provided to perform queries");return e.dataset||""},t$=e=>{if("string"!=typeof e||!/^[a-z0-9._-]{1,75}$/i.test(e))throw Error("Tag can only contain alphanumeric characters, underscores, dashes and dots, and be between one and 75 characters long.");return e},tk=e=>{if(!e["~experimental_resource"])throw Error("`resource` must be provided to perform resource queries");let{type:t,id:r}=e["~experimental_resource"];switch(t){case"dataset":if(2!==r.split(".").length)throw Error('Dataset resource ID must be in the format "project.dataset"');return;case"dashboard":case"media-library":case"canvas":return;default:throw Error(`Unsupported resource type: ${t.toString()}`)}},tN=(e,t)=>{if(t["~experimental_resource"])throw Error(`\`${e}\` does not support resource-based operations`)},tU=e=>(function(e){let t=!1,r;return(...n)=>(t||(r=e(...n),t=!0),r)})((...t)=>console.warn(e.join(" "),...t)),tD=tU(["Because you set `withCredentials` to true, we will override your `useCdn`","setting to be false since (cookie-based) credentials are never set on the CDN"]),tL=tU(["Since you haven't set a value for `useCdn`, we will deliver content using our","global, edge-cached API-CDN. If you wish to have content delivered faster, set","`useCdn: false` to use the Live API. Note: You may incur higher costs using the live API."]),tz=tU(["The Sanity client is configured with the `perspective` set to `drafts` or `previewDrafts`, which doesn't support the API-CDN.","The Live API will be used instead. Set `useCdn: false` in your configuration to hide this warning."]),tW=tU(["The `previewDrafts` perspective has been renamed to  `drafts` and will be removed in a future API version"]),tV=tU(["You have configured Sanity client to use a token in the browser. This may cause unintentional security issues.",`See ${tO("js-client-browser-token")} for more information and how to hide this warning.`]),tB=tU(["You have configured Sanity client to use a token, but also provided `withCredentials: true`.","This is no longer supported - only token will be used - remove `withCredentials: true`."]),tH=tU(["Using the Sanity client without specifying an API version is deprecated.",`See ${tO("js-client-api-version")}`]),tY=(tU(["The default export of @sanity/client has been deprecated. Use the named export `createClient` instead."]),{apiHost:"https://api.sanity.io",apiVersion:"1",useProjectHostname:!0,stega:{enabled:!1}}),tG=["localhost","127.0.0.1","0.0.0.0"],tJ=e=>-1!==tG.indexOf(e);function tK(e){if(Array.isArray(e)&&e.length>1&&e.includes("raw"))throw TypeError('Invalid API perspective value: "raw". The raw-perspective can not be combined with other perspectives')}let tQ=(e,t)=>{let r={...t,...e,stega:{..."boolean"==typeof t.stega?{enabled:t.stega}:t.stega||tY.stega,..."boolean"==typeof e.stega?{enabled:e.stega}:e.stega||{}}};r.apiVersion||tH();let n={...tY,...r},o=n.useProjectHostname&&!n["~experimental_resource"];if(typeof Promise>"u"){let e=tO("js-client-promise-polyfill");throw Error(`No native Promise-implementation found, polyfill needed - see ${e}`)}if(o&&!n.projectId)throw Error("Configuration must contain `projectId`");if(n["~experimental_resource"]&&tk(n),"u">typeof n.perspective&&tK(n.perspective),"encodeSourceMap"in n)throw Error("It looks like you're using options meant for '@sanity/preview-kit/client'. 'encodeSourceMap' is not supported in '@sanity/client'. Did you mean 'stega.enabled'?");if("encodeSourceMapAtPath"in n)throw Error("It looks like you're using options meant for '@sanity/preview-kit/client'. 'encodeSourceMapAtPath' is not supported in '@sanity/client'. Did you mean 'stega.filter'?");if("boolean"!=typeof n.stega.enabled)throw Error(`stega.enabled must be a boolean, received ${n.stega.enabled}`);if(n.stega.enabled&&void 0===n.stega.studioUrl)throw Error("stega.studioUrl must be defined when stega.enabled is true");if(n.stega.enabled&&"string"!=typeof n.stega.studioUrl&&"function"!=typeof n.stega.studioUrl)throw Error(`stega.studioUrl must be a string or a function, received ${n.stega.studioUrl}`);let i="u">typeof window&&window.location&&window.location.hostname,u=i&&tJ(window.location.hostname),s=!!n.token;n.withCredentials&&s&&(tB(),n.withCredentials=!1),i&&u&&s&&!0!==n.ignoreBrowserTokenWarning?tV():typeof n.useCdn>"u"&&tL(),o&&tE(n.projectId),n.dataset&&tx(n.dataset),"requestTagPrefix"in n&&(n.requestTagPrefix=n.requestTagPrefix?t$(n.requestTagPrefix).replace(/\.+$/,""):void 0),n.apiVersion=`${n.apiVersion}`.replace(/^v/,""),n.isDefaultApi=n.apiHost===tY.apiHost,!0===n.useCdn&&n.withCredentials&&tD(),n.useCdn=!1!==n.useCdn&&!n.withCredentials,function(e){if("1"===e||"X"===e)return;let t=new Date(e);if(!(/^\d{4}-\d{2}-\d{2}$/.test(e)&&t instanceof Date&&t.getTime()>0))throw Error("Invalid API version string, expected `1` or date in format `YYYY-MM-DD`")}(n.apiVersion);let a=n.apiHost.split("://",2),c=a[0],l=a[1],f=n.isDefaultApi?"apicdn.sanity.io":l;return o?(n.url=`${c}://${n.projectId}.${l}/v${n.apiVersion}`,n.cdnUrl=`${c}://${n.projectId}.${f}/v${n.apiVersion}`):(n.url=`${n.apiHost}/v${n.apiVersion}`,n.cdnUrl=n.url),n};class tX extends Error{name="ConnectionFailedError"}class tZ extends Error{name="DisconnectError";reason;constructor(e,t,r={}){super(e,r),this.reason=t}}class t0 extends Error{name="ChannelError";data;constructor(e,t){super(e),this.data=t}}class t1 extends Error{name="MessageError";data;constructor(e,t,r={}){super(e,r),this.data=t}}class t2 extends Error{name="MessageParseError"}let t6=["channelError","disconnect"];function t3(e,t){return eM(()=>{let t=e();return t&&(t instanceof ey||B(t.lift)&&B(t.subscribe))?t:eD(t)}).pipe(ez(e=>new ey(r=>{let n=t.includes("open"),o=t.includes("reconnect");function i(t){if("data"in t){let[e,n]=t8(t);r.error(e?new t2("Unable to parse EventSource error message",{cause:n}):new t1((n?.data).message,n));return}e.readyState===e.CLOSED?r.error(new tX("EventSource connection failed")):o&&r.next({type:"reconnect"})}function u(){r.next({type:"open"})}function s(e){let[t,n]=t8(e);if(t){r.error(new t2("Unable to parse EventSource message",{cause:t}));return}if("channelError"===e.type){var o;r.error(new t0((o=n?.data).error?o.error.description?o.error.description:"string"==typeof o.error?o.error:JSON.stringify(o.error,null,2):o.message||"Unknown listener error",n.data));return}if("disconnect"===e.type){r.error(new tZ(`Server disconnected client: ${n.data?.reason||"unknown error"}`));return}r.next({type:e.type,id:e.lastEventId,...n.data?{data:n.data}:{}})}e.addEventListener("error",i),n&&e.addEventListener("open",u);let a=[...new Set([...t6,...t])].filter(e=>"error"!==e&&"open"!==e&&"reconnect"!==e);return a.forEach(t=>e.addEventListener(t,s)),()=>{e.removeEventListener("error",i),n&&e.removeEventListener("open",u),a.forEach(t=>e.removeEventListener(t,s)),e.close()}})))}function t8(e){try{let t="string"==typeof e.data&&JSON.parse(e.data);return[null,{type:e.type,id:e.lastEventId,...!function(e){for(let t in e)return!1;return!0}(t)?{data:t}:{}}]}catch(e){return[e,null]}}function t9(e){if("string"==typeof e)return{id:e};if(Array.isArray(e))return{query:"*[_id in $ids]",params:{ids:e}};if("object"==typeof e&&null!==e&&"query"in e&&"string"==typeof e.query)return"params"in e&&"object"==typeof e.params&&null!==e.params?{query:e.query,params:e.params}:{query:e.query};let t=["* Document ID (<docId>)","* Array of document IDs","* Object containing `query`"].join(`
`);throw Error(`Unknown selection - must be one of:

${t}`)}class t7{selection;operations;constructor(e,t={}){this.selection=e,this.operations=t}set(e){return this._assign("set",e)}setIfMissing(e){return this._assign("setIfMissing",e)}diffMatchPatch(e){return tC("diffMatchPatch",e),this._assign("diffMatchPatch",e)}unset(e){if(!Array.isArray(e))throw Error("unset(attrs) takes an array of attributes to unset, non-array given");return this.operations=Object.assign({},this.operations,{unset:e}),this}inc(e){return this._assign("inc",e)}dec(e){return this._assign("dec",e)}insert(e,t,r){return tq(e,t,r),this._assign("insert",{[e]:t,items:r})}append(e,t){return this.insert("after",`${e}[-1]`,t)}prepend(e,t){return this.insert("before",`${e}[0]`,t)}splice(e,t,r,n){let o=t<0?t-1:t,i=typeof r>"u"||-1===r?-1:Math.max(0,t+r),u=`${e}[${o}:${o<0&&i>=0?"":i}]`;return this.insert("replace",u,n||[])}ifRevisionId(e){return this.operations.ifRevisionID=e,this}serialize(){return{...t9(this.selection),...this.operations}}toJSON(){return this.serialize()}reset(){return this.operations={},this}_assign(e,t,r=!0){return tC(e,t),this.operations=Object.assign({},this.operations,{[e]:Object.assign({},r&&this.operations[e]||{},t)}),this}_set(e,t){return this._assign(e,t,!1)}}class t5 extends t7{#s;constructor(e,t,r){super(e,t),this.#s=r}clone(){return new t5(this.selection,{...this.operations},this.#s)}commit(e){if(!this.#s)throw Error("No `client` passed to patch, either provide one or pass the patch to a clients `mutate()` method");let t=Object.assign({returnFirst:"string"==typeof this.selection,returnDocuments:!0},e);return this.#s.mutate({patch:this.serialize()},t)}}class t4 extends t7{#s;constructor(e,t,r){super(e,t),this.#s=r}clone(){return new t4(this.selection,{...this.operations},this.#s)}commit(e){if(!this.#s)throw Error("No `client` passed to patch, either provide one or pass the patch to a clients `mutate()` method");let t=Object.assign({returnFirst:"string"==typeof this.selection,returnDocuments:!0},e);return this.#s.mutate({patch:this.serialize()},t)}}let re={returnDocuments:!1};class rt{operations;trxId;constructor(e=[],t){this.operations=e,this.trxId=t}create(e){return tC("create",e),this._add({create:e})}createIfNotExists(e){let t="createIfNotExists";return tC(t,e),tI(t,e),this._add({[t]:e})}createOrReplace(e){let t="createOrReplace";return tC(t,e),tI(t,e),this._add({[t]:e})}delete(e){return tM("delete",e),this._add({delete:{id:e}})}transactionId(e){return e?(this.trxId=e,this):this.trxId}serialize(){return[...this.operations]}toJSON(){return this.serialize()}reset(){return this.operations=[],this}_add(e){return this.operations.push(e),this}}class rr extends rt{#s;constructor(e,t,r){super(e,r),this.#s=t}clone(){return new rr([...this.operations],this.#s,this.trxId)}commit(e){if(!this.#s)throw Error("No `client` passed to transaction, either provide one or pass the transaction to a clients `mutate()` method");return this.#s.mutate(this.serialize(),Object.assign({transactionId:this.trxId},re,e||{}))}patch(e,t){let r="function"==typeof t,n="string"!=typeof e&&e instanceof t4,o="object"==typeof e&&("query"in e||"id"in e);if(n)return this._add({patch:e.serialize()});if(r){let r=t(new t4(e,{},this.#s));if(!(r instanceof t4))throw Error("function passed to `patch()` must return the patch");return this._add({patch:r.serialize()})}if(o){let r=new t4(e,t||{},this.#s);return this._add({patch:r.serialize()})}return this._add({patch:{id:e,...t}})}}class rn extends rt{#s;constructor(e,t,r){super(e,r),this.#s=t}clone(){return new rn([...this.operations],this.#s,this.trxId)}commit(e){if(!this.#s)throw Error("No `client` passed to transaction, either provide one or pass the transaction to a clients `mutate()` method");return this.#s.mutate(this.serialize(),Object.assign({transactionId:this.trxId},re,e||{}))}patch(e,t){let r="function"==typeof t;if("string"!=typeof e&&e instanceof t5)return this._add({patch:e.serialize()});if(r){let r=t(new t5(e,{},this.#s));if(!(r instanceof t5))throw Error("function passed to `patch()` must return the patch");return this._add({patch:r.serialize()})}return this._add({patch:{id:e,...t}})}}let ro=({query:e,params:t={},options:r={}})=>{let n=new URLSearchParams,{tag:o,includeMutations:i,returnQuery:u,...s}=r;for(let[r,i]of(o&&n.append("tag",o),n.append("query",e),Object.entries(t)))n.append(`$${r}`,JSON.stringify(i));for(let[e,t]of Object.entries(s))t&&n.append(e,`${t}`);return!1===u&&n.append("returnQuery","false"),!1===i&&n.append("includeMutations","false"),`?${n}`},ri=(e,t)=>!1===e?void 0:typeof e>"u"?t:e,ru=(e={})=>({dryRun:e.dryRun,returnIds:!0,returnDocuments:ri(e.returnDocuments,!0),visibility:e.visibility||"sync",autoGenerateArrayKeys:e.autoGenerateArrayKeys,skipCrossDatasetReferenceValidation:e.skipCrossDatasetReferenceValidation}),rs=e=>"response"===e.type,ra=e=>e.body,rc=(e,t)=>e.reduce((e,r)=>(e[t(r)]=r,e),Object.create(null));function rl(e,t,n,o,i={},u={}){let s="stega"in u?{...n||{},..."boolean"==typeof u.stega?{enabled:u.stega}:u.stega||{}}:n,a=s.enabled?(0,e4.Q)(i):i,c=!1===u.filterResponse?e=>e:e=>e.result,{cache:l,next:f,...p}={useAbortSignal:"u">typeof u.signal,resultSourceMap:s.enabled?"withKeyArraySelector":u.resultSourceMap,...u,returnQuery:!1===u.filterResponse&&!1!==u.returnQuery},d=rS(e,t,"query",{query:o,params:a},"u">typeof l||"u">typeof f?{...p,fetch:{cache:l,next:f}}:p);return s.enabled?d.pipe((0,te.vp)(eU(r.e(770).then(r.bind(r,63770)).then(function(e){return e.stegaEncodeSourceMap$1}).then(({stegaEncodeSourceMap:e})=>e))),(0,te.Tj)(([e,t])=>{let r=t(e.result,e.resultSourceMap,s);return c({...e,result:r})})):d.pipe((0,te.Tj)(c))}function rf(e,t,r,n={}){let o={uri:rq(e,"doc",(()=>{if(!n.releaseId)return r;let e=ta(r);if(!e){if(ti(r))throw Error(`The document ID (\`${r}\`) is a draft, but \`options.releaseId\` is set as \`${n.releaseId}\``);return ts(r,n.releaseId)}if(e!==n.releaseId)throw Error(`The document ID (\`${r}\`) is already a version of \`${e}\` release, but this does not match the provided \`options.releaseId\` (\`${n.releaseId}\`)`);return r})()),json:!0,tag:n.tag,signal:n.signal};return rA(e,t,o).pipe((0,te.pb)(rs),(0,te.Tj)(e=>e.body.documents&&e.body.documents[0]))}function rp(e,t,r,n={}){let o={uri:rq(e,"doc",r.join(",")),json:!0,tag:n.tag,signal:n.signal};return rA(e,t,o).pipe((0,te.pb)(rs),(0,te.Tj)(e=>{let t=rc(e.body.documents||[],e=>e._id);return r.map(e=>t[e]||null)}))}function rd(e,t,r,n={}){return rS(e,t,"query",{query:"*[sanity::partOfRelease($releaseId)]",params:{releaseId:r}},n)}function rh(e,t,r,n){return tI("createIfNotExists",r),rj(e,t,r,"createIfNotExists",n)}function rv(e,t,r,n){return tI("createOrReplace",r),rj(e,t,r,"createOrReplace",n)}function rb(e,t,r,n,o){return tI("createVersion",r),tA("createVersion",r),rO(e,t,{actionType:"sanity.action.document.version.create",publishedId:n,document:r},o)}function ry(e,t,r,n){return rS(e,t,"mutate",{mutations:[{delete:t9(r)}]},n)}function rm(e,t,r,n=!1,o){return rO(e,t,{actionType:"sanity.action.document.version.discard",versionId:r,purge:n},o)}function rg(e,t,r,n){return tI("replaceVersion",r),tA("replaceVersion",r),rO(e,t,{actionType:"sanity.action.document.version.replace",document:r},n)}function r_(e,t,r,n,o){return rO(e,t,{actionType:"sanity.action.document.version.unpublish",versionId:r,publishedId:n},o)}function rw(e,t,r,n){let o;return rS(e,t,"mutate",{mutations:Array.isArray(o=r instanceof t4||r instanceof t5?{patch:r.serialize()}:r instanceof rr||r instanceof rn?r.serialize():r)?o:[o],transactionId:n&&n.transactionId||void 0},n)}function rO(e,t,r,n){let o=Array.isArray(r)?r:[r];return rS(e,t,"actions",{actions:o,transactionId:n&&n.transactionId||void 0,skipCrossDatasetReferenceValidation:n&&n.skipCrossDatasetReferenceValidation||void 0,dryRun:n&&n.dryRun||void 0},n)}function rS(e,t,r,n,o={}){let i="mutate"===r,u="actions"===r,s=i||u?"":ro(n),a=!i&&!u&&s.length<11264,c=a?s:"",l=o.returnFirst,{timeout:f,token:p,tag:d,headers:h,returnQuery:v,lastLiveEventId:b,cacheMode:y}=o,m={method:a?"GET":"POST",uri:rq(e,r,c),json:!0,body:a?void 0:n,query:i&&ru(o),timeout:f,headers:h,token:p,tag:d,returnQuery:v,perspective:o.perspective,resultSourceMap:o.resultSourceMap,lastLiveEventId:Array.isArray(b)?b[0]:b,cacheMode:y,canUseCdn:"query"===r,signal:o.signal,fetch:o.fetch,useAbortSignal:o.useAbortSignal,useCdn:o.useCdn};return rA(e,t,m).pipe((0,te.pb)(rs),(0,te.Tj)(ra),(0,te.Tj)(e=>{if(!i)return e;let t=e.results||[];if(o.returnDocuments)return l?t[0]&&t[0].document:t.map(e=>e.document);let r=l?t[0]&&t[0].id:t.map(e=>e.id);return{transactionId:e.transactionId,results:t,[l?"documentId":"documentIds"]:r}}))}function rj(e,t,r,n,o={}){return rS(e,t,"mutate",{mutations:[{[n]:r}]},Object.assign({returnFirst:!0,returnDocuments:!0},o))}let rx=e=>void 0!==e.config().dataset&&void 0!==e.config().projectId||void 0!==e.config()["~experimental_resource"],rE=(e,t)=>rx(e)&&t.startsWith(rq(e,"query")),rP=(e,t)=>rx(e)&&t.startsWith(rq(e,"mutate")),rC=(e,t)=>rx(e)&&t.startsWith(rq(e,"doc","")),rM=(e,t)=>rx(e)&&t.startsWith(rq(e,"listen")),rI=(e,t)=>rx(e)&&t.startsWith(rq(e,"history","")),rT=(e,t)=>t.startsWith("/data/")||rE(e,t)||rP(e,t)||rC(e,t)||rM(e,t)||rI(e,t);function rA(e,t,r){var n;let o=r.url||r.uri,i=e.config(),u=typeof r.canUseCdn>"u"?["GET","HEAD"].indexOf(r.method||"GET")>=0&&rT(e,o):r.canUseCdn,s=(r.useCdn??i.useCdn)&&u,a=r.tag&&i.requestTagPrefix?[i.requestTagPrefix,r.tag].join("."):r.tag||i.requestTagPrefix;if(a&&null!==r.tag&&(r.query={tag:t$(a),...r.query}),["GET","HEAD","POST"].indexOf(r.method||"GET")>=0&&rE(e,o)){let e=r.resultSourceMap??i.resultSourceMap;void 0!==e&&!1!==e&&(r.query={resultSourceMap:e,...r.query});let t=r.perspective||i.perspective;"u">typeof t&&("previewDrafts"===t&&tW(),tK(t),r.query={perspective:Array.isArray(t)?t.join(","):t,...r.query},(Array.isArray(t)&&t.length>0||"previewDrafts"===t||"drafts"===t)&&s&&(s=!1,tz())),r.lastLiveEventId&&(r.query={...r.query,lastLiveEventId:r.lastLiveEventId}),!1===r.returnQuery&&(r.query={returnQuery:"false",...r.query}),s&&"noStale"==r.cacheMode&&(r.query={cacheMode:"noStale",...r.query})}let c=function(e,t={}){let r={};e.headers&&Object.assign(r,e.headers);let n=t.token||e.token;n&&(r.Authorization=`Bearer ${n}`),t.useGlobalApi||e.useProjectHostname||!e.projectId||(r["X-Sanity-Project-ID"]=e.projectId);let o=!!(typeof t.withCredentials>"u"?e.withCredentials:t.withCredentials),i=typeof t.timeout>"u"?e.timeout:t.timeout;return Object.assign({},t,{headers:Object.assign({},r,t.headers||{}),timeout:typeof i>"u"?3e5:i,proxy:t.proxy||e.proxy,json:!0,withCredentials:o,fetch:"object"==typeof t.fetch&&"object"==typeof e.fetch?{...e.fetch,...t.fetch}:t.fetch||e.fetch})}(i,Object.assign({},r,{url:rF(e,o,s)})),l=new ey(e=>t(c,i.requester).subscribe(e));return r.signal?l.pipe((n=r.signal,e=>new ey(t=>{let r=()=>t.error(function(e){if(r$)return new DOMException(e?.reason??"The operation was aborted.","AbortError");let t=Error(e?.reason??"The operation was aborted.");return t.name="AbortError",t}(n));if(n&&n.aborted){r();return}let o=e.subscribe(t);return n.addEventListener("abort",r),()=>{n.removeEventListener("abort",r),o.unsubscribe()}}))):l}function rR(e,t,r){return rA(e,t,r).pipe((0,te.pb)(e=>"response"===e.type),(0,te.Tj)(e=>e.body))}function rq(e,t,r){let n=e.config();if(n["~experimental_resource"]){tk(n);let e=rk(n),o=void 0!==r?`${t}/${r}`:t;return`${e}/${o}`.replace(/\/($|\?)/,"$1")}let o=tF(n),i=`/${t}/${o}`;return`/data${void 0!==r?`${i}/${r}`:i}`.replace(/\/($|\?)/,"$1")}function rF(e,t,r=!1){let{url:n,cdnUrl:o}=e.config();return`${r?o:n}/${t.replace(/^\//,"")}`}let r$=!!globalThis.DOMException,rk=e=>{if(!e["~experimental_resource"])throw Error("`resource` must be provided to perform resource queries");let{type:t,id:r}=e["~experimental_resource"];switch(t){case"dataset":{let e=r.split(".");if(2!==e.length)throw Error('Dataset ID must be in the format "project.dataset"');return`/projects/${e[0]}/datasets/${e[1]}`}case"canvas":return`/canvases/${r}`;case"media-library":return`/media-libraries/${r}`;case"dashboard":return`/dashboards/${r}`;default:throw Error(`Unsupported resource type: ${t.toString()}`)}};function rN(e,t,r){let n=tF(e.config());return rR(e,t,{method:"POST",uri:`/agent/action/generate/${n}`,body:r})}function rU(e,t,r){let n=tF(e.config());return rR(e,t,{method:"POST",uri:`/agent/action/transform/${n}`,body:r})}function rD(e,t,r){let n=tF(e.config());return rR(e,t,{method:"POST",uri:`/agent/action/translate/${n}`,body:r})}class rL{#s;#a;constructor(e,t){this.#s=e,this.#a=t}generate(e){return rN(this.#s,this.#a,e)}transform(e){return rU(this.#s,this.#a,e)}translate(e){return rD(this.#s,this.#a,e)}}class rz{#s;#a;constructor(e,t){this.#s=e,this.#a=t}generate(e){return eV(rN(this.#s,this.#a,e))}transform(e){return eV(rU(this.#s,this.#a,e))}translate(e){return eV(rD(this.#s,this.#a,e))}}class rW{#s;#a;constructor(e,t){this.#s=e,this.#a=t}upload(e,t,r){return rB(this.#s,this.#a,e,t,r)}}class rV{#s;#a;constructor(e,t){this.#s=e,this.#a=t}upload(e,t,r){return eV(rB(this.#s,this.#a,e,t,r).pipe((0,te.pb)(e=>"response"===e.type),(0,te.Tj)(e=>e.body.document)))}}function rB(e,t,r,n,o={}){tP(r);let i=o.extract||void 0;i&&!i.length&&(i=["none"]);let u=e.config(),s=!(typeof File>"u")&&n instanceof File?Object.assign({filename:!1===o.preserveFilename?void 0:n.name,contentType:n.type},o):o,{tag:a,label:c,title:l,description:f,creditLine:p,filename:d,source:h}=s,v={label:c,title:l,description:f,filename:d,meta:i,creditLine:p};return h&&(v.sourceId=h.id,v.sourceName=h.name,v.sourceUrl=h.url),rA(e,t,{tag:a,method:"POST",timeout:s.timeout||0,uri:function(e,t){let r="image"===t?"images":"files";if(e["~experimental_resource"]){let{type:t,id:n}=e["~experimental_resource"];switch(t){case"dataset":throw Error("Assets are not supported for dataset resources, yet. Configure the client with `{projectId: <projectId>, dataset: <datasetId>}` instead.");case"canvas":return`/canvases/${n}/assets/${r}`;case"media-library":return`/media-libraries/${n}/upload`;case"dashboard":return`/dashboards/${n}/assets/${r}`;default:throw Error(`Unsupported resource type: ${t.toString()}`)}}let n=tF(e);return`assets/${r}/${n}`}(u,r),headers:s.contentType?{"Content-Type":s.contentType}:{},query:v,body:n})}var rH=(e,t)=>Object.keys(t).concat(Object.keys(e)).reduce((r,n)=>(r[n]=typeof e[n]>"u"?t[n]:e[n],r),{});let rY=(e,t)=>t.reduce((t,r)=>(typeof e[r]>"u"||(t[r]=e[r]),t),{}),rG=eM(()=>r.e(87).then(r.t.bind(r,55087,19))).pipe((0,te.Tj)(({default:e})=>e),function(e,t,r){var n;return n=e,eK({connector:function(){return new eJ(n,void 0,void 0)},resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:!1})}(1));function rJ(){return function(e){return e.pipe(eX((e,t)=>{var r;return e instanceof tX?e0(eD({type:"reconnect"}),(void 0===r&&(r=e8),new ey(function(e){var t=1e3,n=0;return r.schedule(function(){e.closed||(e.next(n++),e.complete())},t)})).pipe(ez(()=>t))):e9(()=>e)}))}}let rK=["includePreviousRevision","includeResult","includeMutations","includeAllVersions","visibility","effectFormat","tag"],rQ={includeResult:!0};function rX(e,t,r={}){let{url:n,token:o,withCredentials:i,requestTagPrefix:u}=this.config(),s=r.tag&&u?[u,r.tag].join("."):r.tag,a={...rH(r,rQ),tag:s},c=ro({query:e,params:t,options:{tag:s,...rY(a,rK)}}),l=`${n}${rq(this,"listen",c)}`;if(l.length>14800)return e9(()=>Error("Query too large for listener"));let f=a.events?a.events:["mutation"],p={};return i&&(p.withCredentials=!0),o&&(p.headers={Authorization:`Bearer ${o}`}),t3(()=>(typeof EventSource>"u"||p.headers?rG:eD(EventSource)).pipe((0,te.Tj)(e=>new e(l,p))),f).pipe(rJ(),(0,te.pb)(e=>f.includes(e.type)),(0,te.Tj)(e=>({type:e.type,..."data"in e?e.data:{}})))}let rZ="2021-03-25";class r0{#s;constructor(e){this.#s=e}events({includeDrafts:e=!1,tag:t}={}){var r,n,o;tN("live",this.#s.config());let{projectId:i,apiVersion:u,token:s,withCredentials:a,requestTagPrefix:c}=this.#s.config(),l=u.replace(/^v/,"");if("X"!==l&&l<rZ)throw Error(`The live events API requires API version ${rZ} or later. The current API version is ${l}. Please update your API version to use this feature.`);if(e&&!s&&!a)throw Error("The live events API requires a token or withCredentials when 'includeDrafts: true'. Please update your client configuration. The token should have the lowest possible access role.");let f=rq(this.#s,"live/events"),p=new URL(this.#s.getUrl(f,!1)),d=t&&c?[c,t].join("."):t;d&&p.searchParams.set("tag",d),e&&p.searchParams.set("includeDrafts","true");let h={};e&&s&&(h.headers={Authorization:`Bearer ${s}`}),e&&a&&(h.withCredentials=!0);let v=`${p.href}::${JSON.stringify(h)}`,b=r1.get(v);if(b)return b;let y=t3(()=>(typeof EventSource>"u"||h.headers?rG:eD(EventSource)).pipe((0,te.Tj)(e=>new e(p.href,h))),["message","restart","welcome","reconnect","goaway"]).pipe(rJ(),(0,te.Tj)(e=>{if("message"===e.type){let{data:t,...r}=e;return{...r,tags:t.tags}}return e})),m=e0((n={method:"OPTIONS",mode:"cors",credentials:h.withCredentials?"include":"omit",headers:h.headers},new ey(e=>{let t=new AbortController,r=t.signal;return fetch(p,{...n,signal:t.signal}).then(t=>{e.next(t),e.complete()},t=>{r.aborted||e.error(t)}),()=>t.abort()})).pipe(ez(()=>e7),eX(()=>{throw new tm({projectId:i})})),y).pipe((0,te.jE)(()=>r1.delete(v)),(o="function"==typeof(r={predicate:e=>"welcome"===e.type})?{predicate:r}:r,e=>{var t,r,n,i,u;let s,a=!1,{predicate:c,...l}=o;return function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var n=eT(t),o=(e=1/0,"number"==typeof eI(t)?t.pop():e);return t.length?1===t.length?eP(t[0]):eZ(o)(eU(t,n)):e7}(e.pipe((i=B(t=e=>{o.predicate(e)&&(a=!0,s=e)})?{next:t,error:r,complete:n}:t)?eR(function(e,t){null===(r=i.subscribe)||void 0===r||r.call(i);var r,n=!0;e.subscribe(eq(t,function(e){var r;null===(r=i.next)||void 0===r||r.call(i,e),t.next(e)},function(){var e;n=!1,null===(e=i.complete)||void 0===e||e.call(i),t.complete()},function(e){var r;n=!1,null===(r=i.error)||void 0===r||r.call(i,e),t.error(e)},function(){var e,t;n&&(null===(e=i.unsubscribe)||void 0===e||e.call(i)),null===(t=i.finalize)||void 0===t||t.call(i)}))}):eb,(u=()=>{a=!1,s=void 0},eR(function(e,t){try{e.subscribe(t)}finally{t.add(u)}})),eK(l)),new ey(e=>{a&&e.next(s),e.complete()}))}));return r1.set(v,m),m}}let r1=new Map;class r2{#s;#a;constructor(e,t){this.#s=e,this.#a=t}create(e,t){return r3(this.#s,this.#a,"PUT",e,t)}edit(e,t){return r3(this.#s,this.#a,"PATCH",e,t)}delete(e){return r3(this.#s,this.#a,"DELETE",e)}list(){return rR(this.#s,this.#a,{uri:"/datasets",tag:null})}}class r6{#s;#a;constructor(e,t){this.#s=e,this.#a=t}create(e,t){return tN("dataset",this.#s.config()),eV(r3(this.#s,this.#a,"PUT",e,t))}edit(e,t){return tN("dataset",this.#s.config()),eV(r3(this.#s,this.#a,"PATCH",e,t))}delete(e){return tN("dataset",this.#s.config()),eV(r3(this.#s,this.#a,"DELETE",e))}list(){return tN("dataset",this.#s.config()),eV(rR(this.#s,this.#a,{uri:"/datasets",tag:null}))}}function r3(e,t,r,n,o){return tN("dataset",e.config()),tx(n),rR(e,t,{method:r,uri:`/datasets/${n}`,body:o,tag:null})}class r8{#s;#a;constructor(e,t){this.#s=e,this.#a=t}list(e){tN("projects",this.#s.config());let t=e?.includeMembers===!1?"/projects?includeMembers=false":"/projects";return rR(this.#s,this.#a,{uri:t})}getById(e){return tN("projects",this.#s.config()),rR(this.#s,this.#a,{uri:`/projects/${e}`})}}class r9{#s;#a;constructor(e,t){this.#s=e,this.#a=t}list(e){tN("projects",this.#s.config());let t=e?.includeMembers===!1?"/projects?includeMembers=false":"/projects";return eV(rR(this.#s,this.#a,{uri:t}))}getById(e){return tN("projects",this.#s.config()),eV(rR(this.#s,this.#a,{uri:`/projects/${e}`}))}}let r7=((e,t=21)=>td(e,t,tp))("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",8),r5=(e,t)=>t?ts(e,t):function(e){return tu(e)?tn+tc(e):ti(e)?e:tn+e}(e);function r4(e,{releaseId:t,publishedId:r,document:n}){if(r&&n._id){let e=r5(r,t);return tR(e,n),e}if(n._id){let r=ti(n._id),o=tu(n._id);if(!r&&!o)throw Error(`\`${e}()\` requires a document with an \`_id\` that is a version or draft ID`);if(t){if(r)throw Error(`\`${e}()\` was called with a document ID (\`${n._id}\`) that is a draft ID, but a release ID (\`${t}\`) was also provided.`);let o=ta(n._id);if(o!==t)throw Error(`\`${e}()\` was called with a document ID (\`${n._id}\`) that is a version ID, but the release ID (\`${t}\`) does not match the document's version ID (\`${o}\`).`)}return n._id}if(r)return r5(r,t);throw Error(`\`${e}()\` requires either a publishedId or a document with an \`_id\``)}let ne=(e,t)=>{if("object"==typeof e&&null!==e&&("releaseId"in e||"metadata"in e)){let{releaseId:r=r7(),metadata:n={}}=e;return[r,n,t]}return[r7(),{},e]},nt=(e,t)=>{let[r,n,o]=ne(e,t);return{action:{actionType:"sanity.action.release.create",releaseId:r,metadata:{...n,releaseType:n.releaseType||"undecided"}},options:o}};class nr{#s;#a;constructor(e,t){this.#s=e,this.#a=t}get({releaseId:e},t){return rf(this.#s,this.#a,`_.releases.${e}`,t)}create(e,t){let{action:r,options:n}=nt(e,t),{releaseId:o,metadata:i}=r;return rO(this.#s,this.#a,r,n).pipe(eL(e=>({...e,releaseId:o,metadata:i})))}edit({releaseId:e,patch:t},r){return rO(this.#s,this.#a,{actionType:"sanity.action.release.edit",releaseId:e,patch:t},r)}publish({releaseId:e},t){return rO(this.#s,this.#a,{actionType:"sanity.action.release.publish",releaseId:e},t)}archive({releaseId:e},t){return rO(this.#s,this.#a,{actionType:"sanity.action.release.archive",releaseId:e},t)}unarchive({releaseId:e},t){return rO(this.#s,this.#a,{actionType:"sanity.action.release.unarchive",releaseId:e},t)}schedule({releaseId:e,publishAt:t},r){return rO(this.#s,this.#a,{actionType:"sanity.action.release.schedule",releaseId:e,publishAt:t},r)}unschedule({releaseId:e},t){return rO(this.#s,this.#a,{actionType:"sanity.action.release.unschedule",releaseId:e},t)}delete({releaseId:e},t){return rO(this.#s,this.#a,{actionType:"sanity.action.release.delete",releaseId:e},t)}fetchDocuments({releaseId:e},t){return rd(this.#s,this.#a,e,t)}}class nn{#s;#a;constructor(e,t){this.#s=e,this.#a=t}get({releaseId:e},t){return eV(rf(this.#s,this.#a,`_.releases.${e}`,t))}async create(e,t){let{action:r,options:n}=nt(e,t),{releaseId:o,metadata:i}=r;return{...await eV(rO(this.#s,this.#a,r,n)),releaseId:o,metadata:i}}edit({releaseId:e,patch:t},r){return eV(rO(this.#s,this.#a,{actionType:"sanity.action.release.edit",releaseId:e,patch:t},r))}publish({releaseId:e},t){return eV(rO(this.#s,this.#a,{actionType:"sanity.action.release.publish",releaseId:e},t))}archive({releaseId:e},t){return eV(rO(this.#s,this.#a,{actionType:"sanity.action.release.archive",releaseId:e},t))}unarchive({releaseId:e},t){return eV(rO(this.#s,this.#a,{actionType:"sanity.action.release.unarchive",releaseId:e},t))}schedule({releaseId:e,publishAt:t},r){return eV(rO(this.#s,this.#a,{actionType:"sanity.action.release.schedule",releaseId:e,publishAt:t},r))}unschedule({releaseId:e},t){return eV(rO(this.#s,this.#a,{actionType:"sanity.action.release.unschedule",releaseId:e},t))}delete({releaseId:e},t){return eV(rO(this.#s,this.#a,{actionType:"sanity.action.release.delete",releaseId:e},t))}fetchDocuments({releaseId:e},t){return eV(rd(this.#s,this.#a,e,t))}}class no{#s;#a;constructor(e,t){this.#s=e,this.#a=t}getById(e){return rR(this.#s,this.#a,{uri:`/users/${e}`})}}class ni{#s;#a;constructor(e,t){this.#s=e,this.#a=t}getById(e){return eV(rR(this.#s,this.#a,{uri:`/users/${e}`}))}}class nu{assets;datasets;live;projects;users;agent;releases;#c;#a;listen=rX;constructor(e,t=tY){this.config(t),this.#a=e,this.assets=new rW(this,this.#a),this.datasets=new r2(this,this.#a),this.live=new r0(this),this.projects=new r8(this,this.#a),this.users=new no(this,this.#a),this.agent={action:new rL(this,this.#a)},this.releases=new nr(this,this.#a)}clone(){return new nu(this.#a,this.config())}config(e){if(void 0===e)return{...this.#c};if(this.#c&&!1===this.#c.allowReconfigure)throw Error("Existing client instance cannot be reconfigured - use `withConfig(newConfig)` to return a new client");return this.#c=tQ(e,this.#c||{}),this}withConfig(e){let t=this.config();return new nu(this.#a,{...t,...e,stega:{...t.stega||{},..."boolean"==typeof e?.stega?{enabled:e.stega}:e?.stega||{}}})}fetch(e,t,r){return rl(this,this.#a,this.#c.stega,e,t,r)}getDocument(e,t){return rf(this,this.#a,e,t)}getDocuments(e,t){return rp(this,this.#a,e,t)}create(e,t){return rj(this,this.#a,e,"create",t)}createIfNotExists(e,t){return rh(this,this.#a,e,t)}createOrReplace(e,t){return rv(this,this.#a,e,t)}createVersion({document:e,publishedId:t,releaseId:r},n){let o=r4("createVersion",{document:e,publishedId:t,releaseId:r}),i={...e,_id:o},u=t||tc(e._id);return rb(this,this.#a,i,u,n)}delete(e,t){return ry(this,this.#a,e,t)}discardVersion({releaseId:e,publishedId:t},r,n){let o=r5(t,e);return rm(this,this.#a,o,r,n)}replaceVersion({document:e,publishedId:t,releaseId:r},n){let o=r4("replaceVersion",{document:e,publishedId:t,releaseId:r}),i={...e,_id:o};return rg(this,this.#a,i,n)}unpublishVersion({releaseId:e,publishedId:t},r){let n=ts(t,e);return r_(this,this.#a,n,t,r)}mutate(e,t){return rw(this,this.#a,e,t)}patch(e,t){return new t5(e,t,this)}transaction(e){return new rn(e,this)}action(e,t){return rO(this,this.#a,e,t)}request(e){return rR(this,this.#a,e)}getUrl(e,t){return rF(this,e,t)}getDataUrl(e,t){return rq(this,e,t)}}class ns{assets;datasets;live;projects;users;agent;releases;observable;#c;#a;listen=rX;constructor(e,t=tY){this.config(t),this.#a=e,this.assets=new rV(this,this.#a),this.datasets=new r6(this,this.#a),this.live=new r0(this),this.projects=new r9(this,this.#a),this.users=new ni(this,this.#a),this.agent={action:new rz(this,this.#a)},this.releases=new nn(this,this.#a),this.observable=new nu(e,t)}clone(){return new ns(this.#a,this.config())}config(e){if(void 0===e)return{...this.#c};if(this.#c&&!1===this.#c.allowReconfigure)throw Error("Existing client instance cannot be reconfigured - use `withConfig(newConfig)` to return a new client");return this.observable&&this.observable.config(e),this.#c=tQ(e,this.#c||{}),this}withConfig(e){let t=this.config();return new ns(this.#a,{...t,...e,stega:{...t.stega||{},..."boolean"==typeof e?.stega?{enabled:e.stega}:e?.stega||{}}})}fetch(e,t,r){return eV(rl(this,this.#a,this.#c.stega,e,t,r))}getDocument(e,t){return eV(rf(this,this.#a,e,t))}getDocuments(e,t){return eV(rp(this,this.#a,e,t))}create(e,t){return eV(rj(this,this.#a,e,"create",t))}createIfNotExists(e,t){return eV(rh(this,this.#a,e,t))}createOrReplace(e,t){return eV(rv(this,this.#a,e,t))}createVersion({document:e,publishedId:t,releaseId:r},n){let o=r4("createVersion",{document:e,publishedId:t,releaseId:r}),i={...e,_id:o},u=t||tc(e._id);return e5(rb(this,this.#a,i,u,n))}delete(e,t){return eV(ry(this,this.#a,e,t))}discardVersion({releaseId:e,publishedId:t},r,n){let o=r5(t,e);return eV(rm(this,this.#a,o,r,n))}replaceVersion({document:e,publishedId:t,releaseId:r},n){let o=r4("replaceVersion",{document:e,publishedId:t,releaseId:r}),i={...e,_id:o};return e5(rg(this,this.#a,i,n))}unpublishVersion({releaseId:e,publishedId:t},r){let n=ts(t,e);return eV(r_(this,this.#a,n,t,r))}mutate(e,t){return eV(rw(this,this.#a,e,t))}patch(e,t){return new t4(e,t,this)}transaction(e){return new rr(e,this)}action(e,t){return eV(rO(this,this.#a,e,t))}request(e){return eV(rR(this,this.#a,e))}dataRequest(e,t,r){return eV(rS(this,this.#a,e,t,r))}getUrl(e,t){return rF(this,e,t)}getDataUrl(e,t){return rq(this,e,t)}}let na=function(e,t){return{requester:t_(e),createClient:r=>{let n=t_(e);return new t((e,t)=>(t||n)({maxRedirects:0,maxRetries:r.maxRetries,retryDelay:r.retryDelay,...e}),r)}}}([],ns),nc=(na.requester,na.createClient)}};