{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/Header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/Header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyR,GACtT,uDACA"}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/Header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/Header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAqQ,GAClS,mCACA"}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/Footer.tsx"], "sourcesContent": ["import Link from 'next/link'\n\nexport default function Footer() {\n  const currentYear = new Date().getFullYear()\n\n  return (\n    <footer className=\"bg-gradient-to-b from-neutral-800 to-neutral-900 text-white\">\n      <div className=\"container py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          <div className=\"md:col-span-2\">\n            <h3 className=\"text-xl font-bold mb-4 text-white\">Northern Nepalese United FC</h3>\n            <p className=\"mb-4 text-neutral-300\">\n              Brisbane-based Nepalese football club est. 2023, bringing together the community through the beautiful game.\n            </p>\n            <div className=\"flex space-x-4 mt-6\">\n              <a\n                href=\"https://facebook.com\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                aria-label=\"Facebook\"\n                className=\"bg-neutral-700 hover:bg-neutral-600 text-white p-2 rounded-full transition-colors\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"currentColor\"\n                >\n                  <path d=\"M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z\" />\n                </svg>\n              </a>\n              <a\n                href=\"https://twitter.com\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                aria-label=\"Twitter\"\n                className=\"bg-neutral-700 hover:bg-neutral-600 text-white p-2 rounded-full transition-colors\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"currentColor\"\n                >\n                  <path d=\"M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z\" />\n                </svg>\n              </a>\n              <a\n                href=\"https://instagram.com\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                aria-label=\"Instagram\"\n                className=\"bg-neutral-700 hover:bg-neutral-600 text-white p-2 rounded-full transition-colors\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"currentColor\"\n                >\n                  <path d=\"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z\" />\n                </svg>\n              </a>\n            </div>\n          </div>\n\n          <div>\n            <h3 className=\"text-xl font-bold mb-4 text-white\">Club Links</h3>\n            <ul className=\"space-y-2 text-neutral-300\">\n              <li>\n                <Link href=\"/about\" className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\">\n                  About Us\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/team\" className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\">\n                  Our Team\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/news\" className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\">\n                  News\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/events\" className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\">\n                  Events & Fixtures\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/gallery\" className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\">\n                  Gallery\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          <div>\n            <h3 className=\"text-xl font-bold mb-4 text-white\">Contact</h3>\n            <address className=\"not-italic text-neutral-300\">\n              <p className=\"mb-2 flex items-start\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2 mt-0.5 text-neutral-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                </svg>\n                John Oxley Reserve<br />Murrumba Downs<br />Brisbane, QLD\n              </p>\n              <p className=\"mb-2 flex items-start\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2 mt-0.5 text-neutral-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                </svg>\n                <a href=\"tel:+61424770570\" className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\">\n                  0424 770 570\n                </a>\n              </p>\n              <p className=\"flex items-start\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2 mt-0.5 text-neutral-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                </svg>\n                <a\n                  href=\"mailto:<EMAIL>\"\n                  className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\"\n                >\n                  <EMAIL>\n                </a>\n              </p>\n            </address>\n          </div>\n        </div>\n\n        <div className=\"border-t border-neutral-700 mt-10 pt-8 text-center text-neutral-400\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <p>&copy; {currentYear} Northern Nepalese United - NNUFC. All rights reserved.</p>\n            <ul className=\"flex space-x-4 mt-4 md:mt-0\">\n              <li>\n                <Link href=\"/sponsors\" className=\"text-neutral-400 hover:text-white hover:underline transition-colors no-underline\">\n                  Sponsors\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contact\" className=\"text-neutral-400 hover:text-white hover:underline transition-colors no-underline\">\n                  Contact\n                </Link>\n              </li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;8CAGrC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,cAAW;4CACX,WAAU;sDAEV,cAAA,8OAAC;gDACC,OAAM;gDACN,OAAM;gDACN,QAAO;gDACP,SAAQ;gDACR,MAAK;0DAEL,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,cAAW;4CACX,WAAU;sDAEV,cAAA,8OAAC;gDACC,OAAM;gDACN,OAAM;gDACN,QAAO;gDACP,SAAQ;gDACR,MAAK;0DAEL,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,cAAW;4CACX,WAAU;sDAEV,cAAA,8OAAC;gDACC,OAAM;gDACN,OAAM;gDACN,QAAO;gDACP,SAAQ;gDACR,MAAK;0DAEL,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMhB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAmF;;;;;;;;;;;sDAInH,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAmF;;;;;;;;;;;sDAIlH,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAmF;;;;;;;;;;;sDAIlH,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAU,WAAU;0DAAmF;;;;;;;;;;;sDAIpH,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmF;;;;;;;;;;;;;;;;;;;;;;;sCAOzH,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,8OAAC;oCAAQ,WAAU;;sDACjB,8OAAC;4CAAE,WAAU;;8DACX,8OAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAAuC,MAAK;oDAAO,SAAQ;oDAAY,QAAO;;sEAC9H,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;sEACrE,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;gDACjE;8DACY,8OAAC;;;;;gDAAK;8DAAc,8OAAC;;;;;gDAAK;;;;;;;sDAE9C,8OAAC;4CAAE,WAAU;;8DACX,8OAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAAuC,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAC9H,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;8DAEvE,8OAAC;oDAAE,MAAK;oDAAmB,WAAU;8DAAmF;;;;;;;;;;;;sDAI1H,8OAAC;4CAAE,WAAU;;8DACX,8OAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAAuC,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAC9H,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;8DAEvE,8OAAC;oDACC,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQT,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;oCAAE;oCAAQ;oCAAY;;;;;;;0CACvB,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAY,WAAU;sDAAmF;;;;;;;;;;;kDAItH,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAAmF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUnI"}}, {"offset": {"line": 525, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 539, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/lib/sanity.ts"], "sourcesContent": ["import { createClient } from 'next-sanity'\nimport imageUrlBuilder from '@sanity/image-url'\n\nconst projectId = process.env.NEXT_PUBLIC_SANITY_PROJECT_ID!\nconst dataset = process.env.NEXT_PUBLIC_SANITY_DATASET!\nconst apiVersion = process.env.NEXT_PUBLIC_SANITY_API_VERSION || '2024-05-23'\n\nexport const client = createClient({\n  projectId,\n  dataset,\n  apiVersion,\n  useCdn: typeof document !== 'undefined',\n})\n\n// Helper function for generating image URLs with the Sanity Image Pipeline\nconst builder = imageUrlBuilder({\n  projectId,\n  dataset,\n})\n\nexport function urlForImage(source: any) {\n  return builder.image(source)\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM;AACN,MAAM;AACN,MAAM,aAAa,kDAA8C;AAE1D,MAAM,SAAS,CAAA,GAAA,8KAAA,CAAA,eAAY,AAAD,EAAE;IACjC;IACA;IACA;IACA,QAAQ,OAAO,aAAa;AAC9B;AAEA,2EAA2E;AAC3E,MAAM,UAAU,CAAA,GAAA,gKAAA,CAAA,UAAe,AAAD,EAAE;IAC9B;IACA;AACF;AAEO,SAAS,YAAY,MAAW;IACrC,OAAO,QAAQ,KAAK,CAAC;AACvB"}}, {"offset": {"line": 564, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 570, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/lib/queries.ts"], "sourcesContent": ["// Latest news articles\nexport const latestNewsQuery = `\n  *[_type == \"newsArticle\"] | order(publishedAt desc)[0...3] {\n    _id,\n    title,\n    slug,\n    publishedAt,\n    mainImage,\n    summary\n  }\n`;\n\n// Single news article by slug\nexport const newsArticleBySlugQuery = `\n  *[_type == \"newsArticle\" && slug.current == $slug][0] {\n    _id,\n    title,\n    slug,\n    publishedAt,\n    mainImage,\n    summary,\n    body\n  }\n`;\n\n// All news articles\nexport const allNewsArticlesQuery = `\n  *[_type == \"newsArticle\"] | order(publishedAt desc) {\n    _id,\n    title,\n    slug,\n    publishedAt,\n    mainImage,\n    summary\n  }\n`;\n\n// Upcoming events\nexport const upcomingEventsQuery = `\n  *[_type == \"event\" && date > now()] | order(date asc)[0...5] {\n    _id,\n    title,\n    slug,\n    date,\n    location,\n    eventType,\n    opponent,\n    homeOrAway\n  }\n`;\n\n// All events\nexport const allEventsQuery = `\n  *[_type == \"event\"] | order(date desc) {\n    _id,\n    title,\n    slug,\n    date,\n    location,\n    eventType,\n    opponent,\n    homeOrAway,\n    result\n  }\n`;\n\n// Single event by slug\nexport const eventBySlugQuery = `\n  *[_type == \"event\" && slug.current == $slug][0] {\n    _id,\n    title,\n    slug,\n    date,\n    location,\n    eventType,\n    opponent,\n    homeOrAway,\n    result,\n    description\n  }\n`;\n\n// All sponsors ordered by display order\nexport const sponsorsQuery = `\n  *[_type == \"sponsor\"] | order(displayOrder asc) {\n    _id,\n    name,\n    logo,\n    websiteUrl,\n    sponsorshipLevel\n  }\n`;\n\n// Gallery images\nexport const galleryImagesQuery = `\n  *[_type == \"galleryImage\"] | order(dateTaken desc) {\n    _id,\n    title,\n    imageFile,\n    dateTaken\n  }\n`;\n\n// Players\nexport const playersQuery = `\n  *[_type == \"player\"] | order(jerseyNumber asc) {\n    _id,\n    name,\n    position,\n    jerseyNumber,\n    image,\n    stats,\n    bio\n  }\n`;\n\n// Staff\nexport const staffQuery = `\n  *[_type == \"staff\"] | order(displayOrder asc) {\n    _id,\n    name,\n    role,\n    image,\n    bio,\n    contactInfo\n  }\n`;\n"], "names": [], "mappings": "AAAA,uBAAuB;;;;;;;;;;;;;AAChB,MAAM,kBAAkB,CAAC;;;;;;;;;AAShC,CAAC;AAGM,MAAM,yBAAyB,CAAC;;;;;;;;;;AAUvC,CAAC;AAGM,MAAM,uBAAuB,CAAC;;;;;;;;;AASrC,CAAC;AAGM,MAAM,sBAAsB,CAAC;;;;;;;;;;;AAWpC,CAAC;AAGM,MAAM,iBAAiB,CAAC;;;;;;;;;;;;AAY/B,CAAC;AAGM,MAAM,mBAAmB,CAAC;;;;;;;;;;;;;AAajC,CAAC;AAGM,MAAM,gBAAgB,CAAC;;;;;;;;AAQ9B,CAAC;AAGM,MAAM,qBAAqB,CAAC;;;;;;;AAOnC,CAAC;AAGM,MAAM,eAAe,CAAC;;;;;;;;;;AAU7B,CAAC;AAGM,MAAM,aAAa,CAAC;;;;;;;;;AAS3B,CAAC"}}, {"offset": {"line": 691, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 697, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/ParticleSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/ParticleSection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ParticleSection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA"}}, {"offset": {"line": 705, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 711, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/ParticleSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/ParticleSection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ParticleSection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8Q,GAC3S,4CACA"}}, {"offset": {"line": 719, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 725, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 729, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 735, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/src/app/page.tsx"], "sourcesContent": ["import Image from \"next/image\";\nimport Link from \"next/link\";\nimport Header from \"@/components/Header\";\nimport Footer from \"@/components/Footer\";\nimport { client } from \"@/lib/sanity\";\nimport { latestNewsQuery, upcomingEventsQuery, sponsorsQuery } from \"@/lib/queries\";\nimport { urlForImage } from \"@/lib/sanity\";\nimport ParticleSection from \"@/components/ParticleSection\";\n\n// Define interfaces for Sanity data\ninterface SanityImage {\n  asset: {\n    _ref: string;\n    _type: string;\n  };\n}\n\ninterface Article {\n  _id: string;\n  title: string;\n  slug: { current: string };\n  publishedAt: string;\n  mainImage?: SanityImage;\n  summary?: string;\n}\n\ninterface Event {\n  _id: string;\n  title: string;\n  slug: { current: string };\n  date: string;\n  eventTime?: string;\n  location?: string;\n  eventType?: string;\n  opponent?: string;\n  homeOrAway?: 'home' | 'away';\n}\n\ninterface Sponsor {\n  _id: string;\n  name: string;\n  logo?: SanityImage;\n  websiteUrl?: string;\n  sponsorshipLevel?: string;\n}\n\n// This makes the page dynamic and will revalidate every 60 seconds\nexport const revalidate = 60;\n\nasync function getLatestNews() {\n  return await client.fetch(latestNewsQuery);\n}\n\nasync function getUpcomingEvents() {\n  return await client.fetch(upcomingEventsQuery);\n}\n\nasync function getSponsors() {\n  return await client.fetch(sponsorsQuery);\n}\n\nexport default async function Home() {\n  // Fetch data from Sanity\n  const [latestNews, upcomingEvents, sponsors] = await Promise.all([\n    getLatestNews(),\n    getUpcomingEvents(),\n    getSponsors(),\n  ]);\n  return (\n    <>\n      <Header />\n      <main>\n        {/* Hero Section */}\n        <section className=\"bg-gradient-to-br from-primary-700 to-primary-900 text-white py-24\">\n          <div className=\"container text-center\">\n            <h1 className=\"text-4xl md:text-5xl font-bold mb-6\">Welcome to Northern Nepalese United - NNUFC</h1>\n            <p className=\"text-xl mb-8 max-w-3xl mx-auto\">\n              Brisbane-based Nepalese football team established in 2023, offering a thriving platform for enthusiasts to play and represent our community.\n            </p>\n            <div className=\"flex flex-wrap justify-center gap-4\">\n              <Link\n                href=\"/about\"\n                className=\"bg-white text-primary-800 px-6 py-3 rounded-lg font-semibold hover:bg-primary-50 transition-colors shadow-md\"\n              >\n                Learn More\n              </Link>\n              <Link\n                href=\"/contact\"\n                className=\"bg-transparent border-2 border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary-800 transition-colors\"\n              >\n                Contact Us\n              </Link>\n            </div>\n          </div>\n        </section>\n\n        {/* Latest News Section */}\n        <section className=\"py-16 bg-neutral-50\">\n          <div className=\"container\">\n            <h2 className=\"text-3xl font-bold mb-8 text-center\">Latest News</h2>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n              {latestNews && latestNews.length > 0 ? (\n                latestNews.map((article: Article) => (\n                  <div key={article._id} className=\"card\">\n                    {article.mainImage ? (\n                      <div className=\"relative h-48\">\n                        <Image\n                          src={urlForImage(article.mainImage).url()}\n                          alt={article.title}\n                          fill\n                          className=\"object-cover\"\n                        />\n                      </div>\n                    ) : (\n                      <div className=\"relative h-48 bg-neutral-200\"></div>\n                    )}\n                    <div className=\"p-6\">\n                      <h3 className=\"text-xl font-bold mb-2\">{article.title}</h3>\n                      <p className=\"text-neutral-600 text-sm mb-4\">\n                        {new Date(article.publishedAt).toLocaleDateString()}\n                      </p>\n                      {article.summary && (\n                        <p className=\"text-neutral-700 mb-4\">{article.summary}</p>\n                      )}\n                      <Link\n                        href={`/news/${article.slug.current}`}\n                        className=\"text-primary-600 font-semibold hover:text-primary-800\"\n                      >\n                        Read More →\n                      </Link>\n                    </div>\n                  </div>\n                ))\n              ) : (\n                <div className=\"col-span-3 text-center py-8\">\n                  <p className=\"text-neutral-600\">No news articles available yet.</p>\n                  <p className=\"mt-2\">Check back soon for updates!</p>\n                </div>\n              )}\n            </div>\n\n            <div className=\"text-center mt-10\">\n              <Link\n                href=\"/news\"\n                className=\"btn-primary\"\n              >\n                View All News\n              </Link>\n            </div>\n          </div>\n        </section>\n\n        {/* Upcoming Events Section */}\n        <section className=\"py-16\">\n          <div className=\"container\">\n            <h2 className=\"text-3xl font-bold mb-8 text-center\">Upcoming Events</h2>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              {upcomingEvents && upcomingEvents.length > 0 ? (\n                upcomingEvents.map((event: Event) => (\n                  <div key={event._id} className=\"border rounded-lg p-6 shadow-sm hover:shadow-md transition\">\n                    <div className=\"flex justify-between items-start\">\n                      <div>\n                        <h3 className=\"text-xl font-bold mb-2\">{event.title}</h3>\n                        <p className=\"text-neutral-600 mb-1\">\n                          {new Date(event.date).toLocaleDateString()} {event.eventTime && `at ${event.eventTime}`}\n                        </p>\n                        {event.location && <p className=\"text-neutral-600 mb-2\">{event.location}</p>}\n                      </div>\n                      {event.eventType && (\n                        <span className=\"bg-primary-100 text-primary-800 text-xs px-2 py-1 rounded uppercase font-semibold\">\n                          {event.eventType}\n                        </span>\n                      )}\n                    </div>\n\n                    {event.opponent && (\n                      <div className=\"mt-3\">\n                        <p className=\"text-neutral-800\">\n                          <span className=\"font-medium\">\n                            {event.homeOrAway === 'home' ? 'Home game vs ' : 'Away game vs '}{event.opponent}\n                          </span>\n                        </p>\n                      </div>\n                    )}\n\n                    <Link\n                      href={`/events/${event.slug.current}`}\n                      className=\"mt-4 inline-block text-primary-600 font-semibold hover:text-primary-800\"\n                    >\n                      View Details →\n                    </Link>\n                  </div>\n                ))\n              ) : (\n                <div className=\"col-span-2 text-center py-8\">\n                  <p className=\"text-neutral-600\">No upcoming events scheduled yet.</p>\n                  <p className=\"mt-2\">Check back soon for updates!</p>\n                </div>\n              )}\n            </div>\n\n            <div className=\"text-center mt-10\">\n              <Link\n                href=\"/events\"\n                className=\"btn-primary\"\n              >\n                View All Events\n              </Link>\n            </div>\n          </div>\n        </section>\n\n        {/* Sponsors Section */}\n        <section className=\"py-16 bg-neutral-50\">\n          <div className=\"container\">\n            <h2 className=\"text-3xl font-bold mb-2 text-center\">Our Sponsors</h2>\n            <p className=\"text-center text-neutral-600 mb-10\">Proud supporters of Northern Nepalese United - NNUFC</p>\n\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8 items-center\">\n              {sponsors && sponsors.length > 0 ? (\n                sponsors.map((sponsor: Sponsor) => (\n                  <div key={sponsor._id} className=\"flex justify-center\">\n                    {sponsor.logo ? (\n                      <div className=\"h-24 w-48 relative\">\n                        <Image\n                          src={urlForImage(sponsor.logo).url()}\n                          alt={sponsor.name}\n                          fill\n                          className=\"object-contain\"\n                        />\n                      </div>\n                    ) : (\n                      <div className=\"h-24 w-48 bg-neutral-200 rounded flex items-center justify-center p-2\">\n                        <p className=\"text-center font-semibold\">{sponsor.name}</p>\n                      </div>\n                    )}\n                  </div>\n                ))\n              ) : (\n                <div className=\"col-span-4 text-center py-8\">\n                  <p className=\"text-neutral-600\">Sponsorship opportunities available.</p>\n                  <p className=\"mt-2\">Contact us to become a sponsor!</p>\n                </div>\n              )}\n            </div>\n          </div>\n        </section>\n        \n        {/* Team Spirit Section with Particle Background - Contained within this section only */}\n        <section className=\"py-16 bg-gray-900\">\n          <ParticleSection className=\"py-10\">\n            <div className=\"container\">\n              <div className=\"max-w-3xl mx-auto text-center text-white\">\n                <h2 className=\"text-3xl font-bold mb-6\">Join Our Community</h2>\n                <p className=\"text-lg mb-8\">\n                  Northern Nepalese United FC is more than just a football club—we&apos;re a community that celebrates our heritage while embracing the Australian sporting culture.\n                </p>\n                <Link\n                  href=\"/contact\"\n                  className=\"bg-white text-primary-800 px-8 py-3 rounded-lg font-semibold hover:bg-opacity-90 transition-colors\"\n                >\n                  Get Involved\n                </Link>\n              </div>\n            </div>\n          </ParticleSection>\n        </section>\n      </main>\n      <Footer />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;;;AAwCO,MAAM,aAAa;AAE1B,eAAe;IACb,OAAO,MAAM,6GAAA,CAAA,SAAM,CAAC,KAAK,CAAC,8GAAA,CAAA,kBAAe;AAC3C;AAEA,eAAe;IACb,OAAO,MAAM,6GAAA,CAAA,SAAM,CAAC,KAAK,CAAC,8GAAA,CAAA,sBAAmB;AAC/C;AAEA,eAAe;IACb,OAAO,MAAM,6GAAA,CAAA,SAAM,CAAC,KAAK,CAAC,8GAAA,CAAA,gBAAa;AACzC;AAEe,eAAe;IAC5B,yBAAyB;IACzB,MAAM,CAAC,YAAY,gBAAgB,SAAS,GAAG,MAAM,QAAQ,GAAG,CAAC;QAC/D;QACA;QACA;KACD;IACD,qBACE;;0BACE,8OAAC,qHAAA,CAAA,UAAM;;;;;0BACP,8OAAC;;kCAEC,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,8OAAC;oCAAE,WAAU;8CAAiC;;;;;;8CAG9C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;kCAQP,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAEpD,8OAAC;oCAAI,WAAU;8CACZ,cAAc,WAAW,MAAM,GAAG,IACjC,WAAW,GAAG,CAAC,CAAC,wBACd,8OAAC;4CAAsB,WAAU;;gDAC9B,QAAQ,SAAS,iBAChB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wDACJ,KAAK,CAAA,GAAA,6GAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,SAAS,EAAE,GAAG;wDACvC,KAAK,QAAQ,KAAK;wDAClB,IAAI;wDACJ,WAAU;;;;;;;;;;yEAId,8OAAC;oDAAI,WAAU;;;;;;8DAEjB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAA0B,QAAQ,KAAK;;;;;;sEACrD,8OAAC;4DAAE,WAAU;sEACV,IAAI,KAAK,QAAQ,WAAW,EAAE,kBAAkB;;;;;;wDAElD,QAAQ,OAAO,kBACd,8OAAC;4DAAE,WAAU;sEAAyB,QAAQ,OAAO;;;;;;sEAEvD,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAM,CAAC,MAAM,EAAE,QAAQ,IAAI,CAAC,OAAO,EAAE;4DACrC,WAAU;sEACX;;;;;;;;;;;;;2CAxBK,QAAQ,GAAG;;;;kEA+BvB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAmB;;;;;;0DAChC,8OAAC;gDAAE,WAAU;0DAAO;;;;;;;;;;;;;;;;;8CAK1B,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;kCAQP,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAEpD,8OAAC;oCAAI,WAAU;8CACZ,kBAAkB,eAAe,MAAM,GAAG,IACzC,eAAe,GAAG,CAAC,CAAC,sBAClB,8OAAC;4CAAoB,WAAU;;8DAC7B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAA0B,MAAM,KAAK;;;;;;8EACnD,8OAAC;oEAAE,WAAU;;wEACV,IAAI,KAAK,MAAM,IAAI,EAAE,kBAAkB;wEAAG;wEAAE,MAAM,SAAS,IAAI,CAAC,GAAG,EAAE,MAAM,SAAS,EAAE;;;;;;;gEAExF,MAAM,QAAQ,kBAAI,8OAAC;oEAAE,WAAU;8EAAyB,MAAM,QAAQ;;;;;;;;;;;;wDAExE,MAAM,SAAS,kBACd,8OAAC;4DAAK,WAAU;sEACb,MAAM,SAAS;;;;;;;;;;;;gDAKrB,MAAM,QAAQ,kBACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;kEACX,cAAA,8OAAC;4DAAK,WAAU;;gEACb,MAAM,UAAU,KAAK,SAAS,kBAAkB;gEAAiB,MAAM,QAAQ;;;;;;;;;;;;;;;;;8DAMxF,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,CAAC,QAAQ,EAAE,MAAM,IAAI,CAAC,OAAO,EAAE;oDACrC,WAAU;8DACX;;;;;;;2CA7BO,MAAM,GAAG;;;;kEAmCrB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAmB;;;;;;0DAChC,8OAAC;gDAAE,WAAU;0DAAO;;;;;;;;;;;;;;;;;8CAK1B,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;kCAQP,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;8CAElD,8OAAC;oCAAI,WAAU;8CACZ,YAAY,SAAS,MAAM,GAAG,IAC7B,SAAS,GAAG,CAAC,CAAC,wBACZ,8OAAC;4CAAsB,WAAU;sDAC9B,QAAQ,IAAI,iBACX,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAK,CAAA,GAAA,6GAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,IAAI,EAAE,GAAG;oDAClC,KAAK,QAAQ,IAAI;oDACjB,IAAI;oDACJ,WAAU;;;;;;;;;;qEAId,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;8DAA6B,QAAQ,IAAI;;;;;;;;;;;2CAZlD,QAAQ,GAAG;;;;kEAkBvB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAmB;;;;;;0DAChC,8OAAC;gDAAE,WAAU;0DAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ9B,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC,8HAAA,CAAA,UAAe;4BAAC,WAAU;sCACzB,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA0B;;;;;;sDACxC,8OAAC;4CAAE,WAAU;sDAAe;;;;;;sDAG5B,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQX,8OAAC,qHAAA,CAAA,UAAM;;;;;;;AAGb"}}, {"offset": {"line": 1323, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1334, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 1334, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}