{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/Header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/Header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyR,GACtT,uDACA"}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/Header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/Header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAqQ,GAClS,mCACA"}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/Footer.tsx"], "sourcesContent": ["import Link from 'next/link'\n\nexport default function Footer() {\n  const currentYear = new Date().getFullYear()\n\n  return (\n    <footer className=\"bg-gradient-to-b from-neutral-800 to-neutral-900 text-white\">\n      <div className=\"container py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          <div className=\"md:col-span-2\">\n            <h3 className=\"text-xl font-bold mb-4 text-white\">Northern Nepalese United FC</h3>\n            <p className=\"mb-4 text-neutral-300\">\n              Brisbane-based Nepalese football club est. 2023, bringing together the community through the beautiful game.\n            </p>\n            <div className=\"flex space-x-4 mt-6\">\n              <a\n                href=\"https://facebook.com\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                aria-label=\"Facebook\"\n                className=\"bg-neutral-700 hover:bg-neutral-600 text-white p-2 rounded-full transition-colors\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"currentColor\"\n                >\n                  <path d=\"M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z\" />\n                </svg>\n              </a>\n              <a\n                href=\"https://twitter.com\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                aria-label=\"Twitter\"\n                className=\"bg-neutral-700 hover:bg-neutral-600 text-white p-2 rounded-full transition-colors\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"currentColor\"\n                >\n                  <path d=\"M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z\" />\n                </svg>\n              </a>\n              <a\n                href=\"https://instagram.com\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                aria-label=\"Instagram\"\n                className=\"bg-neutral-700 hover:bg-neutral-600 text-white p-2 rounded-full transition-colors\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"currentColor\"\n                >\n                  <path d=\"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z\" />\n                </svg>\n              </a>\n            </div>\n          </div>\n\n          <div>\n            <h3 className=\"text-xl font-bold mb-4 text-white\">Club Links</h3>\n            <ul className=\"space-y-2 text-neutral-300\">\n              <li>\n                <Link href=\"/about\" className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\">\n                  About Us\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/team\" className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\">\n                  Our Team\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/news\" className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\">\n                  News\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/events\" className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\">\n                  Events & Fixtures\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/gallery\" className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\">\n                  Gallery\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          <div>\n            <h3 className=\"text-xl font-bold mb-4 text-white\">Contact</h3>\n            <address className=\"not-italic text-neutral-300\">\n              <p className=\"mb-2 flex items-start\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2 mt-0.5 text-neutral-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                </svg>\n                John Oxley Reserve<br />Murrumba Downs<br />Brisbane, QLD\n              </p>\n              <p className=\"mb-2 flex items-start\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2 mt-0.5 text-neutral-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                </svg>\n                <a href=\"tel:+61424770570\" className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\">\n                  0424 770 570\n                </a>\n              </p>\n              <p className=\"flex items-start\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2 mt-0.5 text-neutral-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                </svg>\n                <a\n                  href=\"mailto:<EMAIL>\"\n                  className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\"\n                >\n                  <EMAIL>\n                </a>\n              </p>\n            </address>\n          </div>\n        </div>\n\n        <div className=\"border-t border-neutral-700 mt-10 pt-8 text-center text-neutral-400\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <p>&copy; {currentYear} Northern Nepalese United - NNUFC. All rights reserved.</p>\n            <ul className=\"flex space-x-4 mt-4 md:mt-0\">\n              <li>\n                <Link href=\"/sponsors\" className=\"text-neutral-400 hover:text-white hover:underline transition-colors no-underline\">\n                  Sponsors\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contact\" className=\"text-neutral-400 hover:text-white hover:underline transition-colors no-underline\">\n                  Contact\n                </Link>\n              </li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;8CAGrC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,cAAW;4CACX,WAAU;sDAEV,cAAA,8OAAC;gDACC,OAAM;gDACN,OAAM;gDACN,QAAO;gDACP,SAAQ;gDACR,MAAK;0DAEL,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,cAAW;4CACX,WAAU;sDAEV,cAAA,8OAAC;gDACC,OAAM;gDACN,OAAM;gDACN,QAAO;gDACP,SAAQ;gDACR,MAAK;0DAEL,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,cAAW;4CACX,WAAU;sDAEV,cAAA,8OAAC;gDACC,OAAM;gDACN,OAAM;gDACN,QAAO;gDACP,SAAQ;gDACR,MAAK;0DAEL,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMhB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAmF;;;;;;;;;;;sDAInH,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAmF;;;;;;;;;;;sDAIlH,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAmF;;;;;;;;;;;sDAIlH,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAU,WAAU;0DAAmF;;;;;;;;;;;sDAIpH,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmF;;;;;;;;;;;;;;;;;;;;;;;sCAOzH,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,8OAAC;oCAAQ,WAAU;;sDACjB,8OAAC;4CAAE,WAAU;;8DACX,8OAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAAuC,MAAK;oDAAO,SAAQ;oDAAY,QAAO;;sEAC9H,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;sEACrE,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;gDACjE;8DACY,8OAAC;;;;;gDAAK;8DAAc,8OAAC;;;;;gDAAK;;;;;;;sDAE9C,8OAAC;4CAAE,WAAU;;8DACX,8OAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAAuC,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAC9H,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;8DAEvE,8OAAC;oDAAE,MAAK;oDAAmB,WAAU;8DAAmF;;;;;;;;;;;;sDAI1H,8OAAC;4CAAE,WAAU;;8DACX,8OAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAAuC,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAC9H,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;8DAEvE,8OAAC;oDACC,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQT,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;oCAAE;oCAAQ;oCAAY;;;;;;;0CACvB,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAY,WAAU;sDAAmF;;;;;;;;;;;kDAItH,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAAmF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUnI"}}, {"offset": {"line": 525, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 539, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/lib/sanity.ts"], "sourcesContent": ["import { createClient } from 'next-sanity'\nimport imageUrlBuilder from '@sanity/image-url'\n\nconst projectId = process.env.NEXT_PUBLIC_SANITY_PROJECT_ID!\nconst dataset = process.env.NEXT_PUBLIC_SANITY_DATASET!\nconst apiVersion = process.env.NEXT_PUBLIC_SANITY_API_VERSION || '2024-05-23'\n\nexport const client = createClient({\n  projectId,\n  dataset,\n  apiVersion,\n  useCdn: typeof document !== 'undefined',\n})\n\n// Helper function for generating image URLs with the Sanity Image Pipeline\nconst builder = imageUrlBuilder({\n  projectId,\n  dataset,\n})\n\nexport function urlForImage(source: any) {\n  return builder.image(source)\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM;AACN,MAAM;AACN,MAAM,aAAa,kDAA8C;AAE1D,MAAM,SAAS,CAAA,GAAA,8KAAA,CAAA,eAAY,AAAD,EAAE;IACjC;IACA;IACA;IACA,QAAQ,OAAO,aAAa;AAC9B;AAEA,2EAA2E;AAC3E,MAAM,UAAU,CAAA,GAAA,gKAAA,CAAA,UAAe,AAAD,EAAE;IAC9B;IACA;AACF;AAEO,SAAS,YAAY,MAAW;IACrC,OAAO,QAAQ,KAAK,CAAC;AACvB"}}, {"offset": {"line": 564, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 570, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/lib/queries.ts"], "sourcesContent": ["// Latest news articles\nexport const latestNewsQuery = `\n  *[_type == \"newsArticle\"] | order(publishedAt desc)[0...3] {\n    _id,\n    title,\n    slug,\n    publishedAt,\n    mainImage,\n    summary\n  }\n`;\n\n// Single news article by slug\nexport const newsArticleBySlugQuery = `\n  *[_type == \"newsArticle\" && slug.current == $slug][0] {\n    _id,\n    title,\n    slug,\n    publishedAt,\n    mainImage,\n    summary,\n    body\n  }\n`;\n\n// All news articles\nexport const allNewsArticlesQuery = `\n  *[_type == \"newsArticle\"] | order(publishedAt desc) {\n    _id,\n    title,\n    slug,\n    publishedAt,\n    mainImage,\n    summary\n  }\n`;\n\n// Upcoming events\nexport const upcomingEventsQuery = `\n  *[_type == \"event\" && date > now()] | order(date asc)[0...5] {\n    _id,\n    title,\n    slug,\n    date,\n    location,\n    eventType,\n    opponent,\n    homeOrAway\n  }\n`;\n\n// All events\nexport const allEventsQuery = `\n  *[_type == \"event\"] | order(date desc) {\n    _id,\n    title,\n    slug,\n    date,\n    location,\n    eventType,\n    opponent,\n    homeOrAway,\n    result\n  }\n`;\n\n// Single event by slug\nexport const eventBySlugQuery = `\n  *[_type == \"event\" && slug.current == $slug][0] {\n    _id,\n    title,\n    slug,\n    date,\n    location,\n    eventType,\n    opponent,\n    homeOrAway,\n    result,\n    description\n  }\n`;\n\n// All sponsors ordered by display order\nexport const sponsorsQuery = `\n  *[_type == \"sponsor\"] | order(displayOrder asc) {\n    _id,\n    name,\n    logo,\n    websiteUrl,\n    sponsorshipLevel\n  }\n`;\n\n// Gallery images\nexport const galleryImagesQuery = `\n  *[_type == \"galleryImage\"] | order(dateTaken desc) {\n    _id,\n    title,\n    imageFile,\n    dateTaken\n  }\n`;\n\n// Players\nexport const playersQuery = `\n  *[_type == \"player\"] | order(role desc, jerseyNumber asc) {\n    _id,\n    name,\n    position,\n    role,\n    jerseyNumber,\n    image,\n    stats,\n    bio\n  }\n`;\n\n// Staff\nexport const staffQuery = `\n  *[_type == \"staff\"] | order(displayOrder asc) {\n    _id,\n    name,\n    role,\n    image,\n    bio,\n    contactInfo\n  }\n`;\n"], "names": [], "mappings": "AAAA,uBAAuB;;;;;;;;;;;;;AAChB,MAAM,kBAAkB,CAAC;;;;;;;;;AAShC,CAAC;AAGM,MAAM,yBAAyB,CAAC;;;;;;;;;;AAUvC,CAAC;AAGM,MAAM,uBAAuB,CAAC;;;;;;;;;AASrC,CAAC;AAGM,MAAM,sBAAsB,CAAC;;;;;;;;;;;AAWpC,CAAC;AAGM,MAAM,iBAAiB,CAAC;;;;;;;;;;;;AAY/B,CAAC;AAGM,MAAM,mBAAmB,CAAC;;;;;;;;;;;;;AAajC,CAAC;AAGM,MAAM,gBAAgB,CAAC;;;;;;;;AAQ9B,CAAC;AAGM,MAAM,qBAAqB,CAAC;;;;;;;AAOnC,CAAC;AAGM,MAAM,eAAe,CAAC;;;;;;;;;;;AAW7B,CAAC;AAGM,MAAM,aAAa,CAAC;;;;;;;;;AAS3B,CAAC"}}, {"offset": {"line": 692, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 698, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/src/app/page.tsx"], "sourcesContent": ["import Image from \"next/image\";\nimport Link from \"next/link\";\nimport Header from \"@/components/Header\";\nimport Footer from \"@/components/Footer\";\nimport { client } from \"@/lib/sanity\";\nimport { latestNewsQuery, upcomingEventsQuery, sponsorsQuery } from \"@/lib/queries\";\nimport { urlForImage } from \"@/lib/sanity\";\n\n// Define types for our Sanity data\ninterface SanityImage {\n  asset: {\n    _ref: string;\n  };\n  // other possible image fields\n}\n\ninterface NewsArticle {\n  _id: string;\n  title: string;\n  publishedAt: string;\n  summary?: string;\n  mainImage?: SanityImage;\n  slug: {\n    current: string;\n  };\n}\n\ninterface Event {\n  _id: string;\n  title: string;\n  date: string;\n  eventTime?: string;\n  location?: string;\n  eventType?: string;\n  opponent?: string;\n  homeOrAway?: 'home' | 'away';\n  slug: {\n    current: string;\n  };\n}\n\ninterface Sponsor {\n  _id: string;\n  name: string;\n  logo?: SanityImage;\n}\n\n// This makes the page dynamic and will revalidate every 60 seconds\nexport const revalidate = 60;\n\nasync function getLatestNews() {\n  return await client.fetch(latestNewsQuery) as NewsArticle[];\n}\n\nasync function getUpcomingEvents() {\n  return await client.fetch(upcomingEventsQuery) as Event[];\n}\n\nasync function getSponsors() {\n  return await client.fetch(sponsorsQuery) as Sponsor[];\n}\n\nexport default async function Home() {\n  // Fetch data from Sanity\n  const [latestNews, upcomingEvents, sponsors] = await Promise.all([\n    getLatestNews(),\n    getUpcomingEvents(),\n    getSponsors(),\n  ]);\n  return (\n    <>\n      <Header />\n      <main className=\"overflow-hidden\">\n        {/* Hero Section - Enhanced with improved background blur and text contrast */}\n        <section className=\"relative min-h-[90vh] flex items-center overflow-hidden\">\n          {/* Background Image with Subtle Blur Effect */}\n          <div className=\"absolute inset-0 z-0\">\n            <Image\n              src=\"/teamphoto.jpg\"\n              alt=\"Football team\"\n              fill\n              priority\n              className=\"object-cover brightness-75 blur-[1px] scale-105\"\n            />\n          </div>\n\n          {/* Subtle Overlay for Text Contrast - Only where needed */}\n          <div className=\"absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent z-[1]\"></div>\n          <div className=\"absolute inset-0 bg-gradient-to-r from-black/30 via-transparent to-transparent z-[2]\"></div>\n\n          <div className=\"container relative z-10 text-white\">\n            <div className=\"max-w-4xl mx-auto md:mx-0\">\n              <div className=\"animate-fade-in-up\">\n                {/* Enhanced Text Container with Subtle Backdrop */}\n                <div className=\"hero-backdrop bg-black/20 border border-white/30 p-8 md:p-12 rounded-2xl shadow-2xl max-w-4xl\">\n                  <h1 className=\"text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight hero-text-shadow\">\n                    <span className=\"text-white\">Northern Nepalese United</span>{' '}\n                    <span className=\"text-yellow-400\">FC</span>\n                  </h1>\n                  <p className=\"text-lg sm:text-xl md:text-2xl mb-8 text-gray-100 hero-text-shadow leading-relaxed max-w-3xl\">\n                    Bringing together passion, community and excellence in football since 2023\n                  </p>\n\n                  {/* Enhanced Buttons with Better Styling */}\n                  <div className=\"flex flex-col sm:flex-row gap-4 sm:gap-6\">\n                    <Link\n                      href=\"/about\"\n                      className=\"group bg-white text-primary-800 px-8 py-4 rounded-xl font-semibold hover:bg-yellow-400 hover:text-primary-900 transition-all duration-300 hero-button-shadow transform hover:-translate-y-1 text-center\"\n                    >\n                      <span className=\"flex items-center justify-center gap-2\">\n                        Our Story\n                        <svg className=\"w-4 h-4 transition-transform group-hover:translate-x-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 5l7 7-7 7\"></path>\n                        </svg>\n                      </span>\n                    </Link>\n                    <Link\n                      href=\"/contact\"\n                      className=\"group bg-transparent border-2 border-white/80 text-white px-8 py-4 rounded-xl font-semibold hover:bg-white hover:text-primary-800 hover:border-white transition-all duration-300 transform hover:-translate-y-1 backdrop-blur-sm hero-button-shadow text-center\"\n                    >\n                      <span className=\"flex items-center justify-center gap-2\">\n                        Join the Team\n                        <svg className=\"w-4 h-4 transition-transform group-hover:translate-x-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M17 8l4 4m0 0l-4 4m4-4H3\"></path>\n                        </svg>\n                      </span>\n                    </Link>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Enhanced Scroll Indicator */}\n          <div className=\"absolute bottom-8 left-0 right-0 text-center z-10\">\n            <div className=\"animate-bounce inline-block\">\n              <div className=\"bg-white/20 backdrop-blur-sm rounded-full p-3 border border-white/30\">\n                <svg className=\"w-6 h-6 text-white drop-shadow-lg\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M19 14l-7 7m0 0l-7-7m7 7V3\"></path>\n                </svg>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Match Highlight Section - New dynamic section */}\n        <section className=\"py-10 bg-primary-900 text-white overflow-hidden\">\n          <div className=\"container\">\n            <div className=\"flex flex-col md:flex-row items-center justify-between\">\n              <div className=\"text-center md:text-left mb-6 md:mb-0\">\n                <p className=\"text-yellow-400 text-lg font-semibold\">Next Match</p>\n                {upcomingEvents && upcomingEvents.length > 0 ? (\n                  <div className=\"mt-2\">\n                    <h3 className=\"text-2xl md:text-4xl font-bold\">NNUFC vs {upcomingEvents[0].opponent || 'TBA'}</h3>\n                    <p className=\"text-lg opacity-90 mt-1\">\n                      {new Date(upcomingEvents[0].date).toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric' })}\n                      {upcomingEvents[0].eventTime && ` - ${upcomingEvents[0].eventTime}`}\n                    </p>\n                  </div>\n                ) : (\n                  <p className=\"text-xl mt-2\">No upcoming matches</p>\n                )}\n              </div>\n              <Link\n                href={upcomingEvents && upcomingEvents.length > 0 ? `/events/${upcomingEvents[0].slug.current}` : \"/events\"}\n                className=\"bg-yellow-400 text-primary-900 px-6 py-3 rounded font-semibold hover:bg-yellow-300 transition-colors shadow-md\"\n              >\n                Match Details\n              </Link>\n            </div>\n          </div>\n        </section>\n\n        {/* Latest News Section - Enhanced with better cards */}\n        <section className=\"py-20 bg-gradient-to-r from-primary-900/5 to-primary-800/5\">\n          <div className=\"container\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-4xl font-bold mb-4\">Latest News</h2>\n              <div className=\"w-24 h-1 bg-primary-600 mx-auto\"></div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n              {latestNews && latestNews.length > 0 ? (\n                latestNews.map((article: NewsArticle) => (\n                  <div key={article._id} className=\"group bg-white rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2\">\n                    <div className=\"relative h-56 overflow-hidden\">\n                      {article.mainImage ? (\n                        <Image\n                          src={urlForImage(article.mainImage).url()}\n                          alt={article.title}\n                          fill\n                          className=\"object-cover transition-transform duration-500 group-hover:scale-110\"\n                        />\n                      ) : (\n                        <div className=\"h-full bg-gray-200 flex items-center justify-center\">\n                          <svg className=\"w-12 h-12 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"></path>\n                          </svg>\n                        </div>\n                      )}\n                      <div className=\"absolute top-3 right-3 bg-primary-600 text-white text-xs px-3 py-1 rounded-full\">\n                        News\n                      </div>\n                    </div>\n                    <div className=\"p-6\">\n                      <p className=\"text-gray-500 text-sm mb-2\">\n                        {new Date(article.publishedAt).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })}\n                      </p>\n                      <h3 className=\"text-xl font-bold mb-3 line-clamp-2\">{article.title}</h3>\n                      {article.summary && (\n                        <p className=\"text-gray-700 mb-4 line-clamp-3\">{article.summary}</p>\n                      )}\n                      <Link\n                        href={`/news/${article.slug.current}`}\n                        className=\"inline-flex items-center text-primary-600 font-semibold hover:text-primary-800 transition-colors\"\n                      >\n                        Read Article\n                        <svg className=\"w-4 h-4 ml-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M14 5l7 7m0 0l-7 7m7-7H3\"></path>\n                        </svg>\n                      </Link>\n                    </div>\n                  </div>\n                ))\n              ) : (\n                <div className=\"col-span-3 text-center py-8 bg-white rounded-lg shadow-sm\">\n                  <svg className=\"w-16 h-16 text-gray-300 mx-auto mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z\"></path>\n                  </svg>\n                  <p className=\"text-gray-600 text-lg\">No news articles available yet.</p>\n                  <p className=\"mt-2 text-gray-500\">Check back soon for updates!</p>\n                </div>\n              )}\n            </div>\n\n            <div className=\"text-center mt-12\">\n              <Link\n                href=\"/news\"\n                className=\"bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-8 rounded-lg inline-flex items-center transition-colors duration-300\"\n              >\n                All News\n                <svg className=\"w-4 h-4 ml-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M14 5l7 7m0 0l-7 7m7-7H3\"></path>\n                </svg>\n              </Link>\n            </div>\n          </div>\n        </section>\n\n        {/* Upcoming Events Section - Enhanced with timeline style */}\n        <section className=\"py-20 bg-white\">\n          <div className=\"container\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-4xl font-bold mb-4\">Upcoming Events</h2>\n              <div className=\"w-24 h-1 bg-primary-600 mx-auto\"></div>\n            </div>\n\n            <div className=\"max-w-4xl mx-auto\">\n              {upcomingEvents && upcomingEvents.length > 0 ? (\n                <div className=\"space-y-8\">\n                  {upcomingEvents.map((event: Event, index) => (\n                    <div key={event._id} className=\"group relative\">\n                      {/* Timeline connector */}\n                      {index < upcomingEvents.length - 1 && (\n                        <div className=\"absolute top-14 bottom-0 left-[22px] w-1 bg-gray-200 z-0\"></div>\n                      )}\n                      <div className=\"flex gap-6 relative z-10\">\n                        <div className=\"relative min-w-[46px]\">\n                          <div className=\"w-12 h-12 rounded-full bg-primary-600 text-white flex items-center justify-center group-hover:bg-primary-800 transition-colors\">\n                            <span className=\"font-bold\">{new Date(event.date).getDate()}</span>\n                          </div>\n                        </div>\n                        <div className=\"bg-gray-50 rounded-xl p-6 shadow-sm w-full hover:shadow-md transition-shadow\">\n                          <div className=\"flex flex-col md:flex-row justify-between md:items-center mb-3\">\n                            <h3 className=\"text-xl font-bold\">{event.title}</h3>\n                            {event.eventType && (\n                              <span className=\"bg-primary-100 text-primary-800 text-xs px-3 py-1 rounded-full uppercase font-semibold md:ml-2 mt-2 md:mt-0 inline-block md:inline\">\n                                {event.eventType}\n                              </span>\n                            )}\n                          </div>\n\n                          <div className=\"flex flex-col md:flex-row md:items-center text-gray-600 gap-1 md:gap-4 mb-3\">\n                            <p className=\"flex items-center\">\n                              <svg className=\"w-4 h-4 mr-2 text-primary-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"></path>\n                              </svg>\n                              {new Date(event.date).toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric' })}\n                            </p>\n                            {event.eventTime && (\n                              <p className=\"flex items-center\">\n                                <svg className=\"w-4 h-4 mr-2 text-primary-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n                                </svg>\n                                {event.eventTime}\n                              </p>\n                            )}\n                            {event.location && (\n                              <p className=\"flex items-center\">\n                                <svg className=\"w-4 h-4 mr-2 text-primary-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"></path>\n                                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"></path>\n                                </svg>\n                                {event.location}\n                              </p>\n                            )}\n                          </div>\n\n                          {event.opponent && (\n                            <div className=\"bg-gray-100 p-3 rounded-lg mb-4\">\n                              <p className=\"text-gray-800 font-medium flex items-center\">\n                                <svg className=\"w-5 h-5 mr-2 text-primary-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"></path>\n                                </svg>\n                                {event.homeOrAway === 'home' ? 'Home game vs ' : 'Away game vs '}{event.opponent}\n                              </p>\n                            </div>\n                          )}\n\n                          <Link\n                            href={`/events/${event.slug.current}`}\n                            className=\"inline-flex items-center text-primary-600 font-semibold hover:text-primary-800 transition-colors\"\n                          >\n                            View Details\n                            <svg className=\"w-4 h-4 ml-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M14 5l7 7m0 0l-7 7m7-7H3\"></path>\n                            </svg>\n                          </Link>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              ) : (\n                <div className=\"text-center py-12 bg-gray-50 rounded-xl\">\n                  <svg className=\"w-16 h-16 text-gray-300 mx-auto mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"></path>\n                  </svg>\n                  <p className=\"text-gray-600 text-lg\">No upcoming events scheduled yet.</p>\n                  <p className=\"mt-2 text-gray-500\">Check back soon for updates!</p>\n                </div>\n              )}\n            </div>\n\n            <div className=\"text-center mt-12\">\n              <Link\n                href=\"/events\"\n                className=\"bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-8 rounded-lg inline-flex items-center transition-colors duration-300\"\n              >\n                Calendar\n                <svg className=\"w-4 h-4 ml-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M14 5l7 7m0 0l-7 7m7-7H3\"></path>\n                </svg>\n              </Link>\n            </div>\n          </div>\n        </section>\n\n        {/* Team Highlights Section - New section */}\n        <section className=\"relative py-20 bg-primary-900 text-white\">\n          <div className=\"absolute inset-0 bg-pattern opacity-10\"></div>\n          <div className=\"container relative z-10\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-4xl font-bold mb-4\">Team Highlights</h2>\n              <div className=\"w-24 h-1 bg-yellow-400 mx-auto\"></div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n              <div className=\"bg-primary-800/60 backdrop-blur-sm p-8 rounded-xl shadow-lg transform hover:-translate-y-2 transition-all duration-300\">\n                <div className=\"bg-yellow-400 w-16 h-16 rounded-full flex items-center justify-center mb-6 mx-auto\">\n                  <svg className=\"w-8 h-8 text-primary-900\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\n                  </svg>\n                </div>\n                <h3 className=\"text-xl font-bold mb-4 text-center\">Community</h3>\n                <p className=\"text-gray-300 text-center\">\n                  Bringing together the Nepalese community through the beautiful game of football.\n                </p>\n              </div>\n\n              <div className=\"bg-primary-800/60 backdrop-blur-sm p-8 rounded-xl shadow-lg transform hover:-translate-y-2 transition-all duration-300\">\n                <div className=\"bg-yellow-400 w-16 h-16 rounded-full flex items-center justify-center mb-6 mx-auto\">\n                  <svg className=\"w-8 h-8 text-primary-900\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M13 10V3L4 14h7v7l9-11h-7z\"></path>\n                  </svg>\n                </div>\n                <h3 className=\"text-xl font-bold mb-4 text-center\">Passion</h3>\n                <p className=\"text-gray-300 text-center\">\n                  Driven by our love for football and determination to excel on the field.\n                </p>\n              </div>\n\n              <div className=\"bg-primary-800/60 backdrop-blur-sm p-8 rounded-xl shadow-lg transform hover:-translate-y-2 transition-all duration-300\">\n                <div className=\"bg-yellow-400 w-16 h-16 rounded-full flex items-center justify-center mb-6 mx-auto\">\n                  <svg className=\"w-8 h-8 text-primary-900\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"></path>\n                  </svg>\n                </div>\n                <h3 className=\"text-xl font-bold mb-4 text-center\">Teamwork</h3>\n                <p className=\"text-gray-300 text-center\">\n                  Uniting diverse talents and skills to achieve greatness together as one team.\n                </p>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Sponsors Section - Enhanced with better layout */}\n        <section className=\"py-20 bg-gradient-to-r from-primary-900/5 to-primary-800/5\">\n          <div className=\"container\">\n            <div className=\"text-center mb-12\">\n              <p className=\"text-primary-600 font-semibold mb-2\">OUR PARTNERS</p>\n              <h2 className=\"text-4xl font-bold mb-4\">Official Sponsors</h2>\n              <div className=\"w-24 h-1 bg-primary-600 mx-auto mb-6\"></div>\n              <p className=\"text-gray-600 max-w-2xl mx-auto\">\n                We&apos;re proud to be supported by these amazing organizations that help make our club&apos;s vision a reality\n              </p>\n            </div>\n\n            <div className=\"bg-white rounded-2xl shadow-sm p-8 md:p-12\">\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8 md:gap-12 items-center\">\n                {sponsors && sponsors.length > 0 ? (\n                  sponsors.map((sponsor: Sponsor) => (\n                    <div key={sponsor._id} className=\"flex justify-center group\">\n                      {sponsor.logo ? (\n                        <div className=\"h-24 w-48 relative grayscale hover:grayscale-0 transition-all duration-500 transform hover:scale-110\">\n                          <Image\n                            src={urlForImage(sponsor.logo).url()}\n                            alt={sponsor.name}\n                            fill\n                            className=\"object-contain\"\n                          />\n                        </div>\n                      ) : (\n                        <div className=\"h-24 w-48 bg-gray-100 rounded-lg flex items-center justify-center p-2 group-hover:bg-gray-200 transition-colors\">\n                          <p className=\"text-center font-semibold text-gray-700\">{sponsor.name}</p>\n                        </div>\n                      )}\n                    </div>\n                  ))\n                ) : (\n                  <div className=\"col-span-4 text-center py-8\">\n                    <svg className=\"w-16 h-16 text-gray-300 mx-auto mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"></path>\n                    </svg>\n                    <p className=\"text-gray-600 text-lg\">No sponsors available yet.</p>\n                    <p className=\"mt-2 text-gray-500\">Check back soon for updates!</p>\n                  </div>\n                )}\n              </div>\n            </div>\n\n            <div className=\"text-center mt-12\">\n              <Link\n                href=\"/sponsors\"\n                className=\"inline-flex items-center gap-2 font-semibold text-primary-600 hover:text-primary-800 transition-colors\"\n              >\n                Become a Sponsor\n                <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M14 5l7 7m0 0l-7 7m7-7H3\"></path>\n                </svg>\n              </Link>\n            </div>\n          </div>\n        </section>\n\n        {/* Call to Action Section - New section */}\n        <section className=\"bg-gradient-to-r from-primary-800 to-primary-900 py-10\">\n          <div className=\"container\">\n            <div className=\"flex flex-col md:flex-row items-center justify-between max-w-5xl mx-auto\">\n              <div className=\"text-white mb-6 md:mb-0 text-center md:text-left\">\n                <h2 className=\"text-2xl font-bold mb-2\">Ready to Join Our Team?</h2>\n                <p className=\"text-sm text-gray-300 max-w-xl\">\n                  Whether you&apos;re a player, supporter, or potential sponsor, we&apos;d love to welcome you to the NNUFC family.\n                </p>\n              </div>\n              <div className=\"flex gap-3\">\n                <Link\n                  href=\"/contact\"\n                  className=\"bg-white text-primary-800 px-5 py-2 rounded-lg font-semibold hover:bg-yellow-400 transition-colors shadow-md text-sm\"\n                >\n                  Contact Us\n                </Link>\n                <Link\n                  href=\"/membership\"\n                  className=\"bg-transparent border-2 border-white text-white px-5 py-2 rounded-lg font-semibold hover:bg-white hover:text-primary-800 transition-colors text-sm\"\n                >\n                  Membership\n                </Link>\n              </div>\n            </div>\n          </div>\n        </section>\n      </main>\n      <Footer />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AA2CO,MAAM,aAAa;AAE1B,eAAe;IACb,OAAO,MAAM,6GAAA,CAAA,SAAM,CAAC,KAAK,CAAC,8GAAA,CAAA,kBAAe;AAC3C;AAEA,eAAe;IACb,OAAO,MAAM,6GAAA,CAAA,SAAM,CAAC,KAAK,CAAC,8GAAA,CAAA,sBAAmB;AAC/C;AAEA,eAAe;IACb,OAAO,MAAM,6GAAA,CAAA,SAAM,CAAC,KAAK,CAAC,8GAAA,CAAA,gBAAa;AACzC;AAEe,eAAe;IAC5B,yBAAyB;IACzB,MAAM,CAAC,YAAY,gBAAgB,SAAS,GAAG,MAAM,QAAQ,GAAG,CAAC;QAC/D;QACA;QACA;KACD;IACD,qBACE;;0BACE,8OAAC,qHAAA,CAAA,UAAM;;;;;0BACP,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAQ,WAAU;;0CAEjB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,IAAI;oCACJ,QAAQ;oCACR,WAAU;;;;;;;;;;;0CAKd,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDAEb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAK,WAAU;sEAAa;;;;;;wDAAgC;sEAC7D,8OAAC;4DAAK,WAAU;sEAAkB;;;;;;;;;;;;8DAEpC,8OAAC;oDAAE,WAAU;8DAA+F;;;;;;8DAK5G,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;sEAEV,cAAA,8OAAC;gEAAK,WAAU;;oEAAyC;kFAEvD,8OAAC;wEAAI,WAAU;wEAAyD,MAAK;wEAAO,QAAO;wEAAe,SAAQ;kFAChH,cAAA,8OAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAY;4EAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;sEAI3E,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;sEAEV,cAAA,8OAAC;gEAAK,WAAU;;oEAAyC;kFAEvD,8OAAC;wEAAI,WAAU;wEAAyD,MAAK;wEAAO,QAAO;wEAAe,SAAQ;kFAChH,cAAA,8OAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAY;4EAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAWrF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;4CAAoC,MAAK;4CAAO,QAAO;4CAAe,SAAQ;4CAAY,OAAM;sDAC7G,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ/E,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAwC;;;;;;4CACpD,kBAAkB,eAAe,MAAM,GAAG,kBACzC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;;4DAAiC;4DAAU,cAAc,CAAC,EAAE,CAAC,QAAQ,IAAI;;;;;;;kEACvF,8OAAC;wDAAE,WAAU;;4DACV,IAAI,KAAK,cAAc,CAAC,EAAE,CAAC,IAAI,EAAE,kBAAkB,CAAC,SAAS;gEAAE,SAAS;gEAAQ,OAAO;gEAAQ,KAAK;4DAAU;4DAC9G,cAAc,CAAC,EAAE,CAAC,SAAS,IAAI,CAAC,GAAG,EAAE,cAAc,CAAC,EAAE,CAAC,SAAS,EAAE;;;;;;;;;;;;qEAIvE,8OAAC;gDAAE,WAAU;0DAAe;;;;;;;;;;;;kDAGhC,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,kBAAkB,eAAe,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG;wCAClG,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;kCAQP,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA0B;;;;;;sDACxC,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAGjB,8OAAC;oCAAI,WAAU;8CACZ,cAAc,WAAW,MAAM,GAAG,IACjC,WAAW,GAAG,CAAC,CAAC,wBACd,8OAAC;4CAAsB,WAAU;;8DAC/B,8OAAC;oDAAI,WAAU;;wDACZ,QAAQ,SAAS,iBAChB,8OAAC,6HAAA,CAAA,UAAK;4DACJ,KAAK,CAAA,GAAA,6GAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,SAAS,EAAE,GAAG;4DACvC,KAAK,QAAQ,KAAK;4DAClB,IAAI;4DACJ,WAAU;;;;;iFAGZ,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;gEAA0B,MAAK;gEAAO,QAAO;gEAAe,SAAQ;gEAAY,OAAM;0EACnG,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;;;;;;sEAI3E,8OAAC;4DAAI,WAAU;sEAAkF;;;;;;;;;;;;8DAInG,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEACV,IAAI,KAAK,QAAQ,WAAW,EAAE,kBAAkB,CAAC,SAAS;gEAAE,MAAM;gEAAW,OAAO;gEAAS,KAAK;4DAAU;;;;;;sEAE/G,8OAAC;4DAAG,WAAU;sEAAuC,QAAQ,KAAK;;;;;;wDACjE,QAAQ,OAAO,kBACd,8OAAC;4DAAE,WAAU;sEAAmC,QAAQ,OAAO;;;;;;sEAEjE,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAM,CAAC,MAAM,EAAE,QAAQ,IAAI,CAAC,OAAO,EAAE;4DACrC,WAAU;;gEACX;8EAEC,8OAAC;oEAAI,WAAU;oEAAe,MAAK;oEAAO,QAAO;oEAAe,SAAQ;oEAAY,OAAM;8EACxF,cAAA,8OAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAY;wEAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;2CAlCnE,QAAQ,GAAG;;;;kEAyCvB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;gDAAuC,MAAK;gDAAO,QAAO;gDAAe,SAAQ;gDAAY,OAAM;0DAChH,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;0DAEvE,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;;;;;;;;;;;;8CAKxC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;4CACX;0DAEC,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,QAAO;gDAAe,SAAQ;gDAAY,OAAM;0DACxF,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ/E,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA0B;;;;;;sDACxC,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAGjB,8OAAC;oCAAI,WAAU;8CACZ,kBAAkB,eAAe,MAAM,GAAG,kBACzC,8OAAC;wCAAI,WAAU;kDACZ,eAAe,GAAG,CAAC,CAAC,OAAc,sBACjC,8OAAC;gDAAoB,WAAU;;oDAE5B,QAAQ,eAAe,MAAM,GAAG,mBAC/B,8OAAC;wDAAI,WAAU;;;;;;kEAEjB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAK,WAAU;kFAAa,IAAI,KAAK,MAAM,IAAI,EAAE,OAAO;;;;;;;;;;;;;;;;0EAG7D,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAG,WAAU;0FAAqB,MAAM,KAAK;;;;;;4EAC7C,MAAM,SAAS,kBACd,8OAAC;gFAAK,WAAU;0FACb,MAAM,SAAS;;;;;;;;;;;;kFAKtB,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAE,WAAU;;kGACX,8OAAC;wFAAI,WAAU;wFAAgC,MAAK;wFAAO,QAAO;wFAAe,SAAQ;wFAAY,OAAM;kGACzG,cAAA,8OAAC;4FAAK,eAAc;4FAAQ,gBAAe;4FAAQ,aAAY;4FAAI,GAAE;;;;;;;;;;;oFAEtE,IAAI,KAAK,MAAM,IAAI,EAAE,kBAAkB,CAAC,SAAS;wFAAE,SAAS;wFAAQ,OAAO;wFAAQ,KAAK;oFAAU;;;;;;;4EAEpG,MAAM,SAAS,kBACd,8OAAC;gFAAE,WAAU;;kGACX,8OAAC;wFAAI,WAAU;wFAAgC,MAAK;wFAAO,QAAO;wFAAe,SAAQ;wFAAY,OAAM;kGACzG,cAAA,8OAAC;4FAAK,eAAc;4FAAQ,gBAAe;4FAAQ,aAAY;4FAAI,GAAE;;;;;;;;;;;oFAEtE,MAAM,SAAS;;;;;;;4EAGnB,MAAM,QAAQ,kBACb,8OAAC;gFAAE,WAAU;;kGACX,8OAAC;wFAAI,WAAU;wFAAgC,MAAK;wFAAO,QAAO;wFAAe,SAAQ;wFAAY,OAAM;;0GACzG,8OAAC;gGAAK,eAAc;gGAAQ,gBAAe;gGAAQ,aAAY;gGAAI,GAAE;;;;;;0GACrE,8OAAC;gGAAK,eAAc;gGAAQ,gBAAe;gGAAQ,aAAY;gGAAI,GAAE;;;;;;;;;;;;oFAEtE,MAAM,QAAQ;;;;;;;;;;;;;oEAKpB,MAAM,QAAQ,kBACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAE,WAAU;;8FACX,8OAAC;oFAAI,WAAU;oFAAgC,MAAK;oFAAO,QAAO;oFAAe,SAAQ;oFAAY,OAAM;8FACzG,cAAA,8OAAC;wFAAK,eAAc;wFAAQ,gBAAe;wFAAQ,aAAY;wFAAI,GAAE;;;;;;;;;;;gFAEtE,MAAM,UAAU,KAAK,SAAS,kBAAkB;gFAAiB,MAAM,QAAQ;;;;;;;;;;;;kFAKtF,8OAAC,4JAAA,CAAA,UAAI;wEACH,MAAM,CAAC,QAAQ,EAAE,MAAM,IAAI,CAAC,OAAO,EAAE;wEACrC,WAAU;;4EACX;0FAEC,8OAAC;gFAAI,WAAU;gFAAe,MAAK;gFAAO,QAAO;gFAAe,SAAQ;gFAAY,OAAM;0FACxF,cAAA,8OAAC;oFAAK,eAAc;oFAAQ,gBAAe;oFAAQ,aAAY;oFAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+CAhErE,MAAM,GAAG;;;;;;;;;6DAyEvB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;gDAAuC,MAAK;gDAAO,QAAO;gDAAe,SAAQ;gDAAY,OAAM;0DAChH,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;0DAEvE,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;;;;;;;;;;;;8CAKxC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;4CACX;0DAEC,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,QAAO;gDAAe,SAAQ;gDAAY,OAAM;0DACxF,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ/E,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA0B;;;;;;0DACxC,8OAAC;gDAAI,WAAU;;;;;;;;;;;;kDAGjB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAA2B,MAAK;4DAAO,QAAO;4DAAe,SAAQ;4DAAY,OAAM;sEACpG,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;;;;;;;;;;;kEAGzE,8OAAC;wDAAG,WAAU;kEAAqC;;;;;;kEACnD,8OAAC;wDAAE,WAAU;kEAA4B;;;;;;;;;;;;0DAK3C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAA2B,MAAK;4DAAO,QAAO;4DAAe,SAAQ;4DAAY,OAAM;sEACpG,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;;;;;;;;;;;kEAGzE,8OAAC;wDAAG,WAAU;kEAAqC;;;;;;kEACnD,8OAAC;wDAAE,WAAU;kEAA4B;;;;;;;;;;;;0DAK3C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAA2B,MAAK;4DAAO,QAAO;4DAAe,SAAQ;4DAAY,OAAM;sEACpG,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;;;;;;;;;;;kEAGzE,8OAAC;wDAAG,WAAU;kEAAqC;;;;;;kEACnD,8OAAC;wDAAE,WAAU;kEAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASjD,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAsC;;;;;;sDACnD,8OAAC;4CAAG,WAAU;sDAA0B;;;;;;sDACxC,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAE,WAAU;sDAAkC;;;;;;;;;;;;8CAKjD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACZ,YAAY,SAAS,MAAM,GAAG,IAC7B,SAAS,GAAG,CAAC,CAAC,wBACZ,8OAAC;gDAAsB,WAAU;0DAC9B,QAAQ,IAAI,iBACX,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wDACJ,KAAK,CAAA,GAAA,6GAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,IAAI,EAAE,GAAG;wDAClC,KAAK,QAAQ,IAAI;wDACjB,IAAI;wDACJ,WAAU;;;;;;;;;;yEAId,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;kEAA2C,QAAQ,IAAI;;;;;;;;;;;+CAZhE,QAAQ,GAAG;;;;sEAkBvB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;oDAAuC,MAAK;oDAAO,QAAO;oDAAe,SAAQ;oDAAY,OAAM;8DAChH,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;8DAEvE,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;8DACrC,8OAAC;oDAAE,WAAU;8DAAqB;;;;;;;;;;;;;;;;;;;;;;8CAM1C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;4CACX;0DAEC,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;gDAAY,OAAM;0DACnF,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ/E,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA0B;;;;;;0DACxC,8OAAC;gDAAE,WAAU;0DAAiC;;;;;;;;;;;;kDAIhD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;0DAGD,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQX,8OAAC,qHAAA,CAAA,UAAM;;;;;;;AAGb"}}, {"offset": {"line": 2236, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2247, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 2247, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}