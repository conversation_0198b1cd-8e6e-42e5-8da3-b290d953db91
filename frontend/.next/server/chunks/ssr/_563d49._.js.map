{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/Header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/Header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyR,GACtT,uDACA"}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/Header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/Header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAqQ,GAClS,mCACA"}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/Footer.tsx"], "sourcesContent": ["import Link from 'next/link'\n\nexport default function Footer() {\n  const currentYear = new Date().getFullYear()\n\n  return (\n    <footer className=\"bg-gradient-to-b from-neutral-800 to-neutral-900 text-white\">\n      <div className=\"container py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          <div className=\"md:col-span-2\">\n            <h3 className=\"text-xl font-bold mb-4 text-white\">Northern Nepalese United FC</h3>\n            <p className=\"mb-4 text-neutral-300\">\n              Brisbane-based Nepalese football club est. 2023, bringing together the community through the beautiful game.\n            </p>\n            <div className=\"flex space-x-4 mt-6\">\n              <a\n                href=\"https://facebook.com\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                aria-label=\"Facebook\"\n                className=\"bg-neutral-700 hover:bg-neutral-600 text-white p-2 rounded-full transition-colors\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"currentColor\"\n                >\n                  <path d=\"M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z\" />\n                </svg>\n              </a>\n              <a\n                href=\"https://twitter.com\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                aria-label=\"Twitter\"\n                className=\"bg-neutral-700 hover:bg-neutral-600 text-white p-2 rounded-full transition-colors\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"currentColor\"\n                >\n                  <path d=\"M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z\" />\n                </svg>\n              </a>\n              <a\n                href=\"https://instagram.com\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                aria-label=\"Instagram\"\n                className=\"bg-neutral-700 hover:bg-neutral-600 text-white p-2 rounded-full transition-colors\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"currentColor\"\n                >\n                  <path d=\"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z\" />\n                </svg>\n              </a>\n            </div>\n          </div>\n\n          <div>\n            <h3 className=\"text-xl font-bold mb-4 text-white\">Club Links</h3>\n            <ul className=\"space-y-2 text-neutral-300\">\n              <li>\n                <Link href=\"/about\" className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\">\n                  About Us\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/team\" className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\">\n                  Our Team\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/news\" className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\">\n                  News\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/events\" className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\">\n                  Events & Fixtures\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/gallery\" className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\">\n                  Gallery\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          <div>\n            <h3 className=\"text-xl font-bold mb-4 text-white\">Contact</h3>\n            <address className=\"not-italic text-neutral-300\">\n              <p className=\"mb-2 flex items-start\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2 mt-0.5 text-neutral-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                </svg>\n                John Oxley Reserve<br />Murrumba Downs<br />Brisbane, QLD\n              </p>\n              <p className=\"mb-2 flex items-start\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2 mt-0.5 text-neutral-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                </svg>\n                <a href=\"tel:+61424770570\" className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\">\n                  0424 770 570\n                </a>\n              </p>\n              <p className=\"flex items-start\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2 mt-0.5 text-neutral-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                </svg>\n                <a\n                  href=\"mailto:<EMAIL>\"\n                  className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\"\n                >\n                  <EMAIL>\n                </a>\n              </p>\n            </address>\n          </div>\n        </div>\n\n        <div className=\"border-t border-neutral-700 mt-10 pt-8 text-center text-neutral-400\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <p>&copy; {currentYear} Northern Nepalese United - NNUFC. All rights reserved.</p>\n            <ul className=\"flex space-x-4 mt-4 md:mt-0\">\n              <li>\n                <Link href=\"/sponsors\" className=\"text-neutral-400 hover:text-white hover:underline transition-colors no-underline\">\n                  Sponsors\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contact\" className=\"text-neutral-400 hover:text-white hover:underline transition-colors no-underline\">\n                  Contact\n                </Link>\n              </li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;8CAGrC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,cAAW;4CACX,WAAU;sDAEV,cAAA,8OAAC;gDACC,OAAM;gDACN,OAAM;gDACN,QAAO;gDACP,SAAQ;gDACR,MAAK;0DAEL,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,cAAW;4CACX,WAAU;sDAEV,cAAA,8OAAC;gDACC,OAAM;gDACN,OAAM;gDACN,QAAO;gDACP,SAAQ;gDACR,MAAK;0DAEL,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,cAAW;4CACX,WAAU;sDAEV,cAAA,8OAAC;gDACC,OAAM;gDACN,OAAM;gDACN,QAAO;gDACP,SAAQ;gDACR,MAAK;0DAEL,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMhB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAmF;;;;;;;;;;;sDAInH,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAmF;;;;;;;;;;;sDAIlH,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAmF;;;;;;;;;;;sDAIlH,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAU,WAAU;0DAAmF;;;;;;;;;;;sDAIpH,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmF;;;;;;;;;;;;;;;;;;;;;;;sCAOzH,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,8OAAC;oCAAQ,WAAU;;sDACjB,8OAAC;4CAAE,WAAU;;8DACX,8OAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAAuC,MAAK;oDAAO,SAAQ;oDAAY,QAAO;;sEAC9H,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;sEACrE,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;gDACjE;8DACY,8OAAC;;;;;gDAAK;8DAAc,8OAAC;;;;;gDAAK;;;;;;;sDAE9C,8OAAC;4CAAE,WAAU;;8DACX,8OAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAAuC,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAC9H,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;8DAEvE,8OAAC;oDAAE,MAAK;oDAAmB,WAAU;8DAAmF;;;;;;;;;;;;sDAI1H,8OAAC;4CAAE,WAAU;;8DACX,8OAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAAuC,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAC9H,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;8DAEvE,8OAAC;oDACC,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQT,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;oCAAE;oCAAQ;oCAAY;;;;;;;0CACvB,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAY,WAAU;sDAAmF;;;;;;;;;;;kDAItH,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAAmF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUnI"}}, {"offset": {"line": 525, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 531, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/ParticleSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/ParticleSection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ParticleSection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA"}}, {"offset": {"line": 539, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 545, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/ParticleSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/ParticleSection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ParticleSection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8Q,GAC3S,4CACA"}}, {"offset": {"line": 553, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 559, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 563, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 569, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/RegistrationForm.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/RegistrationForm.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/RegistrationForm.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmS,GAChU,iEACA"}}, {"offset": {"line": 577, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 583, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/RegistrationForm.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/RegistrationForm.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/RegistrationForm.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+Q,GAC5S,6CACA"}}, {"offset": {"line": 591, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 597, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 601, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 607, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/src/app/junior-academy/page.tsx"], "sourcesContent": ["import Header from \"@/components/Header\";\nimport Footer from \"@/components/Footer\";\nimport Image from \"next/image\";\nimport Link from \"next/link\";\nimport ParticleSection from \"@/components/ParticleSection\";\nimport RegistrationForm from \"@/components/RegistrationForm\";\n\nexport const metadata = {\n  title: \"Junior Academy | Northern Nepalese United FC\",\n  description: \"Our Junior Academy provides high-quality football training for children ages 5-15, focusing on skill development, teamwork, and love for the game.\",\n};\n\nexport default function JuniorAcademyPage() {\n  return (\n    <>\n      <Header />\n      <main>\n        {/* Hero Section */}\n        <section className=\"relative min-h-[60vh] flex items-center\">\n          {/* Background Image */}\n          <div className=\"absolute inset-0 z-0\">\n            <Image\n              src=\"/junior-academy.jpg\"\n              alt=\"Children playing football\"\n              fill\n              priority\n              className=\"object-cover brightness-75\"\n            />\n          </div>\n          \n          {/* Overlay for better text contrast */}\n          <div className=\"absolute inset-0 bg-gradient-to-r from-black/50 via-black/30 to-transparent z-1\"></div>\n          \n          <div className=\"container relative z-10 text-white\">\n            <div className=\"max-w-2xl\">\n              <h1 className=\"text-4xl md:text-5xl font-bold mb-4 leading-tight\">\n                Junior Academy\n              </h1>\n              <p className=\"text-xl mb-6\">\n                Developing the next generation of football stars through fun, \n                engaging, and professional coaching.\n              </p>\n              <Link\n                href=\"#register\"\n                className=\"bg-yellow-400 text-primary-900 px-6 py-3 rounded-lg font-semibold hover:bg-yellow-300 transition-colors shadow-lg inline-flex items-center\"\n              >\n                Register Now\n                <svg className=\"w-5 h-5 ml-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M14 5l7 7m0 0l-7 7m7-7H3\"></path>\n                </svg>\n              </Link>\n            </div>\n          </div>\n        </section>\n\n        {/* Program Overview Section */}\n        <section className=\"py-16\">\n          <ParticleSection className=\"container\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-12 items-center\">\n              <div>\n                <h2 className=\"text-3xl font-bold mb-6\">Program Overview</h2>\n                <p className=\"mb-4\">\n                  The Northern Nepalese United FC Junior Academy is designed to introduce children \n                  ages 5-15 to the beautiful game of football in a fun, safe, and supportive environment. \n                  Our program focuses on developing fundamental skills, fostering teamwork, and building \n                  confidence both on and off the pitch.\n                </p>\n                <p>\n                  Led by qualified coaches with experience in youth development, our structured training \n                  sessions are tailored to different age groups and skill levels, ensuring every child \n                  can progress at their own pace while enjoying the game.\n                </p>\n              </div>\n              <div className=\"relative h-80 rounded-lg overflow-hidden shadow-lg\">\n                <Image\n                  src=\"/junior-training.jpg\"\n                  alt=\"Junior football training session\"\n                  fill\n                  className=\"object-cover\"\n                />\n              </div>\n            </div>\n          </ParticleSection>\n        </section>\n\n        {/* Program Details Section */}\n        <section className=\"py-16 bg-neutral-50\">\n          <div className=\"container\">\n            <h2 className=\"text-3xl font-bold mb-12 text-center\">Program Details</h2>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n              {/* Age Groups */}\n              <div className=\"bg-white p-8 rounded-lg shadow-md\">\n                <div className=\"bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mb-6 mx-auto\">\n                  <svg className=\"w-8 h-8 text-primary-800\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z\"></path>\n                  </svg>\n                </div>\n                <h3 className=\"text-xl font-bold mb-4 text-center\">Age Groups</h3>\n                <ul className=\"space-y-2\">\n                  <li className=\"flex items-center\">\n                    <span className=\"bg-primary-100 text-primary-800 rounded-full p-1 mr-3\">\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                        <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                      </svg>\n                    </span>\n                    <span>Little Stars: Ages 5-7</span>\n                  </li>\n                  <li className=\"flex items-center\">\n                    <span className=\"bg-primary-100 text-primary-800 rounded-full p-1 mr-3\">\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                        <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                      </svg>\n                    </span>\n                    <span>Junior Developers: Ages 8-11</span>\n                  </li>\n                  <li className=\"flex items-center\">\n                    <span className=\"bg-primary-100 text-primary-800 rounded-full p-1 mr-3\">\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                        <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                      </svg>\n                    </span>\n                    <span>Youth Talents: Ages 12-15</span>\n                  </li>\n                </ul>\n              </div>\n\n              {/* Training Schedule */}\n              <div className=\"bg-white p-8 rounded-lg shadow-md\">\n                <div className=\"bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mb-6 mx-auto\">\n                  <svg className=\"w-8 h-8 text-primary-800\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n                  </svg>\n                </div>\n                <h3 className=\"text-xl font-bold mb-4 text-center\">Training Schedule</h3>\n                <ul className=\"space-y-3\">\n                  <li className=\"mb-2\">\n                    <p className=\"font-semibold text-primary-700\">Little Stars (5-7):</p>\n                    <p>Saturdays, 9:00 AM - 10:00 AM</p>\n                  </li>\n                  <li className=\"mb-2\">\n                    <p className=\"font-semibold text-primary-700\">Junior Developers (8-11):</p>\n                    <p>Saturdays, 10:15 AM - 11:30 AM</p>\n                  </li>\n                  <li>\n                    <p className=\"font-semibold text-primary-700\">Youth Talents (12-15):</p>\n                    <p>Saturdays, 11:45 AM - 1:15 PM</p>\n                  </li>\n                </ul>\n              </div>\n\n              {/* Location */}\n              <div className=\"bg-white p-8 rounded-lg shadow-md\">\n                <div className=\"bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mb-6 mx-auto\">\n                  <svg className=\"w-8 h-8 text-primary-800\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"></path>\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"></path>\n                  </svg>\n                </div>\n                <h3 className=\"text-xl font-bold mb-4 text-center\">Location</h3>\n                <p className=\"mb-4 text-center\">\n                  John Oxley Reserve Field 2\n                  <br />\n                  2 Ogg Rd, Murrumba Downs QLD 4503\n                </p>\n                <div className=\"text-center\">\n                  <a\n                    href=\"https://maps.google.com/?q=2+Ogg+Rd,+Murrumba+Downs+QLD+4503\"\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"inline-flex items-center text-primary-600 font-semibold hover:text-primary-800 transition-colors\"\n                  >\n                    View on Map\n                    <svg className=\"w-4 h-4 ml-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\"></path>\n                    </svg>\n                  </a>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Benefits Section */}\n        <section className=\"py-16\">\n          <div className=\"container\">\n            <h2 className=\"text-3xl font-bold mb-12 text-center\">Benefits of Joining Our Academy</h2>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto\">\n              <div className=\"bg-white border border-gray-100 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow\">\n                <div className=\"flex items-start\">\n                  <div className=\"bg-primary-100 p-3 rounded-full mr-4\">\n                    <svg className=\"w-6 h-6 text-primary-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M13 10V3L4 14h7v7l9-11h-7z\"></path>\n                    </svg>\n                  </div>\n                  <div>\n                    <h3 className=\"text-xl font-bold mb-2\">Skill Development</h3>\n                    <p className=\"text-gray-600\">\n                      Structured training focusing on technical skills such as dribbling, passing, shooting, and game awareness.\n                    </p>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white border border-gray-100 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow\">\n                <div className=\"flex items-start\">\n                  <div className=\"bg-primary-100 p-3 rounded-full mr-4\">\n                    <svg className=\"w-6 h-6 text-primary-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"></path>\n                    </svg>\n                  </div>\n                  <div>\n                    <h3 className=\"text-xl font-bold mb-2\">Teamwork & Social Skills</h3>\n                    <p className=\"text-gray-600\">\n                      Learning to work with others, communicate effectively, and build lasting friendships.\n                    </p>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white border border-gray-100 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow\">\n                <div className=\"flex items-start\">\n                  <div className=\"bg-primary-100 p-3 rounded-full mr-4\">\n                    <svg className=\"w-6 h-6 text-primary-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\"></path>\n                    </svg>\n                  </div>\n                  <div>\n                    <h3 className=\"text-xl font-bold mb-2\">Physical Health</h3>\n                    <p className=\"text-gray-600\">\n                      Regular exercise that improves cardiovascular health, coordination, strength, and overall fitness.\n                    </p>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white border border-gray-100 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow\">\n                <div className=\"flex items-start\">\n                  <div className=\"bg-primary-100 p-3 rounded-full mr-4\">\n                    <svg className=\"w-6 h-6 text-primary-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\"></path>\n                    </svg>\n                  </div>\n                  <div>\n                    <h3 className=\"text-xl font-bold mb-2\">Confidence Building</h3>\n                    <p className=\"text-gray-600\">\n                      Developing self-esteem through mastering new skills and overcoming challenges in a supportive environment.\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Coaches Section */}\n        <section className=\"py-16 bg-primary-900 text-white\">\n          <div className=\"container\">\n            <h2 className=\"text-3xl font-bold mb-12 text-center\">Our Coaching Team</h2>\n\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8 max-w-5xl mx-auto\">\n              {/* Coach 1 */}\n              <div className=\"bg-primary-800/60 p-6 rounded-xl text-center\">\n                <div className=\"w-32 h-32 rounded-full bg-neutral-200 mx-auto mb-4 relative overflow-hidden\">\n                  <div className=\"absolute inset-0 flex items-center justify-center text-neutral-500\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-16 w-16\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                    </svg>\n                  </div>\n                </div>\n                <h3 className=\"text-xl font-bold\">Raj Sharma</h3>\n                <p className=\"text-yellow-400 font-medium mb-2\">Head Coach</p>\n                <p className=\"text-gray-300 text-sm\">\n                  FA Level 2 Coach with 10+ years experience in youth development\n                </p>\n              </div>\n\n              {/* Coach 2 */}\n              <div className=\"bg-primary-800/60 p-6 rounded-xl text-center\">\n                <div className=\"w-32 h-32 rounded-full bg-neutral-200 mx-auto mb-4 relative overflow-hidden\">\n                  <div className=\"absolute inset-0 flex items-center justify-center text-neutral-500\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-16 w-16\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                    </svg>\n                  </div>\n                </div>\n                <h3 className=\"text-xl font-bold\">Sita Gurung</h3>\n                <p className=\"text-yellow-400 font-medium mb-2\">Youth Coach</p>\n                <p className=\"text-gray-300 text-sm\">\n                  Specialized in coaching children aged 5-11, with background in early childhood education\n                </p>\n              </div>\n\n              {/* Coach 3 */}\n              <div className=\"bg-primary-800/60 p-6 rounded-xl text-center\">\n                <div className=\"w-32 h-32 rounded-full bg-neutral-200 mx-auto mb-4 relative overflow-hidden\">\n                  <div className=\"absolute inset-0 flex items-center justify-center text-neutral-500\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-16 w-16\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                    </svg>\n                  </div>\n                </div>\n                <h3 className=\"text-xl font-bold\">Anil Thapa</h3>\n                <p className=\"text-yellow-400 font-medium mb-2\">Technical Coach</p>\n                <p className=\"text-gray-300 text-sm\">\n                  Former professional player focusing on advanced skills for older youth players\n                </p>\n              </div>\n            </div>\n\n            <div className=\"mt-12 text-center\">\n              <p className=\"text-lg max-w-3xl mx-auto\">\n                All our coaches hold current Working with Children Checks and First Aid certifications. They are passionate about youth development both on and off the pitch.\n              </p>\n            </div>\n          </div>\n        </section>\n\n        {/* FAQ Section */}\n        <section className=\"py-16 bg-neutral-50\">\n          <div className=\"container\">\n            <h2 className=\"text-3xl font-bold mb-12 text-center\">Frequently Asked Questions</h2>\n\n            <div className=\"max-w-3xl mx-auto space-y-6\">\n              <div className=\"bg-white p-6 rounded-lg shadow-sm\">\n                <h3 className=\"text-xl font-bold mb-3\">What equipment does my child need?</h3>\n                <p className=\"text-gray-700\">\n                  Players should wear comfortable sports clothes, shin guards, and football boots (sneakers are acceptable for the youngest age group). Each player should bring their own water bottle. Training bibs and balls are provided by the club.\n                </p>\n              </div>\n\n              <div className=\"bg-white p-6 rounded-lg shadow-sm\">\n                <h3 className=\"text-xl font-bold mb-3\">Do you offer trial sessions?</h3>\n                <p className=\"text-gray-700\">\n                  Yes, new players are welcome to attend one free trial session before committing to the program. Please contact us in advance to arrange this.\n                </p>\n              </div>\n\n              <div className=\"bg-white p-6 rounded-lg shadow-sm\">\n                <h3 className=\"text-xl font-bold mb-3\">What happens if it rains?</h3>\n                <p className=\"text-gray-700\">\n                  In case of light rain, training will usually continue. For heavy rain or unsafe conditions, sessions may be canceled. We will notify parents via email and SMS as early as possible.\n                </p>\n              </div>\n\n              <div className=\"bg-white p-6 rounded-lg shadow-sm\">\n                <h3 className=\"text-xl font-bold mb-3\">Are parents required to stay during training?</h3>\n                <p className=\"text-gray-700\">\n                  Parents of children under 8 are required to remain at the venue during sessions. For older children, parents may leave but must be contactable by phone and return promptly at the end of the session.\n                </p>\n              </div>\n\n              <div className=\"bg-white p-6 rounded-lg shadow-sm\">\n                <h3 className=\"text-xl font-bold mb-3\">How much does it cost?</h3>\n                <p className=\"text-gray-700\">\n                  The program costs $150 per term (10 weeks) with discounts available for siblings. This includes weekly training sessions, a club t-shirt, and end-of-term participation certificate.\n                </p>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Registration Section */}\n        <section id=\"register\" className=\"py-16\">\n          <div className=\"container\">\n            <div className=\"bg-white rounded-xl shadow-lg overflow-hidden max-w-5xl mx-auto\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2\">\n                <div className=\"bg-primary-800 p-8 md:p-12 text-white\">\n                  <h2 className=\"text-3xl font-bold mb-6\">Register Your Child</h2>\n                  <p className=\"mb-4\">\n                    Join our Junior Academy today and give your child the opportunity to develop football skills in a fun, supportive environment.\n                  </p>\n                  <ul className=\"space-y-3 mb-8\">\n                    <li className=\"flex items-center\">\n                      <svg className=\"w-5 h-5 mr-3 text-yellow-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\"></path>\n                      </svg>\n                      10-week training programs\n                    </li>\n                    <li className=\"flex items-center\">\n                      <svg className=\"w-5 h-5 mr-3 text-yellow-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\"></path>\n                      </svg>\n                      Professional coaching staff\n                    </li>\n                    <li className=\"flex items-center\">\n                      <svg className=\"w-5 h-5 mr-3 text-yellow-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\"></path>\n                      </svg>\n                      Age-appropriate training methods\n                    </li>\n                    <li className=\"flex items-center\">\n                      <svg className=\"w-5 h-5 mr-3 text-yellow-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\"></path>\n                      </svg>\n                      Free club t-shirt included\n                    </li>\n                  </ul>\n                  <p className=\"text-sm text-gray-300\">\n                    Next term starts: February 3, 2024\n                  </p>\n                </div>\n                <div className=\"p-8 md:p-12\">\n                  <h3 className=\"text-2xl font-bold mb-6 text-primary-800\">Registration Form</h3>\n                  <RegistrationForm />\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Call to Action Section */}\n        <section className=\"bg-gradient-to-r from-primary-800 to-primary-900 py-10\">\n          <div className=\"container\">\n            <div className=\"flex flex-col md:flex-row items-center justify-between max-w-5xl mx-auto\">\n              <div className=\"text-white mb-6 md:mb-0 text-center md:text-left\">\n                <h2 className=\"text-2xl font-bold mb-2\">Have Questions?</h2>\n                <p className=\"text-sm text-gray-300 max-w-xl\">\n                  Contact our Junior Academy coordinator for more information about our programs.\n                </p>\n              </div>\n              <div className=\"flex gap-3\">\n                <Link\n                  href=\"/contact\"\n                  className=\"bg-white text-primary-800 px-5 py-2 rounded-lg font-semibold hover:bg-yellow-400 transition-colors shadow-md text-sm\"\n                >\n                  Contact Us\n                </Link>\n                <a\n                  href=\"tel:+61412345678\"\n                  className=\"bg-transparent border-2 border-white text-white px-5 py-2 rounded-lg font-semibold hover:bg-white hover:text-primary-800 transition-colors text-sm\"\n                >\n                  Call: 0412 345 678\n                </a>\n              </div>\n            </div>\n          </div>\n        </section>\n      </main>\n      <Footer />\n    </>\n  );\n} "], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEO,MAAM,WAAW;IACtB,OAAO;IACP,aAAa;AACf;AAEe,SAAS;IACtB,qBACE;;0BACE,8OAAC,qHAAA,CAAA,UAAM;;;;;0BACP,8OAAC;;kCAEC,8OAAC;wBAAQ,WAAU;;0CAEjB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,IAAI;oCACJ,QAAQ;oCACR,WAAU;;;;;;;;;;;0CAKd,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAoD;;;;;;sDAGlE,8OAAC;4CAAE,WAAU;sDAAe;;;;;;sDAI5B,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;gDACX;8DAEC,8OAAC;oDAAI,WAAU;oDAAe,MAAK;oDAAO,QAAO;oDAAe,SAAQ;oDAAY,OAAM;8DACxF,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ/E,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC,8HAAA,CAAA,UAAe;4BAAC,WAAU;sCACzB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA0B;;;;;;0DACxC,8OAAC;gDAAE,WAAU;0DAAO;;;;;;0DAMpB,8OAAC;0DAAE;;;;;;;;;;;;kDAML,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,IAAI;4CACJ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQpB,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAuC;;;;;;8CAErD,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;wDAA2B,MAAK;wDAAO,QAAO;wDAAe,SAAQ;wDAAY,OAAM;kEACpG,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;;;;;;8DAGzE,8OAAC;oDAAG,WAAU;8DAAqC;;;;;;8DACnD,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAK,WAAU;8EACd,cAAA,8OAAC;wEAAI,OAAM;wEAA6B,WAAU;wEAAU,SAAQ;wEAAY,MAAK;kFACnF,cAAA,8OAAC;4EAAK,UAAS;4EAAU,GAAE;4EAAqH,UAAS;;;;;;;;;;;;;;;;8EAG7J,8OAAC;8EAAK;;;;;;;;;;;;sEAER,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAK,WAAU;8EACd,cAAA,8OAAC;wEAAI,OAAM;wEAA6B,WAAU;wEAAU,SAAQ;wEAAY,MAAK;kFACnF,cAAA,8OAAC;4EAAK,UAAS;4EAAU,GAAE;4EAAqH,UAAS;;;;;;;;;;;;;;;;8EAG7J,8OAAC;8EAAK;;;;;;;;;;;;sEAER,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAK,WAAU;8EACd,cAAA,8OAAC;wEAAI,OAAM;wEAA6B,WAAU;wEAAU,SAAQ;wEAAY,MAAK;kFACnF,cAAA,8OAAC;4EAAK,UAAS;4EAAU,GAAE;4EAAqH,UAAS;;;;;;;;;;;;;;;;8EAG7J,8OAAC;8EAAK;;;;;;;;;;;;;;;;;;;;;;;;sDAMZ,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;wDAA2B,MAAK;wDAAO,QAAO;wDAAe,SAAQ;wDAAY,OAAM;kEACpG,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;;;;;;8DAGzE,8OAAC;oDAAG,WAAU;8DAAqC;;;;;;8DACnD,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAE,WAAU;8EAAiC;;;;;;8EAC9C,8OAAC;8EAAE;;;;;;;;;;;;sEAEL,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAE,WAAU;8EAAiC;;;;;;8EAC9C,8OAAC;8EAAE;;;;;;;;;;;;sEAEL,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAiC;;;;;;8EAC9C,8OAAC;8EAAE;;;;;;;;;;;;;;;;;;;;;;;;sDAMT,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;wDAA2B,MAAK;wDAAO,QAAO;wDAAe,SAAQ;wDAAY,OAAM;;0EACpG,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;0EACrE,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;;;;;;;;;;;;8DAGzE,8OAAC;oDAAG,WAAU;8DAAqC;;;;;;8DACnD,8OAAC;oDAAE,WAAU;;wDAAmB;sEAE9B,8OAAC;;;;;wDAAK;;;;;;;8DAGR,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,MAAK;wDACL,QAAO;wDACP,KAAI;wDACJ,WAAU;;4DACX;0EAEC,8OAAC;gEAAI,WAAU;gEAAe,MAAK;gEAAO,QAAO;gEAAe,SAAQ;gEAAY,OAAM;0EACxF,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUnF,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAuC;;;;;;8CAErD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAA2B,MAAK;4DAAO,QAAO;4DAAe,SAAQ;4DAAY,OAAM;sEACpG,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;;;;;;;;;;;kEAGzE,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAyB;;;;;;0EACvC,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;;;;;;sDAOnC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAA2B,MAAK;4DAAO,QAAO;4DAAe,SAAQ;4DAAY,OAAM;sEACpG,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;;;;;;;;;;;kEAGzE,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAyB;;;;;;0EACvC,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;;;;;;sDAOnC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAA2B,MAAK;4DAAO,QAAO;4DAAe,SAAQ;4DAAY,OAAM;sEACpG,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;;;;;;;;;;;kEAGzE,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAyB;;;;;;0EACvC,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;;;;;;sDAOnC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAA2B,MAAK;4DAAO,QAAO;4DAAe,SAAQ;4DAAY,OAAM;sEACpG,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;;;;;;;;;;;kEAGzE,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAyB;;;;;;0EACvC,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAWzC,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAuC;;;;;;8CAErD,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,OAAM;4DAA6B,WAAU;4DAAY,MAAK;4DAAO,SAAQ;4DAAY,QAAO;sEACnG,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;8DAI3E,8OAAC;oDAAG,WAAU;8DAAoB;;;;;;8DAClC,8OAAC;oDAAE,WAAU;8DAAmC;;;;;;8DAChD,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAMvC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,OAAM;4DAA6B,WAAU;4DAAY,MAAK;4DAAO,SAAQ;4DAAY,QAAO;sEACnG,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;8DAI3E,8OAAC;oDAAG,WAAU;8DAAoB;;;;;;8DAClC,8OAAC;oDAAE,WAAU;8DAAmC;;;;;;8DAChD,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAMvC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,OAAM;4DAA6B,WAAU;4DAAY,MAAK;4DAAO,SAAQ;4DAAY,QAAO;sEACnG,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;8DAI3E,8OAAC;oDAAG,WAAU;8DAAoB;;;;;;8DAClC,8OAAC;oDAAE,WAAU;8DAAmC;;;;;;8DAChD,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAMzC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;kDAA4B;;;;;;;;;;;;;;;;;;;;;;kCAQ/C,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAuC;;;;;;8CAErD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAyB;;;;;;8DACvC,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;sDAK/B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAyB;;;;;;8DACvC,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;sDAK/B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAyB;;;;;;8DACvC,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;sDAK/B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAyB;;;;;;8DACvC,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;sDAK/B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAyB;;;;;;8DACvC,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASrC,8OAAC;wBAAQ,IAAG;wBAAW,WAAU;kCAC/B,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA0B;;;;;;8DACxC,8OAAC;oDAAE,WAAU;8DAAO;;;;;;8DAGpB,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAI,WAAU;oEAA+B,MAAK;oEAAO,QAAO;oEAAe,SAAQ;oEAAY,OAAM;8EACxG,cAAA,8OAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAY;wEAAI,GAAE;;;;;;;;;;;gEACjE;;;;;;;sEAGR,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAI,WAAU;oEAA+B,MAAK;oEAAO,QAAO;oEAAe,SAAQ;oEAAY,OAAM;8EACxG,cAAA,8OAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAY;wEAAI,GAAE;;;;;;;;;;;gEACjE;;;;;;;sEAGR,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAI,WAAU;oEAA+B,MAAK;oEAAO,QAAO;oEAAe,SAAQ;oEAAY,OAAM;8EACxG,cAAA,8OAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAY;wEAAI,GAAE;;;;;;;;;;;gEACjE;;;;;;;sEAGR,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAI,WAAU;oEAA+B,MAAK;oEAAO,QAAO;oEAAe,SAAQ;oEAAY,OAAM;8EACxG,cAAA,8OAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAY;wEAAI,GAAE;;;;;;;;;;;gEACjE;;;;;;;;;;;;;8DAIV,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAIvC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,8OAAC,+HAAA,CAAA,UAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ3B,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA0B;;;;;;0DACxC,8OAAC;gDAAE,WAAU;0DAAiC;;;;;;;;;;;;kDAIhD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;0DAGD,8OAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQX,8OAAC,qHAAA,CAAA,UAAM;;;;;;;AAGb"}}, {"offset": {"line": 2283, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2294, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 2294, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}