{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/Header.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport Image from 'next/image'\nimport { useState, useEffect } from 'react'\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false)\n  const [isScrolled, setIsScrolled] = useState(false)\n\n  // Handle scroll effect for sticky header\n  useEffect(() => {\n    const handleScroll = () => {\n      if (window.scrollY > 10) {\n        setIsScrolled(true)\n      } else {\n        setIsScrolled(false)\n      }\n    }\n\n    window.addEventListener('scroll', handleScroll)\n    return () => window.removeEventListener('scroll', handleScroll)\n  }, [])\n\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen)\n  }\n\n  return (\n    <header\n      className={`bg-white sticky top-0 z-50 transition-all duration-200 ${\n        isScrolled ? 'shadow-lg py-2' : 'shadow-md py-3'\n      }`}\n    >\n      <div className=\"container\">\n        <div className=\"flex justify-between items-center\">\n          <Link href=\"/\" className=\"flex items-center space-x-3\">\n            <div className={`relative transition-all duration-200 ${isScrolled ? 'w-10 h-10' : 'w-12 h-12'}`}>\n              <Image\n                src=\"/logo.png\"\n                alt=\"Northern Nepalese United FC Logo\"\n                fill\n                className=\"object-contain\"\n                priority\n              />\n            </div>\n            <div>\n              <span className=\"text-xl font-bold text-primary-800 hidden sm:inline\">Northern Nepalese United FC</span>\n              <span className=\"text-xl font-bold text-primary-800 sm:hidden\">NNUFC</span>\n            </div>\n          </Link>\n\n          {/* Mobile menu button */}\n          <button\n            className=\"md:hidden p-2 text-primary-800 focus:outline-none focus:ring-2 focus:ring-primary-400 rounded-lg\"\n            onClick={toggleMenu}\n            aria-label=\"Toggle menu\"\n            aria-expanded={isMenuOpen}\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke=\"currentColor\"\n              className=\"h-6 w-6\"\n            >\n              {isMenuOpen ? (\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M6 18L18 6M6 6l12 12\"\n                />\n              ) : (\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M4 6h16M4 12h16M4 18h16\"\n                />\n              )}\n            </svg>\n          </button>\n\n          {/* Desktop navigation */}\n          <nav className=\"hidden md:flex space-x-1 lg:space-x-6\">\n            <NavLink href=\"/\">Home</NavLink>\n            <NavLink href=\"/about\">About</NavLink>\n            <NavLink href=\"/team\">Team</NavLink>\n            <NavLink href=\"/news\">News</NavLink>\n            <NavLink href=\"/events\">Events</NavLink>\n            <NavLink href=\"/sponsors\">Sponsors</NavLink>\n            <NavLink href=\"/gallery\">Gallery</NavLink>\n            <NavLink href=\"/contact\">Contact</NavLink>\n          </nav>\n        </div>\n\n        {/* Mobile navigation - slide down animation */}\n        <div\n          className={`md:hidden overflow-hidden transition-all duration-300 ease-in-out ${\n            isMenuOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'\n          }`}\n        >\n          <nav className=\"mt-4 space-y-2 border-t border-neutral-200 pt-3\">\n            <MobileNavLink href=\"/\" onClick={() => setIsMenuOpen(false)}>Home</MobileNavLink>\n            <MobileNavLink href=\"/about\" onClick={() => setIsMenuOpen(false)}>About</MobileNavLink>\n            <MobileNavLink href=\"/team\" onClick={() => setIsMenuOpen(false)}>Team</MobileNavLink>\n            <MobileNavLink href=\"/news\" onClick={() => setIsMenuOpen(false)}>News</MobileNavLink>\n            <MobileNavLink href=\"/events\" onClick={() => setIsMenuOpen(false)}>Events</MobileNavLink>\n            <MobileNavLink href=\"/sponsors\" onClick={() => setIsMenuOpen(false)}>Sponsors</MobileNavLink>\n            <MobileNavLink href=\"/gallery\" onClick={() => setIsMenuOpen(false)}>Gallery</MobileNavLink>\n            <MobileNavLink href=\"/contact\" onClick={() => setIsMenuOpen(false)}>Contact</MobileNavLink>\n          </nav>\n        </div>\n      </div>\n    </header>\n  )\n}\n\n// Desktop Navigation Link Component\nfunction NavLink({ href, children }: { href: string; children: React.ReactNode }) {\n  return (\n    <Link\n      href={href}\n      className=\"px-3 py-2 rounded-lg font-medium text-primary-700 transition-colors hover:bg-primary-50 hover:text-primary-900\"\n    >\n      {children}\n    </Link>\n  )\n}\n\n// Mobile Navigation Link Component\nfunction MobileNavLink({ href, onClick, children }: {\n  href: string;\n  onClick: () => void;\n  children: React.ReactNode\n}) {\n  return (\n    <Link\n      href={href}\n      className=\"block py-2 px-1 text-primary-700 hover:bg-primary-50 hover:text-primary-900 rounded-lg font-medium transition-colors\"\n      onClick={onClick}\n    >\n      {children}\n    </Link>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,IAAI,OAAO,OAAO,GAAG,IAAI;gBACvB,cAAc;YAChB,OAAO;gBACL,cAAc;YAChB;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,aAAa;QACjB,cAAc,CAAC;IACjB;IAEA,qBACE,8OAAC;QACC,WAAW,CAAC,uDAAuD,EACjE,aAAa,mBAAmB,kBAChC;kBAEF,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAI,WAAW,CAAC,qCAAqC,EAAE,aAAa,cAAc,aAAa;8CAC9F,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,IAAI;wCACJ,WAAU;wCACV,QAAQ;;;;;;;;;;;8CAGZ,8OAAC;;sDACC,8OAAC;4CAAK,WAAU;sDAAsD;;;;;;sDACtE,8OAAC;4CAAK,WAAU;sDAA+C;;;;;;;;;;;;;;;;;;sCAKnE,8OAAC;4BACC,WAAU;4BACV,SAAS;4BACT,cAAW;4BACX,iBAAe;sCAEf,cAAA,8OAAC;gCACC,OAAM;gCACN,MAAK;gCACL,SAAQ;gCACR,QAAO;gCACP,WAAU;0CAET,2BACC,8OAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,aAAa;oCACb,GAAE;;;;;yDAGJ,8OAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,aAAa;oCACb,GAAE;;;;;;;;;;;;;;;;sCAOV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAQ,MAAK;8CAAI;;;;;;8CAClB,8OAAC;oCAAQ,MAAK;8CAAS;;;;;;8CACvB,8OAAC;oCAAQ,MAAK;8CAAQ;;;;;;8CACtB,8OAAC;oCAAQ,MAAK;8CAAQ;;;;;;8CACtB,8OAAC;oCAAQ,MAAK;8CAAU;;;;;;8CACxB,8OAAC;oCAAQ,MAAK;8CAAY;;;;;;8CAC1B,8OAAC;oCAAQ,MAAK;8CAAW;;;;;;8CACzB,8OAAC;oCAAQ,MAAK;8CAAW;;;;;;;;;;;;;;;;;;8BAK7B,8OAAC;oBACC,WAAW,CAAC,kEAAkE,EAC5E,aAAa,yBAAyB,qBACtC;8BAEF,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAc,MAAK;gCAAI,SAAS,IAAM,cAAc;0CAAQ;;;;;;0CAC7D,8OAAC;gCAAc,MAAK;gCAAS,SAAS,IAAM,cAAc;0CAAQ;;;;;;0CAClE,8OAAC;gCAAc,MAAK;gCAAQ,SAAS,IAAM,cAAc;0CAAQ;;;;;;0CACjE,8OAAC;gCAAc,MAAK;gCAAQ,SAAS,IAAM,cAAc;0CAAQ;;;;;;0CACjE,8OAAC;gCAAc,MAAK;gCAAU,SAAS,IAAM,cAAc;0CAAQ;;;;;;0CACnE,8OAAC;gCAAc,MAAK;gCAAY,SAAS,IAAM,cAAc;0CAAQ;;;;;;0CACrE,8OAAC;gCAAc,MAAK;gCAAW,SAAS,IAAM,cAAc;0CAAQ;;;;;;0CACpE,8OAAC;gCAAc,MAAK;gCAAW,SAAS,IAAM,cAAc;0CAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhF;AAEA,oCAAoC;AACpC,SAAS,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAA+C;IAC9E,qBACE,8OAAC,4JAAA,CAAA,UAAI;QACH,MAAM;QACN,WAAU;kBAET;;;;;;AAGP;AAEA,mCAAmC;AACnC,SAAS,cAAc,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAI/C;IACC,qBACE,8OAAC,4JAAA,CAAA,UAAI;QACH,MAAM;QACN,WAAU;QACV,SAAS;kBAER;;;;;;AAGP"}}, {"offset": {"line": 349, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 355, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/ParticleSection.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useCallback } from 'react';\nimport Particles from '@tsparticles/react';\nimport type { Engine, ISourceOptions } from '@tsparticles/engine'; // Changed import source, removed Container\n// import { loadLinksPreset } from 'tsparticles-preset-links'; // A common preset - Removing to simplify\n\ninterface ParticleSectionProps {\n  children: React.ReactNode;\n  className?: string;\n  particlesOptions?: ISourceOptions; // Allow custom options\n}\n\nconst ParticleSection: React.FC<ParticleSectionProps> = ({\n  children,\n  className = '',\n  particlesOptions,\n}) => {\n  const customInit = useCallback(async (_engine: Engine) => {\n    // console.log(engine); // For debugging if needed\n    // Engine is available, presets can be loaded here if needed,\n    // but for now, we'll define all options directly.\n    // await loadLinksPreset(engine); \n  }, []);\n  // const particlesLoaded = useCallback(async (_container?: Container) => { \n  //   // await console.log(_container); \n  // }, []);\n\n  const defaultOptions: ISourceOptions = {\n    // preset: 'links', // Removing preset and defining manually \n    background: {\n      color: {\n        value: 'transparent', \n      },\n    },\n    particles: {\n      color: {\n        value: '#cccccc', \n      },\n      links: {\n        color: '#dddddd', \n        distance: 150,\n        enable: true,\n        opacity: 0.2, \n        width: 1,\n      },\n      move: {\n        direction: 'none',\n        enable: true,\n        outModes: {\n          default: 'bounce',\n        },\n        random: false,\n        speed: 0.5, \n        straight: false,\n      },\n      number: {\n        density: {\n          enable: true,\n          // area: 1000, // 'area' might be deprecated or part of the preset\n        },\n        value: 30, \n      },\n      opacity: {\n        value: 0.3, // Make particles subtle\n      },\n      shape: {\n        type: 'circle',\n      },\n      size: {\n        value: { min: 1, max: 3 }, // Small particles\n      },\n    },\n    detectRetina: true,\n  };\n\n  const options = particlesOptions || defaultOptions;\n\n  return (\n    <div className={`relative ${className}`}>\n      <Particles\n        id={`tsparticles-${Math.random().toString(36).substring(7)}`} // Unique ID for each instance\n        // init={customInit} // Temporarily removed to test type issue\n        // loaded={particlesLoaded} // Use if you need to interact with the container\n        options={options}\n        className=\"absolute inset-0 z-0\" // Position behind content\n      />\n      <div className=\"relative z-10\">{children}</div> {/* Content on top */}\n    </div>\n  );\n};\n\nexport default ParticleSection;"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAaA,MAAM,kBAAkD,CAAC,EACvD,QAAQ,EACR,YAAY,EAAE,EACd,gBAAgB,EACjB;IACC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;IACpC,kDAAkD;IAClD,6DAA6D;IAC7D,kDAAkD;IAClD,kCAAkC;IACpC,GAAG,EAAE;IACL,2EAA2E;IAC3E,uCAAuC;IACvC,UAAU;IAEV,MAAM,iBAAiC;QACrC,6DAA6D;QAC7D,YAAY;YACV,OAAO;gBACL,OAAO;YACT;QACF;QACA,WAAW;YACT,OAAO;gBACL,OAAO;YACT;YACA,OAAO;gBACL,OAAO;gBACP,UAAU;gBACV,QAAQ;gBACR,SAAS;gBACT,OAAO;YACT;YACA,MAAM;gBACJ,WAAW;gBACX,QAAQ;gBACR,UAAU;oBACR,SAAS;gBACX;gBACA,QAAQ;gBACR,OAAO;gBACP,UAAU;YACZ;YACA,QAAQ;gBACN,SAAS;oBACP,QAAQ;gBAEV;gBACA,OAAO;YACT;YACA,SAAS;gBACP,OAAO;YACT;YACA,OAAO;gBACL,MAAM;YACR;YACA,MAAM;gBACJ,OAAO;oBAAE,KAAK;oBAAG,KAAK;gBAAE;YAC1B;QACF;QACA,cAAc;IAChB;IAEA,MAAM,UAAU,oBAAoB;IAEpC,qBACE,8OAAC;QAAI,WAAW,CAAC,SAAS,EAAE,WAAW;;0BACrC,8OAAC,2JAAA,CAAA,UAAS;gBACR,IAAI,CAAC,YAAY,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,IAAI;gBAC5D,8DAA8D;gBAC9D,6EAA6E;gBAC7E,SAAS;gBACT,WAAU,uBAAuB,0BAA0B;;;;;;0BAE7D,8OAAC;gBAAI,WAAU;0BAAiB;;;;;;YAAe;;;;;;;AAGrD;uCAEe"}}, {"offset": {"line": 457, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}