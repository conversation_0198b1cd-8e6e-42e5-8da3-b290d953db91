{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/Header.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport Image from 'next/image'\nimport { useState, useEffect } from 'react'\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false)\n  const [isScrolled, setIsScrolled] = useState(false)\n\n  // Handle scroll effect for sticky header\n  useEffect(() => {\n    const handleScroll = () => {\n      if (window.scrollY > 10) {\n        setIsScrolled(true)\n      } else {\n        setIsScrolled(false)\n      }\n    }\n\n    window.addEventListener('scroll', handleScroll)\n    return () => window.removeEventListener('scroll', handleScroll)\n  }, [])\n\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen)\n  }\n\n  return (\n    <header\n      className={`bg-white sticky top-0 z-50 transition-all duration-200 ${\n        isScrolled ? 'shadow-lg py-2' : 'shadow-md py-3'\n      }`}\n    >\n      <div className=\"container\">\n        <div className=\"flex justify-between items-center\">\n          <Link href=\"/\" className=\"flex items-center space-x-3\">\n            <div className={`relative transition-all duration-200 ${isScrolled ? 'w-10 h-10' : 'w-12 h-12'}`}>\n              <Image\n                src=\"/logo.png\"\n                alt=\"Northern Nepalese United FC Logo\"\n                fill\n                className=\"object-contain\"\n                priority\n              />\n            </div>\n            <div>\n              <span className=\"text-xl font-bold text-primary-800 hidden sm:inline\">Northern Nepalese United FC</span>\n              <span className=\"text-xl font-bold text-primary-800 sm:hidden\">NNUFC</span>\n            </div>\n          </Link>\n\n          {/* Mobile menu button */}\n          <button\n            className=\"md:hidden p-2 text-primary-800 focus:outline-none focus:ring-2 focus:ring-primary-400 rounded-lg\"\n            onClick={toggleMenu}\n            aria-label=\"Toggle menu\"\n            aria-expanded={isMenuOpen}\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke=\"currentColor\"\n              className=\"h-6 w-6\"\n            >\n              {isMenuOpen ? (\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M6 18L18 6M6 6l12 12\"\n                />\n              ) : (\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M4 6h16M4 12h16M4 18h16\"\n                />\n              )}\n            </svg>\n          </button>\n\n          {/* Desktop navigation */}\n          <nav className=\"hidden md:flex space-x-1 lg:space-x-6\">\n            <NavLink href=\"/\">Home</NavLink>\n            <NavLink href=\"/about\">About</NavLink>\n            <NavLink href=\"/team\">Team</NavLink>\n            <NavLink href=\"/news\">News</NavLink>\n            <NavLink href=\"/events\">Events</NavLink>\n            <NavLink href=\"/sponsors\">Sponsors</NavLink>\n            <NavLink href=\"/gallery\">Gallery</NavLink>\n            <NavLink href=\"/contact\">Contact</NavLink>\n          </nav>\n        </div>\n\n        {/* Mobile navigation - slide down animation */}\n        <div\n          className={`md:hidden overflow-hidden transition-all duration-300 ease-in-out ${\n            isMenuOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'\n          }`}\n        >\n          <nav className=\"mt-4 space-y-2 border-t border-neutral-200 pt-3\">\n            <MobileNavLink href=\"/\" onClick={() => setIsMenuOpen(false)}>Home</MobileNavLink>\n            <MobileNavLink href=\"/about\" onClick={() => setIsMenuOpen(false)}>About</MobileNavLink>\n            <MobileNavLink href=\"/team\" onClick={() => setIsMenuOpen(false)}>Team</MobileNavLink>\n            <MobileNavLink href=\"/news\" onClick={() => setIsMenuOpen(false)}>News</MobileNavLink>\n            <MobileNavLink href=\"/events\" onClick={() => setIsMenuOpen(false)}>Events</MobileNavLink>\n            <MobileNavLink href=\"/sponsors\" onClick={() => setIsMenuOpen(false)}>Sponsors</MobileNavLink>\n            <MobileNavLink href=\"/gallery\" onClick={() => setIsMenuOpen(false)}>Gallery</MobileNavLink>\n            <MobileNavLink href=\"/contact\" onClick={() => setIsMenuOpen(false)}>Contact</MobileNavLink>\n          </nav>\n        </div>\n      </div>\n    </header>\n  )\n}\n\n// Desktop Navigation Link Component\nfunction NavLink({ href, children }: { href: string; children: React.ReactNode }) {\n  return (\n    <Link\n      href={href}\n      className=\"px-3 py-2 rounded-lg font-medium text-primary-700 transition-colors hover:bg-primary-50 hover:text-primary-900\"\n    >\n      {children}\n    </Link>\n  )\n}\n\n// Mobile Navigation Link Component\nfunction MobileNavLink({ href, onClick, children }: {\n  href: string;\n  onClick: () => void;\n  children: React.ReactNode\n}) {\n  return (\n    <Link\n      href={href}\n      className=\"block py-2 px-1 text-primary-700 hover:bg-primary-50 hover:text-primary-900 rounded-lg font-medium transition-colors\"\n      onClick={onClick}\n    >\n      {children}\n    </Link>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;iDAAe;oBACnB,IAAI,OAAO,OAAO,GAAG,IAAI;wBACvB,cAAc;oBAChB,OAAO;wBACL,cAAc;oBAChB;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,MAAM,aAAa;QACjB,cAAc,CAAC;IACjB;IAEA,qBACE,6LAAC;QACC,WAAW,CAAC,uDAAuD,EACjE,aAAa,mBAAmB,kBAChC;kBAEF,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC;oCAAI,WAAW,CAAC,qCAAqC,EAAE,aAAa,cAAc,aAAa;8CAC9F,cAAA,6LAAC,gIAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,IAAI;wCACJ,WAAU;wCACV,QAAQ;;;;;;;;;;;8CAGZ,6LAAC;;sDACC,6LAAC;4CAAK,WAAU;sDAAsD;;;;;;sDACtE,6LAAC;4CAAK,WAAU;sDAA+C;;;;;;;;;;;;;;;;;;sCAKnE,6LAAC;4BACC,WAAU;4BACV,SAAS;4BACT,cAAW;4BACX,iBAAe;sCAEf,cAAA,6LAAC;gCACC,OAAM;gCACN,MAAK;gCACL,SAAQ;gCACR,QAAO;gCACP,WAAU;0CAET,2BACC,6LAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,aAAa;oCACb,GAAE;;;;;yDAGJ,6LAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,aAAa;oCACb,GAAE;;;;;;;;;;;;;;;;sCAOV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAQ,MAAK;8CAAI;;;;;;8CAClB,6LAAC;oCAAQ,MAAK;8CAAS;;;;;;8CACvB,6LAAC;oCAAQ,MAAK;8CAAQ;;;;;;8CACtB,6LAAC;oCAAQ,MAAK;8CAAQ;;;;;;8CACtB,6LAAC;oCAAQ,MAAK;8CAAU;;;;;;8CACxB,6LAAC;oCAAQ,MAAK;8CAAY;;;;;;8CAC1B,6LAAC;oCAAQ,MAAK;8CAAW;;;;;;8CACzB,6LAAC;oCAAQ,MAAK;8CAAW;;;;;;;;;;;;;;;;;;8BAK7B,6LAAC;oBACC,WAAW,CAAC,kEAAkE,EAC5E,aAAa,yBAAyB,qBACtC;8BAEF,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAc,MAAK;gCAAI,SAAS,IAAM,cAAc;0CAAQ;;;;;;0CAC7D,6LAAC;gCAAc,MAAK;gCAAS,SAAS,IAAM,cAAc;0CAAQ;;;;;;0CAClE,6LAAC;gCAAc,MAAK;gCAAQ,SAAS,IAAM,cAAc;0CAAQ;;;;;;0CACjE,6LAAC;gCAAc,MAAK;gCAAQ,SAAS,IAAM,cAAc;0CAAQ;;;;;;0CACjE,6LAAC;gCAAc,MAAK;gCAAU,SAAS,IAAM,cAAc;0CAAQ;;;;;;0CACnE,6LAAC;gCAAc,MAAK;gCAAY,SAAS,IAAM,cAAc;0CAAQ;;;;;;0CACrE,6LAAC;gCAAc,MAAK;gCAAW,SAAS,IAAM,cAAc;0CAAQ;;;;;;0CACpE,6LAAC;gCAAc,MAAK;gCAAW,SAAS,IAAM,cAAc;0CAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhF;GA/GwB;KAAA;AAiHxB,oCAAoC;AACpC,SAAS,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAA+C;IAC9E,qBACE,6LAAC,+JAAA,CAAA,UAAI;QACH,MAAM;QACN,WAAU;kBAET;;;;;;AAGP;MATS;AAWT,mCAAmC;AACnC,SAAS,cAAc,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAI/C;IACC,qBACE,6LAAC,+JAAA,CAAA,UAAI;QACH,MAAM;QACN,WAAU;QACV,SAAS;kBAER;;;;;;AAGP;MAdS"}}, {"offset": {"line": 360, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 366, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/ParticleSection.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useRef } from 'react';\nimport Script from 'next/script';\n\ninterface ParticleSectionProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nconst ParticleSection: React.FC<ParticleSectionProps> = ({ \n  children, \n  className = '' \n}) => {\n  const particlesContainerRef = useRef<HTMLDivElement>(null);\n  const uniqueId = useRef(`particles-${Math.random().toString(36).substring(2, 9)}`);\n  \n  useEffect(() => {\n    let isMounted = true;\n    \n    const initParticles = async () => {\n      if (!particlesContainerRef.current) return;\n      \n      // Check if tsParticles is loaded\n      const checkAndInitParticles = async () => {\n        if (typeof window !== 'undefined' && window.tsParticles) {\n          try {\n            await window.tsParticles.load(uniqueId.current, {\n              fullScreen: { enable: false },\n              background: {\n                color: {\n                  value: \"transparent\",\n                }\n              },\n              particles: {\n                color: { \n                  value: \"#ffffff\"  // White particles\n                },\n                links: {\n                  color: \"#ffffff\",  // White links\n                  distance: 150,\n                  enable: true,\n                  opacity: 0.5,  // Semi-transparent\n                  width: 1,\n                },\n                move: {\n                  enable: true,\n                  speed: 0.6,  // Gentle movement\n                },\n                number: {\n                  value: 50,  // Number of particles\n                  density: {\n                    enable: true,\n                    value_area: 800,\n                  }\n                },\n                opacity: {\n                  value: 0.5,  // Semi-transparent particles\n                },\n                size: {\n                  value: { min: 1, max: 3 },  // Particle size range\n                }\n              },\n              detectRetina: true\n            });\n          } catch (err) {\n            console.error('Failed to initialize particles:', err);\n          }\n        } else if (isMounted) {\n          setTimeout(checkAndInitParticles, 500);\n        }\n      };\n      \n      checkAndInitParticles();\n    };\n    \n    // Add tsParticles global type definition\n    if (typeof window !== 'undefined') {\n      window.tsParticles = window.tsParticles || {};\n    }\n    \n    const timer = setTimeout(() => {\n      initParticles();\n    }, 1000);\n    \n    return () => {\n      isMounted = false;\n      clearTimeout(timer);\n    };\n  }, []);\n\n  return (\n    <div className={`relative overflow-hidden ${className}`}>\n      {/* Load the particles script */}\n      <Script \n        id=\"tsparticles-script\"\n        src=\"https://cdn.jsdelivr.net/npm/tsparticles@2/tsparticles.bundle.min.js\"\n        strategy=\"afterInteractive\"\n        onLoad={() => console.log('Particles script loaded')}\n        onError={() => console.error('Failed to load particles script')}\n      />\n      \n      {/* The container for particles */}\n      <div \n        id={uniqueId.current} \n        ref={particlesContainerRef}\n        className=\"absolute inset-0 z-0\"\n        style={{ \n          pointerEvents: 'none',\n          width: '100%',\n          height: '100%'\n        }}\n      />\n      \n      {/* Content goes on top of particles */}\n      <div className=\"relative z-10\">\n        {children}\n      </div>\n    </div>\n  );\n};\n\n// Add global type declaration for tsParticles\ndeclare global {\n  interface Window {\n    tsParticles: any;\n  }\n}\n\nexport default ParticleSection;"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAUA,MAAM,kBAAkD,CAAC,EACvD,QAAQ,EACR,YAAY,EAAE,EACf;;IACC,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IACrD,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,CAAC,UAAU,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,IAAI;IAEjF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,YAAY;YAEhB,MAAM;2DAAgB;oBACpB,IAAI,CAAC,sBAAsB,OAAO,EAAE;oBAEpC,iCAAiC;oBACjC,MAAM;yFAAwB;4BAC5B,IAAI,aAAkB,eAAe,OAAO,WAAW,EAAE;gCACvD,IAAI;oCACF,MAAM,OAAO,WAAW,CAAC,IAAI,CAAC,SAAS,OAAO,EAAE;wCAC9C,YAAY;4CAAE,QAAQ;wCAAM;wCAC5B,YAAY;4CACV,OAAO;gDACL,OAAO;4CACT;wCACF;wCACA,WAAW;4CACT,OAAO;gDACL,OAAO,UAAW,kBAAkB;4CACtC;4CACA,OAAO;gDACL,OAAO;gDACP,UAAU;gDACV,QAAQ;gDACR,SAAS;gDACT,OAAO;4CACT;4CACA,MAAM;gDACJ,QAAQ;gDACR,OAAO;4CACT;4CACA,QAAQ;gDACN,OAAO;gDACP,SAAS;oDACP,QAAQ;oDACR,YAAY;gDACd;4CACF;4CACA,SAAS;gDACP,OAAO;4CACT;4CACA,MAAM;gDACJ,OAAO;oDAAE,KAAK;oDAAG,KAAK;gDAAE;4CAC1B;wCACF;wCACA,cAAc;oCAChB;gCACF,EAAE,OAAO,KAAK;oCACZ,QAAQ,KAAK,CAAC,mCAAmC;gCACnD;4BACF,OAAO,IAAI,WAAW;gCACpB,WAAW,uBAAuB;4BACpC;wBACF;;oBAEA;gBACF;;YAEA,yCAAyC;YACzC,wCAAmC;gBACjC,OAAO,WAAW,GAAG,OAAO,WAAW,IAAI,CAAC;YAC9C;YAEA,MAAM,QAAQ;mDAAW;oBACvB;gBACF;kDAAG;YAEH;6CAAO;oBACL,YAAY;oBACZ,aAAa;gBACf;;QACF;oCAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAW,CAAC,yBAAyB,EAAE,WAAW;;0BAErD,6LAAC,iIAAA,CAAA,UAAM;gBACL,IAAG;gBACH,KAAI;gBACJ,UAAS;gBACT,QAAQ,IAAM,QAAQ,GAAG,CAAC;gBAC1B,SAAS,IAAM,QAAQ,KAAK,CAAC;;;;;;0BAI/B,6LAAC;gBACC,IAAI,SAAS,OAAO;gBACpB,KAAK;gBACL,WAAU;gBACV,OAAO;oBACL,eAAe;oBACf,OAAO;oBACP,QAAQ;gBACV;;;;;;0BAIF,6LAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAIT;GA9GM;KAAA;uCAuHS"}}, {"offset": {"line": 514, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}