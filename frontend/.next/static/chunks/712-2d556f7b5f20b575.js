(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[712],{3836:(e,t)=>{"use strict";t.byteLength=function(e){var t=u(e),r=t[0],n=t[1];return(r+n)*3/4-n},t.toByteArray=function(e){var t,r,s=u(e),o=s[0],a=s[1],c=new i((o+a)*3/4-a),l=0,f=a>0?o-4:o;for(r=0;r<f;r+=4)t=n[e.charCodeAt(r)]<<18|n[e.charCodeAt(r+1)]<<12|n[e.charCodeAt(r+2)]<<6|n[e.charCodeAt(r+3)],c[l++]=t>>16&255,c[l++]=t>>8&255,c[l++]=255&t;return 2===a&&(t=n[e.charCodeAt(r)]<<2|n[e.charCodeAt(r+1)]>>4,c[l++]=255&t),1===a&&(t=n[e.charCodeAt(r)]<<10|n[e.charCodeAt(r+1)]<<4|n[e.charCodeAt(r+2)]>>2,c[l++]=t>>8&255,c[l++]=255&t),c},t.fromByteArray=function(e){for(var t,n=e.length,i=n%3,s=[],o=0,a=n-i;o<a;o+=16383)s.push(function(e,t,n){for(var i,s=[],o=t;o<n;o+=3)s.push(r[(i=(e[o]<<16&0xff0000)+(e[o+1]<<8&65280)+(255&e[o+2]))>>18&63]+r[i>>12&63]+r[i>>6&63]+r[63&i]);return s.join("")}(e,o,o+16383>a?a:o+16383));return 1===i?s.push(r[(t=e[n-1])>>2]+r[t<<4&63]+"=="):2===i&&s.push(r[(t=(e[n-2]<<8)+e[n-1])>>10]+r[t>>4&63]+r[t<<2&63]+"="),s.join("")};for(var r=[],n=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",o=0,a=s.length;o<a;++o)r[o]=s[o],n[s.charCodeAt(o)]=o;function u(e){var t=e.length;if(t%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");-1===r&&(r=t);var n=r===t?0:4-r%4;return[r,n]}n["-".charCodeAt(0)]=62,n["_".charCodeAt(0)]=63},5927:(e,t,r)=>{"use strict";var n=r(3836),i=r(4981),s="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function o(e){if(e>0x7fffffff)throw RangeError('The value "'+e+'" is invalid for option "size"');var t=new Uint8Array(e);return Object.setPrototypeOf(t,a.prototype),t}function a(e,t,r){if("number"==typeof e){if("string"==typeof t)throw TypeError('The "string" argument must be of type string. Received type number');return l(e)}return u(e,t,r)}function u(e,t,r){if("string"==typeof e)return function(e,t){if(("string"!=typeof t||""===t)&&(t="utf8"),!a.isEncoding(t))throw TypeError("Unknown encoding: "+t);var r=0|d(e,t),n=o(r),i=n.write(e,t);return i!==r&&(n=n.slice(0,i)),n}(e,t);if(ArrayBuffer.isView(e))return function(e){if(k(e,Uint8Array)){var t=new Uint8Array(e);return h(t.buffer,t.byteOffset,t.byteLength)}return f(e)}(e);if(null==e)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e);if(k(e,ArrayBuffer)||e&&k(e.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(k(e,SharedArrayBuffer)||e&&k(e.buffer,SharedArrayBuffer)))return h(e,t,r);if("number"==typeof e)throw TypeError('The "value" argument must not be of type number. Received type number');var n=e.valueOf&&e.valueOf();if(null!=n&&n!==e)return a.from(n,t,r);var i=function(e){if(a.isBuffer(e)){var t,r=0|p(e.length),n=o(r);return 0===n.length||e.copy(n,0,0,r),n}return void 0!==e.length?"number"!=typeof e.length||(t=e.length)!=t?o(0):f(e):"Buffer"===e.type&&Array.isArray(e.data)?f(e.data):void 0}(e);if(i)return i;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof e[Symbol.toPrimitive])return a.from(e[Symbol.toPrimitive]("string"),t,r);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e)}function c(e){if("number"!=typeof e)throw TypeError('"size" argument must be of type number');if(e<0)throw RangeError('The value "'+e+'" is invalid for option "size"')}function l(e){return c(e),o(e<0?0:0|p(e))}function f(e){for(var t=e.length<0?0:0|p(e.length),r=o(t),n=0;n<t;n+=1)r[n]=255&e[n];return r}function h(e,t,r){var n;if(t<0||e.byteLength<t)throw RangeError('"offset" is outside of buffer bounds');if(e.byteLength<t+(r||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(n=void 0===t&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,t):new Uint8Array(e,t,r),a.prototype),n}function p(e){if(e>=0x7fffffff)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|e}function d(e,t){if(a.isBuffer(e))return e.length;if(ArrayBuffer.isView(e)||k(e,ArrayBuffer))return e.byteLength;if("string"!=typeof e)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof e);var r=e.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;for(var i=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return C(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return I(e).length;default:if(i)return n?-1:C(e).length;t=(""+t).toLowerCase(),i=!0}}function y(e,t,r){var i,s,o=!1;if((void 0===t||t<0)&&(t=0),t>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0||(r>>>=0)<=(t>>>=0)))return"";for(e||(e="utf8");;)switch(e){case"hex":return function(e,t,r){var n=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>n)&&(r=n);for(var i="",s=t;s<r;++s)i+=$[e[s]];return i}(this,t,r);case"utf8":case"utf-8":return b(this,t,r);case"ascii":return function(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(127&e[i]);return n}(this,t,r);case"latin1":case"binary":return function(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(e[i]);return n}(this,t,r);case"base64":return i=t,s=r,0===i&&s===this.length?n.fromByteArray(this):n.fromByteArray(this.slice(i,s));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(e,t,r){for(var n=e.slice(t,r),i="",s=0;s<n.length-1;s+=2)i+=String.fromCharCode(n[s]+256*n[s+1]);return i}(this,t,r);default:if(o)throw TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),o=!0}}function g(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}function v(e,t,r,n,i){var s;if(0===e.length)return -1;if("string"==typeof r?(n=r,r=0):r>0x7fffffff?r=0x7fffffff:r<-0x80000000&&(r=-0x80000000),(s=r=+r)!=s&&(r=i?0:e.length-1),r<0&&(r=e.length+r),r>=e.length){if(i)return -1;r=e.length-1}else if(r<0){if(!i)return -1;r=0}if("string"==typeof t&&(t=a.from(t,n)),a.isBuffer(t))return 0===t.length?-1:m(e,t,r,n,i);if("number"==typeof t)return(t&=255,"function"==typeof Uint8Array.prototype.indexOf)?i?Uint8Array.prototype.indexOf.call(e,t,r):Uint8Array.prototype.lastIndexOf.call(e,t,r):m(e,[t],r,n,i);throw TypeError("val must be string, number or Buffer")}function m(e,t,r,n,i){var s,o=1,a=e.length,u=t.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(e.length<2||t.length<2)return -1;o=2,a/=2,u/=2,r/=2}function c(e,t){return 1===o?e[t]:e.readUInt16BE(t*o)}if(i){var l=-1;for(s=r;s<a;s++)if(c(e,s)===c(t,-1===l?0:s-l)){if(-1===l&&(l=s),s-l+1===u)return l*o}else -1!==l&&(s-=s-l),l=-1}else for(r+u>a&&(r=a-u),s=r;s>=0;s--){for(var f=!0,h=0;h<u;h++)if(c(e,s+h)!==c(t,h)){f=!1;break}if(f)return s}return -1}function b(e,t,r){r=Math.min(e.length,r);for(var n=[],i=t;i<r;){var s,o,a,u,c=e[i],l=null,f=c>239?4:c>223?3:c>191?2:1;if(i+f<=r)switch(f){case 1:c<128&&(l=c);break;case 2:(192&(s=e[i+1]))==128&&(u=(31&c)<<6|63&s)>127&&(l=u);break;case 3:s=e[i+1],o=e[i+2],(192&s)==128&&(192&o)==128&&(u=(15&c)<<12|(63&s)<<6|63&o)>2047&&(u<55296||u>57343)&&(l=u);break;case 4:s=e[i+1],o=e[i+2],a=e[i+3],(192&s)==128&&(192&o)==128&&(192&a)==128&&(u=(15&c)<<18|(63&s)<<12|(63&o)<<6|63&a)>65535&&u<1114112&&(l=u)}null===l?(l=65533,f=1):l>65535&&(l-=65536,n.push(l>>>10&1023|55296),l=56320|1023&l),n.push(l),i+=f}return function(e){var t=e.length;if(t<=4096)return String.fromCharCode.apply(String,e);for(var r="",n=0;n<t;)r+=String.fromCharCode.apply(String,e.slice(n,n+=4096));return r}(n)}function w(e,t,r){if(e%1!=0||e<0)throw RangeError("offset is not uint");if(e+t>r)throw RangeError("Trying to access beyond buffer length")}function _(e,t,r,n,i,s){if(!a.isBuffer(e))throw TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<s)throw RangeError('"value" argument is out of bounds');if(r+n>e.length)throw RangeError("Index out of range")}function x(e,t,r,n,i,s){if(r+n>e.length||r<0)throw RangeError("Index out of range")}function E(e,t,r,n,s){return t=+t,r>>>=0,s||x(e,t,r,4,34028234663852886e22,-34028234663852886e22),i.write(e,t,r,n,23,4),r+4}function S(e,t,r,n,s){return t=+t,r>>>=0,s||x(e,t,r,8,17976931348623157e292,-17976931348623157e292),i.write(e,t,r,n,52,8),r+8}t.hp=a,t.IS=50,a.TYPED_ARRAY_SUPPORT=function(){try{var e=new Uint8Array(1),t={foo:function(){return 42}};return Object.setPrototypeOf(t,Uint8Array.prototype),Object.setPrototypeOf(e,t),42===e.foo()}catch(e){return!1}}(),a.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(a.prototype,"parent",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.buffer}}),Object.defineProperty(a.prototype,"offset",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.byteOffset}}),a.poolSize=8192,a.from=function(e,t,r){return u(e,t,r)},Object.setPrototypeOf(a.prototype,Uint8Array.prototype),Object.setPrototypeOf(a,Uint8Array),a.alloc=function(e,t,r){return(c(e),e<=0)?o(e):void 0!==t?"string"==typeof r?o(e).fill(t,r):o(e).fill(t):o(e)},a.allocUnsafe=function(e){return l(e)},a.allocUnsafeSlow=function(e){return l(e)},a.isBuffer=function(e){return null!=e&&!0===e._isBuffer&&e!==a.prototype},a.compare=function(e,t){if(k(e,Uint8Array)&&(e=a.from(e,e.offset,e.byteLength)),k(t,Uint8Array)&&(t=a.from(t,t.offset,t.byteLength)),!a.isBuffer(e)||!a.isBuffer(t))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(e===t)return 0;for(var r=e.length,n=t.length,i=0,s=Math.min(r,n);i<s;++i)if(e[i]!==t[i]){r=e[i],n=t[i];break}return r<n?-1:n<r?1:0},a.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},a.concat=function(e,t){if(!Array.isArray(e))throw TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return a.alloc(0);if(void 0===t)for(r=0,t=0;r<e.length;++r)t+=e[r].length;var r,n=a.allocUnsafe(t),i=0;for(r=0;r<e.length;++r){var s=e[r];if(k(s,Uint8Array))i+s.length>n.length?a.from(s).copy(n,i):Uint8Array.prototype.set.call(n,s,i);else if(a.isBuffer(s))s.copy(n,i);else throw TypeError('"list" argument must be an Array of Buffers');i+=s.length}return n},a.byteLength=d,a.prototype._isBuffer=!0,a.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)g(this,t,t+1);return this},a.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)g(this,t,t+3),g(this,t+1,t+2);return this},a.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)g(this,t,t+7),g(this,t+1,t+6),g(this,t+2,t+5),g(this,t+3,t+4);return this},a.prototype.toString=function(){var e=this.length;return 0===e?"":0==arguments.length?b(this,0,e):y.apply(this,arguments)},a.prototype.toLocaleString=a.prototype.toString,a.prototype.equals=function(e){if(!a.isBuffer(e))throw TypeError("Argument must be a Buffer");return this===e||0===a.compare(this,e)},a.prototype.inspect=function(){var e="",r=t.IS;return e=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(e+=" ... "),"<Buffer "+e+">"},s&&(a.prototype[s]=a.prototype.inspect),a.prototype.compare=function(e,t,r,n,i){if(k(e,Uint8Array)&&(e=a.from(e,e.offset,e.byteLength)),!a.isBuffer(e))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),t<0||r>e.length||n<0||i>this.length)throw RangeError("out of range index");if(n>=i&&t>=r)return 0;if(n>=i)return -1;if(t>=r)return 1;if(t>>>=0,r>>>=0,n>>>=0,i>>>=0,this===e)return 0;for(var s=i-n,o=r-t,u=Math.min(s,o),c=this.slice(n,i),l=e.slice(t,r),f=0;f<u;++f)if(c[f]!==l[f]){s=c[f],o=l[f];break}return s<o?-1:o<s?1:0},a.prototype.includes=function(e,t,r){return -1!==this.indexOf(e,t,r)},a.prototype.indexOf=function(e,t,r){return v(this,e,t,r,!0)},a.prototype.lastIndexOf=function(e,t,r){return v(this,e,t,r,!1)},a.prototype.write=function(e,t,r,n){if(void 0===t)n="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)n=t,r=this.length,t=0;else if(isFinite(t))t>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var i,s,o,a,u,c,l,f,h=this.length-t;if((void 0===r||r>h)&&(r=h),e.length>0&&(r<0||t<0)||t>this.length)throw RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var p=!1;;)switch(n){case"hex":return function(e,t,r,n){r=Number(r)||0;var i=e.length-r;n?(n=Number(n))>i&&(n=i):n=i;var s=t.length;n>s/2&&(n=s/2);for(var o=0;o<n;++o){var a=parseInt(t.substr(2*o,2),16);if(a!=a)break;e[r+o]=a}return o}(this,e,t,r);case"utf8":case"utf-8":return i=t,s=r,R(C(e,this.length-i),this,i,s);case"ascii":case"latin1":case"binary":return o=t,a=r,R(function(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}(e),this,o,a);case"base64":return u=t,c=r,R(I(e),this,u,c);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return l=t,f=r,R(function(e,t){for(var r,n,i=[],s=0;s<e.length&&!((t-=2)<0);++s)n=(r=e.charCodeAt(s))>>8,i.push(r%256),i.push(n);return i}(e,this.length-l),this,l,f);default:if(p)throw TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),p=!0}},a.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},a.prototype.slice=function(e,t){var r=this.length;e=~~e,t=void 0===t?r:~~t,e<0?(e+=r)<0&&(e=0):e>r&&(e=r),t<0?(t+=r)<0&&(t=0):t>r&&(t=r),t<e&&(t=e);var n=this.subarray(e,t);return Object.setPrototypeOf(n,a.prototype),n},a.prototype.readUintLE=a.prototype.readUIntLE=function(e,t,r){e>>>=0,t>>>=0,r||w(e,t,this.length);for(var n=this[e],i=1,s=0;++s<t&&(i*=256);)n+=this[e+s]*i;return n},a.prototype.readUintBE=a.prototype.readUIntBE=function(e,t,r){e>>>=0,t>>>=0,r||w(e,t,this.length);for(var n=this[e+--t],i=1;t>0&&(i*=256);)n+=this[e+--t]*i;return n},a.prototype.readUint8=a.prototype.readUInt8=function(e,t){return e>>>=0,t||w(e,1,this.length),this[e]},a.prototype.readUint16LE=a.prototype.readUInt16LE=function(e,t){return e>>>=0,t||w(e,2,this.length),this[e]|this[e+1]<<8},a.prototype.readUint16BE=a.prototype.readUInt16BE=function(e,t){return e>>>=0,t||w(e,2,this.length),this[e]<<8|this[e+1]},a.prototype.readUint32LE=a.prototype.readUInt32LE=function(e,t){return e>>>=0,t||w(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+0x1000000*this[e+3]},a.prototype.readUint32BE=a.prototype.readUInt32BE=function(e,t){return e>>>=0,t||w(e,4,this.length),0x1000000*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},a.prototype.readIntLE=function(e,t,r){e>>>=0,t>>>=0,r||w(e,t,this.length);for(var n=this[e],i=1,s=0;++s<t&&(i*=256);)n+=this[e+s]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*t)),n},a.prototype.readIntBE=function(e,t,r){e>>>=0,t>>>=0,r||w(e,t,this.length);for(var n=t,i=1,s=this[e+--n];n>0&&(i*=256);)s+=this[e+--n]*i;return s>=(i*=128)&&(s-=Math.pow(2,8*t)),s},a.prototype.readInt8=function(e,t){return(e>>>=0,t||w(e,1,this.length),128&this[e])?-((255-this[e]+1)*1):this[e]},a.prototype.readInt16LE=function(e,t){e>>>=0,t||w(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?0xffff0000|r:r},a.prototype.readInt16BE=function(e,t){e>>>=0,t||w(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?0xffff0000|r:r},a.prototype.readInt32LE=function(e,t){return e>>>=0,t||w(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},a.prototype.readInt32BE=function(e,t){return e>>>=0,t||w(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},a.prototype.readFloatLE=function(e,t){return e>>>=0,t||w(e,4,this.length),i.read(this,e,!0,23,4)},a.prototype.readFloatBE=function(e,t){return e>>>=0,t||w(e,4,this.length),i.read(this,e,!1,23,4)},a.prototype.readDoubleLE=function(e,t){return e>>>=0,t||w(e,8,this.length),i.read(this,e,!0,52,8)},a.prototype.readDoubleBE=function(e,t){return e>>>=0,t||w(e,8,this.length),i.read(this,e,!1,52,8)},a.prototype.writeUintLE=a.prototype.writeUIntLE=function(e,t,r,n){if(e=+e,t>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;_(this,e,t,r,i,0)}var s=1,o=0;for(this[t]=255&e;++o<r&&(s*=256);)this[t+o]=e/s&255;return t+r},a.prototype.writeUintBE=a.prototype.writeUIntBE=function(e,t,r,n){if(e=+e,t>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;_(this,e,t,r,i,0)}var s=r-1,o=1;for(this[t+s]=255&e;--s>=0&&(o*=256);)this[t+s]=e/o&255;return t+r},a.prototype.writeUint8=a.prototype.writeUInt8=function(e,t,r){return e=+e,t>>>=0,r||_(this,e,t,1,255,0),this[t]=255&e,t+1},a.prototype.writeUint16LE=a.prototype.writeUInt16LE=function(e,t,r){return e=+e,t>>>=0,r||_(this,e,t,2,65535,0),this[t]=255&e,this[t+1]=e>>>8,t+2},a.prototype.writeUint16BE=a.prototype.writeUInt16BE=function(e,t,r){return e=+e,t>>>=0,r||_(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=255&e,t+2},a.prototype.writeUint32LE=a.prototype.writeUInt32LE=function(e,t,r){return e=+e,t>>>=0,r||_(this,e,t,4,0xffffffff,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e,t+4},a.prototype.writeUint32BE=a.prototype.writeUInt32BE=function(e,t,r){return e=+e,t>>>=0,r||_(this,e,t,4,0xffffffff,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},a.prototype.writeIntLE=function(e,t,r,n){if(e=+e,t>>>=0,!n){var i=Math.pow(2,8*r-1);_(this,e,t,r,i-1,-i)}var s=0,o=1,a=0;for(this[t]=255&e;++s<r&&(o*=256);)e<0&&0===a&&0!==this[t+s-1]&&(a=1),this[t+s]=(e/o>>0)-a&255;return t+r},a.prototype.writeIntBE=function(e,t,r,n){if(e=+e,t>>>=0,!n){var i=Math.pow(2,8*r-1);_(this,e,t,r,i-1,-i)}var s=r-1,o=1,a=0;for(this[t+s]=255&e;--s>=0&&(o*=256);)e<0&&0===a&&0!==this[t+s+1]&&(a=1),this[t+s]=(e/o>>0)-a&255;return t+r},a.prototype.writeInt8=function(e,t,r){return e=+e,t>>>=0,r||_(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=255&e,t+1},a.prototype.writeInt16LE=function(e,t,r){return e=+e,t>>>=0,r||_(this,e,t,2,32767,-32768),this[t]=255&e,this[t+1]=e>>>8,t+2},a.prototype.writeInt16BE=function(e,t,r){return e=+e,t>>>=0,r||_(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=255&e,t+2},a.prototype.writeInt32LE=function(e,t,r){return e=+e,t>>>=0,r||_(this,e,t,4,0x7fffffff,-0x80000000),this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4},a.prototype.writeInt32BE=function(e,t,r){return e=+e,t>>>=0,r||_(this,e,t,4,0x7fffffff,-0x80000000),e<0&&(e=0xffffffff+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},a.prototype.writeFloatLE=function(e,t,r){return E(this,e,t,!0,r)},a.prototype.writeFloatBE=function(e,t,r){return E(this,e,t,!1,r)},a.prototype.writeDoubleLE=function(e,t,r){return S(this,e,t,!0,r)},a.prototype.writeDoubleBE=function(e,t,r){return S(this,e,t,!1,r)},a.prototype.copy=function(e,t,r,n){if(!a.isBuffer(e))throw TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),t>=e.length&&(t=e.length),t||(t=0),n>0&&n<r&&(n=r),n===r||0===e.length||0===this.length)return 0;if(t<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("Index out of range");if(n<0)throw RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);var i=n-r;return this===e&&"function"==typeof Uint8Array.prototype.copyWithin?this.copyWithin(t,r,n):Uint8Array.prototype.set.call(e,this.subarray(r,n),t),i},a.prototype.fill=function(e,t,r,n){if("string"==typeof e){if("string"==typeof t?(n=t,t=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw TypeError("encoding must be a string");if("string"==typeof n&&!a.isEncoding(n))throw TypeError("Unknown encoding: "+n);if(1===e.length){var i,s=e.charCodeAt(0);("utf8"===n&&s<128||"latin1"===n)&&(e=s)}}else"number"==typeof e?e&=255:"boolean"==typeof e&&(e=Number(e));if(t<0||this.length<t||this.length<r)throw RangeError("Out of range index");if(r<=t)return this;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(i=t;i<r;++i)this[i]=e;else{var o=a.isBuffer(e)?e:a.from(e,n),u=o.length;if(0===u)throw TypeError('The value "'+e+'" is invalid for argument "value"');for(i=0;i<r-t;++i)this[i+t]=o[i%u]}return this};var T=/[^+/0-9A-Za-z-_]/g;function C(e,t){t=t||1/0;for(var r,n=e.length,i=null,s=[],o=0;o<n;++o){if((r=e.charCodeAt(o))>55295&&r<57344){if(!i){if(r>56319||o+1===n){(t-=3)>-1&&s.push(239,191,189);continue}i=r;continue}if(r<56320){(t-=3)>-1&&s.push(239,191,189),i=r;continue}r=(i-55296<<10|r-56320)+65536}else i&&(t-=3)>-1&&s.push(239,191,189);if(i=null,r<128){if((t-=1)<0)break;s.push(r)}else if(r<2048){if((t-=2)<0)break;s.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;s.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((t-=4)<0)break;s.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return s}function I(e){return n.toByteArray(function(e){if((e=(e=e.split("=")[0]).trim().replace(T,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function R(e,t,r,n){for(var i=0;i<n&&!(i+r>=t.length)&&!(i>=e.length);++i)t[i+r]=e[i];return i}function k(e,t){return e instanceof t||null!=e&&null!=e.constructor&&null!=e.constructor.name&&e.constructor.name===t.name}var $=function(){for(var e="0123456789abcdef",t=Array(256),r=0;r<16;++r)for(var n=16*r,i=0;i<16;++i)t[n+i]=e[r]+e[i];return t}()},4981:(e,t)=>{t.read=function(e,t,r,n,i){var s,o,a=8*i-n-1,u=(1<<a)-1,c=u>>1,l=-7,f=r?i-1:0,h=r?-1:1,p=e[t+f];for(f+=h,s=p&(1<<-l)-1,p>>=-l,l+=a;l>0;s=256*s+e[t+f],f+=h,l-=8);for(o=s&(1<<-l)-1,s>>=-l,l+=n;l>0;o=256*o+e[t+f],f+=h,l-=8);if(0===s)s=1-c;else{if(s===u)return o?NaN:1/0*(p?-1:1);o+=Math.pow(2,n),s-=c}return(p?-1:1)*o*Math.pow(2,s-n)},t.write=function(e,t,r,n,i,s){var o,a,u,c=8*s-i-1,l=(1<<c)-1,f=l>>1,h=23===i?5960464477539062e-23:0,p=n?0:s-1,d=n?1:-1,y=t<0||0===t&&1/t<0?1:0;for(isNaN(t=Math.abs(t))||t===1/0?(a=isNaN(t)?1:0,o=l):(o=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-o))<1&&(o--,u*=2),o+f>=1?t+=h/u:t+=h*Math.pow(2,1-f),t*u>=2&&(o++,u/=2),o+f>=l?(a=0,o=l):o+f>=1?(a=(t*u-1)*Math.pow(2,i),o+=f):(a=t*Math.pow(2,f-1)*Math.pow(2,i),o=0));i>=8;e[r+p]=255&a,p+=d,a/=256,i-=8);for(o=o<<i|a,c+=i;c>0;e[r+p]=255&o,p+=d,o/=256,c-=8);e[r+p-d]|=128*y}},7711:(e,t,r)=>{"use strict";r.d(t,{default:()=>i.a});var n=r(1956),i=r.n(n)},6046:(e,t,r)=>{"use strict";var n=r(6658);r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}})},5828:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{callServer:function(){return n.callServer},createServerReference:function(){return s},findSourceMapURL:function(){return i.findSourceMapURL}});let n=r(9603),i=r(3355),s=r(4979).createServerReference},1956:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}});let n=r(306)._(r(580));function i(e,t){var r;let i={};"function"==typeof e&&(i.loader=e);let s={...i,...t};return(0,n.default)({...s,modules:null==(r=s.loadableGenerated)?void 0:r.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9827:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return i}});let n=r(3719);function i(e){let{reason:t,children:r}=e;if("undefined"==typeof window)throw new n.BailoutToCSRError(t);return r}},580:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return c}});let n=r(5155),i=r(2115),s=r(9827),o=r(9214);function a(e){return{default:e&&"default"in e?e.default:e}}let u={loader:()=>Promise.resolve(a(()=>null)),loading:null,ssr:!0},c=function(e){let t={...u,...e},r=(0,i.lazy)(()=>t.loader().then(a)),c=t.loading;function l(e){let a=c?(0,n.jsx)(c,{isLoading:!0,pastDelay:!0,error:null}):null,u=!t.ssr||!!t.loading,l=u?i.Suspense:i.Fragment,f=t.ssr?(0,n.jsxs)(n.Fragment,{children:["undefined"==typeof window?(0,n.jsx)(o.PreloadChunks,{moduleIds:t.modules}):null,(0,n.jsx)(r,{...e})]}):(0,n.jsx)(s.BailoutToCSR,{reason:"next/dynamic",children:(0,n.jsx)(r,{...e})});return(0,n.jsx)(l,{...u?{fallback:a}:{},children:f})}return l.displayName="LoadableComponent",l}},9214:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadChunks",{enumerable:!0,get:function(){return a}});let n=r(5155),i=r(7650),s=r(5861),o=r(8284);function a(e){let{moduleIds:t}=e;if("undefined"!=typeof window)return null;let r=s.workAsyncStorage.getStore();if(void 0===r)return null;let a=[];if(r.reactLoadableManifest&&t){let e=r.reactLoadableManifest;for(let r of t){if(!e[r])continue;let t=e[r].files;a.push(...t)}}return 0===a.length?null:(0,n.jsx)(n.Fragment,{children:a.map(e=>{let t=r.assetPrefix+"/_next/"+(0,o.encodeURIPath)(e);return e.endsWith(".css")?(0,n.jsx)("link",{precedence:"dynamic",href:t,rel:"stylesheet",as:"style"},e):((0,i.preload)(t,{as:"script",fetchPriority:"low"}),null)})})}},7279:(e,t,r)=>{"use strict";r.d(t,{c:()=>l});var n=r(3996),i=r(5160),s=r(3822),o=r(6019),a=r(6564),u=r(195),c=r(5110),l=function(){function e(e){e&&(this._subscribe=e)}return e.prototype.lift=function(t){var r=new e;return r.source=this,r.operator=t,r},e.prototype.subscribe=function(e,t,r){var s,o=this,a=(s=e)&&s instanceof n.vU||s&&(0,u.T)(s.next)&&(0,u.T)(s.error)&&(0,u.T)(s.complete)&&(0,i.Uv)(s)?e:new n.Ms(e,t,r);return(0,c.Y)(function(){var e=o.operator,t=o.source;a.add(e?e.call(a,t):t?o._subscribe(a):o._trySubscribe(a))}),a},e.prototype._trySubscribe=function(e){try{return this._subscribe(e)}catch(t){e.error(t)}},e.prototype.forEach=function(e,t){var r=this;return new(t=f(t))(function(t,i){var s=new n.Ms({next:function(t){try{e(t)}catch(e){i(e),s.unsubscribe()}},error:i,complete:t});r.subscribe(s)})},e.prototype._subscribe=function(e){var t;return null===(t=this.source)||void 0===t?void 0:t.subscribe(e)},e.prototype[s.s]=function(){return this},e.prototype.pipe=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return(0,o.m)(e)(this)},e.prototype.toPromise=function(e){var t=this;return new(e=f(e))(function(e,r){var n;t.subscribe(function(e){return n=e},function(e){return r(e)},function(){return e(n)})})},e.create=function(t){return new e(t)},e}();function f(e){var t;return null!==(t=null!=e?e:a.$.Promise)&&void 0!==t?t:Promise}},7659:(e,t,r)=>{"use strict";r.d(t,{m:()=>o});var n=r(6476),i=r(6677),s=r(1833),o=function(e){function t(t,r,n){void 0===t&&(t=1/0),void 0===r&&(r=1/0),void 0===n&&(n=s.U);var i=e.call(this)||this;return i._bufferSize=t,i._windowTime=r,i._timestampProvider=n,i._buffer=[],i._infiniteTimeWindow=!0,i._infiniteTimeWindow=r===1/0,i._bufferSize=Math.max(1,t),i._windowTime=Math.max(1,r),i}return(0,n.C6)(t,e),t.prototype.next=function(t){var r=this.isStopped,n=this._buffer,i=this._infiniteTimeWindow,s=this._timestampProvider,o=this._windowTime;!r&&(n.push(t),i||n.push(s.now()+o)),this._trimBuffer(),e.prototype.next.call(this,t)},t.prototype._subscribe=function(e){this._throwIfClosed(),this._trimBuffer();for(var t=this._innerSubscribe(e),r=this._infiniteTimeWindow,n=this._buffer.slice(),i=0;i<n.length&&!e.closed;i+=r?1:2)e.next(n[i]);return this._checkFinalizedStatuses(e),t},t.prototype._trimBuffer=function(){var e=this._bufferSize,t=this._timestampProvider,r=this._buffer,n=this._infiniteTimeWindow,i=(n?1:2)*e;if(e<1/0&&i<r.length&&r.splice(0,r.length-i),!n){for(var s=t.now(),o=0,a=1;a<r.length&&r[a]<=s;a+=2)o=a;o&&r.splice(0,o+1)}},t}(i.B)},6677:(e,t,r)=>{"use strict";r.d(t,{B:()=>c});var n=r(6476),i=r(7279),s=r(5160),o=(0,r(2985).L)(function(e){return function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"}}),a=r(2238),u=r(5110),c=function(e){function t(){var t=e.call(this)||this;return t.closed=!1,t.currentObservers=null,t.observers=[],t.isStopped=!1,t.hasError=!1,t.thrownError=null,t}return(0,n.C6)(t,e),t.prototype.lift=function(e){var t=new l(this,this);return t.operator=e,t},t.prototype._throwIfClosed=function(){if(this.closed)throw new o},t.prototype.next=function(e){var t=this;(0,u.Y)(function(){var r,i;if(t._throwIfClosed(),!t.isStopped){t.currentObservers||(t.currentObservers=Array.from(t.observers));try{for(var s=(0,n.Ju)(t.currentObservers),o=s.next();!o.done;o=s.next())o.value.next(e)}catch(e){r={error:e}}finally{try{o&&!o.done&&(i=s.return)&&i.call(s)}finally{if(r)throw r.error}}}})},t.prototype.error=function(e){var t=this;(0,u.Y)(function(){if(t._throwIfClosed(),!t.isStopped){t.hasError=t.isStopped=!0,t.thrownError=e;for(var r=t.observers;r.length;)r.shift().error(e)}})},t.prototype.complete=function(){var e=this;(0,u.Y)(function(){if(e._throwIfClosed(),!e.isStopped){e.isStopped=!0;for(var t=e.observers;t.length;)t.shift().complete()}})},t.prototype.unsubscribe=function(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null},Object.defineProperty(t.prototype,"observed",{get:function(){var e;return(null===(e=this.observers)||void 0===e?void 0:e.length)>0},enumerable:!1,configurable:!0}),t.prototype._trySubscribe=function(t){return this._throwIfClosed(),e.prototype._trySubscribe.call(this,t)},t.prototype._subscribe=function(e){return this._throwIfClosed(),this._checkFinalizedStatuses(e),this._innerSubscribe(e)},t.prototype._innerSubscribe=function(e){var t=this,r=this.hasError,n=this.isStopped,i=this.observers;return r||n?s.Kn:(this.currentObservers=null,i.push(e),new s.yU(function(){t.currentObservers=null,(0,a.o)(i,e)}))},t.prototype._checkFinalizedStatuses=function(e){var t=this.hasError,r=this.thrownError,n=this.isStopped;t?e.error(r):n&&e.complete()},t.prototype.asObservable=function(){var e=new i.c;return e.source=this,e},t.create=function(e,t){return new l(e,t)},t}(i.c),l=function(e){function t(t,r){var n=e.call(this)||this;return n.destination=t,n.source=r,n}return(0,n.C6)(t,e),t.prototype.next=function(e){var t,r;null===(r=null===(t=this.destination)||void 0===t?void 0:t.next)||void 0===r||r.call(t,e)},t.prototype.error=function(e){var t,r;null===(r=null===(t=this.destination)||void 0===t?void 0:t.error)||void 0===r||r.call(t,e)},t.prototype.complete=function(){var e,t;null===(t=null===(e=this.destination)||void 0===e?void 0:e.complete)||void 0===t||t.call(e)},t.prototype._subscribe=function(e){var t,r;return null!==(r=null===(t=this.source)||void 0===t?void 0:t.subscribe(e))&&void 0!==r?r:s.Kn},t}(c)},3996:(e,t,r)=>{"use strict";r.d(t,{Ms:()=>v,vU:()=>p});var n=r(6476),i=r(195),s=r(5160),o=r(6564),a=r(7690),u=r(6879),c=l("C",void 0,void 0);function l(e,t,r){return{kind:e,value:t,error:r}}var f=r(522),h=r(5110),p=function(e){function t(t){var r=e.call(this)||this;return r.isStopped=!1,t?(r.destination=t,(0,s.Uv)(t)&&t.add(r)):r.destination=w,r}return(0,n.C6)(t,e),t.create=function(e,t,r){return new v(e,t,r)},t.prototype.next=function(e){this.isStopped?b(l("N",e,void 0),this):this._next(e)},t.prototype.error=function(e){this.isStopped?b(l("E",void 0,e),this):(this.isStopped=!0,this._error(e))},t.prototype.complete=function(){this.isStopped?b(c,this):(this.isStopped=!0,this._complete())},t.prototype.unsubscribe=function(){this.closed||(this.isStopped=!0,e.prototype.unsubscribe.call(this),this.destination=null)},t.prototype._next=function(e){this.destination.next(e)},t.prototype._error=function(e){try{this.destination.error(e)}finally{this.unsubscribe()}},t.prototype._complete=function(){try{this.destination.complete()}finally{this.unsubscribe()}},t}(s.yU),d=Function.prototype.bind;function y(e,t){return d.call(e,t)}var g=function(){function e(e){this.partialObserver=e}return e.prototype.next=function(e){var t=this.partialObserver;if(t.next)try{t.next(e)}catch(e){m(e)}},e.prototype.error=function(e){var t=this.partialObserver;if(t.error)try{t.error(e)}catch(e){m(e)}else m(e)},e.prototype.complete=function(){var e=this.partialObserver;if(e.complete)try{e.complete()}catch(e){m(e)}},e}(),v=function(e){function t(t,r,n){var s,a,u=e.call(this)||this;return(0,i.T)(t)||!t?s={next:null!=t?t:void 0,error:null!=r?r:void 0,complete:null!=n?n:void 0}:u&&o.$.useDeprecatedNextContext?((a=Object.create(t)).unsubscribe=function(){return u.unsubscribe()},s={next:t.next&&y(t.next,a),error:t.error&&y(t.error,a),complete:t.complete&&y(t.complete,a)}):s=t,u.destination=new g(s),u}return(0,n.C6)(t,e),t}(p);function m(e){o.$.useDeprecatedSynchronousErrorHandling?(0,h.l)(e):(0,a.m)(e)}function b(e,t){var r=o.$.onStoppedNotification;r&&f.f.setTimeout(function(){return r(e,t)})}var w={closed:!0,next:u.l,error:function(e){throw e},complete:u.l}},5160:(e,t,r)=>{"use strict";r.d(t,{Kn:()=>u,yU:()=>a,Uv:()=>c});var n=r(6476),i=r(195),s=(0,r(2985).L)(function(e){return function(t){e(this),this.message=t?t.length+" errors occurred during unsubscription:\n"+t.map(function(e,t){return t+1+") "+e.toString()}).join("\n  "):"",this.name="UnsubscriptionError",this.errors=t}}),o=r(2238),a=function(){var e;function t(e){this.initialTeardown=e,this.closed=!1,this._parentage=null,this._finalizers=null}return t.prototype.unsubscribe=function(){if(!this.closed){this.closed=!0;var e,t,r,o,a,u=this._parentage;if(u){if(this._parentage=null,Array.isArray(u))try{for(var c=(0,n.Ju)(u),f=c.next();!f.done;f=c.next())f.value.remove(this)}catch(t){e={error:t}}finally{try{f&&!f.done&&(t=c.return)&&t.call(c)}finally{if(e)throw e.error}}else u.remove(this)}var h=this.initialTeardown;if((0,i.T)(h))try{h()}catch(e){a=e instanceof s?e.errors:[e]}var p=this._finalizers;if(p){this._finalizers=null;try{for(var d=(0,n.Ju)(p),y=d.next();!y.done;y=d.next()){var g=y.value;try{l(g)}catch(e){a=null!=a?a:[],e instanceof s?a=(0,n.fX)((0,n.fX)([],(0,n.zs)(a)),(0,n.zs)(e.errors)):a.push(e)}}}catch(e){r={error:e}}finally{try{y&&!y.done&&(o=d.return)&&o.call(d)}finally{if(r)throw r.error}}}if(a)throw new s(a)}},t.prototype.add=function(e){var r;if(e&&e!==this){if(this.closed)l(e);else{if(e instanceof t){if(e.closed||e._hasParent(this))return;e._addParent(this)}(this._finalizers=null!==(r=this._finalizers)&&void 0!==r?r:[]).push(e)}}},t.prototype._hasParent=function(e){var t=this._parentage;return t===e||Array.isArray(t)&&t.includes(e)},t.prototype._addParent=function(e){var t=this._parentage;this._parentage=Array.isArray(t)?(t.push(e),t):t?[t,e]:e},t.prototype._removeParent=function(e){var t=this._parentage;t===e?this._parentage=null:Array.isArray(t)&&(0,o.o)(t,e)},t.prototype.remove=function(e){var r=this._finalizers;r&&(0,o.o)(r,e),e instanceof t&&e._removeParent(this)},t.EMPTY=((e=new t).closed=!0,e),t}(),u=a.EMPTY;function c(e){return e instanceof a||e&&"closed"in e&&(0,i.T)(e.remove)&&(0,i.T)(e.add)&&(0,i.T)(e.unsubscribe)}function l(e){(0,i.T)(e)?e():e.unsubscribe()}},6564:(e,t,r)=>{"use strict";r.d(t,{$:()=>n});var n={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1}},9176:(e,t,r)=>{"use strict";r.d(t,{v:()=>s});var n=r(7279),i=r(1924);function s(e){return new n.c(function(t){(0,i.Tg)(e()).subscribe(t)})}},65:(e,t,r)=>{"use strict";r.d(t,{w:()=>n});var n=new(r(7279)).c(function(e){return e.complete()})},7994:(e,t,r)=>{"use strict";r.d(t,{H:()=>b});var n=r(1924),i=r(3825),s=r(4482);function o(e,t){return void 0===t&&(t=0),(0,s.N)(function(r,n){n.add(e.schedule(function(){return r.subscribe(n)},t))})}var a=r(7279),u=r(7877),c=r(195),l=r(5623);function f(e,t){if(!e)throw Error("Iterable cannot be null");return new a.c(function(r){(0,l.N)(r,t,function(){var n=e[Symbol.asyncIterator]();(0,l.N)(r,t,function(){n.next().then(function(e){e.done?r.complete():r.next(e.value)})},0,!0)})})}var h=r(8469),p=r(2932),d=r(4255),y=r(8985),g=r(8359),v=r(5415),m=r(1168);function b(e,t){return t?function(e,t){if(null!=e){if((0,h.l)(e))return(0,n.Tg)(e).pipe(o(t),(0,i.Q)(t));if((0,d.X)(e))return new a.c(function(r){var n=0;return t.schedule(function(){n===e.length?r.complete():(r.next(e[n++]),r.closed||this.schedule())})});if((0,p.y)(e))return(0,n.Tg)(e).pipe(o(t),(0,i.Q)(t));if((0,g.T)(e))return f(e,t);if((0,y.x)(e))return new a.c(function(r){var n;return(0,l.N)(r,t,function(){n=e[u.l](),(0,l.N)(r,t,function(){var e,t,i;try{t=(e=n.next()).value,i=e.done}catch(e){r.error(e);return}i?r.complete():r.next(t)},0,!0)}),function(){return(0,c.T)(null==n?void 0:n.return)&&n.return()}});if((0,m.U)(e))return f((0,m.C)(e),t)}throw(0,v.L)(e)}(e,t):(0,n.Tg)(e)}},1924:(e,t,r)=>{"use strict";r.d(t,{Tg:()=>y});var n=r(6476),i=r(4255),s=r(2932),o=r(7279),a=r(8469),u=r(8359),c=r(5415),l=r(8985),f=r(1168),h=r(195),p=r(7690),d=r(3822);function y(e){if(e instanceof o.c)return e;if(null!=e){if((0,a.l)(e))return new o.c(function(t){var r=e[d.s]();if((0,h.T)(r.subscribe))return r.subscribe(t);throw TypeError("Provided object does not correctly implement Symbol.observable")});if((0,i.X)(e))return new o.c(function(t){for(var r=0;r<e.length&&!t.closed;r++)t.next(e[r]);t.complete()});if((0,s.y)(e))return new o.c(function(t){e.then(function(e){t.closed||(t.next(e),t.complete())},function(e){return t.error(e)}).then(null,p.m)});if((0,u.T)(e))return g(e);if((0,l.x)(e))return new o.c(function(t){var r,i;try{for(var s=(0,n.Ju)(e),o=s.next();!o.done;o=s.next()){var a=o.value;if(t.next(a),t.closed)return}}catch(e){r={error:e}}finally{try{o&&!o.done&&(i=s.return)&&i.call(s)}finally{if(r)throw r.error}}t.complete()});if((0,f.U)(e))return g((0,f.C)(e))}throw(0,c.L)(e)}function g(e){return new o.c(function(t){(function(e,t){var r,i,s,o;return(0,n.sH)(this,void 0,void 0,function(){var a;return(0,n.YH)(this,function(u){switch(u.label){case 0:u.trys.push([0,5,6,11]),r=(0,n.xN)(e),u.label=1;case 1:return[4,r.next()];case 2:if((i=u.sent()).done)return[3,4];if(a=i.value,t.next(a),t.closed)return[2];u.label=3;case 3:return[3,1];case 4:return[3,11];case 5:return s={error:u.sent()},[3,11];case 6:if(u.trys.push([6,,9,10]),!(i&&!i.done&&(o=r.return)))return[3,8];return[4,o.call(r)];case 7:u.sent(),u.label=8;case 8:return[3,10];case 9:if(s)throw s.error;return[7];case 10:return[7];case 11:return t.complete(),[2]}})})})(e,t).catch(function(e){return t.error(e)})})}},560:(e,t,r)=>{"use strict";r.d(t,{h:()=>u});var n=r(3087),i=r(1924),s=r(65),o=r(6274),a=r(7994);function u(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=(0,o.lI)(e),u=(0,o.R0)(e,1/0);return e.length?1===e.length?(0,i.Tg)(e[0]):(0,n.U)(u)((0,a.H)(e,r)):s.w}},2894:(e,t,r)=>{"use strict";r.d(t,{_:()=>i});var n=r(6476);function i(e,t,r,n,i){return new s(e,t,r,n,i)}var s=function(e){function t(t,r,n,i,s,o){var a=e.call(this,t)||this;return a.onFinalize=s,a.shouldUnsubscribe=o,a._next=r?function(e){try{r(e)}catch(e){t.error(e)}}:e.prototype._next,a._error=i?function(e){try{i(e)}catch(e){t.error(e)}finally{this.unsubscribe()}}:e.prototype._error,a._complete=n?function(){try{n()}catch(e){t.error(e)}finally{this.unsubscribe()}}:e.prototype._complete,a}return(0,n.C6)(t,e),t.prototype.unsubscribe=function(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){var r=this.closed;e.prototype.unsubscribe.call(this),r||null===(t=this.onFinalize)||void 0===t||t.call(this)}},t}(r(3996).vU)},70:(e,t,r)=>{"use strict";r.d(t,{p:()=>s});var n=r(4482),i=r(2894);function s(e,t){return(0,n.N)(function(r,n){var s=0;r.subscribe((0,i._)(n,function(r){return e.call(t,r,s++)&&n.next(r)}))})}},1190:(e,t,r)=>{"use strict";r.d(t,{T:()=>s});var n=r(4482),i=r(2894);function s(e,t){return(0,n.N)(function(r,n){var s=0;r.subscribe((0,i._)(n,function(r){n.next(e.call(t,r,s++))}))})}},3087:(e,t,r)=>{"use strict";r.d(t,{U:()=>s});var n=r(6295),i=r(8830);function s(e){return void 0===e&&(e=1/0),(0,n.Z)(i.D,e)}},6295:(e,t,r)=>{"use strict";r.d(t,{Z:()=>function e(t,r,u){return(void 0===u&&(u=1/0),(0,a.T)(r))?e(function(e,s){return(0,n.T)(function(t,n){return r(e,t,s,n)})((0,i.Tg)(t(e,s)))},u):("number"==typeof r&&(u=r),(0,s.N)(function(e,r){var n,s,a,c,l,f,h,p,d;return n=u,a=[],c=0,l=0,f=!1,h=function(){!f||a.length||c||r.complete()},p=function(e){return c<n?d(e):a.push(e)},d=function(e){c++;var u=!1;(0,i.Tg)(t(e,l++)).subscribe((0,o._)(r,function(e){s?p(e):r.next(e)},function(){u=!0},void 0,function(){if(u)try{for(c--;a.length&&c<n;)!function(){var e=a.shift();d(e)}();h()}catch(e){r.error(e)}}))},e.subscribe((0,o._)(r,p,function(){f=!0,h()})),function(){}}))}});var n=r(1190),i=r(1924),s=r(4482),o=(r(5623),r(2894)),a=r(195)},3825:(e,t,r)=>{"use strict";r.d(t,{Q:()=>o});var n=r(5623),i=r(4482),s=r(2894);function o(e,t){return void 0===t&&(t=0),(0,i.N)(function(r,i){r.subscribe((0,s._)(i,function(r){return(0,n.N)(i,e,function(){return i.next(r)},t)},function(){return(0,n.N)(i,e,function(){return i.complete()},t)},function(r){return(0,n.N)(i,e,function(){return i.error(r)},t)}))})}},8983:(e,t,r)=>{"use strict";r.d(t,{u:()=>u});var n=r(6476),i=r(1924),s=r(6677),o=r(3996),a=r(4482);function u(e){void 0===e&&(e={});var t=e.connector,r=void 0===t?function(){return new s.B}:t,n=e.resetOnError,u=void 0===n||n,l=e.resetOnComplete,f=void 0===l||l,h=e.resetOnRefCountZero,p=void 0===h||h;return function(e){var t,n,s,l=0,h=!1,d=!1,y=function(){null==n||n.unsubscribe(),n=void 0},g=function(){y(),t=s=void 0,h=d=!1},v=function(){var e=t;g(),null==e||e.unsubscribe()};return(0,a.N)(function(e,a){l++,d||h||y();var m=s=null!=s?s:r();a.add(function(){0!=--l||d||h||(n=c(v,p))}),m.subscribe(a),!t&&l>0&&(t=new o.Ms({next:function(e){return m.next(e)},error:function(e){d=!0,y(),n=c(g,u,e),m.error(e)},complete:function(){h=!0,y(),n=c(g,f),m.complete()}}),(0,i.Tg)(e).subscribe(t))})(e)}}function c(e,t){for(var r=[],s=2;s<arguments.length;s++)r[s-2]=arguments[s];if(!0===t){e();return}if(!1!==t){var a=new o.Ms({next:function(){a.unsubscribe(),e()}});return(0,i.Tg)(t.apply(void 0,(0,n.fX)([],(0,n.zs)(r)))).subscribe(a)}}},2260:(e,t,r)=>{"use strict";r.d(t,{t:()=>s});var n=r(7659),i=r(8983);function s(e,t,r){var s,o,a,u,c=!1;return e&&"object"==typeof e?(u=void 0===(s=e.bufferSize)?1/0:s,t=void 0===(o=e.windowTime)?1/0:o,c=void 0!==(a=e.refCount)&&a,r=e.scheduler):u=null!=e?e:1/0,(0,i.u)({connector:function(){return new n.m(u,t,r)},resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:c})}},1444:(e,t,r)=>{"use strict";r.d(t,{R:()=>a});var n=r(6476),i=function(e){function t(t,r){return e.call(this)||this}return(0,n.C6)(t,e),t.prototype.schedule=function(e,t){return void 0===t&&(t=0),this},t}(r(5160).yU),s={setInterval:function(e,t){for(var r=[],i=2;i<arguments.length;i++)r[i-2]=arguments[i];var o=s.delegate;return(null==o?void 0:o.setInterval)?o.setInterval.apply(o,(0,n.fX)([e,t],(0,n.zs)(r))):setInterval.apply(void 0,(0,n.fX)([e,t],(0,n.zs)(r)))},clearInterval:function(e){var t=s.delegate;return((null==t?void 0:t.clearInterval)||clearInterval)(e)},delegate:void 0},o=r(2238),a=function(e){function t(t,r){var n=e.call(this,t,r)||this;return n.scheduler=t,n.work=r,n.pending=!1,n}return(0,n.C6)(t,e),t.prototype.schedule=function(e,t){if(void 0===t&&(t=0),this.closed)return this;this.state=e;var r,n=this.id,i=this.scheduler;return null!=n&&(this.id=this.recycleAsyncId(i,n,t)),this.pending=!0,this.delay=t,this.id=null!==(r=this.id)&&void 0!==r?r:this.requestAsyncId(i,this.id,t),this},t.prototype.requestAsyncId=function(e,t,r){return void 0===r&&(r=0),s.setInterval(e.flush.bind(e,this),r)},t.prototype.recycleAsyncId=function(e,t,r){if(void 0===r&&(r=0),null!=r&&this.delay===r&&!1===this.pending)return t;null!=t&&s.clearInterval(t)},t.prototype.execute=function(e,t){if(this.closed)return Error("executing a cancelled action");this.pending=!1;var r=this._execute(e,t);if(r)return r;!1===this.pending&&null!=this.id&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))},t.prototype._execute=function(e,t){var r,n=!1;try{this.work(e)}catch(e){n=!0,r=e||Error("Scheduled action threw falsy error")}if(n)return this.unsubscribe(),r},t.prototype.unsubscribe=function(){if(!this.closed){var t=this.id,r=this.scheduler,n=r.actions;this.work=this.state=this.scheduler=null,this.pending=!1,(0,o.o)(n,this),null!=t&&(this.id=this.recycleAsyncId(r,t,null)),this.delay=null,e.prototype.unsubscribe.call(this)}},t}(i)},936:(e,t,r)=>{"use strict";r.d(t,{q:()=>o});var n=r(6476),i=r(1833),s=function(){function e(t,r){void 0===r&&(r=e.now),this.schedulerActionCtor=t,this.now=r}return e.prototype.schedule=function(e,t,r){return void 0===t&&(t=0),new this.schedulerActionCtor(this,e).schedule(r,t)},e.now=i.U.now,e}(),o=function(e){function t(t,r){void 0===r&&(r=s.now);var n=e.call(this,t,r)||this;return n.actions=[],n._active=!1,n}return(0,n.C6)(t,e),t.prototype.flush=function(e){var t,r=this.actions;if(this._active){r.push(e);return}this._active=!0;do if(t=e.execute(e.state,e.delay))break;while(e=r.shift());if(this._active=!1,t){for(;e=r.shift();)e.unsubscribe();throw t}},t}(s)},1833:(e,t,r)=>{"use strict";r.d(t,{U:()=>n});var n={now:function(){return(n.delegate||Date).now()},delegate:void 0}},522:(e,t,r)=>{"use strict";r.d(t,{f:()=>i});var n=r(6476),i={setTimeout:function(e,t){for(var r=[],s=2;s<arguments.length;s++)r[s-2]=arguments[s];var o=i.delegate;return(null==o?void 0:o.setTimeout)?o.setTimeout.apply(o,(0,n.fX)([e,t],(0,n.zs)(r))):setTimeout.apply(void 0,(0,n.fX)([e,t],(0,n.zs)(r)))},clearTimeout:function(e){var t=i.delegate;return((null==t?void 0:t.clearTimeout)||clearTimeout)(e)},delegate:void 0}},7877:(e,t,r)=>{"use strict";r.d(t,{l:()=>n});var n="function"==typeof Symbol&&Symbol.iterator?Symbol.iterator:"@@iterator"},3822:(e,t,r)=>{"use strict";r.d(t,{s:()=>n});var n="function"==typeof Symbol&&Symbol.observable||"@@observable"},6274:(e,t,r)=>{"use strict";r.d(t,{R0:()=>u,lI:()=>a,ms:()=>o});var n=r(195),i=r(220);function s(e){return e[e.length-1]}function o(e){return(0,n.T)(s(e))?e.pop():void 0}function a(e){return(0,i.m)(s(e))?e.pop():void 0}function u(e,t){return"number"==typeof s(e)?e.pop():t}},2238:(e,t,r)=>{"use strict";function n(e,t){if(e){var r=e.indexOf(t);0<=r&&e.splice(r,1)}}r.d(t,{o:()=>n})},2985:(e,t,r)=>{"use strict";function n(e){var t=e(function(e){Error.call(e),e.stack=Error().stack});return t.prototype=Object.create(Error.prototype),t.prototype.constructor=t,t}r.d(t,{L:()=>n})},5110:(e,t,r)=>{"use strict";r.d(t,{Y:()=>s,l:()=>o});var n=r(6564),i=null;function s(e){if(n.$.useDeprecatedSynchronousErrorHandling){var t=!i;if(t&&(i={errorThrown:!1,error:null}),e(),t){var r=i,s=r.errorThrown,o=r.error;if(i=null,s)throw o}}else e()}function o(e){n.$.useDeprecatedSynchronousErrorHandling&&i&&(i.errorThrown=!0,i.error=e)}},5623:(e,t,r)=>{"use strict";function n(e,t,r,n,i){void 0===n&&(n=0),void 0===i&&(i=!1);var s=t.schedule(function(){r(),i?e.add(this.schedule(null,n)):this.unsubscribe()},n);if(e.add(s),!i)return s}r.d(t,{N:()=>n})},8830:(e,t,r)=>{"use strict";function n(e){return e}r.d(t,{D:()=>n})},4255:(e,t,r)=>{"use strict";r.d(t,{X:()=>n});var n=function(e){return e&&"number"==typeof e.length&&"function"!=typeof e}},8359:(e,t,r)=>{"use strict";r.d(t,{T:()=>i});var n=r(195);function i(e){return Symbol.asyncIterator&&(0,n.T)(null==e?void 0:e[Symbol.asyncIterator])}},195:(e,t,r)=>{"use strict";function n(e){return"function"==typeof e}r.d(t,{T:()=>n})},8469:(e,t,r)=>{"use strict";r.d(t,{l:()=>s});var n=r(3822),i=r(195);function s(e){return(0,i.T)(e[n.s])}},8985:(e,t,r)=>{"use strict";r.d(t,{x:()=>s});var n=r(7877),i=r(195);function s(e){return(0,i.T)(null==e?void 0:e[n.l])}},2932:(e,t,r)=>{"use strict";r.d(t,{y:()=>i});var n=r(195);function i(e){return(0,n.T)(null==e?void 0:e.then)}},1168:(e,t,r)=>{"use strict";r.d(t,{C:()=>s,U:()=>o});var n=r(6476),i=r(195);function s(e){return(0,n.AQ)(this,arguments,function(){var t,r,i;return(0,n.YH)(this,function(s){switch(s.label){case 0:t=e.getReader(),s.label=1;case 1:s.trys.push([1,,9,10]),s.label=2;case 2:return[4,(0,n.N3)(t.read())];case 3:if(i=(r=s.sent()).value,!r.done)return[3,5];return[4,(0,n.N3)(void 0)];case 4:return[2,s.sent()];case 5:return[4,(0,n.N3)(i)];case 6:return[4,s.sent()];case 7:return s.sent(),[3,2];case 8:return[3,10];case 9:return t.releaseLock(),[7];case 10:return[2]}})})}function o(e){return(0,i.T)(null==e?void 0:e.getReader)}},220:(e,t,r)=>{"use strict";r.d(t,{m:()=>i});var n=r(195);function i(e){return e&&(0,n.T)(e.schedule)}},4482:(e,t,r)=>{"use strict";r.d(t,{N:()=>i});var n=r(195);function i(e){return function(t){if((0,n.T)(null==t?void 0:t.lift))return t.lift(function(t){try{return e(t,this)}catch(e){this.error(e)}});throw TypeError("Unable to lift unknown Observable type")}}},5309:(e,t,r)=>{"use strict";r.d(t,{I:()=>o});var n=r(6476),i=r(1190),s=Array.isArray;function o(e){return(0,i.T)(function(t){return s(t)?e.apply(void 0,(0,n.fX)([],(0,n.zs)(t))):e(t)})}},6879:(e,t,r)=>{"use strict";function n(){}r.d(t,{l:()=>n})},6019:(e,t,r)=>{"use strict";r.d(t,{F:()=>i,m:()=>s});var n=r(8830);function i(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return s(e)}function s(e){return 0===e.length?n.D:1===e.length?e[0]:function(t){return e.reduce(function(e,t){return t(e)},t)}}},7690:(e,t,r)=>{"use strict";r.d(t,{m:()=>s});var n=r(6564),i=r(522);function s(e){i.f.setTimeout(function(){var t=n.$.onUnhandledError;if(t)t(e);else throw e})}},5415:(e,t,r)=>{"use strict";function n(e){return TypeError("You provided "+(null!==e&&"object"==typeof e?"an invalid object":"'"+e+"'")+" where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.")}r.d(t,{L:()=>n})},8691:(e,t,r)=>{"use strict";r.d(t,{H6:()=>d,OX:()=>u,SP:()=>s,Sx:()=>h});var n=r(8596);let i=new WeakMap;function s(e){return{config:e,start:(t,r)=>{let{self:n,system:s,emit:o}=r,a={receivers:void 0,dispose:void 0};i.set(n,a),a.dispose=e({input:t.input,system:s,self:n,sendBack:e=>{"stopped"!==n.getSnapshot().status&&n._parent&&s._relay(n,n._parent,e)},receive:e=>{a.receivers??=new Set,a.receivers.add(e)},emit:o})},transition:(e,t,r)=>{let s=i.get(r.self);return t.type===n.X?(e={...e,status:"stopped",error:void 0},s.dispose?.()):s.receivers?.forEach(e=>e(t)),e},getInitialSnapshot:(e,t)=>({status:"active",output:void 0,error:void 0,input:t}),getPersistedSnapshot:e=>e,restoreSnapshot:e=>e}}let o="xstate.observable.error",a="xstate.observable.complete";function u(e){return{config:e,transition:(e,t)=>{if("active"!==e.status)return e;switch(t.type){case o:return{...e,status:"error",error:t.data,input:void 0,_subscription:void 0};case a:return{...e,status:"done",input:void 0,_subscription:void 0};case n.X:return e._subscription.unsubscribe(),{...e,status:"stopped",input:void 0,_subscription:void 0};default:return e}},getInitialSnapshot:(e,t)=>({status:"active",output:void 0,error:void 0,context:void 0,input:t,_subscription:void 0}),start:(t,{self:r,system:n,emit:i})=>{"done"!==t.status&&(t._subscription=e({input:t.input,system:n,self:r,emit:i}).subscribe({next:e=>{r._parent&&n._relay(r,r._parent,e)},error:e=>{n._relay(r,r,{type:o,data:e})},complete:()=>{n._relay(r,r,{type:a})}}))},getPersistedSnapshot:({_subscription:e,...t})=>t,restoreSnapshot:e=>({...e,_subscription:void 0})}}let c="xstate.promise.resolve",l="xstate.promise.reject",f=new WeakMap;function h(e){return{config:e,transition:(e,t,r)=>{if("active"!==e.status)return e;switch(t.type){case c:{let r=t.data;return{...e,status:"done",output:r,input:void 0}}case l:return{...e,status:"error",error:t.data,input:void 0};case n.X:return f.get(r.self)?.abort(),{...e,status:"stopped",input:void 0};default:return e}},start:(t,{self:r,system:n,emit:i})=>{if("active"!==t.status)return;let s=new AbortController;f.set(r,s),Promise.resolve(e({input:t.input,system:n,self:r,signal:s.signal,emit:i})).then(e=>{"active"===r.getSnapshot().status&&(f.delete(r),n._relay(r,r,{type:c,data:e}))},e=>{"active"===r.getSnapshot().status&&(f.delete(r),n._relay(r,r,{type:l,data:e}))})},getInitialSnapshot:(e,t)=>({status:"active",output:void 0,error:void 0,input:t}),getPersistedSnapshot:e=>e,restoreSnapshot:e=>e}}let p=function(e,t){return{config:e,transition:(t,r,n)=>({...t,context:e(t.context,r,n)}),getInitialSnapshot:(e,r)=>({status:"active",output:void 0,error:void 0,context:"function"==typeof t?t({input:r}):t}),getPersistedSnapshot:e=>e,restoreSnapshot:e=>e}}(e=>void 0,void 0);function d(){return(0,n.A)(p)}},3915:(e,t,r)=>{"use strict";r.d(t,{a:()=>s,b:()=>g,c:()=>p,e:()=>u,s:()=>d});var n=r(8596);function i(e,t,r,i,{assignment:s}){if(!t.context)throw Error("Cannot assign to undefined `context`. Ensure that `context` is defined in the machine config.");let o={},a={context:t.context,event:r.event,spawn:function(e,{machine:t,context:r},i,s){let o=(o,a)=>{if("string"!=typeof o)return(0,n.A)(o,{id:a?.id,parent:e.self,syncSnapshot:a?.syncSnapshot,input:a?.input,src:o,systemId:a?.systemId});{let u=(0,n.z)(t,o);if(!u)throw Error(`Actor logic '${o}' not implemented in machine '${t.id}'`);let c=(0,n.A)(u,{id:a?.id,parent:e.self,syncSnapshot:a?.syncSnapshot,input:"function"==typeof a?.input?a.input({context:r,event:i,self:e.self}):a?.input,src:o,systemId:a?.systemId});return s[c.id]=c,c}};return(t,r)=>{let i=o(t,r);return s[i.id]=i,e.defer(()=>{i._processingStatus!==n.T.Stopped&&i.start()}),i}}(e,t,r.event,o),self:e.self,system:e.system},u={};if("function"==typeof s)u=s(a,i);else for(let e of Object.keys(s)){let t=s[e];u[e]="function"==typeof t?t(a,i):t}let c=Object.assign({},t.context,u);return[(0,n.U)(t,{context:c,children:Object.keys(o).length?{...t.children,...o}:t.children}),void 0,void 0]}function s(e){function t(e,t){}return t.type="xstate.assign",t.assignment=e,t.resolve=i,t}function o(e,t,r,n,{event:i}){return[t,{event:"function"==typeof i?i(r,n):i},void 0]}function a(e,{event:t}){e.defer(()=>e.emit(t))}function u(e){function t(e,t){}return t.type="xstate.emit",t.event=e,t.resolve=o,t.execute=a,t}let c=function(e){return e.Parent="#_parent",e.Internal="#_internal",e}({});function l(e,t,r,n,{to:i,event:s,id:o,delay:a},u){let l,f;let h=t.machine.implementations.delays;if("string"==typeof s)throw Error(`Only event objects may be used with sendTo; use sendTo({ type: "${s}" }) instead`);let p="function"==typeof s?s(r,n):s;if("string"==typeof a){let e=h&&h[a];l="function"==typeof e?e(r,n):e}else l="function"==typeof a?a(r,n):a;let d="function"==typeof i?i(r,n):i;if("string"==typeof d){if(!(f=d===c.Parent?e.self._parent:d===c.Internal?e.self:d.startsWith("#_")?t.children[d.slice(2)]:u.deferredActorIds?.includes(d)?d:t.children[d]))throw Error(`Unable to send event to actor '${d}' from machine '${t.machine.id}'.`)}else f=d||e.self;return[t,{to:f,targetId:"string"==typeof d?d:void 0,event:p,id:o,delay:l},void 0]}function f(e,t,r){"string"==typeof r.to&&(r.to=t.children[r.to])}function h(e,t){e.defer(()=>{let{to:r,event:i,delay:s,id:o}=t;if("number"==typeof s){e.system.scheduler.schedule(e.self,r,i,s,o);return}e.system._relay(e.self,r,i.type===n.V?(0,n.W)(e.self.id,i.data):i)})}function p(e,t,r){function n(e,t){}return n.type="xstate.sendTo",n.to=e,n.event=t,n.id=r?.id,n.delay=r?.delay,n.resolve=l,n.retryResolve=f,n.execute=h,n}function d(e,t){return p(c.Parent,e,t)}function y(e,t,r,i,{collect:o}){let a=[],c=function(e){a.push(e)};return c.assign=(...e)=>{a.push(s(...e))},c.cancel=(...e)=>{a.push((0,n.M)(...e))},c.raise=(...e)=>{a.push((0,n.O)(...e))},c.sendTo=(...e)=>{a.push(p(...e))},c.sendParent=(...e)=>{a.push(d(...e))},c.spawnChild=(...e)=>{a.push((0,n.P)(...e))},c.stopChild=(...e)=>{a.push((0,n.R)(...e))},c.emit=(...e)=>{a.push(u(...e))},o({context:r.context,event:r.event,enqueue:c,check:e=>(0,n.e)(e,t.context,r.event,t),self:e.self,system:e.system},i),[t,void 0,a]}function g(e){function t(e,t){}return t.type="xstate.enqueueActions",t.collect=e,t.resolve=y,t}},8596:(e,t,r)=>{"use strict";r.d(t,{$:()=>k,A:()=>O,M:()=>U,N:()=>o,O:()=>eO,P:()=>D,R:()=>F,S:()=>s,T:()=>$,U:()=>ek,V:()=>u,W:()=>f,X:()=>c,a:()=>_,b:()=>ee,c:()=>S,d:()=>er,e:()=>z,f:()=>et,g:()=>K,h:()=>Z,i:()=>J,j:()=>ec,k:()=>eR,l:()=>G,m:()=>v,n:()=>eb,o:()=>function e(t,r,n,i){return"string"==typeof r?function(e,t,r,n){let i=ea(e,t).next(r,n);return i&&i.length?i:e.next(r,n)}(t,r,n,i):1===Object.keys(r).length?function(t,r,n,i){let s=Object.keys(r),o=e(ea(t,s[0]),r[s[0]],n,i);return o&&o.length?o:t.next(n,i)}(t,r,n,i):function(t,r,n,i){let s=[];for(let o of Object.keys(r)){let a=r[o];if(!a)continue;let u=e(ea(t,o),a,n,i);u&&s.push(...u)}return s.length?s:t.next(n,i)}(t,r,n,i)},p:()=>em,q:()=>h,r:()=>ex,s:()=>ey,t:()=>m,u:()=>eo,v:()=>y,w:()=>Q,x:()=>eu,y:()=>e$,z:()=>T});let n=e=>{if("undefined"==typeof window)return;let t=function(){let e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:void 0!==r.g?r.g:void 0;if(e.__xstate__)return e.__xstate__}();t&&t.register(e)};class i{constructor(e){this._process=e,this._active=!1,this._current=null,this._last=null}start(){this._active=!0,this.flush()}clear(){this._current&&(this._current.next=null,this._last=this._current)}enqueue(e){let t={value:e,next:null};if(this._current){this._last.next=t,this._last=t;return}this._current=t,this._last=t,this._active&&this.flush()}flush(){for(;this._current;){let e=this._current;this._process(e.value),this._current=e.next}this._last=null}}let s=".",o="",a="xstate.init",u="xstate.error",c="xstate.stop";function l(e,t){return{type:`xstate.done.state.${e}`,output:t}}function f(e,t){return{type:`xstate.error.actor.${e}`,error:t,actorId:e}}function h(e){return{type:a,input:e}}function p(e){setTimeout(()=>{throw e})}let d="function"==typeof Symbol&&Symbol.observable||"@@observable";function y(e){if(w(e))return e;let t=[],r="";for(let n=0;n<e.length;n++){switch(e.charCodeAt(n)){case 92:r+=e[n+1],n++;continue;case 46:t.push(r),r="";continue}r+=e[n]}return t.push(r),t}function g(e){return e&&"object"==typeof e&&"machine"in e&&"value"in e?e.value:"string"!=typeof e?e:function(e){if(1===e.length)return e[0];let t={},r=t;for(let t=0;t<e.length-1;t++)if(t===e.length-2)r[e[t]]=e[t+1];else{let n=r;r={},n[e[t]]=r}return t}(y(e))}function v(e,t){let r={},n=Object.keys(e);for(let i=0;i<n.length;i++){let s=n[i];r[s]=t(e[s],s,e,i)}return r}function m(e){return void 0===e?[]:w(e)?e:[e]}function b(e,t,r,n){return"function"==typeof e?e({context:t,event:r,self:n}):e}function w(e){return Array.isArray(e)}function _(e){return(w(e)?e:[e]).map(e=>void 0===e||"string"==typeof e?{target:e}:e)}function x(e){if(void 0!==e&&""!==e)return m(e)}function E(e,t,r){let n="object"==typeof e,i=n?e:void 0;return{next:(n?e.next:e)?.bind(i),error:(n?e.error:t)?.bind(i),complete:(n?e.complete:r)?.bind(i)}}function S(e,t){return`${t}.${e}`}function T(e,t){let r=t.match(/^xstate\.invoke\.(\d+)\.(.*)/);if(!r)return e.implementations.actors[t];let[,n,i]=r,s=e.getStateNodeById(i).config.invoke;return(Array.isArray(s)?s[n]:s).src}function C(e,t){return`${e.sessionId}.${t}`}let I=0,R=!1,k=1,$=function(e){return e[e.NotStarted=0]="NotStarted",e[e.Running=1]="Running",e[e.Stopped=2]="Stopped",e}({}),A={clock:{setTimeout:(e,t)=>setTimeout(e,t),clearTimeout:e=>clearTimeout(e)},logger:console.log.bind(console),devTools:!1};class q{constructor(e,t){this.logic=e,this._snapshot=void 0,this.clock=void 0,this.options=void 0,this.id=void 0,this.mailbox=new i(this._process.bind(this)),this.observers=new Set,this.eventListeners=new Map,this.logger=void 0,this._processingStatus=$.NotStarted,this._parent=void 0,this._syncSnapshot=void 0,this.ref=void 0,this._actorScope=void 0,this._systemId=void 0,this.sessionId=void 0,this.system=void 0,this._doneEvent=void 0,this.src=void 0,this._deferred=[];let r={...A,...t},{clock:n,logger:s,parent:o,syncSnapshot:a,id:u,systemId:c,inspect:l}=r;this.system=o?o.system:function(e,t){let r=new Map,n=new Map,i=new WeakMap,s=new Set,o={},{clock:a,logger:u}=t,c={schedule:(e,t,r,n,i=Math.random().toString(36).slice(2))=>{let s={source:e,target:t,event:r,delay:n,id:i,startedAt:Date.now()},u=C(e,i);l._snapshot._scheduledEvents[u]=s;let c=a.setTimeout(()=>{delete o[u],delete l._snapshot._scheduledEvents[u],l._relay(e,t,r)},n);o[u]=c},cancel:(e,t)=>{let r=C(e,t),n=o[r];delete o[r],delete l._snapshot._scheduledEvents[r],void 0!==n&&a.clearTimeout(n)},cancelAll:e=>{for(let t in l._snapshot._scheduledEvents){let r=l._snapshot._scheduledEvents[t];r.source===e&&c.cancel(e,r.id)}}},l={_snapshot:{_scheduledEvents:(t?.snapshot&&t.snapshot.scheduler)??{}},_bookId:()=>`x:${I++}`,_register:(e,t)=>(r.set(e,t),e),_unregister:e=>{r.delete(e.sessionId);let t=i.get(e);void 0!==t&&(n.delete(t),i.delete(e))},get:e=>n.get(e),_set:(e,t)=>{let r=n.get(e);if(r&&r!==t)throw Error(`Actor with system ID '${e}' already exists.`);n.set(e,t),i.set(t,e)},inspect:e=>{let t=E(e);return s.add(t),{unsubscribe(){s.delete(t)}}},_sendInspectionEvent:t=>{if(!s.size)return;let r={...t,rootId:e.sessionId};s.forEach(e=>e.next?.(r))},_relay:(e,t,r)=>{l._sendInspectionEvent({type:"@xstate.event",sourceRef:e,actorRef:t,event:r}),t._send(r)},scheduler:c,getSnapshot:()=>({_scheduledEvents:{...l._snapshot._scheduledEvents}}),start:()=>{let e=l._snapshot._scheduledEvents;for(let t in l._snapshot._scheduledEvents={},e){let{source:r,target:n,event:i,delay:s,id:o}=e[t];c.schedule(r,n,i,s,o)}},_clock:a,_logger:u};return l}(this,{clock:n,logger:s}),l&&!o&&this.system.inspect(E(l)),this.sessionId=this.system._bookId(),this.id=u??this.sessionId,this.logger=t?.logger??this.system._logger,this.clock=t?.clock??this.system._clock,this._parent=o,this._syncSnapshot=a,this.options=r,this.src=r.src??e,this.ref=this,this._actorScope={self:this,id:this.id,sessionId:this.sessionId,logger:this.logger,defer:e=>{this._deferred.push(e)},system:this.system,stopChild:e=>{if(e._parent!==this)throw Error(`Cannot stop child actor ${e.id} of ${this.id} because it is not a child`);e._stop()},emit:e=>{let t=this.eventListeners.get(e.type),r=this.eventListeners.get("*");if(t||r)for(let n of[...t?t.values():[],...r?r.values():[]])n(e)},actionExecutor:e=>{let t=()=>{if(this._actorScope.system._sendInspectionEvent({type:"@xstate.action",actorRef:this,action:{type:e.type,params:e.params}}),!e.exec)return;let t=R;try{R=!0,e.exec(e.info,e.params)}finally{R=t}};this._processingStatus===$.Running?t():this._deferred.push(t)}},this.send=this.send.bind(this),this.system._sendInspectionEvent({type:"@xstate.actor",actorRef:this}),c&&(this._systemId=c,this.system._set(c,this)),this._initState(t?.snapshot??t?.state),c&&"active"!==this._snapshot.status&&this.system._unregister(this)}_initState(e){try{this._snapshot=e?this.logic.restoreSnapshot?this.logic.restoreSnapshot(e,this._actorScope):e:this.logic.getInitialSnapshot(this._actorScope,this.options?.input)}catch(e){this._snapshot={status:"error",output:void 0,error:e}}}update(e,t){let r;for(this._snapshot=e;r=this._deferred.shift();)try{r()}catch(t){this._deferred.length=0,this._snapshot={...e,status:"error",error:t}}switch(this._snapshot.status){case"active":for(let t of this.observers)try{t.next?.(e)}catch(e){p(e)}break;case"done":var n;for(let t of this.observers)try{t.next?.(e)}catch(e){p(e)}this._stopProcedure(),this._complete(),this._doneEvent=(n=this.id,{type:`xstate.done.actor.${n}`,output:this._snapshot.output,actorId:n}),this._parent&&this.system._relay(this,this._parent,this._doneEvent);break;case"error":this._error(this._snapshot.error)}this.system._sendInspectionEvent({type:"@xstate.snapshot",actorRef:this,event:t,snapshot:e})}subscribe(e,t,r){let n=E(e,t,r);if(this._processingStatus!==$.Stopped)this.observers.add(n);else switch(this._snapshot.status){case"done":try{n.complete?.()}catch(e){p(e)}break;case"error":{let e=this._snapshot.error;if(n.error)try{n.error(e)}catch(e){p(e)}else p(e)}}return{unsubscribe:()=>{this.observers.delete(n)}}}on(e,t){let r=this.eventListeners.get(e);r||(r=new Set,this.eventListeners.set(e,r));let n=t.bind(void 0);return r.add(n),{unsubscribe:()=>{r.delete(n)}}}start(){if(this._processingStatus===$.Running)return this;this._syncSnapshot&&this.subscribe({next:e=>{"active"===e.status&&this.system._relay(this,this._parent,{type:`xstate.snapshot.${this.id}`,snapshot:e})},error:()=>{}}),this.system._register(this.sessionId,this),this._systemId&&this.system._set(this._systemId,this),this._processingStatus=$.Running;let e=h(this.options.input);switch(this.system._sendInspectionEvent({type:"@xstate.event",sourceRef:this._parent,actorRef:this,event:e}),this._snapshot.status){case"done":return this.update(this._snapshot,e),this;case"error":return this._error(this._snapshot.error),this}if(this._parent||this.system.start(),this.logic.start)try{this.logic.start(this._snapshot,this._actorScope)}catch(e){return this._snapshot={...this._snapshot,status:"error",error:e},this._error(e),this}return this.update(this._snapshot,e),this.options.devTools&&this.attachDevTools(),this.mailbox.start(),this}_process(e){let t,r;try{t=this.logic.transition(this._snapshot,e,this._actorScope)}catch(e){r={err:e}}if(r){let{err:e}=r;this._snapshot={...this._snapshot,status:"error",error:e},this._error(e);return}this.update(t,e),e.type===c&&(this._stopProcedure(),this._complete())}_stop(){return this._processingStatus===$.Stopped||((this.mailbox.clear(),this._processingStatus===$.NotStarted)?this._processingStatus=$.Stopped:this.mailbox.enqueue({type:c})),this}stop(){if(this._parent)throw Error("A non-root actor cannot be stopped directly.");return this._stop()}_complete(){for(let e of this.observers)try{e.complete?.()}catch(e){p(e)}this.observers.clear()}_reportError(e){if(!this.observers.size){this._parent||p(e);return}let t=!1;for(let r of this.observers){let n=r.error;t||=!n;try{n?.(e)}catch(e){p(e)}}this.observers.clear(),t&&p(e)}_error(e){this._stopProcedure(),this._reportError(e),this._parent&&this.system._relay(this,this._parent,f(this.id,e))}_stopProcedure(){return this._processingStatus!==$.Running||(this.system.scheduler.cancelAll(this),this.mailbox.clear(),this.mailbox=new i(this._process.bind(this)),this._processingStatus=$.Stopped,this.system._unregister(this)),this}_send(e){this._processingStatus!==$.Stopped&&this.mailbox.enqueue(e)}send(e){this.system._relay(void 0,this,e)}attachDevTools(){let{devTools:e}=this.options;e&&("function"==typeof e?e:n)(this)}toJSON(){return{xstate$$type:k,id:this.id}}getPersistedSnapshot(e){return this.logic.getPersistedSnapshot(this._snapshot,e)}[d](){return this}getSnapshot(){return this._snapshot}}function O(e,...[t]){return new q(e,t)}function j(e,t,r,n,{sendId:i}){return[t,{sendId:"function"==typeof i?i(r,n):i},void 0]}function P(e,t){e.defer(()=>{e.system.scheduler.cancel(e.self,t.sendId)})}function U(e){function t(e,t){}return t.type="xstate.cancel",t.sendId=e,t.resolve=j,t.execute=P,t}function M(e,t,r,n,{id:i,systemId:s,src:o,input:a,syncSnapshot:u}){let c,l;let f="string"==typeof o?T(t.machine,o):o,h="function"==typeof i?i(r):i;return f&&(l="function"==typeof a?a({context:t.context,event:r.event,self:e.self}):a,c=O(f,{id:h,src:o,parent:e.self,syncSnapshot:u,systemId:s,input:l})),[ek(t,{children:{...t.children,[h]:c}}),{id:i,systemId:s,actorRef:c,src:o,input:l},void 0]}function N(e,{actorRef:t}){t&&e.defer(()=>{t._processingStatus!==$.Stopped&&t.start()})}function D(...[e,{id:t,systemId:r,input:n,syncSnapshot:i=!1}={}]){function s(e,t){}return s.type="xstate.spawnChild",s.id=t,s.systemId=r,s.src=e,s.input=n,s.syncSnapshot=i,s.resolve=M,s.execute=N,s}function B(e,t,r,n,{actorRef:i}){let s="function"==typeof i?i(r,n):i,o="string"==typeof s?t.children[s]:s,a=t.children;return o&&(a={...a},delete a[o.id]),[ek(t,{children:a}),o,void 0]}function L(e,t){if(t){if(e.system._unregister(t),t._processingStatus!==$.Running){e.stopChild(t);return}e.defer(()=>{e.stopChild(t)})}}function F(e){function t(e,t){}return t.type="xstate.stopChild",t.actorRef=e,t.resolve=B,t.execute=L,t}function z(e,t,r,n){let{machine:i}=n,s="function"==typeof e,o=s?e:i.implementations.guards["string"==typeof e?e:e.type];if(!s&&!o)throw Error(`Guard '${"string"==typeof e?e:e.type}' is not implemented.'.`);if("function"!=typeof o)return z(o,t,r,n);let a={context:t,event:r},u=s||"string"==typeof e?void 0:"params"in e?"function"==typeof e.params?e.params({context:t,event:r}):e.params:void 0;return"check"in o?o.check(n,a,o):o(a,u)}let V=e=>"atomic"===e.type||"final"===e.type;function H(e){return Object.values(e.states).filter(e=>"history"!==e.type)}function W(e,t){let r=[];if(t===e)return r;let n=e.parent;for(;n&&n!==t;)r.push(n),n=n.parent;return r}function J(e){let t=new Set(e),r=X(t);for(let e of t)if("compound"!==e.type||r.get(e)&&r.get(e).length){if("parallel"===e.type){for(let r of H(e))if("history"!==r.type&&!t.has(r))for(let e of es(r))t.add(e)}}else es(e).forEach(e=>t.add(e));for(let e of t){let r=e.parent;for(;r;)t.add(r),r=r.parent}return t}function X(e){let t=new Map;for(let r of e)t.has(r)||t.set(r,[]),r.parent&&(t.has(r.parent)||t.set(r.parent,[]),t.get(r.parent).push(r));return t}function Y(e,t){return function e(t,r){let n=r.get(t);if(!n)return{};if("compound"===t.type){let e=n[0];if(!e)return{};if(V(e))return e.key}let i={};for(let t of n)i[t.key]=e(t,r);return i}(e,X(J(t)))}function G(e,t){return"compound"===t.type?H(t).some(t=>"final"===t.type&&e.has(t)):"parallel"===t.type?H(t).every(t=>G(e,t)):"final"===t.type}let Q=e=>"#"===e[0];function Z(e,t){return e.transitions.get(t)||[...e.transitions.keys()].filter(e=>{if("*"===e)return!0;if(!e.endsWith(".*"))return!1;let r=e.split("."),n=t.split(".");for(let e=0;e<r.length;e++){let t=r[e],i=n[e];if("*"===t)return e===r.length-1;if(t!==i)return!1}return!0}).sort((e,t)=>t.length-e.length).flatMap(t=>e.transitions.get(t))}function K(e){let t=e.config.after;if(!t)return[];let r=t=>{var r;let n=(r=e.id,{type:`xstate.after.${t}.${r}`}),i=n.type;return e.entry.push(eO(n,{id:i,delay:t})),e.exit.push(U(i)),i};return Object.keys(t).flatMap(e=>{let n=t[e],i=Number.isNaN(+e)?e:+e,s=r(i);return m("string"==typeof n?{target:n}:n).map(e=>({...e,event:s,delay:i}))}).map(t=>{let{delay:r}=t;return{...ee(e,t.event,t),delay:r}})}function ee(e,t,r){let n=x(r.target),i=r.reenter??!1,o=function(e,t){if(void 0!==t)return t.map(t=>{if("string"!=typeof t)return t;if(Q(t))return e.machine.getStateNodeById(t);let r=t[0]===s;if(r&&!e.parent)return eu(e,t.slice(1));let n=r?e.key+t:t;if(e.parent)try{return eu(e.parent,n)}catch(t){throw Error(`Invalid transition definition for state node '${e.id}':
${t.message}`)}else throw Error(`Invalid target: "${t}" is not a valid target from the root node. Did you mean ".${t}"?`)})}(e,n),a={...r,actions:m(r.actions),guard:r.guard,target:o,source:e,reenter:i,eventType:t,toJSON:()=>({...a,source:`#${e.id}`,target:o?o.map(e=>`#${e.id}`):void 0})};return a}function et(e){let t=new Map;if(e.config.on)for(let r of Object.keys(e.config.on)){if(r===o)throw Error('Null events ("") cannot be specified as a transition key. Use `always: { ... }` instead.');let n=e.config.on[r];t.set(r,_(n).map(t=>ee(e,r,t)))}if(e.config.onDone){let r=`xstate.done.state.${e.id}`;t.set(r,_(e.config.onDone).map(t=>ee(e,r,t)))}for(let r of e.invoke){if(r.onDone){let n=`xstate.done.actor.${r.id}`;t.set(n,_(r.onDone).map(t=>ee(e,n,t)))}if(r.onError){let n=`xstate.error.actor.${r.id}`;t.set(n,_(r.onError).map(t=>ee(e,n,t)))}if(r.onSnapshot){let n=`xstate.snapshot.${r.id}`;t.set(n,_(r.onSnapshot).map(t=>ee(e,n,t)))}}for(let r of e.after){let e=t.get(r.eventType);e||(e=[],t.set(r.eventType,e)),e.push(r)}return t}function er(e,t){let r="string"==typeof t?e.states[t]:t?e.states[t.target]:void 0;if(!r&&t)throw Error(`Initial state node "${t}" not found on parent state node #${e.id}`);let n={source:e,actions:t&&"string"!=typeof t?m(t.actions):[],eventType:null,reenter:!1,target:r?[r]:[],toJSON:()=>({...n,source:`#${e.id}`,target:r?[`#${r.id}`]:[]})};return n}function en(e){let t=x(e.config.target);return t?{target:t.map(t=>"string"==typeof t?eu(e.parent,t):t)}:e.parent.initial}function ei(e){return"history"===e.type}function es(e){let t=eo(e);for(let r of t)for(let n of W(r,e))t.add(n);return t}function eo(e){let t=new Set;return!function e(r){if(!t.has(r)){if(t.add(r),"compound"===r.type)e(r.initial.target[0]);else if("parallel"===r.type)for(let t of H(r))e(t)}}(e),t}function ea(e,t){if(Q(t))return e.machine.getStateNodeById(t);if(!e.states)throw Error(`Unable to retrieve child state '${t}' from '${e.id}'; no child states exist.`);let r=e.states[t];if(!r)throw Error(`Child state '${t}' does not exist on '${e.id}'`);return r}function eu(e,t){if("string"==typeof t&&Q(t))try{return e.machine.getStateNodeById(t)}catch{}let r=y(t).slice(),n=e;for(;r.length;){let e=r.shift();if(!e.length)break;n=ea(n,e)}return n}function ec(e,t){if("string"==typeof t){let r=e.states[t];if(!r)throw Error(`State '${t}' does not exist on '${e.id}'`);return[e,r]}let r=Object.keys(t),n=r.map(t=>ea(e,t)).filter(Boolean);return[e.machine.root,e].concat(n,r.reduce((r,n)=>{let i=ea(e,n);if(!i)return r;let s=ec(i,t[n]);return r.concat(s)},[]))}function el(e,t){let r=e;for(;r.parent&&r.parent!==t;)r=r.parent;return r.parent===t}function ef(e,t,r){let n=new Set;for(let i of e){let e=!1,s=new Set;for(let o of n)if(function(e,t){let r=new Set(e),n=new Set(t);for(let e of r)if(n.has(e))return!0;for(let e of n)if(r.has(e))return!0;return!1}(ed([i],t,r),ed([o],t,r))){if(el(i.source,o.source))s.add(o);else{e=!0;break}}if(!e){for(let e of s)n.delete(e);n.add(i)}}return Array.from(n)}function eh(e,t){if(!e.target)return[];let r=new Set;for(let n of e.target)if(ei(n)){if(t[n.id])for(let e of t[n.id])r.add(e);else for(let e of eh(en(n),t))r.add(e)}else r.add(n);return[...r]}function ep(e,t){let r=eh(e,t);if(!r)return;if(!e.reenter&&r.every(t=>t===e.source||el(t,e.source)))return e.source;let n=function(e){let[t,...r]=e;for(let e of W(t,void 0))if(r.every(t=>el(t,e)))return e}(r.concat(e.source));return n||(e.reenter?void 0:e.source.machine.root)}function ed(e,t,r){let n=new Set;for(let i of e)if(i.target?.length){let e=ep(i,r);for(let r of(i.reenter&&i.source===e&&n.add(e),t))el(r,e)&&n.add(r)}return[...n]}function ey(e,t,r,n,i,s){if(!e.length)return t;let o=new Set(t._nodes),a=t.historyValue,u=ef(e,o,a),c=t;i||([c,a]=function(e,t,r,n,i,s,o,a){let u,c=e,l=ed(n,i,s);for(let e of(l.sort((e,t)=>t.order-e.order),l))for(let t of function(e){return Object.keys(e.states).map(t=>e.states[t]).filter(e=>"history"===e.type)}(e)){let r;r="deep"===t.history?t=>V(t)&&el(t,e):t=>t.parent===e,(u??={...s})[t.id]=Array.from(i).filter(r)}for(let e of l)c=em(c,t,r,[...e.exit,...e.invoke.map(e=>F(e.id))],o,void 0),i.delete(e);return[c,u||s]}(c,n,r,u,o,a,s,r.actionExecutor)),c=function(e,t,r,n,i,s,o,a){let u=e,c=new Set,f=new Set;(function(e,t,r,n){for(let i of e){let e=ep(i,t);for(let s of i.target||[])!ei(s)&&(i.source!==s||i.source!==e||i.reenter)&&(n.add(s),r.add(s)),eg(s,t,r,n);for(let s of eh(i,t)){let o=W(s,e);e?.type==="parallel"&&o.push(e),ev(n,t,r,o,!i.source.parent&&i.reenter?void 0:e)}}})(n,o,f,c),a&&f.add(e.machine.root);let h=new Set;for(let e of[...c].sort((e,t)=>e.order-t.order)){i.add(e);let n=[];for(let t of(n.push(...e.entry),e.invoke))n.push(D(t.src,{...t,syncSnapshot:!!t.onSnapshot}));if(f.has(e)){let t=e.initial.actions;n.push(...t)}if(u=em(u,t,r,n,s,e.invoke.map(e=>e.id)),"final"===e.type){let n=e.parent,o=n?.type==="parallel"?n:n?.parent,a=o||e;for(n?.type==="compound"&&s.push(l(n.id,void 0!==e.output?b(e.output,u.context,t,r.self):void 0));o?.type==="parallel"&&!h.has(o)&&G(i,o);)h.add(o),s.push(l(o.id)),a=o,o=o.parent;if(o)continue;u=ek(u,{status:"done",output:function(e,t,r,n,i){if(void 0===n.output)return;let s=l(i.id,void 0!==i.output&&i.parent?b(i.output,e.context,t,r.self):void 0);return b(n.output,e.context,s,r.self)}(u,t,r,u.machine.root,a)})}}return u}(c=em(c,n,r,u.flatMap(e=>e.actions),s,void 0),n,r,u,o,s,a,i);let f=[...o];"done"===c.status&&(c=em(c,n,r,f.sort((e,t)=>t.order-e.order).flatMap(e=>e.exit),s,void 0));try{if(a===t.historyValue&&function(e,t){if(e.length!==t.size)return!1;for(let r of e)if(!t.has(r))return!1;return!0}(t._nodes,o))return c;return ek(c,{_nodes:f,historyValue:a})}catch(e){throw e}}function eg(e,t,r,n){if(ei(e)){if(t[e.id]){let i=t[e.id];for(let e of i)n.add(e),eg(e,t,r,n);for(let s of i)ev(n,t,r,W(s,e.parent))}else{let i=en(e);for(let s of i.target)n.add(s),i===e.parent?.initial&&r.add(e.parent),eg(s,t,r,n);for(let s of i.target)ev(n,t,r,W(s,e.parent))}}else if("compound"===e.type){let[i]=e.initial.target;ei(i)||(n.add(i),r.add(i)),eg(i,t,r,n),ev(n,t,r,W(i,e))}else if("parallel"===e.type)for(let i of H(e).filter(e=>!ei(e)))[...n].some(e=>el(e,i))||(ei(i)||(n.add(i),r.add(i)),eg(i,t,r,n))}function ev(e,t,r,n,i){for(let s of n)if((!i||el(s,i))&&e.add(s),"parallel"===s.type)for(let n of H(s).filter(e=>!ei(e)))[...e].some(e=>el(e,n))||(e.add(n),eg(n,t,r,e))}function em(e,t,r,n,i,s){let o=s?[]:void 0,a=function e(t,r,n,i,s,o){let{machine:a}=t,u=t;for(let t of i){var c;let i="function"==typeof t,l=i?t:(c="string"==typeof t?t:t.type,a.implementations.actions[c]),f={context:u.context,event:r,self:n.self,system:n.system},h=i||"string"==typeof t?void 0:"params"in t?"function"==typeof t.params?t.params({context:u.context,event:r}):t.params:void 0;if(!l||!("resolve"in l)){n.actionExecutor({type:"string"==typeof t?t:"object"==typeof t?t.type:t.name||"(anonymous)",info:f,params:h,exec:l});continue}let[p,d,y]=l.resolve(n,u,f,h,l,s);u=p,"retryResolve"in l&&o?.push([l,d]),"execute"in l&&n.actionExecutor({type:l.type,info:f,params:d,exec:l.execute.bind(null,n,d)}),y&&(u=e(u,r,n,y,s,o))}return u}(e,t,r,n,{internalQueue:i,deferredActorIds:s},o);return o?.forEach(([e,t])=>{e.retryResolve(r,a,t)}),a}function eb(e,t,r,n){let i=e,s=[];function o(e,t,n){r.system._sendInspectionEvent({type:"@xstate.microstep",actorRef:r.self,event:t,snapshot:e,_transitions:n}),s.push(e)}if(t.type===c)return o(i=ek(ew(i,t,r),{status:"stopped"}),t,[]),{snapshot:i,microstates:s};let u=t;if(u.type!==a){let t=u,a=t.type.startsWith("xstate.error.actor"),c=e_(t,i);if(a&&!c.length)return o(i=ek(e,{status:"error",error:t.error}),t,[]),{snapshot:i,microstates:s};o(i=ey(c,e,r,u,!1,n),t,c)}let l=!0;for(;"active"===i.status;){let e=l?function(e,t){let r=new Set;for(let n of e._nodes.filter(V))e:for(let i of[n].concat(W(n,void 0)))if(i.always){for(let n of i.always)if(void 0===n.guard||z(n.guard,e.context,t,e)){r.add(n);break e}}return ef(Array.from(r),new Set(e._nodes),e.historyValue)}(i,u):[],t=e.length?i:void 0;if(!e.length){if(!n.length)break;e=e_(u=n.shift(),i)}l=(i=ey(e,i,r,u,!1,n))!==t,o(i,u,e)}return"active"!==i.status&&ew(i,u,r),{snapshot:i,microstates:s}}function ew(e,t,r){return em(e,t,r,Object.values(e.children).map(e=>F(e)),[],void 0)}function e_(e,t){return t.machine.getTransitionData(t,e)}function ex(e,t){let r=J(ec(e,t));return Y(e,[...r])}let eE=function(e){return function e(t,r){let n=g(t),i=g(r);return"string"==typeof i?"string"==typeof n&&i===n:"string"==typeof n?n in i:Object.keys(n).every(t=>t in i&&e(n[t],i[t]))}(e,this.value)},eS=function(e){return this.tags.has(e)},eT=function(e){let t=this.machine.getTransitionData(this,e);return!!t?.length&&t.some(e=>void 0!==e.target||e.actions.length)},eC=function(){let{_nodes:e,tags:t,machine:r,getMeta:n,toJSON:i,can:s,hasTag:o,matches:a,...u}=this;return{...u,tags:Array.from(t)}},eI=function(){return this._nodes.reduce((e,t)=>(void 0!==t.meta&&(e[t.id]=t.meta),e),{})};function eR(e,t){return{status:e.status,output:e.output,error:e.error,machine:t,context:e.context,_nodes:e._nodes,value:Y(t.root,e._nodes),tags:new Set(e._nodes.flatMap(e=>e.tags)),children:e.children,historyValue:e.historyValue||{},matches:eE,hasTag:eS,can:eT,getMeta:eI,toJSON:eC}}function ek(e,t={}){return eR({...e,...t},e.machine)}function e$(e,t){let{_nodes:r,tags:n,machine:i,children:s,context:o,can:a,hasTag:u,matches:c,getMeta:l,toJSON:f,...h}=e,p={};for(let e in s){let r=s[e];p[e]={snapshot:r.getPersistedSnapshot(t),src:r.src,systemId:r._systemId,syncSnapshot:r._syncSnapshot}}return{...h,context:function e(t){let r;for(let n in t){let i=t[n];if(i&&"object"==typeof i){if("sessionId"in i&&"send"in i&&"ref"in i)(r??=Array.isArray(t)?t.slice():{...t})[n]={xstate$$type:k,id:i.id};else{let s=e(i);s!==i&&((r??=Array.isArray(t)?t.slice():{...t})[n]=s)}}}return r??t}(o),children:p,historyValue:function(e){if("object"!=typeof e||null===e)return{};let t={};for(let r in e){let n=e[r];Array.isArray(n)&&(t[r]=n.map(e=>({id:e.id})))}return t}(h.historyValue)}}function eA(e,t,r,n,{event:i,id:s,delay:o},{internalQueue:a}){let u;let c=t.machine.implementations.delays;if("string"==typeof i)throw Error(`Only event objects may be used with raise; use raise({ type: "${i}" }) instead`);let l="function"==typeof i?i(r,n):i;if("string"==typeof o){let e=c&&c[o];u="function"==typeof e?e(r,n):e}else u="function"==typeof o?o(r,n):o;return"number"!=typeof u&&a.push(l),[t,{event:l,id:s,delay:u},void 0]}function eq(e,t){let{event:r,delay:n,id:i}=t;if("number"==typeof n){e.defer(()=>{let t=e.self;e.system.scheduler.schedule(t,t,r,n,i)});return}}function eO(e,t){function r(e,t){}return r.type="xstate.raise",r.event=e,r.id=t?.id,r.delay=t?.delay,r.resolve=eA,r.execute=eq,r}},7289:(e,t,r)=>{"use strict";r.d(t,{DT:()=>s,mj:()=>h}),r(8691);var n=r(8596),i=r(3915);function s(e,t){let r=(0,n.t)(t);if(!r.includes(e.type)){let t=1===r.length?`type "${r[0]}"`:`one of types "${r.join('", "')}"`;throw Error(`Expected event ${JSON.stringify(e)} to have ${t}`)}}let o=new WeakMap;function a(e,t,r){let n=o.get(e);return n?t in n||(n[t]=r()):(n={[t]:r()},o.set(e,n)),n[t]}let u={},c=e=>"string"==typeof e?{type:e}:"function"==typeof e?"resolve"in e?{type:e.type}:{type:e.name}:e;class l{constructor(e,t){if(this.config=e,this.key=void 0,this.id=void 0,this.type=void 0,this.path=void 0,this.states=void 0,this.history=void 0,this.entry=void 0,this.exit=void 0,this.parent=void 0,this.machine=void 0,this.meta=void 0,this.output=void 0,this.order=-1,this.description=void 0,this.tags=[],this.transitions=void 0,this.always=void 0,this.parent=t._parent,this.key=t._key,this.machine=t._machine,this.path=this.parent?this.parent.path.concat(this.key):[],this.id=this.config.id||[this.machine.id,...this.path].join(n.S),this.type=this.config.type||(this.config.states&&Object.keys(this.config.states).length?"compound":this.config.history?"history":"atomic"),this.description=this.config.description,this.order=this.machine.idMap.size,this.machine.idMap.set(this.id,this),this.states=this.config.states?(0,n.m)(this.config.states,(e,t)=>new l(e,{_parent:this,_key:t,_machine:this.machine})):u,"compound"===this.type&&!this.config.initial)throw Error(`No initial state specified for compound state node "#${this.id}". Try adding { initial: "${Object.keys(this.states)[0]}" } to the state config.`);this.history=!0===this.config.history?"shallow":this.config.history||!1,this.entry=(0,n.t)(this.config.entry).slice(),this.exit=(0,n.t)(this.config.exit).slice(),this.meta=this.config.meta,this.output="final"!==this.type&&this.parent?void 0:this.config.output,this.tags=(0,n.t)(e.tags).slice()}_initialize(){this.transitions=(0,n.f)(this),this.config.always&&(this.always=(0,n.a)(this.config.always).map(e=>(0,n.b)(this,n.N,e))),Object.keys(this.states).forEach(e=>{this.states[e]._initialize()})}get definition(){return{id:this.id,key:this.key,version:this.machine.version,type:this.type,initial:this.initial?{target:this.initial.target,source:this,actions:this.initial.actions.map(c),eventType:null,reenter:!1,toJSON:()=>({target:this.initial.target.map(e=>`#${e.id}`),source:`#${this.id}`,actions:this.initial.actions.map(c),eventType:null})}:void 0,history:this.history,states:(0,n.m)(this.states,e=>e.definition),on:this.on,transitions:[...this.transitions.values()].flat().map(e=>({...e,actions:e.actions.map(c)})),entry:this.entry.map(c),exit:this.exit.map(c),meta:this.meta,order:this.order||-1,output:this.output,invoke:this.invoke,description:this.description,tags:this.tags}}toJSON(){return this.definition}get invoke(){return a(this,"invoke",()=>(0,n.t)(this.config.invoke).map((e,t)=>{let{src:r,systemId:i}=e,s=e.id??(0,n.c)(this.id,t),o="string"==typeof r?r:`xstate.invoke.${(0,n.c)(this.id,t)}`;return{...e,src:o,id:s,systemId:i,toJSON(){let{onDone:t,onError:r,...n}=e;return{...n,type:"xstate.invoke",src:o,id:s}}}}))}get on(){return a(this,"on",()=>[...this.transitions].flatMap(([e,t])=>t.map(t=>[e,t])).reduce((e,[t,r])=>(e[t]=e[t]||[],e[t].push(r),e),{}))}get after(){return a(this,"delayedTransitions",()=>(0,n.g)(this))}get initial(){return a(this,"initial",()=>(0,n.d)(this,this.config.initial))}next(e,t){let r;let i=t.type,s=[];for(let o of a(this,`candidates-${i}`,()=>(0,n.h)(this,i))){let{guard:a}=o,u=e.context,c=!1;try{c=!a||(0,n.e)(a,u,t,e)}catch(t){let e="string"==typeof a?a:"object"==typeof a?a.type:void 0;throw Error(`Unable to evaluate guard ${e?`'${e}' `:""}in transition for event '${i}' in state node '${this.id}':
${t.message}`)}if(c){s.push(...o.actions),r=o;break}}return r?[r]:void 0}get events(){return a(this,"events",()=>{let{states:e}=this,t=new Set(this.ownEvents);if(e)for(let r of Object.keys(e)){let n=e[r];if(n.states)for(let e of n.events)t.add(`${e}`)}return Array.from(t)})}get ownEvents(){return Array.from(new Set([...this.transitions.keys()].filter(e=>this.transitions.get(e).some(e=>!(!e.target&&!e.actions.length&&!e.reenter)))))}}class f{constructor(e,t){this.config=e,this.version=void 0,this.schemas=void 0,this.implementations=void 0,this.__xstatenode=!0,this.idMap=new Map,this.root=void 0,this.id=void 0,this.states=void 0,this.events=void 0,this.id=e.id||"(machine)",this.implementations={actors:t?.actors??{},actions:t?.actions??{},delays:t?.delays??{},guards:t?.guards??{}},this.version=this.config.version,this.schemas=this.config.schemas,this.transition=this.transition.bind(this),this.getInitialSnapshot=this.getInitialSnapshot.bind(this),this.getPersistedSnapshot=this.getPersistedSnapshot.bind(this),this.restoreSnapshot=this.restoreSnapshot.bind(this),this.start=this.start.bind(this),this.root=new l(e,{_key:this.id,_machine:this}),this.root._initialize(),this.states=this.root.states,this.events=this.root.events}provide(e){let{actions:t,guards:r,actors:n,delays:i}=this.implementations;return new f(this.config,{actions:{...t,...e.actions},guards:{...r,...e.guards},actors:{...n,...e.actors},delays:{...i,...e.delays}})}resolveState(e){let t=(0,n.r)(this.root,e.value),r=(0,n.i)((0,n.j)(this.root,t));return(0,n.k)({_nodes:[...r],context:e.context||{},children:{},status:(0,n.l)(r,this.root)?"done":e.status||"active",output:e.output,error:e.error,historyValue:e.historyValue},this)}transition(e,t,r){return(0,n.n)(e,t,r,[]).snapshot}microstep(e,t,r){return(0,n.n)(e,t,r,[]).microstates}getTransitionData(e,t){return(0,n.o)(this.root,e.value,e,t)||[]}getPreInitialState(e,t,r){let{context:s}=this.config,o=(0,n.k)({context:"function"!=typeof s&&s?s:{},_nodes:[this.root],children:{},status:"active"},this);return"function"==typeof s?(0,n.p)(o,t,e,[(0,i.a)(({spawn:e,event:t,self:r})=>s({spawn:e,input:t.input,self:r}))],r,void 0):o}getInitialSnapshot(e,t){let r=(0,n.q)(t),i=[],s=this.getPreInitialState(e,r,i),o=(0,n.s)([{target:[...(0,n.u)(this.root)],source:this.root,reenter:!0,actions:[],eventType:null,toJSON:null}],s,e,r,!0,i),{snapshot:a}=(0,n.n)(o,r,e,i);return a}start(e){Object.values(e.children).forEach(e=>{"active"===e.getSnapshot().status&&e.start()})}getStateNodeById(e){let t=(0,n.v)(e),r=t.slice(1),i=(0,n.w)(t[0])?t[0].slice(1):t[0],s=this.idMap.get(i);if(!s)throw Error(`Child state node '#${i}' does not exist on machine '${this.id}'`);return(0,n.x)(s,r)}get definition(){return this.root.definition}toJSON(){return this.definition}getPersistedSnapshot(e,t){return(0,n.y)(e,t)}restoreSnapshot(e,t){let r={},i=e.children;Object.keys(i).forEach(e=>{let s=i[e],o=s.snapshot,a=s.src,u="string"==typeof a?(0,n.z)(this,a):a;if(!u)return;let c=(0,n.A)(u,{id:e,parent:t.self,syncSnapshot:s.syncSnapshot,snapshot:o,src:a,systemId:s.systemId});r[e]=c});let s=function(e,t){if(!e||"object"!=typeof e)return{};let r={};for(let i in e){let s=e[i];r[i]=s.map(e=>{if(e instanceof l)return e;if("object"==typeof e&&"id"in e&&"string"==typeof e.id){let r=function(e,t){try{return t.machine.getStateNodeById(e)}catch{try{return(0,n.x)(t,e.split("."))}catch{return e}}}(e.id,t);if(r instanceof l)return r}}).filter(e=>void 0!==e),0===r[i].length&&delete r[i]}return r}(e.historyValue,this.root),o=(0,n.k)({...e,children:r,_nodes:Array.from((0,n.i)((0,n.j)(this.root,e.value))),historyValue:s},this),a=new Set;return!function e(t,r){if(!a.has(t))for(let i in a.add(t),t){let s=t[i];if(s&&"object"==typeof s){if("xstate$$type"in s&&s.xstate$$type===n.$){t[i]=r[s.id];continue}e(s,r)}}}(o.context,r),o}}function h({schemas:e,actors:t,actions:r,guards:n,delays:i}){return{createMachine:s=>new f({...s,schemas:e},{actors:t,actions:r,guards:n,delays:i})}}},5239:(e,t,r)=>{"use strict";r.d(t,{A_:()=>g,C:()=>h,IM:()=>w,R8:()=>y,T4:()=>v,_S:()=>b,ct:()=>m,iz:()=>_});let n=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,i=/_key\s*==\s*['"](.*)['"]/,s=/^\d*:\d*$/;function o(e){return"number"==typeof e||"string"==typeof e&&/^\[\d+\]$/.test(e)}function a(e){return"string"==typeof e?i.test(e.trim()):"object"==typeof e&&"_key"in e}function u(e){if("string"==typeof e&&s.test(e))return!0;if(!Array.isArray(e)||2!==e.length)return!1;let[t,r]=e;return("number"==typeof t||""===t)&&("number"==typeof r||""===r)}function c(e){if(!Array.isArray(e))throw Error("Path is not an array");return e.reduce((e,t,r)=>{let n=typeof t;if("number"===n)return`${e}[${t}]`;if("string"===n)return`${e}${0===r?"":"."}${t}`;if(a(t)&&t._key)return`${e}[_key=="${t._key}"]`;if(Array.isArray(t)){let[r,n]=t;return`${e}[${r}:${n}]`}throw Error(`Unsupported path segment \`${JSON.stringify(t)}\``)},"")}function l(e){if("string"!=typeof e)throw Error("Path is not a string");let t=e.match(n);if(!t)throw Error("Invalid path string");return t.map(f)}function f(e){return o(e)?Number(e.replace(/[^\d]/g,"")):a(e)?{_key:e.match(i)[1]}:u(e)?function(e){let[t,r]=e.split(":").map(e=>""===e?e:Number(e));return[t,r]}(e):e}var h=Object.freeze({__proto__:null,fromString:l,get:function(e,t,r){let n="string"==typeof t?l(t):t;if(!Array.isArray(n))throw Error("Path must be an array or a string");let i=e;for(let e=0;e<n.length;e++){let t=n[e];if(o(t)){if(!Array.isArray(i))return r;i=i[t]}if(a(t)){if(!Array.isArray(i))return r;i=i.find(e=>e._key===t._key)}if("string"==typeof t&&(i="object"==typeof i&&null!==i?i[t]:void 0),typeof i>"u")return r}return i},isIndexSegment:o,isIndexTuple:u,isKeySegment:a,reKeySegment:i,toString:c});let p="drafts.",d="versions.";function y(e){return e.startsWith(p)}function g(e){return e.startsWith(d)}function v(e){return g(e)?p+w(e):y(e)?e:p+e}function m(e,t){if("drafts"===t||"published"===t)throw Error('Version can not be "published" or "drafts"');return`${d}${t}.${w(e)}`}function b(e){if(!g(e))return;let[t,r,...n]=e.split(".");return r}function w(e){return g(e)?e.split(".").slice(2).join("."):y(e)?e.slice(p.length):e}function _(e){let{baseUrl:t,workspace:r="default",tool:n="default",id:i,type:s,path:o,projectId:a,dataset:u}=e;if(!t)throw Error("baseUrl is required");if(!o)throw Error("path is required");if(!i)throw Error("id is required");if("/"!==t&&t.endsWith("/"))throw Error("baseUrl must not end with a slash");let l="default"===r?void 0:r,f="default"===n?void 0:n,h=w(i),p=Array.isArray(o)?c(o.map(e=>{if("string"==typeof e||"number"==typeof e)return e;if(""!==e._key)return{_key:e._key};if(-1!==e._index)return e._index;throw Error(`invalid segment:${JSON.stringify(e)}`)})):o,d=new URLSearchParams({baseUrl:t,id:h,type:s,path:p});if(l&&d.set("workspace",l),f&&d.set("tool",f),a&&d.set("projectId",a),u&&d.set("dataset",u),y(i)||g(i)){if(g(i)){let e=b(i);d.set("perspective",e)}}else d.set("perspective","published");let v=["/"===t?"":t];l&&v.push(l);let m=["mode=presentation",`id=${h}`,`type=${s}`,`path=${encodeURIComponent(p)}`];return f&&m.push(`tool=${f}`),v.push("intent","edit",`${m.join(";")}?${d}`),v.join("/")}},758:(e,t,r)=>{"use strict";r.d(t,{C:()=>o,Q:()=>c});var n={0:8203,1:8204,2:8205,3:8290,4:8291,5:8288,6:65279,7:8289,8:119155,9:119156,a:119157,b:119158,c:119159,d:119160,e:119161,f:119162},i={0:8203,1:8204,2:8205,3:65279},s=[,,,,].fill(String.fromCodePoint(i[0])).join("");function o(e,t,r="auto"){let n;return!0===r||"auto"===r&&(!(!Number.isNaN(Number(e))||/[a-z]/i.test(e)&&!/\d+(?:[-:\/]\d+){2}(?:T\d+(?:[-:\/]\d+){1,2}(\.\d+)?Z?)?/.test(e))&&Date.parse(e)||function(e){try{new URL(e,e.startsWith("/")?"https://acme.com":void 0)}catch{return!1}return!0}(e))?e:`${e}${n=JSON.stringify(t),`${s}${Array.from(n).map(e=>{let t=e.charCodeAt(0);if(t>255)throw Error(`Only ASCII edit info can be encoded. Error attempting to encode ${n} on character ${e} (${t})`);return Array.from(t.toString(4).padStart(4,"0")).map(e=>String.fromCodePoint(i[e])).join("")}).join("")}`}`}Object.fromEntries(Object.entries(i).map(e=>e.reverse())),Object.fromEntries(Object.entries(n).map(e=>e.reverse()));var a=`${Object.values(n).map(e=>`\\u{${e.toString(16)}}`).join("")}`,u=RegExp(`[${a}]{4,}`,"gu");function c(e){var t,r;return e&&JSON.parse({cleaned:(t=JSON.stringify(e)).replace(u,""),encoded:(null==(r=t.match(u))?void 0:r[0])||""}.cleaned)}},3656:(e,t,r)=>{"use strict";let n;r.d(t,{V2:()=>D,FS:()=>F,IM:()=>H,Tx:()=>z,Rr:()=>V,vd:()=>L,_K:()=>B,CC:()=>N,RM:()=>Y,Uc:()=>X,tP:()=>J});let i={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)},s=new Uint8Array(16),o=[];for(let e=0;e<256;++e)o.push((e+256).toString(16).slice(1));let a=function(e,t,r){if(i.randomUUID&&!t&&!e)return i.randomUUID();let a=(e=e||{}).random??e.rng?.()??function(){if(!n){if("undefined"==typeof crypto||!crypto.getRandomValues)throw Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");n=crypto.getRandomValues.bind(crypto)}return n(s)}();if(a.length<16)throw Error("Random bytes length must be >= 16");if(a[6]=15&a[6]|64,a[8]=63&a[8]|128,t){if((r=r||0)<0||r+16>t.length)throw RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let e=0;e<16;++e)t[r+e]=a[e];return t}return function(e,t=0){return(o[e[t+0]]+o[e[t+1]]+o[e[t+2]]+o[e[t+3]]+"-"+o[e[t+4]]+o[e[t+5]]+"-"+o[e[t+6]]+o[e[t+7]]+"-"+o[e[t+8]]+o[e[t+9]]+"-"+o[e[t+10]]+o[e[t+11]]+o[e[t+12]]+o[e[t+13]]+o[e[t+14]]+o[e[t+15]]).toLowerCase()}(a)};var u=r(8691),c=r(7289),l=r(3915),f=r(8596),h=r(9176),p=r(6476),d=r(1924),y=r(7279),g=r(6295),v=r(4255),m=r(195),b=r(5309),w=["addListener","removeListener"],_=["addEventListener","removeEventListener"],x=["on","off"];function E(e,t,r,n){if((0,m.T)(r)&&(n=r,r=void 0),n)return E(e,t,r).pipe((0,b.I)(n));var i=(0,p.zs)((0,m.T)(e.addEventListener)&&(0,m.T)(e.removeEventListener)?_.map(function(n){return function(i){return e[n](t,i,r)}}):(0,m.T)(e.addListener)&&(0,m.T)(e.removeListener)?w.map(S(e,t)):(0,m.T)(e.on)&&(0,m.T)(e.off)?x.map(S(e,t)):[],2),s=i[0],o=i[1];if(!s&&(0,v.X)(e))return(0,g.Z)(function(e){return E(e,t,r)})((0,d.Tg)(e));if(!s)throw TypeError("Invalid event target");return new y.c(function(e){var t=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return e.next(1<t.length?t:t[0])};return s(t),function(){return o(t)}})}function S(e,t){return function(r){return function(n){return e[r](t,n)}}}var T=r(1190),C=r(6019),I=r(70),R=r(4482),k=r(2894),$=r(2238),A=r(65);function q(e){return e<=0?function(){return A.w}:(0,R.N)(function(t,r){var n=0;t.subscribe((0,k._)(r,function(t){++n<=e&&(r.next(t),e<=n&&r.complete())}))})}var O=r(6879);let j=e=>({context:t})=>{let{count:r,include:n,exclude:i,responseType:s="message.received"}=e;return{count:r,domain:t.domain,from:t.connectTo,include:n?Array.isArray(n)?n:[n]:[],exclude:i?Array.isArray(i)?i:[i]:[],responseType:s,target:t.target,to:t.name}},P=e=>t=>{let{data:r}=t;return(!e.include.length||e.include.includes(r.type))&&(!e.exclude.length||!e.exclude.includes(r.type))&&r.domain===e.domain&&r.from===e.from&&r.to===e.to&&(!e.target||t.source===e.target)},U=e=>t=>({type:e,message:t}),M=(0,h.v)(()=>E(window,"message")),N=e=>(0,u.OX)(({input:t})=>M.pipe(e?(0,T.T)(e):(0,C.F)(),(0,I.p)(P(t)),(0,T.T)(U(t.responseType)),t.count?(0,C.F)(function(e,t){return void 0===t&&(t=null),t=null!=t?t:e,(0,R.N)(function(r,n){var i=[],s=0;r.subscribe((0,k._)(n,function(r){var o,a,u,c,l=null;s++%t==0&&i.push([]);try{for(var f=(0,p.Ju)(i),h=f.next();!h.done;h=f.next()){var d=h.value;d.push(r),e<=d.length&&(l=null!=l?l:[]).push(d)}}catch(e){o={error:e}}finally{try{h&&!h.done&&(a=f.return)&&a.call(f)}finally{if(o)throw o.error}}if(l)try{for(var y=(0,p.Ju)(l),g=y.next();!g.done;g=y.next()){var d=g.value;(0,$.o)(i,d),n.next(d)}}catch(e){u={error:e}}finally{try{g&&!g.done&&(c=y.return)&&c.call(y)}finally{if(u)throw u.error}}},function(){var e,t;try{for(var r=(0,p.Ju)(i),s=r.next();!s.done;s=r.next()){var o=s.value;n.next(o)}}catch(t){e={error:t}}finally{try{s&&!s.done&&(t=r.return)&&t.call(r)}finally{if(e)throw e.error}}n.complete()},void 0,function(){i=null}))})}(t.count),function(e,t){return(0,m.T)(void 0)?(0,g.Z)(e,void 0,1):(0,g.Z)(e,1)}(e=>e),q(t.count)):(0,C.F)())),D="sanity/comlink",B="comlink/response",L="comlink/heartbeat",F="comlink/disconnect",z="comlink/handshake/syn",V="comlink/handshake/syn-ack",H="comlink/handshake/ack",W=e=>t=>t.pipe(q(1),(0,T.T)(()=>{throw Error(e)})),J=()=>(0,c.mj)({types:{},actors:{listen:(0,u.OX)(({input:e})=>{let t=e.signal?E(e.signal,"abort").pipe(W(`Request ${e.requestId} aborted`)):A.w;return E(window,"message").pipe((0,I.p)(t=>t.data?.type===B&&t.data?.responseTo===e.requestId&&!!t.source&&e.sources.has(t.source)),q(e.sources.size),(0,R.N)(function(e,r){(0,d.Tg)(t).subscribe((0,k._)(r,function(){return r.complete()},O.l)),r.closed||e.subscribe(r)}))})},actions:{"send message":({context:e},t)=>{let{sources:r,targetOrigin:n}=e,{message:i}=t;r.forEach(e=>{e.postMessage(i,{targetOrigin:n})})},"on success":(0,l.c)(({context:e})=>e.parentRef,({context:e,self:t})=>(e.response&&e.resolvable?.resolve(e.response),{type:"request.success",requestId:t.id,response:e.response,responseTo:e.responseTo})),"on fail":(0,l.c)(({context:e})=>e.parentRef,({context:e,self:t})=>(e.suppressWarnings||console.warn(`[@sanity/comlink] Received no response to message '${e.type}' on client '${e.from}' (ID: '${e.id}').`),e.resolvable?.reject(Error("No response received")),{type:"request.failed",requestId:t.id})),"on abort":(0,l.c)(({context:e})=>e.parentRef,({context:e,self:t})=>(e.resolvable?.reject(Error("Request aborted")),{type:"request.aborted",requestId:t.id}))},guards:{expectsResponse:({context:e})=>e.expectResponse},delays:{initialTimeout:0,responseTimeout:({context:e})=>e.responseTimeout??3e3}}).createMachine({context:({input:e})=>({channelId:e.channelId,data:e.data,domain:e.domain,expectResponse:e.expectResponse??!1,from:e.from,id:`msg-${a()}`,parentRef:e.parentRef,resolvable:e.resolvable,response:null,responseTimeout:e.responseTimeout,responseTo:e.responseTo,signal:e.signal,sources:e.sources instanceof Set?e.sources:new Set([e.sources]),suppressWarnings:e.suppressWarnings,targetOrigin:e.targetOrigin,to:e.to,type:e.type}),initial:"idle",on:{abort:".aborted"},states:{idle:{after:{initialTimeout:[{target:"sending"}]}},sending:{entry:{type:"send message",params:({context:e})=>{let{channelId:t,data:r,domain:n,from:i,id:s,responseTo:o,to:a,type:u}=e;return{message:{channelId:t,data:r,domain:n,from:i,id:s,to:a,type:u,responseTo:o}}}},always:[{guard:"expectsResponse",target:"awaiting"},"success"]},awaiting:{invoke:{id:"listen for response",src:"listen",input:({context:e})=>({requestId:e.id,sources:e.sources,signal:e.signal}),onError:"aborted"},after:{responseTimeout:"failed"},on:{message:{actions:(0,l.a)({response:({event:e})=>e.data.data,responseTo:({event:e})=>e.data.responseTo}),target:"success"}}},failed:{type:"final",entry:"on fail"},success:{type:"final",entry:"on success"},aborted:{type:"final",entry:"on abort"}},output:({context:e,self:t})=>({requestId:t.id,response:e.response,responseTo:e.responseTo})});(0,u.SP)(({sendBack:e,input:t})=>{let r=()=>{e(t.event)};t.immediate&&r();let n=setInterval(r,t.interval);return()=>{clearInterval(n)}});let X=()=>(0,c.mj)({types:{},actors:{requestMachine:J(),listen:N()},actions:{"buffer handshake":(0,l.a)({handshakeBuffer:({event:e,context:t})=>((0,c.DT)(e,"message.received"),[...t.handshakeBuffer,e])}),"buffer message":(0,l.b)(({enqueue:e})=>{e.assign({buffer:({event:e,context:t})=>((0,c.DT)(e,"post"),[...t.buffer,{data:e.data,resolvable:e.resolvable,options:e.options}])}),e.emit(({event:e})=>((0,c.DT)(e,"post"),{type:"buffer.added",message:e.data}))}),"create request":(0,l.a)({requests:({context:e,event:t,self:r,spawn:n})=>{(0,c.DT)(t,"request");let i=(Array.isArray(t.data)?t.data:[t.data]).map(t=>n("requestMachine",{id:`req-${a()}`,input:{channelId:e.channelId,data:t.data,domain:e.domain,expectResponse:t.expectResponse,from:e.name,parentRef:r,resolvable:t.resolvable,responseTimeout:t.options?.responseTimeout,responseTo:t.responseTo,signal:t.options?.signal,sources:e.target,suppressWarnings:t.options?.suppressWarnings,targetOrigin:e.targetOrigin,to:e.connectTo,type:t.type}}));return[...e.requests,...i]}}),"emit heartbeat":(0,l.e)(()=>({type:"heartbeat"})),"emit received message":(0,l.b)(({enqueue:e})=>{e.emit(({event:e})=>((0,c.DT)(e,"message.received"),{type:"message",message:e.message.data}))}),"emit status":(0,l.e)((e,t)=>({type:"status",status:t.status})),"post message":(0,f.O)(({event:e})=>((0,c.DT)(e,"post"),{type:"request",data:{data:e.data.data,expectResponse:!!e.resolvable,type:e.data.type,resolvable:e.resolvable,options:e.options}})),"process pending handshakes":(0,l.b)(({context:e,enqueue:t})=>{e.handshakeBuffer.forEach(e=>t.raise(e)),t.assign({handshakeBuffer:[]})}),"remove request":(0,l.b)(({context:e,enqueue:t,event:r})=>{(0,c.DT)(r,["request.success","request.failed","request.aborted"]),(0,f.R)(r.requestId),t.assign({requests:e.requests.filter(({id:e})=>e!==r.requestId)})}),"send response":(0,f.O)(({event:e})=>((0,c.DT)(e,["message.received","heartbeat.received"]),{type:"request",data:{type:B,responseTo:e.message.data.id,data:void 0}})),"send handshake syn ack":(0,f.O)({type:"request",data:{type:V}}),"send pending messages":(0,l.b)(({enqueue:e})=>{e.raise(({context:e})=>({type:"request",data:e.buffer.map(({data:e,resolvable:t,options:r})=>({data:e.data,type:e.type,expectResponse:!!t,resolvable:t,options:r}))})),e.emit(({context:e})=>({type:"buffer.flushed",messages:e.buffer.map(({data:e})=>e)})),e.assign({buffer:[]})}),"set connection config":(0,l.a)({channelId:({event:e})=>((0,c.DT)(e,"handshake.syn"),e.message.data.channelId),target:({event:e})=>((0,c.DT)(e,"handshake.syn"),e.message.source||void 0),targetOrigin:({event:e})=>((0,c.DT)(e,"handshake.syn"),e.message.origin)})},guards:{hasSource:({context:e})=>null!==e.target}}).createMachine({id:"node",context:({input:e})=>({buffer:[],channelId:null,connectTo:e.connectTo,domain:e.domain??D,handshakeBuffer:[],name:e.name,requests:[],target:void 0,targetOrigin:null}),invoke:{id:"listen for handshake syn",src:"listen",input:j({include:z,responseType:"handshake.syn"})},on:{"request.success":{actions:"remove request"},"request.failed":{actions:"remove request"},"request.aborted":{actions:"remove request"},"handshake.syn":{actions:"set connection config",target:".handshaking"}},initial:"idle",states:{idle:{entry:[{type:"emit status",params:{status:"idle"}}],on:{post:{actions:"buffer message"}}},handshaking:{guard:"hasSource",entry:["send handshake syn ack",{type:"emit status",params:{status:"handshaking"}}],invoke:[{id:"listen for handshake ack",src:"listen",input:j({include:H,count:1,responseType:"handshake.complete"}),onDone:"connected"},{id:"listen for disconnect",src:"listen",input:j({include:F,count:1,responseType:"disconnect"})},{id:"listen for messages",src:"listen",input:j({exclude:[F,z,H,L,B]})}],on:{request:{actions:"create request"},post:{actions:"buffer message"},"message.received":{actions:"buffer handshake"},disconnect:{target:"idle"}}},connected:{entry:["process pending handshakes","send pending messages",{type:"emit status",params:{status:"connected"}}],invoke:[{id:"listen for messages",src:"listen",input:j({exclude:[F,z,H,L,B]})},{id:"listen for heartbeat",src:"listen",input:j({include:L,responseType:"heartbeat.received"})},{id:"listen for disconnect",src:"listen",input:j({include:F,count:1,responseType:"disconnect"})}],on:{request:{actions:"create request"},post:{actions:"post message"},disconnect:{target:"idle"},"message.received":{actions:["send response","emit received message"]},"heartbeat.received":{actions:["send response","emit heartbeat"]}}}}}),Y=(e,t=X())=>{let r;let n=(0,f.A)(t,{input:e}),i=new Map,s=new Map;n.on("message",({message:e})=>{let t=i.get(e.type);if(t){t.forEach(t=>t(e.data));return}let r=s.get(e.type);r?r.add(e):s.set(e.type,new Set([e]))});let o=()=>{n.stop()};return{actor:n,fetch:(e,t,r)=>{let{responseTimeout:i=1e4,signal:s,suppressWarnings:o}=r||{},a=function(){let e,t;return"function"==typeof Promise.withResolvers?Promise.withResolvers():{promise:new Promise((r,n)=>{e=r,t=n}),resolve:e,reject:t}}();return n.send({type:"post",data:{type:e,data:t},resolvable:a,options:{responseTimeout:i,signal:s,suppressWarnings:o}}),a.promise},machine:t,on:(e,t,r)=>{let n=i.get(e)||new Set;i.has(e)||i.set(e,n),n.add(t);let o=s.get(e);if(o){let n=r?.replay??1;Array.from(o).slice(-n).forEach(({data:e})=>t(e)),s.delete(e)}return()=>{n.delete(t)}},onStatus:(e,t)=>{let{unsubscribe:i}=n.on("status",n=>{r=n.status,t&&n.status!==t||e(n.status)});return r&&e(r),i},post:(e,t)=>{n.send({type:"post",data:{type:e,data:t}})},start:()=>(n.start(),o),stop:o}}},5086:(e,t,r)=>{"use strict";r.d(t,{El:()=>l,Hi:()=>u,NL:()=>s,kN:()=>a,lc:()=>f,sq:()=>c});let n=new Set,i="checking";function s(e){if(i!==e)for(let t of(i=e,n))t()}let o=new Set;function a(e){for(let e of o)e()}let u=new Set,c=null;function l(e){for(let t of(c=e,u))t()}function f(e,t){for(let e of u)e()}},64:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});var n=r(5155);let i=(0,r(7711).default)(()=>r.e(172).then(r.bind(r,5172)),{loadableGenerated:{webpack:()=>[require.resolveWeak("../_chunks-es/SanityLiveStream.js")]},ssr:!1});function s(e){return(0,n.jsx)(i,{...e})}},2918:(e,t,r)=>{"use strict";r.d(t,{default:()=>rO});var n=r(5155);let i=!(typeof navigator>"u")&&"ReactNative"===navigator.product,s={timeout:i?6e4:12e4},o=function(e){let t={...s,..."string"==typeof e?{url:e}:e};if(t.timeout=function e(t){if(!1===t||0===t)return!1;if(t.connect||t.socket)return t;let r=Number(t);return isNaN(r)?e(s.timeout):{connect:r,socket:r}}(t.timeout),t.query){let{url:e,searchParams:r}=function(e){let t=e.indexOf("?");if(-1===t)return{url:e,searchParams:new URLSearchParams};let r=e.slice(0,t),n=e.slice(t+1);if(!i)return{url:r,searchParams:new URLSearchParams(n)};if("function"!=typeof decodeURIComponent)throw Error("Broken `URLSearchParams` implementation, and `decodeURIComponent` is not defined");let s=new URLSearchParams;for(let e of n.split("&")){let[t,r]=e.split("=");t&&s.append(a(t),a(r||""))}return{url:r,searchParams:s}}(t.url);for(let[n,i]of Object.entries(t.query)){if(void 0!==i){if(Array.isArray(i))for(let e of i)r.append(n,e);else r.append(n,i)}let s=r.toString();s&&(t.url=`${e}?${s}`)}}return t.method=t.body&&!t.method?"POST":(t.method||"GET").toUpperCase(),t};function a(e){return decodeURIComponent(e.replace(/\+/g," "))}let u=/^https?:\/\//i,c=function(e){if(!u.test(e.url))throw Error(`"${e.url}" is not a valid URL`)},l=["request","response","progress","error","abort"],f=["processOptions","validateOptions","interceptRequest","finalizeOptions","onRequest","onResponse","onError","onReturn","onHeaders"];var h,p,d=function(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}(function(){if(p)return h;p=1;var e=function(e){return e.replace(/^\s+|\s+$/g,"")};return h=function(t){if(!t)return{};for(var r,n={},i=e(t).split("\n"),s=0;s<i.length;s++){var o=i[s],a=o.indexOf(":"),u=e(o.slice(0,a)).toLowerCase(),c=e(o.slice(a+1));typeof n[u]>"u"?n[u]=c:(r=n[u],"[object Array]"===Object.prototype.toString.call(r)?n[u].push(c):n[u]=[n[u],c])}return n}}());class y{onabort;onerror;onreadystatechange;ontimeout;readyState=0;response;responseText="";responseType="";status;statusText;withCredentials;#e;#t;#r;#n={};#i;#s={};#o;open(e,t,r){this.#e=e,this.#t=t,this.#r="",this.readyState=1,this.onreadystatechange?.(),this.#i=void 0}abort(){this.#i&&this.#i.abort()}getAllResponseHeaders(){return this.#r}setRequestHeader(e,t){this.#n[e]=t}setInit(e,t=!0){this.#s=e,this.#o=t}send(e){let t="arraybuffer"!==this.responseType,r={...this.#s,method:this.#e,headers:this.#n,body:e};"function"==typeof AbortController&&this.#o&&(this.#i=new AbortController,"u">typeof EventTarget&&this.#i.signal instanceof EventTarget&&(r.signal=this.#i.signal)),"u">typeof document&&(r.credentials=this.withCredentials?"include":"omit"),fetch(this.#t,r).then(e=>(e.headers.forEach((e,t)=>{this.#r+=`${t}: ${e}\r
`}),this.status=e.status,this.statusText=e.statusText,this.readyState=3,this.onreadystatechange?.(),t?e.text():e.arrayBuffer())).then(e=>{"string"==typeof e?this.responseText=e:this.response=e,this.readyState=4,this.onreadystatechange?.()}).catch(e=>{"AbortError"!==e.name?this.onerror?.(e):this.onabort?.()})}}let g="function"==typeof XMLHttpRequest?"xhr":"fetch",v="xhr"===g?XMLHttpRequest:y,m=(e,t)=>{let r=e.options,n=e.applyMiddleware("finalizeOptions",r),i={},s=e.applyMiddleware("interceptRequest",void 0,{adapter:g,context:e});if(s){let e=setTimeout(t,0,null,s);return{abort:()=>clearTimeout(e)}}let o=new v;o instanceof y&&"object"==typeof n.fetch&&o.setInit(n.fetch,n.useAbortSignal??!0);let a=n.headers,u=n.timeout,c=!1,l=!1,f=!1;if(o.onerror=e=>{m(o instanceof y?e instanceof Error?e:Error(`Request error while attempting to reach is ${n.url}`,{cause:e}):Error(`Request error while attempting to reach is ${n.url}${e.lengthComputable?`(${e.loaded} of ${e.total} bytes transferred)`:""}`))},o.ontimeout=e=>{m(Error(`Request timeout while attempting to reach ${n.url}${e.lengthComputable?`(${e.loaded} of ${e.total} bytes transferred)`:""}`))},o.onabort=()=>{p(!0),c=!0},o.onreadystatechange=function(){u&&(p(),i.socket=setTimeout(()=>h("ESOCKETTIMEDOUT"),u.socket)),!c&&o&&4===o.readyState&&0!==o.status&&function(){if(!(c||l||f)){if(0===o.status)return void m(Error("Unknown XHR error"));p(),l=!0,t(null,{body:o.response||(""===o.responseType||"text"===o.responseType?o.responseText:""),url:n.url,method:n.method,headers:d(o.getAllResponseHeaders()),statusCode:o.status,statusMessage:o.statusText})}}()},o.open(n.method,n.url,!0),o.withCredentials=!!n.withCredentials,a&&o.setRequestHeader)for(let e in a)a.hasOwnProperty(e)&&o.setRequestHeader(e,a[e]);return n.rawBody&&(o.responseType="arraybuffer"),e.applyMiddleware("onRequest",{options:n,adapter:g,request:o,context:e}),o.send(n.body||null),u&&(i.connect=setTimeout(()=>h("ETIMEDOUT"),u.connect)),{abort:function(){c=!0,o&&o.abort()}};function h(t){f=!0,o.abort();let r=Error("ESOCKETTIMEDOUT"===t?`Socket timed out on request to ${n.url}`:`Connection timed out on request to ${n.url}`);r.code=t,e.channels.error.publish(r)}function p(e){(e||c||o&&o.readyState>=2&&i.connect)&&clearTimeout(i.connect),i.socket&&clearTimeout(i.socket)}function m(e){if(l)return;p(!0),l=!0,o=null;let r=e||Error(`Network error while attempting to reach ${n.url}`);r.isNetworkError=!0,r.request=n,t(r)}},b=(e=[],t=m)=>(function e(t,r){let n=[],i=f.reduce((e,t)=>(e[t]=e[t]||[],e),{processOptions:[o],validateOptions:[c]});function s(e){let t;let n=l.reduce((e,t)=>(e[t]=function(){let e=Object.create(null),t=0;return{publish:function(t){for(let r in e)e[r](t)},subscribe:function(r){let n=t++;return e[n]=r,function(){delete e[n]}}}}(),e),{}),s=function(e,t,...r){let n="onError"===e,s=t;for(let t=0;t<i[e].length&&(s=(0,i[e][t])(s,...r),!n||s);t++);return s},o=s("processOptions",e);s("validateOptions",o);let a={options:o,channels:n,applyMiddleware:s},u=n.request.subscribe(e=>{t=r(e,(t,r)=>((e,t,r)=>{let i=e,o=t;if(!i)try{o=s("onResponse",t,r)}catch(e){o=null,i=e}(i=i&&s("onError",i,r))?n.error.publish(i):o&&n.response.publish(o)})(t,r,e))});n.abort.subscribe(()=>{u(),t&&t.abort()});let c=s("onReturn",n,a);return c===n&&n.request.publish(a),c}return s.use=function(e){if(!e)throw Error("Tried to add middleware that resolved to falsey value");if("function"==typeof e)throw Error("Tried to add middleware that was a function. It probably expects you to pass options to it.");if(e.onReturn&&i.onReturn.length>0)throw Error("Tried to add new middleware with `onReturn` handler, but another handler has already been registered for this event");return f.forEach(t=>{e[t]&&i[t].push(e[t])}),n.push(e),s},s.clone=()=>e(n,r),t.forEach(s.use),s})(e,t);var w,_,x,E,S,T=r(7358),C=r(5927).hp,I={exports:{}};S||(S=1,function(e,t){let r;t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let r="color: "+this.color;t.splice(1,0,r,"color: inherit");let n=0,i=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(n++,"%c"===e&&(i=n))}),t.splice(i,0,r)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch{}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch{}return!e&&"u">typeof T&&"env"in T&&(e=T.env.DEBUG),e},t.useColors=function(){let e;return"u">typeof window&&!!window.process&&("renderer"===window.process.type||!!window.process.__nwjs)||!("u">typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("u">typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"u">typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"u">typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"u">typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch{}}(),t.destroy=(r=!1,()=>{r||(r=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=(E?x:(E=1,x=function(e){function t(e){let n,i,s,o=null;function a(...e){if(!a.enabled)return;let r=Number(new Date),i=r-(n||r);a.diff=i,a.prev=n,a.curr=r,n=r,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let s=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(r,n)=>{if("%%"===r)return"%";s++;let i=t.formatters[n];if("function"==typeof i){let t=e[s];r=i.call(a,t),e.splice(s,1),s--}return r}),t.formatArgs.call(a,e),(a.log||t.log).apply(a,e)}return a.namespace=e,a.useColors=t.useColors(),a.color=t.selectColor(e),a.extend=r,a.destroy=t.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==o?o:(i!==t.namespaces&&(i=t.namespaces,s=t.enabled(e)),s),set:e=>{o=e}}),"function"==typeof t.init&&t.init(a),a}function r(e,r){let n=t(this.namespace+(typeof r>"u"?":":r)+e);return n.log=this.log,n}function n(e,t){let r=0,n=0,i=-1,s=0;for(;r<e.length;)if(n<t.length&&(t[n]===e[r]||"*"===t[n]))"*"===t[n]?(i=n,s=r):r++,n++;else{if(-1===i)return!1;n=i+1,r=++s}for(;n<t.length&&"*"===t[n];)n++;return n===t.length}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){let e=[...t.names,...t.skips.map(e=>"-"+e)].join(",");return t.enable(""),e},t.enable=function(e){for(let r of(t.save(e),t.namespaces=e,t.names=[],t.skips=[],("string"==typeof e?e:"").trim().replace(" ",",").split(",").filter(Boolean)))"-"===r[0]?t.skips.push(r.slice(1)):t.names.push(r)},t.enabled=function(e){for(let r of t.skips)if(n(e,r))return!1;for(let r of t.names)if(n(e,r))return!0;return!1},t.humanize=function(){if(_)return w;function e(e,t,r,n){return Math.round(e/r)+" "+n+(t>=1.5*r?"s":"")}return _=1,w=function(t,r){r=r||{};var n,i,s=typeof t;if("string"===s&&t.length>0)return function(e){if(!((e=String(e)).length>100)){var t=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(t){var r=parseFloat(t[1]);switch((t[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*r;case"weeks":case"week":case"w":return 6048e5*r;case"days":case"day":case"d":return 864e5*r;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*r;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*r;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*r;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return r;default:return}}}}(t);if("number"===s&&isFinite(t))return r.long?(i=Math.abs(t))>=864e5?e(t,i,864e5,"day"):i>=36e5?e(t,i,36e5,"hour"):i>=6e4?e(t,i,6e4,"minute"):i>=1e3?e(t,i,1e3,"second"):t+" ms":(n=Math.abs(t))>=864e5?Math.round(t/864e5)+"d":n>=36e5?Math.round(t/36e5)+"h":n>=6e4?Math.round(t/6e4)+"m":n>=1e3?Math.round(t/1e3)+"s":t+"ms";throw Error("val is not a non-empty string or a valid number. val="+JSON.stringify(t))}}(),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(r=>{t[r]=e[r]}),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let r=0;for(let t=0;t<e.length;t++)r=(r<<5)-r+e.charCodeAt(t)|0;return t.colors[Math.abs(r)%t.colors.length]},t.enable(t.load()),t}))(t);let{formatters:n}=e.exports;n.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}}(I,I.exports)),I.exports,Object.prototype.hasOwnProperty;let R=typeof C>"u"?()=>!1:e=>C.isBuffer(e);function k(e){return"[object Object]"===Object.prototype.toString.call(e)}let $=["boolean","string","number"],A={};"u">typeof globalThis?A=globalThis:"u">typeof window?A=window:"u">typeof global?A=global:"u">typeof self&&(A=self);var q=A;let O=(e={})=>{let t=e.implementation||Promise;if(!t)throw Error("`Promise` is not available in global scope, and no implementation was passed");return{onReturn:(r,n)=>new t((t,i)=>{let s=n.options.cancelToken;s&&s.promise.then(e=>{r.abort.publish(e),i(e)}),r.error.subscribe(i),r.response.subscribe(r=>{t(e.onlyBody?r.body:r)}),setTimeout(()=>{try{r.request.publish(n)}catch(e){i(e)}},0)})}};class j{__CANCEL__=!0;message;constructor(e){this.message=e}toString(){return"Cancel"+(this.message?`: ${this.message}`:"")}}class P{promise;reason;constructor(e){if("function"!=typeof e)throw TypeError("executor must be a function.");let t=null;this.promise=new Promise(e=>{t=e}),e(e=>{this.reason||(this.reason=new j(e),t(this.reason))})}static source=()=>{let e;return{token:new P(t=>{e=t}),cancel:e}}}O.Cancel=j,O.CancelToken=P,O.isCancel=e=>!(!e||!e?.__CANCEL__);var U=(e,t,r)=>("GET"===r.method||"HEAD"===r.method)&&(e.isNetworkError||!1);function M(e){return 100*Math.pow(2,e)+100*Math.random()}let N=(e={})=>(e=>{let t=e.maxRetries||5,r=e.retryDelay||M,n=e.shouldRetry;return{onError:(e,i)=>{var s;let o=i.options,a=o.maxRetries||t,u=o.retryDelay||r,c=o.shouldRetry||n,l=o.attemptNumber||0;if(null!==(s=o.body)&&"object"==typeof s&&"function"==typeof s.pipe||!c(e,l,o)||l>=a)return e;let f=Object.assign({},i,{options:Object.assign({},o,{attemptNumber:l+1})});return setTimeout(()=>i.channels.request.publish(f),u(l)),null}}})({shouldRetry:U,...e});N.shouldRetry=U;var D=r(7279),B=r(9176),L=r(195),F=r(6274),z=r(7994);function V(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=(0,F.lI)(e);return(0,z.H)(e,r)}var H=r(6295),W=(0,r(2985).L)(function(e){return function(){e(this),this.name="EmptyError",this.message="no elements in sequence"}});function J(e,t){var r="object"==typeof t;return new Promise(function(n,i){var s,o=!1;e.subscribe({next:function(e){s=e,o=!0},error:i,complete:function(){o?n(s):r?n(t.defaultValue):i(new W)}})})}var X=r(2260),Y=r(1924),G=r(2894),Q=r(4482);function Z(e){return(0,Q.N)(function(t,r){var n,i=null,s=!1;i=t.subscribe((0,G._)(r,void 0,void 0,function(o){n=(0,Y.Tg)(e(o,Z(e)(t))),i?(i.unsubscribe(),i=null,n.subscribe(r)):s=!0})),s&&(i.unsubscribe(),i=null,n.subscribe(r))})}var K=r(3087);function ee(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return(0,K.U)(1)((0,z.H)(e,(0,F.lI)(e)))}var et=r(1444),er=new(r(936)).q(et.R);function en(e,t){var r=(0,L.T)(e)?e:function(){return e},n=function(e){return e.error(r())};return new D.c(t?function(e){return t.schedule(n,0,e)}:n)}r(220);var ei=r(8830);function es(e){return(0,Q.N)(function(t,r){try{t.subscribe(r)}finally{r.add(e)}})}var eo=r(8983),ea=r(560),eu=r(65),ec=r(1190),el=r(3996);function ef(e,t){var r="object"==typeof t;return new Promise(function(n,i){var s=new el.Ms({next:function(e){n(e),s.unsubscribe()},error:i,complete:function(){r?n(t.defaultValue):i(new W)}});e.subscribe(s)})}var eh=r(758),ep=r(6476),ed=r(5623);function ey(e,t,r){e?(0,ed.N)(r,e,t):t()}var eg=Array.isArray,ev=r(5309),em=r(6019);function eb(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=(0,F.ms)(e);return r?(0,em.F)(eb.apply(void 0,(0,ep.fX)([],(0,ep.zs)(e))),(0,ev.I)(r)):(0,Q.N)(function(t,r){var n,i,s;(n=(0,ep.fX)([t],(0,ep.zs)(1===e.length&&eg(e[0])?e[0]:e)),void 0===s&&(s=ei.D),function(e){ey(void 0,function(){for(var t=n.length,r=Array(t),o=t,a=t,u=function(t){ey(i,function(){var u=(0,z.H)(n[t],i),c=!1;u.subscribe((0,G._)(e,function(n){r[t]=n,!c&&(c=!0,a--),a||e.next(s(r.slice()))},function(){--o||e.complete()}))},e)},c=0;c<t;c++)u(c)},e)})(r)})}var ew=r(70),e_=r(5239);let ex=e=>crypto.getRandomValues(new Uint8Array(e)),eE=(e,t,r)=>{let n=(2<<Math.log(e.length-1)/Math.LN2)-1,i=-~(1.6*n*t/e.length);return (s=t)=>{let o="";for(;;){let t=r(i),a=0|i;for(;a--;)if((o+=e[t[a]&n]||"").length===s)return o}}};class eS extends Error{response;statusCode=400;responseBody;details;constructor(e){let t=eC(e);super(t.message),Object.assign(this,t)}}class eT extends Error{response;statusCode=500;responseBody;details;constructor(e){let t=eC(e);super(t.message),Object.assign(this,t)}}function eC(e){let t=e.body,r={response:e,statusCode:e.statusCode,responseBody:-1!==(e.headers["content-type"]||"").toLowerCase().indexOf("application/json")?JSON.stringify(t,null,2):t,message:"",details:void 0};if(t.error&&t.message)return r.message=`${t.error} - ${t.message}`,r;if(eI(t)&&eI(t.error)&&"mutationError"===t.error.type&&"string"==typeof t.error.description||eI(t)&&eI(t.error)&&"actionError"===t.error.type&&"string"==typeof t.error.description){let e=t.error.items||[],n=e.slice(0,5).map(e=>e.error?.description).filter(Boolean),i=n.length?`:
- ${n.join(`
- `)}`:"";return e.length>5&&(i+=`
...and ${e.length-5} more`),r.message=`${t.error.description}${i}`,r.details=t.error,r}return t.error&&t.error.description?(r.message=t.error.description,r.details=t.error):r.message=t.error||t.message||function(e){let t=e.statusMessage?` ${e.statusMessage}`:"";return`${e.method}-request to ${e.url} resulted in HTTP ${e.statusCode}${t}`}(e),r}function eI(e){return"object"==typeof e&&null!==e&&!Array.isArray(e)}class eR extends Error{projectId;addOriginUrl;constructor({projectId:e}){super("CorsOriginError"),this.name="CorsOriginError",this.projectId=e;let t=new URL(`https://sanity.io/manage/project/${e}/api`);if("u">typeof location){let{origin:e}=location;t.searchParams.set("cors","add"),t.searchParams.set("origin",e),this.addOriginUrl=t,this.message=`The current origin is not allowed to connect to the Live Content API. Add it here: ${t}`}else this.message=`The current origin is not allowed to connect to the Live Content API. Change your configuration here: ${t}`}}let ek={onResponse:e=>{if(e.statusCode>=500)throw new eT(e);if(e.statusCode>=400)throw new eS(e);return e}};function e$(e){return b([N({shouldRetry:eA}),...e,function(){let e={};return{onResponse:t=>{let r=t.headers["x-sanity-warning"];for(let t of Array.isArray(r)?r:[r])!t||e[t]||(e[t]=!0,console.warn(t));return t}}}(),{processOptions:e=>{let t=e.body;return!t||"function"==typeof t.pipe||R(t)||-1===$.indexOf(typeof t)&&!Array.isArray(t)&&!function(e){if(!1===k(e))return!1;let t=e.constructor;if(void 0===t)return!0;let r=t.prototype;return!(!1===k(r)||!1===r.hasOwnProperty("isPrototypeOf"))}(t)?e:Object.assign({},e,{body:JSON.stringify(e.body),headers:Object.assign({},e.headers,{"Content-Type":"application/json"})})}},{onResponse:e=>{let t=e.headers["content-type"]||"",r=-1!==t.indexOf("application/json");return e.body&&t&&r?Object.assign({},e,{body:function(e){try{return JSON.parse(e)}catch(e){throw e.message=`Failed to parsed response body as JSON: ${e.message}`,e}}(e.body)}):e},processOptions:e=>Object.assign({},e,{headers:Object.assign({Accept:"application/json"},e.headers)})},{onRequest:e=>{if("xhr"!==e.adapter)return;let t=e.request,r=e.context;function n(e){return t=>{let n=t.lengthComputable?t.loaded/t.total*100:-1;r.channels.progress.publish({stage:e,percent:n,total:t.total,loaded:t.loaded,lengthComputable:t.lengthComputable})}}"upload"in t&&"onprogress"in t.upload&&(t.upload.onprogress=n("upload")),"onprogress"in t&&(t.onprogress=n("download"))}},ek,function(e={}){let t=e.implementation||q.Observable;if(!t)throw Error("`Observable` is not available in global scope, and no implementation was passed");return{onReturn:(e,r)=>new t(t=>(e.error.subscribe(e=>t.error(e)),e.progress.subscribe(e=>t.next(Object.assign({type:"progress"},e))),e.response.subscribe(e=>{t.next(Object.assign({type:"response"},e)),t.complete()}),e.request.publish(r),()=>e.abort.publish()))}}({implementation:D.c})])}function eA(e,t,r){if(0===r.maxRetries)return!1;let n="GET"===r.method||"HEAD"===r.method,i=(r.uri||r.url).startsWith("/data/query"),s=e.response&&(429===e.response.statusCode||502===e.response.statusCode||503===e.response.statusCode);return(!!n||!!i)&&!!s||N.shouldRetry(e,t,r)}function eq(e){return"https://www.sanity.io/help/"+e}let eO=["image","file"],ej=["before","after","replace"],eP=e=>{if(!/^(~[a-z0-9]{1}[-\w]{0,63}|[a-z0-9]{1}[-\w]{0,63})$/.test(e))throw Error("Datasets can only contain lowercase characters, numbers, underscores and dashes, and start with tilde, and be maximum 64 characters")},eU=e=>{if(!/^[-a-z0-9]+$/i.test(e))throw Error("`projectId` can only contain only a-z, 0-9 and dashes")},eM=e=>{if(-1===eO.indexOf(e))throw Error(`Invalid asset type: ${e}. Must be one of ${eO.join(", ")}`)},eN=(e,t)=>{if(null===t||"object"!=typeof t||Array.isArray(t))throw Error(`${e}() takes an object of properties`)},eD=(e,t)=>{if("string"!=typeof t||!/^[a-z0-9_][a-z0-9_.-]{0,127}$/i.test(t)||t.includes(".."))throw Error(`${e}(): "${t}" is not a valid document ID`)},eB=(e,t)=>{if(!t._id)throw Error(`${e}() requires that the document contains an ID ("_id" property)`);eD(e,t._id)},eL=(e,t)=>{if("string"!=typeof t)throw Error(`\`${e}()\`: \`${t}\` is not a valid document type`)},eF=(e,t)=>{if(!t._type)throw Error(`\`${e}()\` requires that the document contains a type (\`_type\` property)`);eL(e,t._type)},ez=(e,t)=>{if(t._id&&t._id!==e)throw Error(`The provided document ID (\`${t._id}\`) does not match the generated version ID (\`${e}\`)`)},eV=(e,t,r)=>{let n="insert(at, selector, items)";if(-1===ej.indexOf(e)){let e=ej.map(e=>`"${e}"`).join(", ");throw Error(`${n} takes an "at"-argument which is one of: ${e}`)}if("string"!=typeof t)throw Error(`${n} takes a "selector"-argument which must be a string`);if(!Array.isArray(r))throw Error(`${n} takes an "items"-argument which must be an array`)},eH=e=>{if(!e.dataset)throw Error("`dataset` must be provided to perform queries");return e.dataset||""},eW=e=>{if("string"!=typeof e||!/^[a-z0-9._-]{1,75}$/i.test(e))throw Error("Tag can only contain alphanumeric characters, underscores, dashes and dots, and be between one and 75 characters long.");return e},eJ=e=>{if(!e["~experimental_resource"])throw Error("`resource` must be provided to perform resource queries");let{type:t,id:r}=e["~experimental_resource"];switch(t){case"dataset":if(2!==r.split(".").length)throw Error('Dataset resource ID must be in the format "project.dataset"');return;case"dashboard":case"media-library":case"canvas":return;default:throw Error(`Unsupported resource type: ${t.toString()}`)}},eX=(e,t)=>{if(t["~experimental_resource"])throw Error(`\`${e}\` does not support resource-based operations`)},eY=e=>(function(e){let t=!1,r;return(...n)=>(t||(r=e(...n),t=!0),r)})((...t)=>console.warn(e.join(" "),...t)),eG=eY(["Because you set `withCredentials` to true, we will override your `useCdn`","setting to be false since (cookie-based) credentials are never set on the CDN"]),eQ=eY(["Since you haven't set a value for `useCdn`, we will deliver content using our","global, edge-cached API-CDN. If you wish to have content delivered faster, set","`useCdn: false` to use the Live API. Note: You may incur higher costs using the live API."]),eZ=eY(["The Sanity client is configured with the `perspective` set to `drafts` or `previewDrafts`, which doesn't support the API-CDN.","The Live API will be used instead. Set `useCdn: false` in your configuration to hide this warning."]),eK=eY(["The `previewDrafts` perspective has been renamed to  `drafts` and will be removed in a future API version"]),e0=eY(["You have configured Sanity client to use a token in the browser. This may cause unintentional security issues.",`See ${eq("js-client-browser-token")} for more information and how to hide this warning.`]),e1=eY(["You have configured Sanity client to use a token, but also provided `withCredentials: true`.","This is no longer supported - only token will be used - remove `withCredentials: true`."]),e2=eY(["Using the Sanity client without specifying an API version is deprecated.",`See ${eq("js-client-api-version")}`]),e6=(eY(["The default export of @sanity/client has been deprecated. Use the named export `createClient` instead."]),{apiHost:"https://api.sanity.io",apiVersion:"1",useProjectHostname:!0,stega:{enabled:!1}}),e3=["localhost","127.0.0.1","0.0.0.0"],e5=e=>-1!==e3.indexOf(e);function e8(e){if(Array.isArray(e)&&e.length>1&&e.includes("raw"))throw TypeError('Invalid API perspective value: "raw". The raw-perspective can not be combined with other perspectives')}let e9=(e,t)=>{let r={...t,...e,stega:{..."boolean"==typeof t.stega?{enabled:t.stega}:t.stega||e6.stega,..."boolean"==typeof e.stega?{enabled:e.stega}:e.stega||{}}};r.apiVersion||e2();let n={...e6,...r},i=n.useProjectHostname&&!n["~experimental_resource"];if(typeof Promise>"u"){let e=eq("js-client-promise-polyfill");throw Error(`No native Promise-implementation found, polyfill needed - see ${e}`)}if(i&&!n.projectId)throw Error("Configuration must contain `projectId`");if(n["~experimental_resource"]&&eJ(n),"u">typeof n.perspective&&e8(n.perspective),"encodeSourceMap"in n)throw Error("It looks like you're using options meant for '@sanity/preview-kit/client'. 'encodeSourceMap' is not supported in '@sanity/client'. Did you mean 'stega.enabled'?");if("encodeSourceMapAtPath"in n)throw Error("It looks like you're using options meant for '@sanity/preview-kit/client'. 'encodeSourceMapAtPath' is not supported in '@sanity/client'. Did you mean 'stega.filter'?");if("boolean"!=typeof n.stega.enabled)throw Error(`stega.enabled must be a boolean, received ${n.stega.enabled}`);if(n.stega.enabled&&void 0===n.stega.studioUrl)throw Error("stega.studioUrl must be defined when stega.enabled is true");if(n.stega.enabled&&"string"!=typeof n.stega.studioUrl&&"function"!=typeof n.stega.studioUrl)throw Error(`stega.studioUrl must be a string or a function, received ${n.stega.studioUrl}`);let s="u">typeof window&&window.location&&window.location.hostname,o=s&&e5(window.location.hostname),a=!!n.token;n.withCredentials&&a&&(e1(),n.withCredentials=!1),s&&o&&a&&!0!==n.ignoreBrowserTokenWarning?e0():typeof n.useCdn>"u"&&eQ(),i&&eU(n.projectId),n.dataset&&eP(n.dataset),"requestTagPrefix"in n&&(n.requestTagPrefix=n.requestTagPrefix?eW(n.requestTagPrefix).replace(/\.+$/,""):void 0),n.apiVersion=`${n.apiVersion}`.replace(/^v/,""),n.isDefaultApi=n.apiHost===e6.apiHost,!0===n.useCdn&&n.withCredentials&&eG(),n.useCdn=!1!==n.useCdn&&!n.withCredentials,function(e){if("1"===e||"X"===e)return;let t=new Date(e);if(!(/^\d{4}-\d{2}-\d{2}$/.test(e)&&t instanceof Date&&t.getTime()>0))throw Error("Invalid API version string, expected `1` or date in format `YYYY-MM-DD`")}(n.apiVersion);let u=n.apiHost.split("://",2),c=u[0],l=u[1],f=n.isDefaultApi?"apicdn.sanity.io":l;return i?(n.url=`${c}://${n.projectId}.${l}/v${n.apiVersion}`,n.cdnUrl=`${c}://${n.projectId}.${f}/v${n.apiVersion}`):(n.url=`${n.apiHost}/v${n.apiVersion}`,n.cdnUrl=n.url),n};class e4 extends Error{name="ConnectionFailedError"}class e7 extends Error{name="DisconnectError";reason;constructor(e,t,r={}){super(e,r),this.reason=t}}class te extends Error{name="ChannelError";data;constructor(e,t){super(e),this.data=t}}class tt extends Error{name="MessageError";data;constructor(e,t,r={}){super(e,r),this.data=t}}class tr extends Error{name="MessageParseError"}let tn=["channelError","disconnect"];function ti(e,t){return(0,B.v)(()=>{let t=e();return t&&(t instanceof D.c||(0,L.T)(t.lift)&&(0,L.T)(t.subscribe))?t:V(t)}).pipe((0,H.Z)(e=>new D.c(r=>{let n=t.includes("open"),i=t.includes("reconnect");function s(t){if("data"in t){let[e,n]=ts(t);r.error(e?new tr("Unable to parse EventSource error message",{cause:n}):new tt((n?.data).message,n));return}e.readyState===e.CLOSED?r.error(new e4("EventSource connection failed")):i&&r.next({type:"reconnect"})}function o(){r.next({type:"open"})}function a(e){let[t,n]=ts(e);if(t){r.error(new tr("Unable to parse EventSource message",{cause:t}));return}if("channelError"===e.type){var i;r.error(new te((i=n?.data).error?i.error.description?i.error.description:"string"==typeof i.error?i.error:JSON.stringify(i.error,null,2):i.message||"Unknown listener error",n.data));return}if("disconnect"===e.type){r.error(new e7(`Server disconnected client: ${n.data?.reason||"unknown error"}`));return}r.next({type:e.type,id:e.lastEventId,...n.data?{data:n.data}:{}})}e.addEventListener("error",s),n&&e.addEventListener("open",o);let u=[...new Set([...tn,...t])].filter(e=>"error"!==e&&"open"!==e&&"reconnect"!==e);return u.forEach(t=>e.addEventListener(t,a)),()=>{e.removeEventListener("error",s),n&&e.removeEventListener("open",o),u.forEach(t=>e.removeEventListener(t,a)),e.close()}})))}function ts(e){try{let t="string"==typeof e.data&&JSON.parse(e.data);return[null,{type:e.type,id:e.lastEventId,...!function(e){for(let t in e)return!1;return!0}(t)?{data:t}:{}}]}catch(e){return[e,null]}}function to(e){if("string"==typeof e)return{id:e};if(Array.isArray(e))return{query:"*[_id in $ids]",params:{ids:e}};if("object"==typeof e&&null!==e&&"query"in e&&"string"==typeof e.query)return"params"in e&&"object"==typeof e.params&&null!==e.params?{query:e.query,params:e.params}:{query:e.query};let t=["* Document ID (<docId>)","* Array of document IDs","* Object containing `query`"].join(`
`);throw Error(`Unknown selection - must be one of:

${t}`)}class ta{selection;operations;constructor(e,t={}){this.selection=e,this.operations=t}set(e){return this._assign("set",e)}setIfMissing(e){return this._assign("setIfMissing",e)}diffMatchPatch(e){return eN("diffMatchPatch",e),this._assign("diffMatchPatch",e)}unset(e){if(!Array.isArray(e))throw Error("unset(attrs) takes an array of attributes to unset, non-array given");return this.operations=Object.assign({},this.operations,{unset:e}),this}inc(e){return this._assign("inc",e)}dec(e){return this._assign("dec",e)}insert(e,t,r){return eV(e,t,r),this._assign("insert",{[e]:t,items:r})}append(e,t){return this.insert("after",`${e}[-1]`,t)}prepend(e,t){return this.insert("before",`${e}[0]`,t)}splice(e,t,r,n){let i=t<0?t-1:t,s=typeof r>"u"||-1===r?-1:Math.max(0,t+r),o=`${e}[${i}:${i<0&&s>=0?"":s}]`;return this.insert("replace",o,n||[])}ifRevisionId(e){return this.operations.ifRevisionID=e,this}serialize(){return{...to(this.selection),...this.operations}}toJSON(){return this.serialize()}reset(){return this.operations={},this}_assign(e,t,r=!0){return eN(e,t),this.operations=Object.assign({},this.operations,{[e]:Object.assign({},r&&this.operations[e]||{},t)}),this}_set(e,t){return this._assign(e,t,!1)}}class tu extends ta{#a;constructor(e,t,r){super(e,t),this.#a=r}clone(){return new tu(this.selection,{...this.operations},this.#a)}commit(e){if(!this.#a)throw Error("No `client` passed to patch, either provide one or pass the patch to a clients `mutate()` method");let t=Object.assign({returnFirst:"string"==typeof this.selection,returnDocuments:!0},e);return this.#a.mutate({patch:this.serialize()},t)}}class tc extends ta{#a;constructor(e,t,r){super(e,t),this.#a=r}clone(){return new tc(this.selection,{...this.operations},this.#a)}commit(e){if(!this.#a)throw Error("No `client` passed to patch, either provide one or pass the patch to a clients `mutate()` method");let t=Object.assign({returnFirst:"string"==typeof this.selection,returnDocuments:!0},e);return this.#a.mutate({patch:this.serialize()},t)}}let tl={returnDocuments:!1};class tf{operations;trxId;constructor(e=[],t){this.operations=e,this.trxId=t}create(e){return eN("create",e),this._add({create:e})}createIfNotExists(e){let t="createIfNotExists";return eN(t,e),eB(t,e),this._add({[t]:e})}createOrReplace(e){let t="createOrReplace";return eN(t,e),eB(t,e),this._add({[t]:e})}delete(e){return eD("delete",e),this._add({delete:{id:e}})}transactionId(e){return e?(this.trxId=e,this):this.trxId}serialize(){return[...this.operations]}toJSON(){return this.serialize()}reset(){return this.operations=[],this}_add(e){return this.operations.push(e),this}}class th extends tf{#a;constructor(e,t,r){super(e,r),this.#a=t}clone(){return new th([...this.operations],this.#a,this.trxId)}commit(e){if(!this.#a)throw Error("No `client` passed to transaction, either provide one or pass the transaction to a clients `mutate()` method");return this.#a.mutate(this.serialize(),Object.assign({transactionId:this.trxId},tl,e||{}))}patch(e,t){let r="function"==typeof t,n="string"!=typeof e&&e instanceof tc,i="object"==typeof e&&("query"in e||"id"in e);if(n)return this._add({patch:e.serialize()});if(r){let r=t(new tc(e,{},this.#a));if(!(r instanceof tc))throw Error("function passed to `patch()` must return the patch");return this._add({patch:r.serialize()})}if(i){let r=new tc(e,t||{},this.#a);return this._add({patch:r.serialize()})}return this._add({patch:{id:e,...t}})}}class tp extends tf{#a;constructor(e,t,r){super(e,r),this.#a=t}clone(){return new tp([...this.operations],this.#a,this.trxId)}commit(e){if(!this.#a)throw Error("No `client` passed to transaction, either provide one or pass the transaction to a clients `mutate()` method");return this.#a.mutate(this.serialize(),Object.assign({transactionId:this.trxId},tl,e||{}))}patch(e,t){let r="function"==typeof t;if("string"!=typeof e&&e instanceof tu)return this._add({patch:e.serialize()});if(r){let r=t(new tu(e,{},this.#a));if(!(r instanceof tu))throw Error("function passed to `patch()` must return the patch");return this._add({patch:r.serialize()})}return this._add({patch:{id:e,...t}})}}let td=({query:e,params:t={},options:r={}})=>{let n=new URLSearchParams,{tag:i,includeMutations:s,returnQuery:o,...a}=r;for(let[r,s]of(i&&n.append("tag",i),n.append("query",e),Object.entries(t)))n.append(`$${r}`,JSON.stringify(s));for(let[e,t]of Object.entries(a))t&&n.append(e,`${t}`);return!1===o&&n.append("returnQuery","false"),!1===s&&n.append("includeMutations","false"),`?${n}`},ty=(e,t)=>!1===e?void 0:typeof e>"u"?t:e,tg=(e={})=>({dryRun:e.dryRun,returnIds:!0,returnDocuments:ty(e.returnDocuments,!0),visibility:e.visibility||"sync",autoGenerateArrayKeys:e.autoGenerateArrayKeys,skipCrossDatasetReferenceValidation:e.skipCrossDatasetReferenceValidation}),tv=e=>"response"===e.type,tm=e=>e.body,tb=(e,t)=>e.reduce((e,r)=>(e[t(r)]=r,e),Object.create(null));function tw(e,t,n,i,s={},o={}){let a="stega"in o?{...n||{},..."boolean"==typeof o.stega?{enabled:o.stega}:o.stega||{}}:n,u=a.enabled?(0,eh.Q)(s):s,c=!1===o.filterResponse?e=>e:e=>e.result,{cache:l,next:f,...h}={useAbortSignal:"u">typeof o.signal,resultSourceMap:a.enabled?"withKeyArraySelector":o.resultSourceMap,...o,returnQuery:!1===o.filterResponse&&!1!==o.returnQuery},p=tO(e,t,"query",{query:i,params:u},"u">typeof l||"u">typeof f?{...h,fetch:{cache:l,next:f}}:h);return a.enabled?p.pipe(function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return eb.apply(void 0,(0,ep.fX)([],(0,ep.zs)(e)))}((0,z.H)(r.e(836).then(r.bind(r,4836)).then(function(e){return e.stegaEncodeSourceMap$1}).then(({stegaEncodeSourceMap:e})=>e))),(0,ec.T)(([e,t])=>{let r=t(e.result,e.resultSourceMap,a);return c({...e,result:r})})):p.pipe((0,ec.T)(c))}function t_(e,t,r,n={}){let i={uri:tV(e,"doc",(()=>{if(!n.releaseId)return r;let e=(0,e_._S)(r);if(!e){if((0,e_.R8)(r))throw Error(`The document ID (\`${r}\`) is a draft, but \`options.releaseId\` is set as \`${n.releaseId}\``);return(0,e_.ct)(r,n.releaseId)}if(e!==n.releaseId)throw Error(`The document ID (\`${r}\`) is already a version of \`${e}\` release, but this does not match the provided \`options.releaseId\` (\`${n.releaseId}\`)`);return r})()),json:!0,tag:n.tag,signal:n.signal};return tF(e,t,i).pipe((0,ew.p)(tv),(0,ec.T)(e=>e.body.documents&&e.body.documents[0]))}function tx(e,t,r,n={}){let i={uri:tV(e,"doc",r.join(",")),json:!0,tag:n.tag,signal:n.signal};return tF(e,t,i).pipe((0,ew.p)(tv),(0,ec.T)(e=>{let t=tb(e.body.documents||[],e=>e._id);return r.map(e=>t[e]||null)}))}function tE(e,t,r,n={}){return tO(e,t,"query",{query:"*[sanity::partOfRelease($releaseId)]",params:{releaseId:r}},n)}function tS(e,t,r,n){return eB("createIfNotExists",r),tj(e,t,r,"createIfNotExists",n)}function tT(e,t,r,n){return eB("createOrReplace",r),tj(e,t,r,"createOrReplace",n)}function tC(e,t,r,n,i){return eB("createVersion",r),eF("createVersion",r),tq(e,t,{actionType:"sanity.action.document.version.create",publishedId:n,document:r},i)}function tI(e,t,r,n){return tO(e,t,"mutate",{mutations:[{delete:to(r)}]},n)}function tR(e,t,r,n=!1,i){return tq(e,t,{actionType:"sanity.action.document.version.discard",versionId:r,purge:n},i)}function tk(e,t,r,n){return eB("replaceVersion",r),eF("replaceVersion",r),tq(e,t,{actionType:"sanity.action.document.version.replace",document:r},n)}function t$(e,t,r,n,i){return tq(e,t,{actionType:"sanity.action.document.version.unpublish",versionId:r,publishedId:n},i)}function tA(e,t,r,n){let i;return tO(e,t,"mutate",{mutations:Array.isArray(i=r instanceof tc||r instanceof tu?{patch:r.serialize()}:r instanceof th||r instanceof tp?r.serialize():r)?i:[i],transactionId:n&&n.transactionId||void 0},n)}function tq(e,t,r,n){let i=Array.isArray(r)?r:[r];return tO(e,t,"actions",{actions:i,transactionId:n&&n.transactionId||void 0,skipCrossDatasetReferenceValidation:n&&n.skipCrossDatasetReferenceValidation||void 0,dryRun:n&&n.dryRun||void 0},n)}function tO(e,t,r,n,i={}){let s="mutate"===r,o="actions"===r,a=s||o?"":td(n),u=!s&&!o&&a.length<11264,c=u?a:"",l=i.returnFirst,{timeout:f,token:h,tag:p,headers:d,returnQuery:y,lastLiveEventId:g,cacheMode:v}=i,m={method:u?"GET":"POST",uri:tV(e,r,c),json:!0,body:u?void 0:n,query:s&&tg(i),timeout:f,headers:d,token:h,tag:p,returnQuery:y,perspective:i.perspective,resultSourceMap:i.resultSourceMap,lastLiveEventId:Array.isArray(g)?g[0]:g,cacheMode:v,canUseCdn:"query"===r,signal:i.signal,fetch:i.fetch,useAbortSignal:i.useAbortSignal,useCdn:i.useCdn};return tF(e,t,m).pipe((0,ew.p)(tv),(0,ec.T)(tm),(0,ec.T)(e=>{if(!s)return e;let t=e.results||[];if(i.returnDocuments)return l?t[0]&&t[0].document:t.map(e=>e.document);let r=l?t[0]&&t[0].id:t.map(e=>e.id);return{transactionId:e.transactionId,results:t,[l?"documentId":"documentIds"]:r}}))}function tj(e,t,r,n,i={}){return tO(e,t,"mutate",{mutations:[{[n]:r}]},Object.assign({returnFirst:!0,returnDocuments:!0},i))}let tP=e=>void 0!==e.config().dataset&&void 0!==e.config().projectId||void 0!==e.config()["~experimental_resource"],tU=(e,t)=>tP(e)&&t.startsWith(tV(e,"query")),tM=(e,t)=>tP(e)&&t.startsWith(tV(e,"mutate")),tN=(e,t)=>tP(e)&&t.startsWith(tV(e,"doc","")),tD=(e,t)=>tP(e)&&t.startsWith(tV(e,"listen")),tB=(e,t)=>tP(e)&&t.startsWith(tV(e,"history","")),tL=(e,t)=>t.startsWith("/data/")||tU(e,t)||tM(e,t)||tN(e,t)||tD(e,t)||tB(e,t);function tF(e,t,r){var n;let i=r.url||r.uri,s=e.config(),o=typeof r.canUseCdn>"u"?["GET","HEAD"].indexOf(r.method||"GET")>=0&&tL(e,i):r.canUseCdn,a=(r.useCdn??s.useCdn)&&o,u=r.tag&&s.requestTagPrefix?[s.requestTagPrefix,r.tag].join("."):r.tag||s.requestTagPrefix;if(u&&null!==r.tag&&(r.query={tag:eW(u),...r.query}),["GET","HEAD","POST"].indexOf(r.method||"GET")>=0&&tU(e,i)){let e=r.resultSourceMap??s.resultSourceMap;void 0!==e&&!1!==e&&(r.query={resultSourceMap:e,...r.query});let t=r.perspective||s.perspective;"u">typeof t&&("previewDrafts"===t&&eK(),e8(t),r.query={perspective:Array.isArray(t)?t.join(","):t,...r.query},(Array.isArray(t)&&t.length>0||"previewDrafts"===t||"drafts"===t)&&a&&(a=!1,eZ())),r.lastLiveEventId&&(r.query={...r.query,lastLiveEventId:r.lastLiveEventId}),!1===r.returnQuery&&(r.query={returnQuery:"false",...r.query}),a&&"noStale"==r.cacheMode&&(r.query={cacheMode:"noStale",...r.query})}let c=function(e,t={}){let r={};e.headers&&Object.assign(r,e.headers);let n=t.token||e.token;n&&(r.Authorization=`Bearer ${n}`),t.useGlobalApi||e.useProjectHostname||!e.projectId||(r["X-Sanity-Project-ID"]=e.projectId);let i=!!(typeof t.withCredentials>"u"?e.withCredentials:t.withCredentials),s=typeof t.timeout>"u"?e.timeout:t.timeout;return Object.assign({},t,{headers:Object.assign({},r,t.headers||{}),timeout:typeof s>"u"?3e5:s,proxy:t.proxy||e.proxy,json:!0,withCredentials:i,fetch:"object"==typeof t.fetch&&"object"==typeof e.fetch?{...e.fetch,...t.fetch}:t.fetch||e.fetch})}(s,Object.assign({},r,{url:tH(e,i,a)})),l=new D.c(e=>t(c,s.requester).subscribe(e));return r.signal?l.pipe((n=r.signal,e=>new D.c(t=>{let r=()=>t.error(function(e){if(tW)return new DOMException(e?.reason??"The operation was aborted.","AbortError");let t=Error(e?.reason??"The operation was aborted.");return t.name="AbortError",t}(n));if(n&&n.aborted){r();return}let i=e.subscribe(t);return n.addEventListener("abort",r),()=>{n.removeEventListener("abort",r),i.unsubscribe()}}))):l}function tz(e,t,r){return tF(e,t,r).pipe((0,ew.p)(e=>"response"===e.type),(0,ec.T)(e=>e.body))}function tV(e,t,r){let n=e.config();if(n["~experimental_resource"]){eJ(n);let e=tJ(n),i=void 0!==r?`${t}/${r}`:t;return`${e}/${i}`.replace(/\/($|\?)/,"$1")}let i=eH(n),s=`/${t}/${i}`;return`/data${void 0!==r?`${s}/${r}`:s}`.replace(/\/($|\?)/,"$1")}function tH(e,t,r=!1){let{url:n,cdnUrl:i}=e.config();return`${r?i:n}/${t.replace(/^\//,"")}`}let tW=!!globalThis.DOMException,tJ=e=>{if(!e["~experimental_resource"])throw Error("`resource` must be provided to perform resource queries");let{type:t,id:r}=e["~experimental_resource"];switch(t){case"dataset":{let e=r.split(".");if(2!==e.length)throw Error('Dataset ID must be in the format "project.dataset"');return`/projects/${e[0]}/datasets/${e[1]}`}case"canvas":return`/canvases/${r}`;case"media-library":return`/media-libraries/${r}`;case"dashboard":return`/dashboards/${r}`;default:throw Error(`Unsupported resource type: ${t.toString()}`)}};function tX(e,t,r){let n=eH(e.config());return tz(e,t,{method:"POST",uri:`/agent/action/generate/${n}`,body:r})}function tY(e,t,r){let n=eH(e.config());return tz(e,t,{method:"POST",uri:`/agent/action/transform/${n}`,body:r})}function tG(e,t,r){let n=eH(e.config());return tz(e,t,{method:"POST",uri:`/agent/action/translate/${n}`,body:r})}class tQ{#a;#u;constructor(e,t){this.#a=e,this.#u=t}generate(e){return tX(this.#a,this.#u,e)}transform(e){return tY(this.#a,this.#u,e)}translate(e){return tG(this.#a,this.#u,e)}}class tZ{#a;#u;constructor(e,t){this.#a=e,this.#u=t}generate(e){return J(tX(this.#a,this.#u,e))}transform(e){return J(tY(this.#a,this.#u,e))}translate(e){return J(tG(this.#a,this.#u,e))}}class tK{#a;#u;constructor(e,t){this.#a=e,this.#u=t}upload(e,t,r){return t1(this.#a,this.#u,e,t,r)}}class t0{#a;#u;constructor(e,t){this.#a=e,this.#u=t}upload(e,t,r){return J(t1(this.#a,this.#u,e,t,r).pipe((0,ew.p)(e=>"response"===e.type),(0,ec.T)(e=>e.body.document)))}}function t1(e,t,r,n,i={}){eM(r);let s=i.extract||void 0;s&&!s.length&&(s=["none"]);let o=e.config(),a=!(typeof File>"u")&&n instanceof File?Object.assign({filename:!1===i.preserveFilename?void 0:n.name,contentType:n.type},i):i,{tag:u,label:c,title:l,description:f,creditLine:h,filename:p,source:d}=a,y={label:c,title:l,description:f,filename:p,meta:s,creditLine:h};return d&&(y.sourceId=d.id,y.sourceName=d.name,y.sourceUrl=d.url),tF(e,t,{tag:u,method:"POST",timeout:a.timeout||0,uri:function(e,t){let r="image"===t?"images":"files";if(e["~experimental_resource"]){let{type:t,id:n}=e["~experimental_resource"];switch(t){case"dataset":throw Error("Assets are not supported for dataset resources, yet. Configure the client with `{projectId: <projectId>, dataset: <datasetId>}` instead.");case"canvas":return`/canvases/${n}/assets/${r}`;case"media-library":return`/media-libraries/${n}/upload`;case"dashboard":return`/dashboards/${n}/assets/${r}`;default:throw Error(`Unsupported resource type: ${t.toString()}`)}}let n=eH(e);return`assets/${r}/${n}`}(o,r),headers:a.contentType?{"Content-Type":a.contentType}:{},query:y,body:n})}var t2=(e,t)=>Object.keys(t).concat(Object.keys(e)).reduce((r,n)=>(r[n]=typeof e[n]>"u"?t[n]:e[n],r),{});let t6=(e,t)=>t.reduce((t,r)=>(typeof e[r]>"u"||(t[r]=e[r]),t),{}),t3=(0,B.v)(()=>r.e(805).then(r.t.bind(r,2805,19))).pipe((0,ec.T)(({default:e})=>e),(0,X.t)(1));function t5(){return function(e){return e.pipe(Z((e,t)=>{var r;return e instanceof e4?ee(V({type:"reconnect"}),(void 0===r&&(r=er),new D.c(function(e){var t=1e3,n=0;return r.schedule(function(){e.closed||(e.next(n++),e.complete())},t)})).pipe((0,H.Z)(()=>t))):en(()=>e)}))}}let t8=["includePreviousRevision","includeResult","includeMutations","includeAllVersions","visibility","effectFormat","tag"],t9={includeResult:!0};function t4(e,t,r={}){let{url:n,token:i,withCredentials:s,requestTagPrefix:o}=this.config(),a=r.tag&&o?[o,r.tag].join("."):r.tag,u={...t2(r,t9),tag:a},c=td({query:e,params:t,options:{tag:a,...t6(u,t8)}}),l=`${n}${tV(this,"listen",c)}`;if(l.length>14800)return en(()=>Error("Query too large for listener"));let f=u.events?u.events:["mutation"],h={};return s&&(h.withCredentials=!0),i&&(h.headers={Authorization:`Bearer ${i}`}),ti(()=>(typeof EventSource>"u"||h.headers?t3:V(EventSource)).pipe((0,ec.T)(e=>new e(l,h))),f).pipe(t5(),(0,ew.p)(e=>f.includes(e.type)),(0,ec.T)(e=>({type:e.type,..."data"in e?e.data:{}})))}let t7="2021-03-25";class re{#a;constructor(e){this.#a=e}events({includeDrafts:e=!1,tag:t}={}){var r,n,i;eX("live",this.#a.config());let{projectId:s,apiVersion:o,token:a,withCredentials:u,requestTagPrefix:c}=this.#a.config(),l=o.replace(/^v/,"");if("X"!==l&&l<t7)throw Error(`The live events API requires API version ${t7} or later. The current API version is ${l}. Please update your API version to use this feature.`);if(e&&!a&&!u)throw Error("The live events API requires a token or withCredentials when 'includeDrafts: true'. Please update your client configuration. The token should have the lowest possible access role.");let f=tV(this.#a,"live/events"),h=new URL(this.#a.getUrl(f,!1)),p=t&&c?[c,t].join("."):t;p&&h.searchParams.set("tag",p),e&&h.searchParams.set("includeDrafts","true");let d={};e&&a&&(d.headers={Authorization:`Bearer ${a}`}),e&&u&&(d.withCredentials=!0);let y=`${h.href}::${JSON.stringify(d)}`,g=rt.get(y);if(g)return g;let v=ti(()=>(typeof EventSource>"u"||d.headers?t3:V(EventSource)).pipe((0,ec.T)(e=>new e(h.href,d))),["message","restart","welcome","reconnect","goaway"]).pipe(t5(),(0,ec.T)(e=>{if("message"===e.type){let{data:t,...r}=e;return{...r,tags:t.tags}}return e})),m=ee((n={method:"OPTIONS",mode:"cors",credentials:d.withCredentials?"include":"omit",headers:d.headers},new D.c(e=>{let t=new AbortController,r=t.signal;return fetch(h,{...n,signal:t.signal}).then(t=>{e.next(t),e.complete()},t=>{r.aborted||e.error(t)}),()=>t.abort()})).pipe((0,H.Z)(()=>eu.w),Z(()=>{throw new eR({projectId:s})})),v).pipe(es(()=>rt.delete(y)),(i="function"==typeof(r={predicate:e=>"welcome"===e.type})?{predicate:r}:r,e=>{var t,r,n,s;let o,a=!1,{predicate:u,...c}=i,l=e.pipe((t=e=>{i.predicate(e)&&(a=!0,o=e)},(s=(0,L.T)(t)?{next:t,error:r,complete:n}:t)?(0,Q.N)(function(e,t){null===(r=s.subscribe)||void 0===r||r.call(s);var r,n=!0;e.subscribe((0,G._)(t,function(e){var r;null===(r=s.next)||void 0===r||r.call(s,e),t.next(e)},function(){var e;n=!1,null===(e=s.complete)||void 0===e||e.call(s),t.complete()},function(e){var r;n=!1,null===(r=s.error)||void 0===r||r.call(s,e),t.error(e)},function(){var e,t;n&&(null===(e=s.unsubscribe)||void 0===e||e.call(s)),null===(t=s.finalize)||void 0===t||t.call(s)}))}):ei.D),es(()=>{a=!1,o=void 0}),(0,eo.u)(c)),f=new D.c(e=>{a&&e.next(o),e.complete()});return(0,ea.h)(l,f)}));return rt.set(y,m),m}}let rt=new Map;class rr{#a;#u;constructor(e,t){this.#a=e,this.#u=t}create(e,t){return ri(this.#a,this.#u,"PUT",e,t)}edit(e,t){return ri(this.#a,this.#u,"PATCH",e,t)}delete(e){return ri(this.#a,this.#u,"DELETE",e)}list(){return tz(this.#a,this.#u,{uri:"/datasets",tag:null})}}class rn{#a;#u;constructor(e,t){this.#a=e,this.#u=t}create(e,t){return eX("dataset",this.#a.config()),J(ri(this.#a,this.#u,"PUT",e,t))}edit(e,t){return eX("dataset",this.#a.config()),J(ri(this.#a,this.#u,"PATCH",e,t))}delete(e){return eX("dataset",this.#a.config()),J(ri(this.#a,this.#u,"DELETE",e))}list(){return eX("dataset",this.#a.config()),J(tz(this.#a,this.#u,{uri:"/datasets",tag:null}))}}function ri(e,t,r,n,i){return eX("dataset",e.config()),eP(n),tz(e,t,{method:r,uri:`/datasets/${n}`,body:i,tag:null})}class rs{#a;#u;constructor(e,t){this.#a=e,this.#u=t}list(e){eX("projects",this.#a.config());let t=e?.includeMembers===!1?"/projects?includeMembers=false":"/projects";return tz(this.#a,this.#u,{uri:t})}getById(e){return eX("projects",this.#a.config()),tz(this.#a,this.#u,{uri:`/projects/${e}`})}}class ro{#a;#u;constructor(e,t){this.#a=e,this.#u=t}list(e){eX("projects",this.#a.config());let t=e?.includeMembers===!1?"/projects?includeMembers=false":"/projects";return J(tz(this.#a,this.#u,{uri:t}))}getById(e){return eX("projects",this.#a.config()),J(tz(this.#a,this.#u,{uri:`/projects/${e}`}))}}let ra=((e,t=21)=>eE(e,t,ex))("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",8),ru=(e,t)=>t?(0,e_.ct)(e,t):(0,e_.T4)(e);function rc(e,{releaseId:t,publishedId:r,document:n}){if(r&&n._id){let e=ru(r,t);return ez(e,n),e}if(n._id){let r=(0,e_.R8)(n._id),i=(0,e_.A_)(n._id);if(!r&&!i)throw Error(`\`${e}()\` requires a document with an \`_id\` that is a version or draft ID`);if(t){if(r)throw Error(`\`${e}()\` was called with a document ID (\`${n._id}\`) that is a draft ID, but a release ID (\`${t}\`) was also provided.`);let i=(0,e_._S)(n._id);if(i!==t)throw Error(`\`${e}()\` was called with a document ID (\`${n._id}\`) that is a version ID, but the release ID (\`${t}\`) does not match the document's version ID (\`${i}\`).`)}return n._id}if(r)return ru(r,t);throw Error(`\`${e}()\` requires either a publishedId or a document with an \`_id\``)}let rl=(e,t)=>{if("object"==typeof e&&null!==e&&("releaseId"in e||"metadata"in e)){let{releaseId:r=ra(),metadata:n={}}=e;return[r,n,t]}return[ra(),{},e]},rf=(e,t)=>{let[r,n,i]=rl(e,t);return{action:{actionType:"sanity.action.release.create",releaseId:r,metadata:{...n,releaseType:n.releaseType||"undecided"}},options:i}};class rh{#a;#u;constructor(e,t){this.#a=e,this.#u=t}get({releaseId:e},t){return t_(this.#a,this.#u,`_.releases.${e}`,t)}create(e,t){let{action:r,options:n}=rf(e,t),{releaseId:i,metadata:s}=r;return tq(this.#a,this.#u,r,n).pipe((0,ec.T)(e=>({...e,releaseId:i,metadata:s})))}edit({releaseId:e,patch:t},r){return tq(this.#a,this.#u,{actionType:"sanity.action.release.edit",releaseId:e,patch:t},r)}publish({releaseId:e},t){return tq(this.#a,this.#u,{actionType:"sanity.action.release.publish",releaseId:e},t)}archive({releaseId:e},t){return tq(this.#a,this.#u,{actionType:"sanity.action.release.archive",releaseId:e},t)}unarchive({releaseId:e},t){return tq(this.#a,this.#u,{actionType:"sanity.action.release.unarchive",releaseId:e},t)}schedule({releaseId:e,publishAt:t},r){return tq(this.#a,this.#u,{actionType:"sanity.action.release.schedule",releaseId:e,publishAt:t},r)}unschedule({releaseId:e},t){return tq(this.#a,this.#u,{actionType:"sanity.action.release.unschedule",releaseId:e},t)}delete({releaseId:e},t){return tq(this.#a,this.#u,{actionType:"sanity.action.release.delete",releaseId:e},t)}fetchDocuments({releaseId:e},t){return tE(this.#a,this.#u,e,t)}}class rp{#a;#u;constructor(e,t){this.#a=e,this.#u=t}get({releaseId:e},t){return J(t_(this.#a,this.#u,`_.releases.${e}`,t))}async create(e,t){let{action:r,options:n}=rf(e,t),{releaseId:i,metadata:s}=r;return{...await J(tq(this.#a,this.#u,r,n)),releaseId:i,metadata:s}}edit({releaseId:e,patch:t},r){return J(tq(this.#a,this.#u,{actionType:"sanity.action.release.edit",releaseId:e,patch:t},r))}publish({releaseId:e},t){return J(tq(this.#a,this.#u,{actionType:"sanity.action.release.publish",releaseId:e},t))}archive({releaseId:e},t){return J(tq(this.#a,this.#u,{actionType:"sanity.action.release.archive",releaseId:e},t))}unarchive({releaseId:e},t){return J(tq(this.#a,this.#u,{actionType:"sanity.action.release.unarchive",releaseId:e},t))}schedule({releaseId:e,publishAt:t},r){return J(tq(this.#a,this.#u,{actionType:"sanity.action.release.schedule",releaseId:e,publishAt:t},r))}unschedule({releaseId:e},t){return J(tq(this.#a,this.#u,{actionType:"sanity.action.release.unschedule",releaseId:e},t))}delete({releaseId:e},t){return J(tq(this.#a,this.#u,{actionType:"sanity.action.release.delete",releaseId:e},t))}fetchDocuments({releaseId:e},t){return J(tE(this.#a,this.#u,e,t))}}class rd{#a;#u;constructor(e,t){this.#a=e,this.#u=t}getById(e){return tz(this.#a,this.#u,{uri:`/users/${e}`})}}class ry{#a;#u;constructor(e,t){this.#a=e,this.#u=t}getById(e){return J(tz(this.#a,this.#u,{uri:`/users/${e}`}))}}class rg{assets;datasets;live;projects;users;agent;releases;#c;#u;listen=t4;constructor(e,t=e6){this.config(t),this.#u=e,this.assets=new tK(this,this.#u),this.datasets=new rr(this,this.#u),this.live=new re(this),this.projects=new rs(this,this.#u),this.users=new rd(this,this.#u),this.agent={action:new tQ(this,this.#u)},this.releases=new rh(this,this.#u)}clone(){return new rg(this.#u,this.config())}config(e){if(void 0===e)return{...this.#c};if(this.#c&&!1===this.#c.allowReconfigure)throw Error("Existing client instance cannot be reconfigured - use `withConfig(newConfig)` to return a new client");return this.#c=e9(e,this.#c||{}),this}withConfig(e){let t=this.config();return new rg(this.#u,{...t,...e,stega:{...t.stega||{},..."boolean"==typeof e?.stega?{enabled:e.stega}:e?.stega||{}}})}fetch(e,t,r){return tw(this,this.#u,this.#c.stega,e,t,r)}getDocument(e,t){return t_(this,this.#u,e,t)}getDocuments(e,t){return tx(this,this.#u,e,t)}create(e,t){return tj(this,this.#u,e,"create",t)}createIfNotExists(e,t){return tS(this,this.#u,e,t)}createOrReplace(e,t){return tT(this,this.#u,e,t)}createVersion({document:e,publishedId:t,releaseId:r},n){let i=rc("createVersion",{document:e,publishedId:t,releaseId:r}),s={...e,_id:i},o=t||(0,e_.IM)(e._id);return tC(this,this.#u,s,o,n)}delete(e,t){return tI(this,this.#u,e,t)}discardVersion({releaseId:e,publishedId:t},r,n){let i=ru(t,e);return tR(this,this.#u,i,r,n)}replaceVersion({document:e,publishedId:t,releaseId:r},n){let i=rc("replaceVersion",{document:e,publishedId:t,releaseId:r}),s={...e,_id:i};return tk(this,this.#u,s,n)}unpublishVersion({releaseId:e,publishedId:t},r){let n=(0,e_.ct)(t,e);return t$(this,this.#u,n,t,r)}mutate(e,t){return tA(this,this.#u,e,t)}patch(e,t){return new tu(e,t,this)}transaction(e){return new tp(e,this)}action(e,t){return tq(this,this.#u,e,t)}request(e){return tz(this,this.#u,e)}getUrl(e,t){return tH(this,e,t)}getDataUrl(e,t){return tV(this,e,t)}}class rv{assets;datasets;live;projects;users;agent;releases;observable;#c;#u;listen=t4;constructor(e,t=e6){this.config(t),this.#u=e,this.assets=new t0(this,this.#u),this.datasets=new rn(this,this.#u),this.live=new re(this),this.projects=new ro(this,this.#u),this.users=new ry(this,this.#u),this.agent={action:new tZ(this,this.#u)},this.releases=new rp(this,this.#u),this.observable=new rg(e,t)}clone(){return new rv(this.#u,this.config())}config(e){if(void 0===e)return{...this.#c};if(this.#c&&!1===this.#c.allowReconfigure)throw Error("Existing client instance cannot be reconfigured - use `withConfig(newConfig)` to return a new client");return this.observable&&this.observable.config(e),this.#c=e9(e,this.#c||{}),this}withConfig(e){let t=this.config();return new rv(this.#u,{...t,...e,stega:{...t.stega||{},..."boolean"==typeof e?.stega?{enabled:e.stega}:e?.stega||{}}})}fetch(e,t,r){return J(tw(this,this.#u,this.#c.stega,e,t,r))}getDocument(e,t){return J(t_(this,this.#u,e,t))}getDocuments(e,t){return J(tx(this,this.#u,e,t))}create(e,t){return J(tj(this,this.#u,e,"create",t))}createIfNotExists(e,t){return J(tS(this,this.#u,e,t))}createOrReplace(e,t){return J(tT(this,this.#u,e,t))}createVersion({document:e,publishedId:t,releaseId:r},n){let i=rc("createVersion",{document:e,publishedId:t,releaseId:r}),s={...e,_id:i},o=t||(0,e_.IM)(e._id);return ef(tC(this,this.#u,s,o,n))}delete(e,t){return J(tI(this,this.#u,e,t))}discardVersion({releaseId:e,publishedId:t},r,n){let i=ru(t,e);return J(tR(this,this.#u,i,r,n))}replaceVersion({document:e,publishedId:t,releaseId:r},n){let i=rc("replaceVersion",{document:e,publishedId:t,releaseId:r}),s={...e,_id:i};return ef(tk(this,this.#u,s,n))}unpublishVersion({releaseId:e,publishedId:t},r){let n=(0,e_.ct)(t,e);return J(t$(this,this.#u,n,t,r))}mutate(e,t){return J(tA(this,this.#u,e,t))}patch(e,t){return new tc(e,t,this)}transaction(e){return new th(e,this)}action(e,t){return J(tq(this,this.#u,e,t))}request(e){return J(tz(this,this.#u,e))}dataRequest(e,t,r){return J(tO(this,this.#u,e,t,r))}getUrl(e,t){return tH(this,e,t)}getDataUrl(e,t){return tV(this,e,t)}}let rm=function(e,t){return{requester:e$(e),createClient:r=>{let n=e$(e);return new t((e,t)=>(t||n)({maxRedirects:0,maxRetries:r.maxRetries,retryDelay:r.retryDelay,...e}),r)}}}([],rv),rb=(rm.requester,rm.createClient);var rw=r(1687),r_=r(8307),rx=r(7711),rE=r(6046),rS=r(2115),rT=r(4880),rC=r(5086);let rI=(0,rx.default)(()=>r.e(277).then(r.bind(r,3277)),{loadableGenerated:{webpack:()=>[require.resolveWeak("../_chunks-es/PresentationComlink.js")]},ssr:!1}),rR=(0,rx.default)(()=>r.e(848).then(r.bind(r,2848)),{loadableGenerated:{webpack:()=>[require.resolveWeak("../_chunks-es/RefreshOnMount.js")]},ssr:!1}),rk=(0,rx.default)(()=>r.e(869).then(r.bind(r,3869)),{loadableGenerated:{webpack:()=>[require.resolveWeak("../_chunks-es/RefreshOnFocus.js")]},ssr:!1}),r$=(0,rx.default)(()=>r.e(284).then(r.bind(r,284)),{loadableGenerated:{webpack:()=>[require.resolveWeak("../_chunks-es/RefreshOnReconnect.js")]},ssr:!1});function rA(e){var t;e instanceof eR?console.warn("Sanity Live is unable to connect to the Sanity API as the current origin - ".concat(window.origin," - is not in the list of allowed CORS origins for this Sanity Project."),e.addOriginUrl&&"Add it here:",null===(t=e.addOriginUrl)||void 0===t?void 0:t.toString()):console.error(e)}function rq(e,t){t?console.warn("Sanity Live connection closed, switching to long polling set to a interval of",t/1e3,"seconds and the server gave this reason:",e.reason):console.error("Sanity Live connection closed, automatic revalidation is disabled, the server gave this reason:",e.reason)}function rO(e){let{projectId:t,dataset:r,apiHost:i,apiVersion:s,useProjectHostname:o,token:a,requestTagPrefix:u,draftModeEnabled:c,draftModePerspective:l,refreshOnMount:f=!1,refreshOnFocus:h=!c&&(typeof window>"u"||window.self===window.top),refreshOnReconnect:p=!0,intervalOnGoAway:d=3e4,requestTag:y="next-loader.live",onError:g=rA,onGoAway:v=rq,revalidateSyncTags:m=rw.Q}=e,b=(0,rS.useMemo)(()=>rb({projectId:t,dataset:r,apiHost:i,apiVersion:s,useProjectHostname:o,ignoreBrowserTokenWarning:!0,token:a,useCdn:!1,requestTagPrefix:u}),[i,s,r,t,u,a,o]),[w,_]=(0,rS.useState)(!1),x=(0,rE.useRouter)(),E=(0,rT.J)(e=>{"message"===e.type?m(e.tags):"restart"===e.type||"reconnect"===e.type?x.refresh():"goaway"===e.type&&(v(e,d),_(d))});(0,rS.useEffect)(()=>{let e=b.live.events({includeDrafts:!!a,tag:y}).subscribe({next:E,error:e=>{g(e)}});return()=>e.unsubscribe()},[b.live,g,y,a]),(0,rS.useEffect)(()=>{c&&l?(0,rC.NL)(l):(0,rC.NL)("unknown")},[c,l]);let[S,T]=(0,rS.useState)(!1);(0,rS.useEffect)(()=>{if(!(0,r_.vY)()){if(c&&a){(0,rC.kN)("live");return}if(c){(0,rC.kN)("static");return}(0,rC.kN)("unknown")}},[c,a]),(0,rS.useEffect)(()=>{if(!(0,r_.vY)())return;let e=new AbortController,t=setTimeout(()=>(0,rC.kN)("live"),3e3);return window.addEventListener("message",r=>{let{data:n}=r;n&&"object"==typeof n&&"domain"in n&&"sanity/channels"===n.domain&&"from"in n&&"presentation"===n.from&&(clearTimeout(t),(0,rC.kN)((0,r_.v$)()?"presentation-window":"presentation-iframe"),T(!0),e.abort())},{signal:e.signal}),()=>{clearTimeout(t),e.abort()}},[]);let C=(0,rS.useRef)(void 0);return(0,rS.useEffect)(()=>{if(c)return clearTimeout(C.current),()=>{C.current=setTimeout(()=>{console.warn("Sanity Live: Draft mode was enabled, but is now being disabled")})}},[c]),(0,rS.useEffect)(()=>{if(!w)return;let e=setInterval(()=>x.refresh(),w);return()=>clearInterval(e)},[w,x]),(0,n.jsxs)(n.Fragment,{children:[c&&S&&(0,n.jsx)(rI,{projectId:t,dataset:r,draftModeEnabled:c,draftModePerspective:l}),!c&&f&&(0,n.jsx)(rR,{}),!c&&h&&(0,n.jsx)(rk,{}),!c&&p&&(0,n.jsx)(r$,{})]})}rO.displayName="SanityLive"},8307:(e,t,r)=>{"use strict";r.d(t,{Fk:()=>l,jF:()=>c,v$:()=>f,vY:()=>h});var n=r(3656);let i={"handshake/syn":n.Tx,"handshake/syn-ack":n.Rr,"handshake/ack":n.IM,"channel/response":n._K,"channel/heartbeat":n.vd,"channel/disconnect":n.FS,"overlay/focus":"visual-editing/focus","overlay/navigate":"visual-editing/navigate","overlay/toggle":"visual-editing/toggle","presentation/toggleOverlay":"presentation/toggle-overlay"},s={[n.Tx]:"handshake/syn",[n.Rr]:"handshake/syn-ack",[n.IM]:"handshake/ack",[n._K]:"channel/response",[n.vd]:"channel/heartbeat",[n.FS]:"channel/disconnect","visual-editing/focus":"overlay/focus","visual-editing/navigate":"overlay/navigate","visual-editing/toggle":"overlay/toggle","presentation/toggle-overlay":"presentation/toggleOverlay"},o=e=>{let{data:t}=e;return t&&"object"==typeof t&&"domain"in t&&"type"in t&&"from"in t&&"to"in t&&("sanity/channels"===t.domain&&(t.domain=n.V2),"overlays"===t.to&&(t.to="visual-editing"),"overlays"===t.from&&(t.from="visual-editing"),t.channelId=t.connectionId,delete t.connectionId,t.type=i[t.type]??t.type),e},a=e=>{let{channelId:t,...r}=e,i={...r,connectionId:t};return i.domain===n.V2&&(i.domain="sanity/channels"),"visual-editing"===i.to&&(i.to="overlays"),"visual-editing"===i.from&&(i.from="overlays"),i.type=s[i.type]??i.type,"channel/response"===i.type&&i.responseTo&&!i.data&&(i.data={responseTo:i.responseTo}),("handshake/syn"===i.type||"handshake/syn-ack"===i.type||"handshake/ack"===i.type)&&(i.data={id:i.connectionId}),i},u=({context:e},t)=>{let{sources:r,targetOrigin:n}=e,i=a(t.message);r.forEach(e=>{e.postMessage(i,{targetOrigin:n})})},c=()=>({listen:(0,n.CC)(o),requestMachine:(0,n.tP)().provide({actions:{"send message":u}})});function l(){return window.self!==window.top}function f(){return!!window.opener}function h(){return l()||f()}},4687:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var n=r(5155),i=r(2115);let s=(0,i.lazy)(()=>Promise.all([r.e(977),r.e(654),r.e(678),r.e(248),r.e(950)]).then(r.bind(r,5359)));function o(e){return(0,n.jsx)(i.Suspense,{fallback:null,children:(0,n.jsx)(s,{...e})})}},6476:(e,t,r)=>{"use strict";r.d(t,{AQ:()=>f,C6:()=>i,Ju:()=>a,N3:()=>l,YH:()=>o,fX:()=>c,sH:()=>s,xN:()=>h,zs:()=>u});var n=function(e,t){return(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)};function i(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}function s(e,t,r,n){return new(r||(r=Promise))(function(i,s){function o(e){try{u(n.next(e))}catch(e){s(e)}}function a(e){try{u(n.throw(e))}catch(e){s(e)}}function u(e){var t;e.done?i(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(o,a)}u((n=n.apply(e,t||[])).next())})}function o(e,t){var r,n,i,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=a(0),o.throw=a(1),o.return=a(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(a){return function(u){return function(a){if(r)throw TypeError("Generator is already executing.");for(;o&&(o=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(i=2&a[0]?n.return:a[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,a[1])).done)return i;switch(n=0,i&&(a=[2&a[0],i.value]),a[0]){case 0:case 1:i=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(i=(i=s.trys).length>0&&i[i.length-1])&&(6===a[0]||2===a[0])){s=0;continue}if(3===a[0]&&(!i||a[1]>i[0]&&a[1]<i[3])){s.label=a[1];break}if(6===a[0]&&s.label<i[1]){s.label=i[1],i=a;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(a);break}i[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],n=0}finally{r=i=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}}function a(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function u(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,s=r.call(e),o=[];try{for(;(void 0===t||t-- >0)&&!(n=s.next()).done;)o.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=s.return)&&r.call(s)}finally{if(i)throw i.error}}return o}function c(e,t,r){if(r||2==arguments.length)for(var n,i=0,s=t.length;i<s;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))}function l(e){return this instanceof l?(this.v=e,this):new l(e)}function f(e,t,r){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var n,i=r.apply(e,t||[]),s=[];return n=Object.create(("function"==typeof AsyncIterator?AsyncIterator:Object).prototype),o("next"),o("throw"),o("return",function(e){return function(t){return Promise.resolve(t).then(e,c)}}),n[Symbol.asyncIterator]=function(){return this},n;function o(e,t){i[e]&&(n[e]=function(t){return new Promise(function(r,n){s.push([e,t,r,n])>1||a(e,t)})},t&&(n[e]=t(n[e])))}function a(e,t){try{var r;(r=i[e](t)).value instanceof l?Promise.resolve(r.value.v).then(u,c):f(s[0][2],r)}catch(e){f(s[0][3],e)}}function u(e){a("next",e)}function c(e){a("throw",e)}function f(e,t){e(t),s.shift(),s.length&&a(s[0][0],s[0][1])}}function h(e){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var t,r=e[Symbol.asyncIterator];return r?r.call(e):(e=a(e),t={},n("next"),n("throw"),n("return"),t[Symbol.asyncIterator]=function(){return this},t);function n(r){t[r]=e[r]&&function(t){return new Promise(function(n,i){!function(e,t,r,n){Promise.resolve(n).then(function(t){e({value:t,done:r})},t)}(n,i,(t=e[r](t)).done,t.value)})}}}Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError},4880:(e,t,r)=>{"use strict";r.d(t,{J:()=>i});var n=r(2115);function i(e){let t=(0,n.useRef)(null);return(0,n.useInsertionEffect)(()=>{t.current=e},[e]),(0,n.useCallback)((...e)=>(0,t.current)(...e),[])}}}]);