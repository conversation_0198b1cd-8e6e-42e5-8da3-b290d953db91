"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[665],{2665:(t,e,i)=>{var s,a,n,o,r,l,h,c,d,u,p,f,v,m,g,y,_,w,b,x,z,M;i.d(e,{HKZ:()=>c,A9e:()=>ey,H_P:()=>n,jlt:()=>e5,_or:()=>m,VKE:()=>o,Q5V:()=>p,mgy:()=>M,sJd:()=>ic,Oit:()=>t8,Yzx:()=>g,v5k:()=>r,x_I:()=>w,U4p:()=>id,qiC:()=>l,M_G:()=>e6,pTR:()=>x,PVU:()=>eb,Miz:()=>Q,n02:()=>tw,MhH:()=>tc,l1q:()=>td,AEc:()=>tz,pEn:()=>tS,Tgl:()=>tC,qE8:()=>K,EYT:()=>tN,zwS:()=>tM,puj:()=>tl,NVT:()=>tO,U6C:()=>tP,gdl:()=>F,V6E:()=>t1,dIx:()=>S,wJ2:()=>tD,YfF:()=>tr,vrU:()=>to,pzy:()=>tZ,_hh:()=>tX,PGG:()=>tQ,tZQ:()=>tf,G0i:()=>J,W9e:()=>ta,VGA:()=>ti,YCE:()=>tF,LCf:()=>tY,xxz:()=>tG,MXx:()=>D,YLU:()=>tU,ayx:()=>tW,XsS:()=>tE,cyL:()=>Y,iKf:()=>tk,hnD:()=>ty,kZJ:()=>N,EtT:()=>$,Gvm:()=>G,Tj8:()=>tx,B90:()=>tm,Vh1:()=>tb,TA3:()=>tI,AlN:()=>t_,yVN:()=>eZ,XuV:()=>I,Z04:()=>P,RbF:()=>O,boI:()=>R,M3Y:()=>tu,a56:()=>T,U43:()=>te,R5R:()=>tB,BN0:()=>tH,jon:()=>tT,K6u:()=>tq,DT4:()=>tn,$k3:()=>iu,UC0:()=>tA,JvX:()=>t0});let k="generated",P="pointerleave",O="pointermove",C="touchend",S="tsParticles - Error",T=100,D=.5,I=1e3,R={x:0,y:0,z:0},E={a:1,b:0,c:0,d:1},L="random",F=2,A=2*Math.PI,V="true",H="false",B="canvas",q=0;function U(t){return"boolean"==typeof t}function W(t){return"string"==typeof t}function $(t){return"number"==typeof t}function G(t){return"object"==typeof t&&null!==t}function Y(t){return Array.isArray(t)}function N(t){return null==t}!function(t){t.bottom="bottom",t.bottomLeft="bottom-left",t.bottomRight="bottom-right",t.left="left",t.none="none",t.right="right",t.top="top",t.topLeft="top-left",t.topRight="top-right",t.outside="outside",t.inside="inside"}(s||(s={}));class X{constructor(t,e,i){if(this._updateFromAngle=(t,e)=>{this.x=Math.cos(t)*e,this.y=Math.sin(t)*e},!$(t)&&t)this.x=t.x,this.y=t.y,this.z=t.z?t.z:R.z;else if(void 0!==t&&void 0!==e)this.x=t,this.y=e,this.z=i??R.z;else throw Error(`${S} Vector3d not initialized correctly`)}static get origin(){return X.create(R.x,R.y,R.z)}get angle(){return Math.atan2(this.y,this.x)}set angle(t){this._updateFromAngle(t,this.length)}get length(){return Math.sqrt(this.getLengthSq())}set length(t){this._updateFromAngle(this.angle,t)}static clone(t){return X.create(t.x,t.y,t.z)}static create(t,e,i){return new X(t,e,i)}add(t){return X.create(this.x+t.x,this.y+t.y,this.z+t.z)}addTo(t){this.x+=t.x,this.y+=t.y,this.z+=t.z}copy(){return X.clone(this)}distanceTo(t){return this.sub(t).length}distanceToSq(t){return this.sub(t).getLengthSq()}div(t){return X.create(this.x/t,this.y/t,this.z/t)}divTo(t){this.x/=t,this.y/=t,this.z/=t}getLengthSq(){return this.x**2+this.y**2}mult(t){return X.create(this.x*t,this.y*t,this.z*t)}multTo(t){this.x*=t,this.y*=t,this.z*=t}normalize(){let t=this.length;0!=t&&this.multTo(1/t)}rotate(t){return X.create(this.x*Math.cos(t)-this.y*Math.sin(t),this.x*Math.sin(t)+this.y*Math.cos(t),R.z)}setTo(t){this.x=t.x,this.y=t.y,this.z=t.z?t.z:R.z}sub(t){return X.create(this.x-t.x,this.y-t.y,this.z-t.z)}subFrom(t){this.x-=t.x,this.y-=t.y,this.z-=t.z}}class Q extends X{constructor(t,e){super(t,e,R.z)}static get origin(){return Q.create(R.x,R.y)}static clone(t){return Q.create(t.x,t.y)}static create(t,e){return new Q(t,e)}}let j=Math.random,Z={nextFrame:t=>requestAnimationFrame(t),cancel:t=>cancelAnimationFrame(t)};function J(){return K(j(),0,1-Number.EPSILON)}function K(t,e,i){return Math.min(Math.max(t,e),i)}function tt(t,e,i,s){return Math.floor((t*i+e*s)/(i+s))}function te(t){let e=ta(t),i=ts(t);return e===i&&(i=0),J()*(e-i)+i}function ti(t){return $(t)?t:te(t)}function ts(t){return $(t)?t:t.min}function ta(t){return $(t)?t:t.max}function tn(t,e){if(t===e||void 0===e&&$(t))return t;let i=ts(t),s=ta(t);return void 0!==e?{min:Math.min(i,e),max:Math.max(s,e)}:tn(i,s)}function to(t,e){let i=t.x-e.x,s=t.y-e.y;return{dx:i,dy:s,distance:Math.sqrt(i**2+s**2)}}function tr(t,e){return to(t,e).distance}function tl(t){return t*Math.PI/180}function th(t,e,i,s){return Q.create(t.x*(i-s)/(i+s)+e.x*F*s/(i+s),t.y)}function tc(t){return{x:(t.position?.x??J()*T)*t.size.width/T,y:(t.position?.y??J()*T)*t.size.height/T}}function td(t){let e={x:t.position?.x!==void 0?ti(t.position.x):void 0,y:t.position?.y!==void 0?ti(t.position.y):void 0};return tc({size:t.size,position:e})}function tu(t){return t?t.endsWith("%")?parseFloat(t)/T:parseFloat(t):1}!function(t){t.auto="auto",t.increase="increase",t.decrease="decrease",t.random="random"}(a||(a={})),function(t){t.increasing="increasing",t.decreasing="decreasing"}(n||(n={})),function(t){t.none="none",t.max="max",t.min="min"}(o||(o={})),function(t){t.bottom="bottom",t.left="left",t.right="right",t.top="top"}(r||(r={})),function(t){t.precise="precise",t.percent="percent"}(l||(l={})),function(t){t.max="max",t.min="min",t.random="random"}(h||(h={}));let tp={debug:console.debug,error:console.error,info:console.info,log:console.log,verbose:console.log,warning:console.warn};function tf(){return tp}function tv(t){let e={bounced:!1},{pSide:i,pOtherSide:s,rectSide:a,rectOtherSide:n,velocity:o,factor:r}=t;return s.min<n.min||s.min>n.max||s.max<n.min||s.max>n.max||(i.max>=a.min&&i.max<=(a.max+a.min)*D&&o>0||i.min<=a.max&&i.min>(a.max+a.min)*D&&o<0)&&(e.velocity=-(o*r),e.bounced=!0),e}function tm(){return"undefined"==typeof window||!window||void 0===window.document||!window.document}function tg(t){if(!tm()&&"undefined"!=typeof matchMedia)return matchMedia(t)}function ty(t,e){return t===e||Y(e)&&e.indexOf(t)>-1}async function t_(t,e){try{await document.fonts.load(`${e??"400"} 36px '${t??"Verdana"}'`)}catch{}}function tw(t){return Math.floor(J()*t.length)}function tb(t,e,i=!0){return t[void 0!==e&&i?e%t.length:tw(t)]}function tx(t,e,i,s,a){var n;let o;return n=tz(t,s??0),o=!0,a&&a!==r.bottom||(o=n.top<e.height+i.x),o&&(!a||a===r.left)&&(o=n.right>i.x),o&&(!a||a===r.right)&&(o=n.left<e.width+i.y),o&&(!a||a===r.top)&&(o=n.bottom>i.y),o}function tz(t,e){return{bottom:t.y+e,left:t.x-e,right:t.x+e,top:t.y-e}}function tM(t,...e){for(let i of e){if(null==i)continue;if(!G(i)){t=i;continue}let e=Array.isArray(i);for(let s in e&&(G(t)||!t||!Array.isArray(t))?t=[]:!e&&(G(t)||!t||Array.isArray(t))&&(t={}),i){if("__proto__"===s)continue;let e=i[s],a=t;a[s]=G(e)&&Array.isArray(e)?e.map(t=>tM(a[s],t)):tM(a[s],e)}}return t}function tk(t,e){return!!tR(e,e=>e.enable&&ty(t,e.mode))}function tP(t,e,i){tD(e,e=>{let s=e.mode;e.enable&&ty(t,s)&&tD(e.selectors,t=>{i(t,e)})})}function tO(t,e){if(e&&t)return tR(t,t=>(function(t,e){let i=tD(e,e=>t.matches(e));return Y(i)?i.some(t=>t):i})(e,t.selectors))}function tC(t){return{position:t.getPosition(),radius:t.getRadius(),mass:t.getMass(),velocity:t.velocity,factor:Q.create(ti(t.options.bounce.horizontal.value),ti(t.options.bounce.vertical.value))}}function tS(t,e){let{x:i,y:s}=t.velocity.sub(e.velocity),[a,n]=[t.position,e.position],{dx:o,dy:r}=to(n,a);if(i*o+s*r<0)return;let l=-Math.atan2(r,o),h=t.mass,c=e.mass,d=t.velocity.rotate(l),u=e.velocity.rotate(l),p=th(d,u,h,c),f=th(u,d,h,c),v=p.rotate(-l),m=f.rotate(-l);t.velocity.x=v.x*t.factor.x,t.velocity.y=v.y*t.factor.y,e.velocity.x=m.x*e.factor.x,e.velocity.y=m.y*e.factor.y}function tT(t,e){let i=tz(t.getPosition(),t.getRadius()),s=t.options.bounce,a=tv({pSide:{min:i.left,max:i.right},pOtherSide:{min:i.top,max:i.bottom},rectSide:{min:e.left,max:e.right},rectOtherSide:{min:e.top,max:e.bottom},velocity:t.velocity.x,factor:ti(s.horizontal.value)});a.bounced&&(void 0!==a.velocity&&(t.velocity.x=a.velocity),void 0!==a.position&&(t.position.x=a.position));let n=tv({pSide:{min:i.top,max:i.bottom},pOtherSide:{min:i.left,max:i.right},rectSide:{min:e.top,max:e.bottom},rectOtherSide:{min:e.left,max:e.right},velocity:t.velocity.y,factor:ti(s.vertical.value)});n.bounced&&(void 0!==n.velocity&&(t.velocity.y=n.velocity),void 0!==n.position&&(t.position.y=n.position))}function tD(t,e){return Y(t)?t.map((t,i)=>e(t,i)):e(t,0)}function tI(t,e,i){return Y(t)?tb(t,e,i):t}function tR(t,e){return Y(t)?t.find((t,i)=>e(t,i)):e(t,0)?t:void 0}function tE(t,e){let i=t.value,s=t.animation,o={delayTime:ti(s.delay)*I,enable:s.enable,value:ti(t.value)*e,max:ta(i)*e,min:ts(i)*e,loops:0,maxLoops:ti(s.count),time:0};if(s.enable){switch(o.decay=1-ti(s.decay),s.mode){case a.increase:o.status=n.increasing;break;case a.decrease:o.status=n.decreasing;break;case a.random:o.status=J()>=D?n.increasing:n.decreasing}let t=s.mode===a.auto;switch(s.startValue){case h.min:o.value=o.min,t&&(o.status=n.increasing);break;case h.max:o.value=o.max,t&&(o.status=n.decreasing);break;case h.random:default:o.value=te(o),t&&(o.status=J()>=D?n.increasing:n.decreasing)}}return o.initialValue=o.value,o}function tL(t,e){if(t.mode!==l.percent){let{mode:e,...i}=t;return i}return"x"in t?{x:t.x/T*e.width,y:t.y/T*e.height}:{width:t.width/T*e.width,height:t.height/T*e.height}}function tF(t,e){return tL(t,e)}function tA(t,e,i,s,a){if(t.destroyed||!e||!e.enable||(e.maxLoops??0)>0&&(e.loops??0)>(e.maxLoops??0))return;let r=(e.velocity??0)*a.factor,l=e.min,h=e.max,c=e.decay??1;if(e.time||(e.time=0),(e.delayTime??0)>0&&e.time<(e.delayTime??0)&&(e.time+=a.value),!((e.delayTime??0)>0)||!(e.time<(e.delayTime??0))){switch(e.status){case n.increasing:e.value>=h?(i?e.status=n.decreasing:e.value-=h,e.loops||(e.loops=0),e.loops++):e.value+=r;break;case n.decreasing:e.value<=l?(i?e.status=n.increasing:e.value+=h,e.loops||(e.loops=0),e.loops++):e.value-=r}e.velocity&&1!==c&&(e.velocity*=c),function(t,e,i,s,a){switch(e){case o.max:i>=a&&t.destroy();break;case o.min:i<=s&&t.destroy()}}(t,s,e.value,l,h),t.destroyed||(e.value=K(e.value,l,h))}}let tV=function(t){let e=new Map;return(...i)=>{let s=JSON.stringify(i);if(e.has(s))return e.get(s);let a=t(...i);return e.set(s,a),a}}(function(t){let e=document.createElement("div").style,i={width:"100%",height:"100%",margin:"0",padding:"0",borderWidth:"0",position:"fixed",zIndex:t.toString(10),"z-index":t.toString(10),top:"0",left:"0"};for(let t in i){let s=i[t];e.setProperty(t,s)}return e});function tH(t,e,i,s=!0){if(!e)return;let a=W(e)?{value:e}:e;if(W(a.value))return function t(e,i,s,a=!0){if(!i)return;let n=W(i)?{value:i}:i;if(W(n.value))return n.value===L?t$():function(t,e){if(e){for(let i of t.colorManagers.values())if(e.startsWith(i.stringPrefix))return i.parseString(e)}}(e,n.value);if(Y(n.value))return t(e,{value:tb(n.value,s,a)});for(let t of e.colorManagers.values()){let e=t.handleColor(n);if(e)return e}}(t,a.value,i,s);if(Y(a.value))return tH(t,{value:tb(a.value,i,s)});for(let e of t.colorManagers.values()){let t=e.handleRangeColor(a);if(t)return t}}function tB(t,e,i,s=!0){let a=tH(t,e,i,s);return a?tq(a):void 0}function tq(t){let e=t.r/255,i=t.g/255,s=t.b/255,a=Math.max(e,i,s),n=Math.min(e,i,s),o={h:0,l:(a+n)*D,s:0};return a!==n&&(o.s=o.l<D?(a-n)/(a+n):(a-n)/(F-a-n),o.h=e===a?(i-s)/(a-n):o.h=i===a?F+(s-e)/(a-n):F*F+(e-i)/(a-n)),o.l*=100,o.s*=100,o.h*=60,o.h<0&&(o.h+=360),o.h>=360&&(o.h-=360),o}function tU(t){let e=(t.h%360+360)%360,i=Math.max(0,Math.min(100,t.s)),s=Math.max(0,Math.min(100,t.l)),a=e/360,n=i/100,o=s/100;if(0===i){let t=Math.round(255*o);return{r:t,g:t,b:t}}let r=(t,e,i)=>(i<0&&i++,i>1&&i--,6*i<1)?t+(e-t)*6*i:i*F<1?e:3*i<1*F?t+(e-t)*(F/3-i)*6:t,l=o<D?o*(1+n):o+n-o*n,h=F*o-l;return{r:Math.round(Math.min(255,255*r(h,l,a+.3333333333333333))),g:Math.round(Math.min(255,255*r(h,l,a))),b:Math.round(Math.min(255,255*r(h,l,a-.3333333333333333)))}}function tW(t){let e=tU(t);return{a:t.a,b:e.b,g:e.g,r:e.r}}function t$(t){let e=t??0;return{b:Math.floor(te(tn(e,256))),g:Math.floor(te(tn(e,256))),r:Math.floor(te(tn(e,256)))}}function tG(t,e){return`rgba(${t.r}, ${t.g}, ${t.b}, ${e??1})`}function tY(t,e){return`hsla(${t.h}, ${t.s}%, ${t.l}%, ${e??1})`}function tN(t,e,i,s){let a=t,n=e;return void 0===a.r&&(a=tU(t)),void 0===n.r&&(n=tU(e)),{b:tt(a.b,n.b,i,s),g:tt(a.g,n.g,i,s),r:tt(a.r,n.r,i,s)}}function tX(t,e,i){if(i===L)return t$();if("mid"!==i)return i;{let i=t.getFillColor()??t.getStrokeColor(),s=e?.getFillColor()??e?.getStrokeColor();if(i&&s&&e)return tN(i,s,t.getRadius(),e.getRadius());{let t=i??s;if(t)return tU(t)}}}function tQ(t,e,i,s){let a=W(e)?e:e.value;return a===L?s?tH(t,{value:a}):i?L:"mid":"mid"===a?"mid":tH(t,{value:a})}function tj(t){return void 0!==t?{h:t.h.value,s:t.s.value,l:t.l.value}:void 0}function tZ(t,e,i){let s={h:{enable:!1,value:t.h},s:{enable:!1,value:t.s},l:{enable:!1,value:t.l}};return e&&(tJ(s.h,e.h,i),tJ(s.s,e.s,i),tJ(s.l,e.l,i)),s}function tJ(t,e,i){t.enable=e.enable,t.enable?(t.velocity=ti(e.speed)/T*i,t.decay=1-ti(e.decay),t.status=n.increasing,t.loops=0,t.maxLoops=ti(e.count),t.time=0,t.delayTime=ti(e.delay)*I,e.sync||(t.velocity*=J(),t.value*=J()),t.initialValue=t.value,t.offset=tn(e.offset)):t.velocity=0}function tK(t,e,i,s){if(!t||!t.enable||(t.maxLoops??0)>0&&(t.loops??0)>(t.maxLoops??0)||(t.time||(t.time=0),(t.delayTime??0)>0&&t.time<(t.delayTime??0)&&(t.time+=s.value),(t.delayTime??0)>0&&t.time<(t.delayTime??0)))return;let a=t.offset?te(t.offset):0,o=(t.velocity??0)*s.factor+3.6*a,r=t.decay??1,l=ta(e),h=ts(e);i&&t.status!==n.increasing?(t.value-=o,t.value<0&&(t.loops||(t.loops=0),t.loops++,t.status=n.increasing)):(t.value+=o,t.value>l&&(t.loops||(t.loops=0),t.loops++,i?t.status=n.decreasing:t.value-=l)),t.velocity&&1!==r&&(t.velocity*=r),t.value=K(t.value,h,l)}function t0(t,e){if(!t)return;let{h:i,s,l:a}=t,n={h:{min:0,max:360},s:{min:0,max:100},l:{min:0,max:100}};i&&tK(i,n.h,!1,e),s&&tK(s,n.s,!0,e),a&&tK(a,n.l,!0,e)}function t1(t,e,i){t.beginPath(),t.moveTo(e.x,e.y),t.lineTo(i.x,i.y),t.closePath()}function t3(t,e){t.clearRect(R.x,R.y,e.width,e.height)}function t2(t,e,i=!1){if(!e||!t)return;let s=t.style;if(!s)return;let a=new Set;for(let t in s)Object.prototype.hasOwnProperty.call(s,t)&&a.add(s[t]);for(let t in e)Object.prototype.hasOwnProperty.call(e,t)&&a.add(e[t]);for(let t of a){let a=e.getPropertyValue(t);a?s.setProperty(t,a,i?"important":""):s.removeProperty(t)}}!function(t){t.darken="darken",t.enlighten="enlighten"}(c||(c={}));class t5{constructor(t,e){this.container=t,this._applyPostDrawUpdaters=t=>{for(let e of this._postDrawUpdaters)e.afterDraw?.(t)},this._applyPreDrawUpdaters=(t,e,i,s,a,n)=>{for(let o of this._preDrawUpdaters){if(o.getColorStyles){let{fill:n,stroke:r}=o.getColorStyles(e,t,i,s);n&&(a.fill=n),r&&(a.stroke=r)}if(o.getTransformValues){let t=o.getTransformValues(e);for(let e in t)!function(t,e,i){let s=e[i];void 0!==s&&(t[i]=(t[i]??1)*s)}(n,t,e)}o.beforeDraw?.(e)}},this._applyResizePlugins=()=>{for(let t of this._resizePlugins)t.resize?.()},this._getPluginParticleColors=t=>{let e,i;for(let s of this._colorPlugins)if(!e&&s.particleFillColor&&(e=tB(this._engine,s.particleFillColor(t))),!i&&s.particleStrokeColor&&(i=tB(this._engine,s.particleStrokeColor(t))),e&&i)break;return[e,i]},this._initCover=async()=>{let t=this.container.actualOptions.backgroundMask.cover,e=t.color;if(e){let i=tH(this._engine,e);if(i){let e={...i,a:t.opacity};this._coverColorStyle=tG(e,e.a)}}else await new Promise((e,i)=>{if(!t.image)return;let s=document.createElement("img");s.addEventListener("load",()=>{this._coverImage={image:s,opacity:t.opacity},e()}),s.addEventListener("error",t=>{i(t.error)}),s.src=t.image})},this._initStyle=()=>{let t=this.element,e=this.container.actualOptions;if(t)for(let i in this._fullScreen?this._setFullScreenStyle():this._resetOriginalStyle(),e.style){if(!i||!e.style||!Object.prototype.hasOwnProperty.call(e.style,i))continue;let s=e.style[i];s&&t.style.setProperty(i,s,"important")}},this._initTrail=async()=>{let t=this.container.actualOptions.particles.move.trail,e=t.fill;if(!t.enable)return;let i=1/t.length;if(e.color){let t=tH(this._engine,e.color);if(!t)return;this._trailFill={color:{...t},opacity:i}}else await new Promise((t,s)=>{if(!e.image)return;let a=document.createElement("img");a.addEventListener("load",()=>{this._trailFill={image:a,opacity:i},t()}),a.addEventListener("error",t=>{s(t.error)}),a.src=e.image})},this._paintBase=t=>{this.draw(e=>(function(t,e,i){t.fillStyle=i??"rgba(0,0,0,0)",t.fillRect(R.x,R.y,e.width,e.height)})(e,this.size,t))},this._paintImage=(t,e)=>{this.draw(i=>(function(t,e,i,s){i&&(t.globalAlpha=s,t.drawImage(i,R.x,R.y,e.width,e.height),t.globalAlpha=1)})(i,this.size,t,e))},this._repairStyle=()=>{let t=this.element;t&&(this._safeMutationObserver(t=>t.disconnect()),this._initStyle(),this.initBackground(),this._safeMutationObserver(e=>{t&&t instanceof Node&&e.observe(t,{attributes:!0})}))},this._resetOriginalStyle=()=>{let t=this.element,e=this._originalStyle;t&&e&&t2(t,e,!0)},this._safeMutationObserver=t=>{this._mutationObserver&&t(this._mutationObserver)},this._setFullScreenStyle=()=>{let t=this.element;t&&t2(t,tV(this.container.actualOptions.fullScreen.zIndex),!0)},this._engine=e,this._standardSize={height:0,width:0};let i=t.retina.pixelRatio,s=this._standardSize;this.size={height:s.height*i,width:s.width*i},this._context=null,this._generated=!1,this._preDrawUpdaters=[],this._postDrawUpdaters=[],this._resizePlugins=[],this._colorPlugins=[]}get _fullScreen(){return this.container.actualOptions.fullScreen.enable}clear(){let t=this.container.actualOptions,e=t.particles.move.trail,i=this._trailFill;t.backgroundMask.enable?this.paint():e.enable&&e.length>0&&i?i.color?this._paintBase(tG(i.color,i.opacity)):i.image&&this._paintImage(i.image,i.opacity):t.clear&&this.draw(t=>{t3(t,this.size)})}destroy(){if(this.stop(),this._generated){let t=this.element;t?.remove(),this.element=void 0}else this._resetOriginalStyle();this._preDrawUpdaters=[],this._postDrawUpdaters=[],this._resizePlugins=[],this._colorPlugins=[]}draw(t){let e=this._context;if(e)return t(e)}drawAsync(t){let e=this._context;if(e)return t(e)}drawParticle(t,e){if(t.spawning||t.destroyed)return;let i=t.getRadius();if(i<=0)return;let s=t.getFillColor(),a=t.getStrokeColor()??s,[n,o]=this._getPluginParticleColors(t);n||(n=s),o||(o=a),(n||o)&&this.draw(s=>{let a=this.container,r=a.actualOptions,l=t.options.zIndex,h=1-t.zIndexFactor,c=h**l.opacityRate,d=t.bubble.opacity??t.opacity?.value??1,u=t.strokeOpacity??d,p=d*c,f={},v={fill:n?tY(n,p):void 0};v.stroke=o?tY(o,u*c):v.fill,this._applyPreDrawUpdaters(s,t,i,p,v,f),function(t){let{container:e,context:i,particle:s,delta:a,colorStyles:n,backgroundMask:o,composite:r,radius:l,opacity:h,shadow:c,transform:d}=t,u=s.getPosition(),p=s.rotation+(s.pathRotation?s.velocity.angle:0),f={sin:Math.sin(p),cos:Math.cos(p)},v=!!p,m={a:f.cos*(d.a??E.a),b:v?f.sin*(d.b??1):d.b??E.b,c:v?-f.sin*(d.c??1):d.c??E.c,d:f.cos*(d.d??E.d)};i.setTransform(m.a,m.b,m.c,m.d,u.x,u.y),o&&(i.globalCompositeOperation=r);let g=s.shadowColor;c.enable&&g&&(i.shadowBlur=c.blur,i.shadowColor=tG(g),i.shadowOffsetX=c.offset.x,i.shadowOffsetY=c.offset.y),n.fill&&(i.fillStyle=n.fill);let y=s.strokeWidth??0;i.lineWidth=y,n.stroke&&(i.strokeStyle=n.stroke);let _={container:e,context:i,particle:s,radius:l,opacity:h,delta:a,transformData:m,strokeWidth:y};(function(t){let{container:e,context:i,particle:s,radius:a,opacity:n,delta:o,strokeWidth:r,transformData:l}=t;if(!s.shape)return;let h=e.shapeDrawers.get(s.shape);h&&(i.beginPath(),h.draw({context:i,particle:s,radius:a,opacity:n,delta:o,pixelRatio:e.retina.pixelRatio,transformData:{...l}}),s.shapeClose&&i.closePath(),r>0&&i.stroke(),s.shapeFill&&i.fill())})(_),function(t){let{container:e,context:i,particle:s,radius:a,opacity:n,delta:o,transformData:r}=t;if(!s.shape)return;let l=e.shapeDrawers.get(s.shape);l?.afterDraw&&l.afterDraw({context:i,particle:s,radius:a,opacity:n,delta:o,pixelRatio:e.retina.pixelRatio,transformData:{...r}})}(_),function(t){let{container:e,context:i,particle:s,radius:a,opacity:n,delta:o,transformData:r}=t;if(!s.effect)return;let l=e.effectDrawers.get(s.effect);l&&l.draw({context:i,particle:s,radius:a,opacity:n,delta:o,pixelRatio:e.retina.pixelRatio,transformData:{...r}})}(_),i.globalCompositeOperation="source-over",i.resetTransform()}({container:a,context:s,particle:t,delta:e,colorStyles:v,backgroundMask:r.backgroundMask.enable,composite:r.backgroundMask.composite,radius:i*h**l.sizeRate,opacity:p,shadow:t.options.shadow,transform:f}),this._applyPostDrawUpdaters(t)})}drawParticlePlugin(t,e,i){this.draw(s=>{t.drawParticle&&t.drawParticle(s,e,i)})}drawPlugin(t,e){this.draw(i=>{t.draw&&t.draw(i,e)})}async init(){this._safeMutationObserver(t=>t.disconnect()),this._mutationObserver=function(t){if(!tm()&&"undefined"!=typeof MutationObserver)return new MutationObserver(t)}(t=>{for(let e of t)"attributes"===e.type&&"style"===e.attributeName&&this._repairStyle()}),this.resize(),this._initStyle(),await this._initCover();try{await this._initTrail()}catch(t){tp.error(t)}this.initBackground(),this._safeMutationObserver(t=>{this.element&&this.element instanceof Node&&t.observe(this.element,{attributes:!0})}),this.initUpdaters(),this.initPlugins(),this.paint()}initBackground(){let t=this.container.actualOptions.background,e=this.element;if(!e)return;let i=e.style;if(i){if(t.color){let e=tH(this._engine,t.color);i.backgroundColor=e?tG(e,t.opacity):""}else i.backgroundColor="";i.backgroundImage=t.image||"",i.backgroundPosition=t.position||"",i.backgroundRepeat=t.repeat||"",i.backgroundSize=t.size||""}}initPlugins(){for(let t of(this._resizePlugins=[],this.container.plugins.values()))t.resize&&this._resizePlugins.push(t),(t.particleFillColor??t.particleStrokeColor)&&this._colorPlugins.push(t)}initUpdaters(){for(let t of(this._preDrawUpdaters=[],this._postDrawUpdaters=[],this.container.particles.updaters))t.afterDraw&&this._postDrawUpdaters.push(t),(t.getColorStyles??t.getTransformValues??t.beforeDraw)&&this._preDrawUpdaters.push(t)}loadCanvas(t){this._generated&&this.element&&this.element.remove(),this._generated=t.dataset&&k in t.dataset?"true"===t.dataset[k]:this._generated,this.element=t,this.element.ariaHidden="true",this._originalStyle=function(t){let e=document.createElement("div").style;if(!t)return e;for(let i in t){let s=t[i];if(!Object.prototype.hasOwnProperty.call(t,i)||N(s))continue;let a=t.getPropertyValue?.(s);if(!a)continue;let n=t.getPropertyPriority?.(s);n?e.setProperty?.(s,a,n):e.setProperty?.(s,a)}return e}(this.element.style);let e=this._standardSize;e.height=t.offsetHeight,e.width=t.offsetWidth;let i=this.container.retina.pixelRatio,s=this.size;t.height=s.height=e.height*i,t.width=s.width=e.width*i,this._context=this.element.getContext("2d"),this._safeMutationObserver(t=>t.disconnect()),this.container.retina.init(),this.initBackground(),this._safeMutationObserver(t=>{this.element&&this.element instanceof Node&&t.observe(this.element,{attributes:!0})})}paint(){let t=this.container.actualOptions;this.draw(e=>{t.backgroundMask.enable&&t.backgroundMask.cover?(t3(e,this.size),this._coverImage?this._paintImage(this._coverImage.image,this._coverImage.opacity):this._coverColorStyle?this._paintBase(this._coverColorStyle):this._paintBase()):this._paintBase()})}resize(){if(!this.element)return!1;let t=this.container,e=t.canvas._standardSize,i={width:this.element.offsetWidth,height:this.element.offsetHeight},s=t.retina.pixelRatio,a={width:i.width*s,height:i.height*s};if(i.height===e.height&&i.width===e.width&&a.height===this.element.height&&a.width===this.element.width)return!1;let n={...e};e.height=i.height,e.width=i.width;let o=this.size;return this.element.width=o.width=a.width,this.element.height=o.height=a.height,this.container.started&&t.particles.setResizeFactor({width:e.width/n.width,height:e.height/n.height}),!0}stop(){this._safeMutationObserver(t=>t.disconnect()),this._mutationObserver=void 0,this.draw(t=>t3(t,this.size))}async windowResize(){if(!this.element||!this.resize())return;let t=this.container,e=t.updateActualOptions();t.particles.setDensity(),this._applyResizePlugins(),e&&await t.refresh()}}function t6(t,e,i,s,a){if(s){let s={passive:!0};U(a)?s.capture=a:void 0!==a&&(s=a),t.addEventListener(e,i,s)}else t.removeEventListener(e,i,a)}!function(t){t.canvas="canvas",t.parent="parent",t.window="window"}(d||(d={}));class t4{constructor(t){this.container=t,this._doMouseTouchClick=t=>{let e=this.container,i=e.actualOptions;if(this._canPush){let t=e.interactivity.mouse,s=t.position;if(!s)return;t.clickPosition={...s},t.clickTime=new Date().getTime(),tD(i.interactivity.events.onClick.mode,t=>this.container.handleClickMode(t))}"touchend"===t.type&&setTimeout(()=>this._mouseTouchFinish(),500)},this._handleThemeChange=t=>{let e=this.container,i=e.options,s=i.defaultThemes,a=t.matches?s.dark:s.light,n=i.themes.find(t=>t.name===a);n?.default.auto&&e.loadTheme(a)},this._handleVisibilityChange=()=>{let t=this.container,e=t.actualOptions;this._mouseTouchFinish(),e.pauseOnBlur&&(document?.hidden?(t.pageHidden=!0,t.pause()):(t.pageHidden=!1,t.animationStatus?t.play(!0):t.draw(!0)))},this._handleWindowResize=()=>{this._resizeTimeout&&(clearTimeout(this._resizeTimeout),delete this._resizeTimeout);let t=async()=>{let t=this.container.canvas;await t?.windowResize()};this._resizeTimeout=setTimeout(()=>void t(),this.container.actualOptions.interactivity.events.resize.delay*I)},this._manageInteractivityListeners=(t,e)=>{let i=this._handlers,s=this.container,a=s.actualOptions,n=s.interactivity.element;if(!n)return;let o=s.canvas.element;o&&(o.style.pointerEvents=n===o?"initial":"none"),(a.interactivity.events.onHover.enable||a.interactivity.events.onClick.enable)&&(t6(n,O,i.mouseMove,e),t6(n,"touchstart",i.touchStart,e),t6(n,"touchmove",i.touchMove,e),a.interactivity.events.onClick.enable?(t6(n,C,i.touchEndClick,e),t6(n,"pointerup",i.mouseUp,e),t6(n,"pointerdown",i.mouseDown,e)):t6(n,C,i.touchEnd,e),t6(n,t,i.mouseLeave,e),t6(n,"touchcancel",i.touchCancel,e))},this._manageListeners=t=>{let e=this._handlers,i=this.container,s=i.actualOptions.interactivity.detectsOn,a=i.canvas.element,n=P;s===d.window?(i.interactivity.element=window,n="pointerout"):s===d.parent&&a?i.interactivity.element=a.parentElement??a.parentNode:i.interactivity.element=a,this._manageMediaMatch(t),this._manageResize(t),this._manageInteractivityListeners(n,t),document&&t6(document,"visibilitychange",e.visibilityChange,t,!1)},this._manageMediaMatch=t=>{let e=this._handlers,i=tg("(prefers-color-scheme: dark)");if(i){if(void 0!==i.addEventListener){t6(i,"change",e.themeChange,t);return}void 0!==i.addListener&&(t?i.addListener(e.oldThemeChange):i.removeListener(e.oldThemeChange))}},this._manageResize=t=>{let e=this._handlers,i=this.container;if(!i.actualOptions.interactivity.events.resize)return;if("undefined"==typeof ResizeObserver){t6(window,"resize",e.resize,t);return}let s=i.canvas.element;this._resizeObserver&&!t?(s&&this._resizeObserver.unobserve(s),this._resizeObserver.disconnect(),delete this._resizeObserver):!this._resizeObserver&&t&&s&&(this._resizeObserver=new ResizeObserver(t=>{t.find(t=>t.target===s)&&this._handleWindowResize()}),this._resizeObserver.observe(s))},this._mouseDown=()=>{let{interactivity:t}=this.container;if(!t)return;let{mouse:e}=t;e.clicking=!0,e.downPosition=e.position},this._mouseTouchClick=t=>{let e=this.container,i=e.actualOptions,{mouse:s}=e.interactivity;s.inside=!0;let a=!1,n=s.position;if(n&&i.interactivity.events.onClick.enable){for(let t of e.plugins.values())if(t.clickPositionValid&&(a=t.clickPositionValid(n)))break;a||this._doMouseTouchClick(t),s.clicking=!1}},this._mouseTouchFinish=()=>{let t=this.container.interactivity;if(!t)return;let e=t.mouse;delete e.position,delete e.clickPosition,delete e.downPosition,t.status=P,e.inside=!1,e.clicking=!1},this._mouseTouchMove=t=>{let e;let i=this.container,s=i.actualOptions,a=i.interactivity,n=i.canvas.element;if(!a?.element)return;if(a.mouse.inside=!0,t.type.startsWith("pointer")){if(this._canPush=!0,a.element===window){if(n){let i=n.getBoundingClientRect();e={x:t.clientX-i.left,y:t.clientY-i.top}}}else if(s.interactivity.detectsOn===d.parent){let i=t.target,s=t.currentTarget;if(i&&s&&n){let a=i.getBoundingClientRect(),o=s.getBoundingClientRect(),r=n.getBoundingClientRect();e={x:t.offsetX+F*a.left-(o.left+r.left),y:t.offsetY+F*a.top-(o.top+r.top)}}else e={x:t.offsetX??t.clientX,y:t.offsetY??t.clientY}}else t.target===n&&(e={x:t.offsetX??t.clientX,y:t.offsetY??t.clientY})}else if(this._canPush="touchmove"!==t.type,n){let i=t.touches[t.touches.length-1],s=n.getBoundingClientRect();e={x:i.clientX-(s.left??0),y:i.clientY-(s.top??0)}}let o=i.retina.pixelRatio;e&&(e.x*=o,e.y*=o),a.mouse.position=e,a.status=O},this._touchEnd=t=>{for(let e of Array.from(t.changedTouches))this._touches.delete(e.identifier);this._mouseTouchFinish()},this._touchEndClick=t=>{for(let e of Array.from(t.changedTouches))this._touches.delete(e.identifier);this._mouseTouchClick(t)},this._touchStart=t=>{for(let e of Array.from(t.changedTouches))this._touches.set(e.identifier,performance.now());this._mouseTouchMove(t)},this._canPush=!0,this._touches=new Map,this._handlers={mouseDown:()=>this._mouseDown(),mouseLeave:()=>this._mouseTouchFinish(),mouseMove:t=>this._mouseTouchMove(t),mouseUp:t=>this._mouseTouchClick(t),touchStart:t=>this._touchStart(t),touchMove:t=>this._mouseTouchMove(t),touchEnd:t=>this._touchEnd(t),touchCancel:t=>this._touchEnd(t),touchEndClick:t=>this._touchEndClick(t),visibilityChange:()=>this._handleVisibilityChange(),themeChange:t=>this._handleThemeChange(t),oldThemeChange:t=>this._handleThemeChange(t),resize:()=>{this._handleWindowResize()}}}addListeners(){this._manageListeners(!0)}removeListeners(){this._manageListeners(!1)}}!function(t){t.configAdded="configAdded",t.containerInit="containerInit",t.particlesSetup="particlesSetup",t.containerStarted="containerStarted",t.containerStopped="containerStopped",t.containerDestroyed="containerDestroyed",t.containerPaused="containerPaused",t.containerPlay="containerPlay",t.containerBuilt="containerBuilt",t.particleAdded="particleAdded",t.particleDestroyed="particleDestroyed",t.particleRemoved="particleRemoved"}(u||(u={}));class t8{constructor(){this.value=""}static create(t,e){let i=new t8;return i.load(t),void 0!==e&&(W(e)||Y(e)?i.load({value:e}):i.load(e)),i}load(t){!N(t)&&(N(t.value)||(this.value=t.value))}}class t9{constructor(){this.color=new t8,this.color.value="",this.image="",this.position="",this.repeat="",this.size="",this.opacity=1}load(t){N(t)||(void 0!==t.color&&(this.color=t8.create(this.color,t.color)),void 0!==t.image&&(this.image=t.image),void 0!==t.position&&(this.position=t.position),void 0!==t.repeat&&(this.repeat=t.repeat),void 0!==t.size&&(this.size=t.size),void 0===t.opacity||(this.opacity=t.opacity))}}class t7{constructor(){this.opacity=1}load(t){N(t)||(void 0!==t.color&&(this.color=t8.create(this.color,t.color)),void 0!==t.image&&(this.image=t.image),void 0===t.opacity||(this.opacity=t.opacity))}}class et{constructor(){this.composite="destination-out",this.cover=new t7,this.enable=!1}load(t){if(!N(t)){if(void 0!==t.composite&&(this.composite=t.composite),void 0!==t.cover){let e=t.cover,i=W(t.cover)?{color:t.cover}:t.cover;this.cover.load(void 0!==e.color||void 0!==e.image?e:{color:i})}void 0!==t.enable&&(this.enable=t.enable)}}}class ee{constructor(){this.enable=!0,this.zIndex=0}load(t){N(t)||(void 0!==t.enable&&(this.enable=t.enable),void 0===t.zIndex||(this.zIndex=t.zIndex))}}class ei{constructor(){this.enable=!1,this.mode=[]}load(t){N(t)||(void 0!==t.enable&&(this.enable=t.enable),void 0===t.mode||(this.mode=t.mode))}}!function(t){t.circle="circle",t.rectangle="rectangle"}(p||(p={}));class es{constructor(){this.selectors=[],this.enable=!1,this.mode=[],this.type=p.circle}load(t){N(t)||(void 0!==t.selectors&&(this.selectors=t.selectors),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.mode&&(this.mode=t.mode),void 0===t.type||(this.type=t.type))}}class ea{constructor(){this.enable=!1,this.force=2,this.smooth=10}load(t){N(t)||(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.force&&(this.force=t.force),void 0===t.smooth||(this.smooth=t.smooth))}}class en{constructor(){this.enable=!1,this.mode=[],this.parallax=new ea}load(t){N(t)||(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.mode&&(this.mode=t.mode),this.parallax.load(t.parallax))}}class eo{constructor(){this.delay=.5,this.enable=!0}load(t){N(t)||(void 0!==t.delay&&(this.delay=t.delay),void 0===t.enable||(this.enable=t.enable))}}class er{constructor(){this.onClick=new ei,this.onDiv=new es,this.onHover=new en,this.resize=new eo}load(t){if(N(t))return;this.onClick.load(t.onClick);let e=t.onDiv;void 0!==e&&(this.onDiv=tD(e,t=>{let e=new es;return e.load(t),e})),this.onHover.load(t.onHover),this.resize.load(t.resize)}}class el{constructor(t,e){this._engine=t,this._container=e}load(t){if(N(t)||!this._container)return;let e=this._engine.interactors.get(this._container);if(e)for(let i of e)i.loadModeOptions&&i.loadModeOptions(this,t)}}class eh{constructor(t,e){this.detectsOn=d.window,this.events=new er,this.modes=new el(t,e)}load(t){if(N(t))return;let e=t.detectsOn;void 0!==e&&(this.detectsOn=e),this.events.load(t.events),this.modes.load(t.modes)}}class ec{load(t){!N(t)&&(t.position&&(this.position={x:t.position.x??50,y:t.position.y??50,mode:t.position.mode??l.percent}),t.options&&(this.options=tM({},t.options)))}}!function(t){t.screen="screen",t.canvas="canvas"}(f||(f={}));class ed{constructor(){this.maxWidth=1/0,this.options={},this.mode=f.canvas}load(t){!N(t)&&(N(t.maxWidth)||(this.maxWidth=t.maxWidth),N(t.mode)||(t.mode===f.screen?this.mode=f.screen:this.mode=f.canvas),N(t.options)||(this.options=tM({},t.options)))}}!function(t){t.any="any",t.dark="dark",t.light="light"}(v||(v={}));class eu{constructor(){this.auto=!1,this.mode=v.any,this.value=!1}load(t){N(t)||(void 0!==t.auto&&(this.auto=t.auto),void 0!==t.mode&&(this.mode=t.mode),void 0===t.value||(this.value=t.value))}}class ep{constructor(){this.name="",this.default=new eu}load(t){N(t)||(void 0!==t.name&&(this.name=t.name),this.default.load(t.default),void 0!==t.options&&(this.options=tM({},t.options)))}}class ef{constructor(){this.count=0,this.enable=!1,this.speed=1,this.decay=0,this.delay=0,this.sync=!1}load(t){N(t)||(void 0!==t.count&&(this.count=tn(t.count)),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.speed&&(this.speed=tn(t.speed)),void 0!==t.decay&&(this.decay=tn(t.decay)),void 0!==t.delay&&(this.delay=tn(t.delay)),void 0===t.sync||(this.sync=t.sync))}}class ev extends ef{constructor(){super(),this.mode=a.auto,this.startValue=h.random}load(t){super.load(t),N(t)||(void 0!==t.mode&&(this.mode=t.mode),void 0===t.startValue||(this.startValue=t.startValue))}}class em extends ef{constructor(){super(),this.offset=0,this.sync=!0}load(t){super.load(t),N(t)||void 0===t.offset||(this.offset=tn(t.offset))}}class eg{constructor(){this.h=new em,this.s=new em,this.l=new em}load(t){N(t)||(this.h.load(t.h),this.s.load(t.s),this.l.load(t.l))}}class ey extends t8{constructor(){super(),this.animation=new eg}static create(t,e){let i=new ey;return i.load(t),void 0!==e&&(W(e)||Y(e)?i.load({value:e}):i.load(e)),i}load(t){if(super.load(t),N(t))return;let e=t.animation;void 0!==e&&(void 0!==e.enable?this.animation.h.load(e):this.animation.load(t.animation))}}!function(t){t.absorb="absorb",t.bounce="bounce",t.destroy="destroy"}(m||(m={}));class e_{constructor(){this.speed=2}load(t){N(t)||void 0===t.speed||(this.speed=t.speed)}}class ew{constructor(){this.enable=!0,this.retries=0}load(t){N(t)||(void 0!==t.enable&&(this.enable=t.enable),void 0===t.retries||(this.retries=t.retries))}}class eb{constructor(){this.value=0}load(t){!N(t)&&(N(t.value)||(this.value=tn(t.value)))}}class ex extends eb{constructor(){super(),this.animation=new ef}load(t){if(super.load(t),N(t))return;let e=t.animation;void 0!==e&&this.animation.load(e)}}class ez extends ex{constructor(){super(),this.animation=new ev}load(t){super.load(t)}}class eM extends eb{constructor(){super(),this.value=1}}class ek{constructor(){this.horizontal=new eM,this.vertical=new eM}load(t){N(t)||(this.horizontal.load(t.horizontal),this.vertical.load(t.vertical))}}class eP{constructor(){this.absorb=new e_,this.bounce=new ek,this.enable=!1,this.maxSpeed=50,this.mode=m.bounce,this.overlap=new ew}load(t){N(t)||(this.absorb.load(t.absorb),this.bounce.load(t.bounce),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.maxSpeed&&(this.maxSpeed=tn(t.maxSpeed)),void 0!==t.mode&&(this.mode=t.mode),this.overlap.load(t.overlap))}}class eO{constructor(){this.close=!0,this.fill=!0,this.options={},this.type=[]}load(t){if(N(t))return;let e=t.options;if(void 0!==e)for(let t in e){let i=e[t];i&&(this.options[t]=tM(this.options[t]??{},i))}void 0!==t.close&&(this.close=t.close),void 0!==t.fill&&(this.fill=t.fill),void 0!==t.type&&(this.type=t.type)}}class eC{constructor(){this.offset=0,this.value=90}load(t){N(t)||(void 0!==t.offset&&(this.offset=tn(t.offset)),void 0!==t.value&&(this.value=tn(t.value)))}}class eS{constructor(){this.distance=200,this.enable=!1,this.rotate={x:3e3,y:3e3}}load(t){if(!N(t)&&(void 0!==t.distance&&(this.distance=tn(t.distance)),void 0!==t.enable&&(this.enable=t.enable),t.rotate)){let e=t.rotate.x;void 0!==e&&(this.rotate.x=e);let i=t.rotate.y;void 0!==i&&(this.rotate.y=i)}}}class eT{constructor(){this.x=50,this.y=50,this.mode=l.percent,this.radius=0}load(t){N(t)||(void 0!==t.x&&(this.x=t.x),void 0!==t.y&&(this.y=t.y),void 0!==t.mode&&(this.mode=t.mode),void 0===t.radius||(this.radius=t.radius))}}class eD{constructor(){this.acceleration=9.81,this.enable=!1,this.inverse=!1,this.maxSpeed=50}load(t){N(t)||(void 0!==t.acceleration&&(this.acceleration=tn(t.acceleration)),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.inverse&&(this.inverse=t.inverse),void 0!==t.maxSpeed&&(this.maxSpeed=tn(t.maxSpeed)))}}class eI{constructor(){this.clamp=!0,this.delay=new eb,this.enable=!1,this.options={}}load(t){!N(t)&&(void 0!==t.clamp&&(this.clamp=t.clamp),this.delay.load(t.delay),void 0!==t.enable&&(this.enable=t.enable),this.generator=t.generator,t.options&&(this.options=tM(this.options,t.options)))}}class eR{load(t){N(t)||(void 0!==t.color&&(this.color=t8.create(this.color,t.color)),void 0===t.image||(this.image=t.image))}}class eE{constructor(){this.enable=!1,this.length=10,this.fill=new eR}load(t){N(t)||(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.fill&&this.fill.load(t.fill),void 0===t.length||(this.length=t.length))}}!function(t){t.bounce="bounce",t.none="none",t.out="out",t.destroy="destroy",t.split="split"}(g||(g={}));class eL{constructor(){this.default=g.out}load(t){N(t)||(void 0!==t.default&&(this.default=t.default),this.bottom=t.bottom??t.default,this.left=t.left??t.default,this.right=t.right??t.default,this.top=t.top??t.default)}}class eF{constructor(){this.acceleration=0,this.enable=!1}load(t){!N(t)&&(void 0!==t.acceleration&&(this.acceleration=tn(t.acceleration)),void 0!==t.enable&&(this.enable=t.enable),t.position&&(this.position=tM({},t.position)))}}class eA{constructor(){this.angle=new eC,this.attract=new eS,this.center=new eT,this.decay=0,this.distance={},this.direction=s.none,this.drift=0,this.enable=!1,this.gravity=new eD,this.path=new eI,this.outModes=new eL,this.random=!1,this.size=!1,this.speed=2,this.spin=new eF,this.straight=!1,this.trail=new eE,this.vibrate=!1,this.warp=!1}load(t){if(N(t))return;this.angle.load($(t.angle)?{value:t.angle}:t.angle),this.attract.load(t.attract),this.center.load(t.center),void 0!==t.decay&&(this.decay=tn(t.decay)),void 0!==t.direction&&(this.direction=t.direction),void 0!==t.distance&&(this.distance=$(t.distance)?{horizontal:t.distance,vertical:t.distance}:{...t.distance}),void 0!==t.drift&&(this.drift=tn(t.drift)),void 0!==t.enable&&(this.enable=t.enable),this.gravity.load(t.gravity);let e=t.outModes;void 0!==e&&(G(e)?this.outModes.load(e):this.outModes.load({default:e})),this.path.load(t.path),void 0!==t.random&&(this.random=t.random),void 0!==t.size&&(this.size=t.size),void 0!==t.speed&&(this.speed=tn(t.speed)),this.spin.load(t.spin),void 0!==t.straight&&(this.straight=t.straight),this.trail.load(t.trail),void 0!==t.vibrate&&(this.vibrate=t.vibrate),void 0!==t.warp&&(this.warp=t.warp)}}class eV extends ev{constructor(){super(),this.destroy=o.none,this.speed=2}load(t){super.load(t),N(t)||void 0===t.destroy||(this.destroy=t.destroy)}}class eH extends ez{constructor(){super(),this.animation=new eV,this.value=1}load(t){if(N(t))return;super.load(t);let e=t.animation;void 0!==e&&this.animation.load(e)}}class eB{constructor(){this.enable=!1,this.width=1920,this.height=1080}load(t){if(N(t))return;void 0!==t.enable&&(this.enable=t.enable);let e=t.width;void 0!==e&&(this.width=e);let i=t.height;void 0!==i&&(this.height=i)}}!function(t){t.delete="delete",t.wait="wait"}(y||(y={}));class eq{constructor(){this.mode=y.delete,this.value=0}load(t){N(t)||(void 0!==t.mode&&(this.mode=t.mode),void 0===t.value||(this.value=t.value))}}class eU{constructor(){this.density=new eB,this.limit=new eq,this.value=0}load(t){N(t)||(this.density.load(t.density),this.limit.load(t.limit),void 0===t.value||(this.value=t.value))}}class eW{constructor(){this.blur=0,this.color=new t8,this.enable=!1,this.offset={x:0,y:0},this.color.value="#000"}load(t){!N(t)&&(void 0!==t.blur&&(this.blur=t.blur),this.color=t8.create(this.color,t.color),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.offset&&(void 0!==t.offset.x&&(this.offset.x=t.offset.x),void 0!==t.offset.y&&(this.offset.y=t.offset.y)))}}class e${constructor(){this.close=!0,this.fill=!0,this.options={},this.type="circle"}load(t){if(N(t))return;let e=t.options;if(void 0!==e)for(let t in e){let i=e[t];i&&(this.options[t]=tM(this.options[t]??{},i))}void 0!==t.close&&(this.close=t.close),void 0!==t.fill&&(this.fill=t.fill),void 0!==t.type&&(this.type=t.type)}}class eG extends ev{constructor(){super(),this.destroy=o.none,this.speed=5}load(t){super.load(t),N(t)||void 0===t.destroy||(this.destroy=t.destroy)}}class eY extends ez{constructor(){super(),this.animation=new eG,this.value=3}load(t){if(super.load(t),N(t))return;let e=t.animation;void 0!==e&&this.animation.load(e)}}class eN{constructor(){this.width=0}load(t){N(t)||(void 0!==t.color&&(this.color=ey.create(this.color,t.color)),void 0!==t.width&&(this.width=tn(t.width)),void 0!==t.opacity&&(this.opacity=tn(t.opacity)))}}class eX extends eb{constructor(){super(),this.opacityRate=1,this.sizeRate=1,this.velocityRate=1}load(t){super.load(t),N(t)||(void 0!==t.opacityRate&&(this.opacityRate=t.opacityRate),void 0!==t.sizeRate&&(this.sizeRate=t.sizeRate),void 0===t.velocityRate||(this.velocityRate=t.velocityRate))}}class eQ{constructor(t,e){this._engine=t,this._container=e,this.bounce=new ek,this.collisions=new eP,this.color=new ey,this.color.value="#fff",this.effect=new eO,this.groups={},this.move=new eA,this.number=new eU,this.opacity=new eH,this.reduceDuplicates=!1,this.shadow=new eW,this.shape=new e$,this.size=new eY,this.stroke=new eN,this.zIndex=new eX}load(t){if(N(t))return;if(void 0!==t.groups)for(let e of Object.keys(t.groups)){if(!Object.hasOwn(t.groups,e))continue;let i=t.groups[e];void 0!==i&&(this.groups[e]=tM(this.groups[e]??{},i))}void 0!==t.reduceDuplicates&&(this.reduceDuplicates=t.reduceDuplicates),this.bounce.load(t.bounce),this.color.load(ey.create(this.color,t.color)),this.effect.load(t.effect),this.move.load(t.move),this.number.load(t.number),this.opacity.load(t.opacity),this.shape.load(t.shape),this.size.load(t.size),this.shadow.load(t.shadow),this.zIndex.load(t.zIndex),this.collisions.load(t.collisions),void 0!==t.interactivity&&(this.interactivity=tM({},t.interactivity));let e=t.stroke;if(e&&(this.stroke=tD(e,t=>{let e=new eN;return e.load(t),e})),this._container){let e=this._engine.updaters.get(this._container);if(e)for(let i of e)i.loadOptions&&i.loadOptions(this,t);let i=this._engine.interactors.get(this._container);if(i)for(let e of i)e.loadParticlesOptions&&e.loadParticlesOptions(this,t)}}}function ej(t,...e){for(let i of e)t.load(i)}function eZ(t,e,...i){let s=new eQ(t,e);return ej(s,...i),s}class eJ{constructor(t,e){this._findDefaultTheme=t=>this.themes.find(e=>e.default.value&&e.default.mode===t)??this.themes.find(t=>t.default.value&&t.default.mode===v.any),this._importPreset=t=>{this.load(this._engine.getPreset(t))},this._engine=t,this._container=e,this.autoPlay=!0,this.background=new t9,this.backgroundMask=new et,this.clear=!0,this.defaultThemes={},this.delay=0,this.fullScreen=new ee,this.detectRetina=!0,this.duration=0,this.fpsLimit=120,this.interactivity=new eh(t,e),this.manualParticles=[],this.particles=eZ(this._engine,this._container),this.pauseOnBlur=!0,this.pauseOnOutsideViewport=!0,this.responsive=[],this.smooth=!1,this.style={},this.themes=[],this.zLayers=100}load(t){if(N(t))return;void 0!==t.preset&&tD(t.preset,t=>this._importPreset(t)),void 0!==t.autoPlay&&(this.autoPlay=t.autoPlay),void 0!==t.clear&&(this.clear=t.clear),void 0!==t.key&&(this.key=t.key),void 0!==t.name&&(this.name=t.name),void 0!==t.delay&&(this.delay=tn(t.delay));let e=t.detectRetina;void 0!==e&&(this.detectRetina=e),void 0!==t.duration&&(this.duration=tn(t.duration));let i=t.fpsLimit;void 0!==i&&(this.fpsLimit=i),void 0!==t.pauseOnBlur&&(this.pauseOnBlur=t.pauseOnBlur),void 0!==t.pauseOnOutsideViewport&&(this.pauseOnOutsideViewport=t.pauseOnOutsideViewport),void 0!==t.zLayers&&(this.zLayers=t.zLayers),this.background.load(t.background);let s=t.fullScreen;U(s)?this.fullScreen.enable=s:this.fullScreen.load(s),this.backgroundMask.load(t.backgroundMask),this.interactivity.load(t.interactivity),t.manualParticles&&(this.manualParticles=t.manualParticles.map(t=>{let e=new ec;return e.load(t),e})),this.particles.load(t.particles),this.style=tM(this.style,t.style),this._engine.loadOptions(this,t),void 0!==t.smooth&&(this.smooth=t.smooth);let a=this._engine.interactors.get(this._container);if(a)for(let e of a)e.loadOptions&&e.loadOptions(this,t);if(void 0!==t.responsive)for(let e of t.responsive){let t=new ed;t.load(e),this.responsive.push(t)}if(this.responsive.sort((t,e)=>t.maxWidth-e.maxWidth),void 0!==t.themes)for(let e of t.themes){let t=this.themes.find(t=>t.name===e.name);if(t)t.load(e);else{let t=new ep;t.load(e),this.themes.push(t)}}this.defaultThemes.dark=this._findDefaultTheme(v.dark)?.name,this.defaultThemes.light=this._findDefaultTheme(v.light)?.name}setResponsive(t,e,i){this.load(i);let s=this.responsive.find(i=>i.mode===f.screen&&screen?i.maxWidth>screen.availWidth:i.maxWidth*e>t);return this.load(s?.options),s?.maxWidth}setTheme(t){if(t){let e=this.themes.find(e=>e.name===t);e&&this.load(e.options)}else{let t=tg("(prefers-color-scheme: dark)"),e=t?.matches,i=this._findDefaultTheme(e?v.dark:v.light);i&&this.load(i.options)}}}!function(t){t.external="external",t.particles="particles"}(_||(_={}));class eK{constructor(t,e){this.container=e,this._engine=t,this._interactors=[],this._externalInteractors=[],this._particleInteractors=[]}externalInteract(t){for(let e of this._externalInteractors)e.isEnabled()&&e.interact(t)}handleClickMode(t){for(let e of this._externalInteractors)e.handleClickMode?.(t)}async init(){for(let t of(this._interactors=await this._engine.getInteractors(this.container,!0),this._externalInteractors=[],this._particleInteractors=[],this._interactors)){switch(t.type){case _.external:this._externalInteractors.push(t);break;case _.particles:this._particleInteractors.push(t)}t.init()}}particlesInteract(t,e){for(let i of this._externalInteractors)i.clear(t,e);for(let i of this._particleInteractors)i.isEnabled(t)&&i.interact(t,e)}reset(t){for(let e of this._externalInteractors)e.isEnabled()&&e.reset(t);for(let e of this._particleInteractors)e.isEnabled(t)&&e.reset(t)}}function e0(t){if(!ty(t.outMode,t.checkModes))return;let e=t.radius*F;t.coord>t.maxCoord-e?t.setCb(-t.radius):t.coord<e&&t.setCb(t.radius)}!function(t){t.normal="normal",t.inside="inside",t.outside="outside"}(w||(w={}));class e1{constructor(t,e){this.container=e,this._calcPosition=(t,e,i,s=q)=>{for(let s of t.plugins.values()){let t=void 0!==s.particlePosition?s.particlePosition(e,this):void 0;if(t)return X.create(t.x,t.y,i)}let a=function(t){return{x:t.position?.x??J()*t.size.width,y:t.position?.y??J()*t.size.height}}({size:t.canvas.size,position:e}),n=X.create(a.x,a.y,i),o=this.getRadius(),r=this.options.move.outModes,l=e=>{e0({outMode:e,checkModes:[g.bounce],coord:n.x,maxCoord:t.canvas.size.width,setCb:t=>n.x+=t,radius:o})},h=e=>{e0({outMode:e,checkModes:[g.bounce],coord:n.y,maxCoord:t.canvas.size.height,setCb:t=>n.y+=t,radius:o})};return(l(r.left??r.default),l(r.right??r.default),h(r.top??r.default),h(r.bottom??r.default),this._checkOverlap(n,s))?this._calcPosition(t,void 0,i,s+1):n},this._calculateVelocity=()=>{let t=(function(t){let e=Q.origin;return e.length=1,e.angle=t,e})(this.direction).copy(),e=this.options.move;if(e.direction===s.inside||e.direction===s.outside)return t;let i=tl(ti(e.angle.value)),a=tl(ti(e.angle.offset)),n={left:a-i*D,right:a+i*D};return e.straight||(t.angle+=te(tn(n.left,n.right))),e.random&&"number"==typeof e.speed&&(t.length*=J()),t},this._checkOverlap=(t,e=q)=>{let i=this.options.collisions,s=this.getRadius();if(!i.enable)return!1;let a=i.overlap;if(a.enable)return!1;let n=a.retries;if(n>=0&&e>n)throw Error(`${S} particle is overlapping and can't be placed`);return!!this.container.particles.find(e=>tr(t,e.position)<s+e.getRadius())},this._getRollColor=t=>{if(!t||!this.roll||!this.backColor&&!this.roll.alter)return t;let e=this.roll.horizontal&&this.roll.vertical?1*F:1,i=this.roll.horizontal?Math.PI*D:0;return Math.floor(((this.roll.angle??0)+i)/(Math.PI/e))%F?this.backColor?this.backColor:this.roll.alter?function(t,e,i){return{h:t.h,s:t.s,l:t.l+(e===c.darken?-1:1)*i}}(t,this.roll.alter.type,this.roll.alter.value):t:t},this._initPosition=t=>{let e=this.container,i=ti(this.options.zIndex.value);this.position=this._calcPosition(e,t,K(i,0,e.zLayers)),this.initialPosition=this.position.copy();let a=e.canvas.size;switch(this.moveCenter={...tL(this.options.move.center,a),radius:this.options.move.center.radius??0,mode:this.options.move.center.mode??l.percent},this.direction=function(t,e,i){if($(t))return tl(t);switch(t){case s.top:return-Math.PI*D;case s.topRight:return-(.25*Math.PI);case s.right:return 0;case s.bottomRight:return .25*Math.PI;case s.bottom:return Math.PI*D;case s.bottomLeft:return .75*Math.PI;case s.left:return Math.PI;case s.topLeft:return-(.75*Math.PI);case s.inside:return Math.atan2(i.y-e.y,i.x-e.x);case s.outside:return Math.atan2(e.y-i.y,e.x-i.x);default:return J()*A}}(this.options.move.direction,this.position,this.moveCenter),this.options.move.direction){case s.inside:this.outType=w.inside;break;case s.outside:this.outType=w.outside}this.offset=Q.origin},this._engine=t}destroy(t){if(this.unbreakable||this.destroyed)return;this.destroyed=!0,this.bubble.inRange=!1,this.slow.inRange=!1;let e=this.container,i=this.pathGenerator,s=e.shapeDrawers.get(this.shape);for(let i of(s?.particleDestroy?.(this),e.plugins.values()))i.particleDestroyed?.(this,t);for(let i of e.particles.updaters)i.particleDestroyed?.(this,t);i?.reset(this),this._engine.dispatchEvent(u.particleDestroyed,{container:this.container,data:{particle:this}})}draw(t){let e=this.container,i=e.canvas;for(let s of e.plugins.values())i.drawParticlePlugin(s,this,t);i.drawParticle(this,t)}getFillColor(){return this._getRollColor(this.bubble.color??tj(this.color))}getMass(){return this.getRadius()**2*Math.PI*D}getPosition(){return{x:this.position.x+this.offset.x,y:this.position.y+this.offset.y,z:this.position.z}}getRadius(){return this.bubble.radius??this.size.value}getStrokeColor(){return this._getRollColor(this.bubble.color??tj(this.strokeColor))}init(t,e,i,s){let a=this.container,n=this._engine;this.id=t,this.group=s,this.effectClose=!0,this.effectFill=!0,this.shapeClose=!0,this.shapeFill=!0,this.pathRotation=!1,this.lastPathTime=0,this.destroyed=!1,this.unbreakable=!1,this.isRotating=!1,this.rotation=0,this.misplaced=!1,this.retina={maxDistance:{}},this.outType=w.normal,this.ignoresResizeRatio=!0;let o=a.retina.pixelRatio,r=a.actualOptions,l=eZ(this._engine,a,r.particles),{reduceDuplicates:h}=l,c=l.effect.type,d=l.shape.type;this.effect=tI(c,this.id,h),this.shape=tI(d,this.id,h);let u=l.effect,p=l.shape;if(i){if(i.effect?.type){let t=tI(i.effect.type,this.id,h);t&&(this.effect=t,u.load(i.effect))}if(i.shape?.type){let t=tI(i.shape.type,this.id,h);t&&(this.shape=t,p.load(i.shape))}}if(this.effect===L){let t=[...this.container.effectDrawers.keys()];this.effect=t[Math.floor(Math.random()*t.length)]}if(this.shape===L){let t=[...this.container.shapeDrawers.keys()];this.shape=t[Math.floor(Math.random()*t.length)]}this.effectData=function(t,e,i,s){let a=e.options[t];if(a)return tM({close:e.close,fill:e.fill},tI(a,i,s))}(this.effect,u,this.id,h),this.shapeData=function(t,e,i,s){let a=e.options[t];if(a)return tM({close:e.close,fill:e.fill},tI(a,i,s))}(this.shape,p,this.id,h),l.load(i);let f=this.effectData;f&&l.load(f.particles);let v=this.shapeData;v&&l.load(v.particles);let m=new eh(n,a);m.load(a.actualOptions.interactivity),m.load(l.interactivity),this.interactivity=m,this.effectFill=f?.fill??l.effect.fill,this.effectClose=f?.close??l.effect.close,this.shapeFill=v?.fill??l.shape.fill,this.shapeClose=v?.close??l.shape.close,this.options=l;let g=this.options.move.path;this.pathDelay=ti(g.delay.value)*I,g.generator&&(this.pathGenerator=this._engine.getPathGenerator(g.generator),this.pathGenerator&&a.addPath(g.generator,this.pathGenerator)&&this.pathGenerator.init(a)),a.retina.initParticle(this),this.size=tE(this.options.size,o),this.bubble={inRange:!1},this.slow={inRange:!1,factor:1},this._initPosition(e),this.initialVelocity=this._calculateVelocity(),this.velocity=this.initialVelocity.copy(),this.moveDecay=1-ti(this.options.move.decay);let y=a.particles;y.setLastZIndex(this.position.z),this.zIndexFactor=this.position.z/a.zLayers,this.sides=24;let _=a.effectDrawers.get(this.effect);!_&&(_=this._engine.getEffectDrawer(this.effect))&&a.effectDrawers.set(this.effect,_),_?.loadEffect&&_.loadEffect(this);let b=a.shapeDrawers.get(this.shape);!b&&(b=this._engine.getShapeDrawer(this.shape))&&a.shapeDrawers.set(this.shape,b),b?.loadShape&&b.loadShape(this);let x=b?.getSidesCount;for(let t of(x&&(this.sides=x(this)),this.spawning=!1,this.shadowColor=tH(this._engine,this.options.shadow.color),y.updaters))t.init(this);for(let t of y.movers)t.init?.(this);for(let t of(_?.particleInit?.(a,this),b?.particleInit?.(a,this),a.plugins.values()))t.particleCreated?.(this)}isInsideCanvas(){let t=this.getRadius(),e=this.container.canvas.size,i=this.position;return i.x>=-t&&i.y>=-t&&i.y<=e.height+t&&i.x<=e.width+t}isVisible(){return!this.destroyed&&!this.spawning&&this.isInsideCanvas()}reset(){for(let t of this.container.particles.updaters)t.reset?.(this)}}class e3{constructor(t,e){this.position=t,this.particle=e}}!function(t){t.circle="circle",t.rectangle="rectangle"}(b||(b={}));class e2{constructor(t,e,i){this.position={x:t,y:e},this.type=i}}class e5 extends e2{constructor(t,e,i){super(t,e,b.circle),this.radius=i}contains(t){return tr(t,this.position)<=this.radius}intersects(t){let e=this.position,i=t.position,s={x:Math.abs(i.x-e.x),y:Math.abs(i.y-e.y)},a=this.radius;if(t instanceof e5||t.type===b.circle)return a+t.radius>Math.sqrt(s.x**2+s.y**2);if(t instanceof e6||t.type===b.rectangle){let{width:e,height:i}=t.size;return Math.pow(s.x-e,2)+Math.pow(s.y-i,2)<=a**2||s.x<=a+e&&s.y<=a+i||s.x<=e||s.y<=i}return!1}}class e6 extends e2{constructor(t,e,i,s){super(t,e,b.rectangle),this.size={height:s,width:i}}contains(t){let e=this.size.width,i=this.size.height,s=this.position;return t.x>=s.x&&t.x<=s.x+e&&t.y>=s.y&&t.y<=s.y+i}intersects(t){if(t instanceof e5)return t.intersects(this);let e=this.size.width,i=this.size.height,s=this.position,a=t.position,n=t instanceof e6?t.size:{width:0,height:0},o=n.width,r=n.height;return a.x<s.x+e&&a.x+o>s.x&&a.y<s.y+i&&a.y+r>s.y}}class e4{constructor(t,e){this.rectangle=t,this.capacity=e,this._subdivide=()=>{let{x:t,y:e}=this.rectangle.position,{width:i,height:s}=this.rectangle.size,{capacity:a}=this;for(let n=0;n<4;n++){let o=n%F;this._subs.push(new e4(new e6(t+i*D*o,e+s*D*(Math.round(n*D)-o),i*D,s*D),a))}this._divided=!0},this._points=[],this._divided=!1,this._subs=[]}insert(t){return!!this.rectangle.contains(t.position)&&(this._points.length<this.capacity?(this._points.push(t),!0):(this._divided||this._subdivide(),this._subs.some(e=>e.insert(t))))}query(t,e){let i=[];if(!t.intersects(this.rectangle))return[];for(let s of this._points)!t.contains(s.position)&&tr(t.position,s.position)>s.particle.getRadius()&&(!e||e(s.particle))||i.push(s.particle);if(this._divided)for(let s of this._subs)i.push(...s.query(t,e));return i}queryCircle(t,e,i){return this.query(new e5(t.x,t.y,e),i)}queryRectangle(t,e,i){return this.query(new e6(t.x,t.y,e.width,e.height),i)}}let e8=t=>{let{height:e,width:i}=t;return new e6(-.25*i,-.25*e,1.5*i,1.5*e)};class e9{constructor(t,e){this._addToPool=(...t)=>{this._pool.push(...t)},this._applyDensity=(t,e,i)=>{let s=t.number;if(!t.number.density?.enable){void 0===i?this._limit=s.limit.value:s.limit&&this._groupLimits.set(i,s.limit.value);return}let a=this._initDensityFactor(s.density),n=s.value,o=s.limit.value>0?s.limit.value:n,r=Math.min(n,o)*a+e,l=Math.min(this.count,this.filter(t=>t.group===i).length);void 0===i?this._limit=s.limit.value*a:this._groupLimits.set(i,s.limit.value*a),l<r?this.push(Math.abs(r-l),void 0,t,i):l>r&&this.removeQuantity(l-r,i)},this._initDensityFactor=t=>{let e=this._container;if(!e.canvas.element||!t.enable)return 1;let i=e.canvas.element,s=e.retina.pixelRatio;return i.width*i.height/(t.height*t.width*s**2)},this._pushParticle=(t,e,i,s)=>{try{let a=this._pool.pop();a||(a=new e1(this._engine,this._container)),a.init(this._nextId,t,e,i);let n=!0;if(s&&(n=s(a)),!n)return;return this._array.push(a),this._zArray.push(a),this._nextId++,this._engine.dispatchEvent(u.particleAdded,{container:this._container,data:{particle:a}}),a}catch(t){tp.warning(`${S} adding particle: ${t}`)}},this._removeParticle=(t,e,i)=>{let s=this._array[t];if(!s||s.group!==e)return!1;let a=this._zArray.indexOf(s);return this._array.splice(t,1),this._zArray.splice(a,1),s.destroy(i),this._engine.dispatchEvent(u.particleRemoved,{container:this._container,data:{particle:s}}),this._addToPool(s),!0},this._engine=t,this._container=e,this._nextId=0,this._array=[],this._zArray=[],this._pool=[],this._limit=0,this._groupLimits=new Map,this._needsSort=!1,this._lastZIndex=0,this._interactionManager=new eK(t,e),this._pluginsInitialized=!1;let i=e.canvas.size;this.quadTree=new e4(e8(i),4),this.movers=[],this.updaters=[]}get count(){return this._array.length}addManualParticles(){let t=this._container;t.actualOptions.manualParticles.forEach(e=>this.addParticle(e.position?tL(e.position,t.canvas.size):void 0,e.options))}addParticle(t,e,i,s){let a=this._container.actualOptions.particles.number.limit.mode,n=void 0===i?this._limit:this._groupLimits.get(i)??this._limit,o=this.count;if(n>0)switch(a){case y.delete:{let t=o+1-n;t>0&&this.removeQuantity(t);break}case y.wait:if(o>=n)return}return this._pushParticle(t,e,i,s)}clear(){this._array=[],this._zArray=[],this._pluginsInitialized=!1}destroy(){this._array=[],this._zArray=[],this.movers=[],this.updaters=[]}draw(t){let e=this._container,i=e.canvas;for(let s of(i.clear(),this.update(t),e.plugins.values()))i.drawPlugin(s,t);for(let e of this._zArray)e.draw(t)}filter(t){return this._array.filter(t)}find(t){return this._array.find(t)}get(t){return this._array[t]}handleClickMode(t){this._interactionManager.handleClickMode(t)}async init(){let t=this._container,e=t.actualOptions;this._lastZIndex=0,this._needsSort=!1,await this.initPlugins();let i=!1;for(let e of t.plugins.values())if(i=e.particlesInitialization?.()??i)break;if(this.addManualParticles(),!i){let t=e.particles,i=t.groups;for(let e in i){let s=i[e];for(let i=this.count,a=0;a<s.number?.value&&i<t.number.value;i++,a++)this.addParticle(void 0,s,e)}for(let e=this.count;e<t.number.value;e++)this.addParticle()}}async initPlugins(){if(this._pluginsInitialized)return;let t=this._container;for(let e of(this.movers=await this._engine.getMovers(t,!0),this.updaters=await this._engine.getUpdaters(t,!0),await this._interactionManager.init(),t.pathGenerators.values()))e.init(t)}push(t,e,i,s){for(let a=0;a<t;a++)this.addParticle(e?.position,i,s)}async redraw(){this.clear(),await this.init(),this.draw({value:0,factor:0})}remove(t,e,i){this.removeAt(this._array.indexOf(t),void 0,e,i)}removeAt(t,e=1,i,s){if(t<0||t>this.count)return;let a=0;for(let n=t;a<e&&n<this.count;n++)this._removeParticle(n,i,s)&&(n--,a++)}removeQuantity(t,e){this.removeAt(0,t,e)}setDensity(){let t=this._container.actualOptions,e=t.particles.groups;for(let t in e)this._applyDensity(e[t],0,t);this._applyDensity(t.particles,t.manualParticles.length)}setLastZIndex(t){this._lastZIndex=t,this._needsSort=this._needsSort||this._lastZIndex<t}setResizeFactor(t){this._resizeFactor=t}update(t){let e=this._container,i=new Set;for(let t of(this.quadTree=new e4(e8(e.canvas.size),4),e.pathGenerators.values()))t.update();for(let i of e.plugins.values())i.update?.(t);let s=this._resizeFactor;for(let e of this._array){for(let i of(s&&!e.ignoresResizeRatio&&(e.position.x*=s.width,e.position.y*=s.height,e.initialPosition.x*=s.width,e.initialPosition.y*=s.height),e.ignoresResizeRatio=!1,this._interactionManager.reset(e),this._container.plugins.values())){if(e.destroyed)break;i.particleUpdate?.(e,t)}for(let i of this.movers)i.isEnabled(e)&&i.move(e,t);if(e.destroyed){i.add(e);continue}this.quadTree.insert(new e3(e.getPosition(),e))}if(i.size){let t=t=>!i.has(t);for(let e of(this._array=this.filter(t),this._zArray=this._zArray.filter(t),i))this._engine.dispatchEvent(u.particleRemoved,{container:this._container,data:{particle:e}});this._addToPool(...i)}for(let e of(this._interactionManager.externalInteract(t),this._array)){for(let i of this.updaters)i.update(e,t);e.destroyed||e.spawning||this._interactionManager.particlesInteract(e,t)}if(delete this._resizeFactor,this._needsSort){let t=this._zArray;t.sort((t,e)=>e.position.z-t.position.z||t.id-e.id),this._lastZIndex=t[t.length-1].position.z,this._needsSort=!1}}}class e7{constructor(t){this.container=t,this.pixelRatio=1,this.reduceFactor=1}init(){let t=this.container,e=t.actualOptions;this.pixelRatio=!e.detectRetina||tm()?1:window.devicePixelRatio,this.reduceFactor=1;let i=this.pixelRatio,s=t.canvas;if(s.element){let t=s.element;s.size.width=t.offsetWidth*i,s.size.height=t.offsetHeight*i}let a=e.particles,n=a.move;this.maxSpeed=ti(n.gravity.maxSpeed)*i,this.sizeAnimationSpeed=ti(a.size.animation.speed)*i}initParticle(t){let e=t.options,i=this.pixelRatio,s=e.move,a=s.distance,n=t.retina;n.moveDrift=ti(s.drift)*i,n.moveSpeed=ti(s.speed)*i,n.sizeAnimationSpeed=ti(e.size.animation.speed)*i;let o=n.maxDistance;o.horizontal=void 0!==a.horizontal?a.horizontal*i:void 0,o.vertical=void 0!==a.vertical?a.vertical*i:void 0,n.maxSpeed=ti(s.gravity.maxSpeed)*i}}function it(t){return t&&!t.destroyed}function ie(t,e,...i){let s=new eJ(t,e);return ej(s,...i),s}class ii{constructor(t,e,i){this._intersectionManager=t=>{if(it(this)&&this.actualOptions.pauseOnOutsideViewport)for(let e of t)e.target===this.interactivity.element&&(e.isIntersecting?this.play():this.pause())},this._nextFrame=t=>{try{if(!this._smooth&&void 0!==this._lastFrameTime&&t<this._lastFrameTime+I/this.fpsLimit){this.draw(!1);return}this._lastFrameTime??=t;let e=function(t,e=60,i=!1){return{value:t,factor:i?60/e:60*t/I}}(t-this._lastFrameTime,this.fpsLimit,this._smooth);if(this.addLifeTime(e.value),this._lastFrameTime=t,e.value>I){this.draw(!1);return}if(this.particles.draw(e),!this.alive()){this.destroy();return}this.animationStatus&&this.draw(!1)}catch(t){tp.error(`${S} in animation loop`,t)}},this._engine=t,this.id=Symbol(e),this.fpsLimit=120,this._smooth=!1,this._delay=0,this._duration=0,this._lifeTime=0,this._firstStart=!0,this.started=!1,this.destroyed=!1,this._paused=!0,this._lastFrameTime=0,this.zLayers=100,this.pageHidden=!1,this._clickHandlers=new Map,this._sourceOptions=i,this._initialSourceOptions=i,this.retina=new e7(this),this.canvas=new t5(this,this._engine),this.particles=new e9(this._engine,this),this.pathGenerators=new Map,this.interactivity={mouse:{clicking:!1,inside:!1}},this.plugins=new Map,this.effectDrawers=new Map,this.shapeDrawers=new Map,this._options=ie(this._engine,this),this.actualOptions=ie(this._engine,this),this._eventListeners=new t4(this),this._intersectionObserver=function(t){if(!tm()&&"undefined"!=typeof IntersectionObserver)return new IntersectionObserver(t)}(t=>this._intersectionManager(t)),this._engine.dispatchEvent(u.containerBuilt,{container:this})}get animationStatus(){return!this._paused&&!this.pageHidden&&it(this)}get options(){return this._options}get sourceOptions(){return this._sourceOptions}addClickHandler(t){if(!it(this))return;let e=this.interactivity.element;if(!e)return;let i=(e,i,s)=>{if(!it(this))return;let a=this.retina.pixelRatio,n={x:i.x*a,y:i.y*a};t(e,this.particles.quadTree.queryCircle(n,s*a))},s=!1,a=!1;for(let[t,n]of(this._clickHandlers.set("click",t=>{if(!it(this))return;let e={x:t.offsetX||t.clientX,y:t.offsetY||t.clientY};i(t,e,1)}),this._clickHandlers.set("touchstart",()=>{it(this)&&(s=!0,a=!1)}),this._clickHandlers.set("touchmove",()=>{it(this)&&(a=!0)}),this._clickHandlers.set("touchend",t=>{if(it(this)){if(s&&!a){let e=t.touches[t.touches.length-1];if(!e&&!(e=t.changedTouches[t.changedTouches.length-1]))return;let s=this.canvas.element,a=s?s.getBoundingClientRect():void 0;i(t,{x:e.clientX-(a?a.left:0),y:e.clientY-(a?a.top:0)},Math.max(e.radiusX,e.radiusY))}s=!1,a=!1}}),this._clickHandlers.set("touchcancel",()=>{it(this)&&(s=!1,a=!1)}),this._clickHandlers))e.addEventListener(t,n)}addLifeTime(t){this._lifeTime+=t}addPath(t,e,i=!1){return!(!it(this)||!i&&this.pathGenerators.has(t))&&(this.pathGenerators.set(t,e),!0)}alive(){return!this._duration||this._lifeTime<=this._duration}clearClickHandlers(){if(it(this)){for(let[t,e]of this._clickHandlers)this.interactivity.element?.removeEventListener(t,e);this._clickHandlers.clear()}}destroy(t=!0){if(it(this)){for(let t of(this.stop(),this.clearClickHandlers(),this.particles.destroy(),this.canvas.destroy(),this.effectDrawers.values()))t.destroy?.(this);for(let t of this.shapeDrawers.values())t.destroy?.(this);for(let t of this.effectDrawers.keys())this.effectDrawers.delete(t);for(let t of this.shapeDrawers.keys())this.shapeDrawers.delete(t);if(this._engine.clearPlugins(this),this.destroyed=!0,t){let t=this._engine.items,e=t.findIndex(t=>t===this);e>=0&&t.splice(e,1)}this._engine.dispatchEvent(u.containerDestroyed,{container:this})}}draw(t){var e;if(!it(this))return;let i=t,s=t=>{i&&(this._lastFrameTime=void 0,i=!1),this._nextFrame(t)};this._drawAnimationFrame=(e=t=>s(t),Z.nextFrame(e))}async export(t,e={}){for(let i of this.plugins.values()){if(!i.export)continue;let s=await i.export(t,e);if(s.supported)return s.blob}tp.error(`${S} - Export plugin with type ${t} not found`)}handleClickMode(t){if(it(this))for(let e of(this.particles.handleClickMode(t),this.plugins.values()))e.handleClickMode?.(t)}async init(){if(!it(this))return;for(let t of this._engine.getSupportedEffects()){let e=this._engine.getEffectDrawer(t);e&&this.effectDrawers.set(t,e)}for(let t of this._engine.getSupportedShapes()){let e=this._engine.getShapeDrawer(t);e&&this.shapeDrawers.set(t,e)}for(let[t,e]of(await this.particles.initPlugins(),this._options=ie(this._engine,this,this._initialSourceOptions,this.sourceOptions),this.actualOptions=ie(this._engine,this,this._options),await this._engine.getAvailablePlugins(this)))this.plugins.set(t,e);this.retina.init(),await this.canvas.init(),this.updateActualOptions(),this.canvas.initBackground(),this.canvas.resize();let{zLayers:t,duration:e,delay:i,fpsLimit:s,smooth:a}=this.actualOptions;for(let n of(this.zLayers=t,this._duration=ti(e)*I,this._delay=ti(i)*I,this._lifeTime=0,this.fpsLimit=s>0?s:120,this._smooth=a,this.effectDrawers.values()))await n.init?.(this);for(let t of this.shapeDrawers.values())await t.init?.(this);for(let t of this.plugins.values())await t.init?.();for(let t of(this._engine.dispatchEvent(u.containerInit,{container:this}),await this.particles.init(),this.particles.setDensity(),this.plugins.values()))t.particlesSetup?.();this._engine.dispatchEvent(u.particlesSetup,{container:this})}async loadTheme(t){it(this)&&(this._currentTheme=t,await this.refresh())}pause(){if(it(this)){if(void 0!==this._drawAnimationFrame){var t;t=this._drawAnimationFrame,Z.cancel(t),delete this._drawAnimationFrame}if(!this._paused){for(let t of this.plugins.values())t.pause?.();this.pageHidden||(this._paused=!0),this._engine.dispatchEvent(u.containerPaused,{container:this})}}}play(t){if(!it(this))return;let e=this._paused||t;if(this._firstStart&&!this.actualOptions.autoPlay){this._firstStart=!1;return}if(this._paused&&(this._paused=!1),e)for(let t of this.plugins.values())t.play&&t.play();this._engine.dispatchEvent(u.containerPlay,{container:this}),this.draw(e??!1)}async refresh(){if(it(this))return this.stop(),this.start()}async reset(t){if(it(this))return this._initialSourceOptions=t,this._sourceOptions=t,this._options=ie(this._engine,this,this._initialSourceOptions,this.sourceOptions),this.actualOptions=ie(this._engine,this,this._options),this.refresh()}async start(){it(this)&&!this.started&&(await this.init(),this.started=!0,await new Promise(t=>{let e=async()=>{for(let t of(this._eventListeners.addListeners(),this.interactivity.element instanceof HTMLElement&&this._intersectionObserver&&this._intersectionObserver.observe(this.interactivity.element),this.plugins.values()))await t.start?.();this._engine.dispatchEvent(u.containerStarted,{container:this}),this.play(),t()};this._delayTimeout=setTimeout(()=>void e(),this._delay)}))}stop(){if(it(this)&&this.started){for(let t of(this._delayTimeout&&(clearTimeout(this._delayTimeout),delete this._delayTimeout),this._firstStart=!0,this.started=!1,this._eventListeners.removeListeners(),this.pause(),this.particles.clear(),this.canvas.stop(),this.interactivity.element instanceof HTMLElement&&this._intersectionObserver&&this._intersectionObserver.unobserve(this.interactivity.element),this.plugins.values()))t.stop?.();for(let t of this.plugins.keys())this.plugins.delete(t);this._sourceOptions=this._options,this._engine.dispatchEvent(u.containerStopped,{container:this})}}updateActualOptions(){this.actualOptions.responsive=[];let t=this.actualOptions.setResponsive(this.canvas.size.width,this.retina.pixelRatio,this._options);return this.actualOptions.setTheme(this._currentTheme),this._responsiveMaxWidth!==t&&(this._responsiveMaxWidth=t,!0)}}class is{constructor(){this._listeners=new Map}addEventListener(t,e){this.removeEventListener(t,e);let i=this._listeners.get(t);i||(i=[],this._listeners.set(t,i)),i.push(e)}dispatchEvent(t,e){let i=this._listeners.get(t);i?.forEach(t=>t(e))}hasEventListener(t){return!!this._listeners.get(t)}removeAllEventListeners(t){t?this._listeners.delete(t):this._listeners=new Map}removeEventListener(t,e){let i=this._listeners.get(t);if(!i)return;let s=i.length,a=i.indexOf(e);a<0||(1===s?this._listeners.delete(t):i.splice(a,1))}}async function ia(t,e,i,s=!1){let a=e.get(t);return(!a||s)&&(a=await Promise.all([...i.values()].map(e=>e(t))),e.set(t,a)),a}async function io(t){let e=tI(t.url,t.index);if(!e)return t.fallback;let i=await fetch(e);return i.ok?await i.json():(tp.error(`${S} ${i.status} while retrieving config file`),t.fallback)}let ir=t=>{let e;if(t instanceof HTMLCanvasElement||t.tagName.toLowerCase()===B)(e=t).dataset[k]||(e.dataset[k]=H);else{let i=t.getElementsByTagName(B);i.length?(e=i[0]).dataset[k]=H:((e=document.createElement(B)).dataset[k]=V,t.appendChild(e))}let i="100%";return e.style.width||(e.style.width=i),e.style.height||(e.style.height=i),e},il=(t,e)=>{let i=e??document.getElementById(t);return i||((i=document.createElement("div")).id=t,i.dataset[k]=V,document.body.append(i)),i};class ih{constructor(){this._configs=new Map,this._domArray=[],this._eventDispatcher=new is,this._initialized=!1,this.plugins=[],this.colorManagers=new Map,this.easingFunctions=new Map,this._initializers={interactors:new Map,movers:new Map,updaters:new Map},this.interactors=new Map,this.movers=new Map,this.updaters=new Map,this.presets=new Map,this.effectDrawers=new Map,this.shapeDrawers=new Map,this.pathGenerators=new Map}get configs(){let t={};for(let[e,i]of this._configs)t[e]=i;return t}get items(){return this._domArray}get version(){return"3.8.1"}async addColorManager(t,e=!0){this.colorManagers.set(t.key,t),await this.refresh(e)}addConfig(t){let e=t.key??t.name??"default";this._configs.set(e,t),this._eventDispatcher.dispatchEvent(u.configAdded,{data:{name:e,config:t}})}async addEasing(t,e,i=!0){this.getEasing(t)||(this.easingFunctions.set(t,e),await this.refresh(i))}async addEffect(t,e,i=!0){tD(t,t=>{this.getEffectDrawer(t)||this.effectDrawers.set(t,e)}),await this.refresh(i)}addEventListener(t,e){this._eventDispatcher.addEventListener(t,e)}async addInteractor(t,e,i=!0){this._initializers.interactors.set(t,e),await this.refresh(i)}async addMover(t,e,i=!0){this._initializers.movers.set(t,e),await this.refresh(i)}async addParticleUpdater(t,e,i=!0){this._initializers.updaters.set(t,e),await this.refresh(i)}async addPathGenerator(t,e,i=!0){this.getPathGenerator(t)||this.pathGenerators.set(t,e),await this.refresh(i)}async addPlugin(t,e=!0){this.getPlugin(t.id)||this.plugins.push(t),await this.refresh(e)}async addPreset(t,e,i=!1,s=!0){(i||!this.getPreset(t))&&this.presets.set(t,e),await this.refresh(s)}async addShape(t,e=!0){for(let e of t.validTypes)this.getShapeDrawer(e)||this.shapeDrawers.set(e,t);await this.refresh(e)}checkVersion(t){if(this.version!==t)throw Error(`The tsParticles version is different from the loaded plugins version. Engine version: ${this.version}. Plugin version: ${t}`)}clearPlugins(t){this.updaters.delete(t),this.movers.delete(t),this.interactors.delete(t)}dispatchEvent(t,e){this._eventDispatcher.dispatchEvent(t,e)}dom(){return this.items}domItem(t){return this.item(t)}async getAvailablePlugins(t){let e=new Map;for(let i of this.plugins)i.needsPlugin(t.actualOptions)&&e.set(i.id,await i.getPlugin(t));return e}getEasing(t){return this.easingFunctions.get(t)??(t=>t)}getEffectDrawer(t){return this.effectDrawers.get(t)}async getInteractors(t,e=!1){return ia(t,this.interactors,this._initializers.interactors,e)}async getMovers(t,e=!1){return ia(t,this.movers,this._initializers.movers,e)}getPathGenerator(t){return this.pathGenerators.get(t)}getPlugin(t){return this.plugins.find(e=>e.id===t)}getPreset(t){return this.presets.get(t)}getShapeDrawer(t){return this.shapeDrawers.get(t)}getSupportedEffects(){return this.effectDrawers.keys()}getSupportedShapes(){return this.shapeDrawers.keys()}async getUpdaters(t,e=!1){return ia(t,this.updaters,this._initializers.updaters,e)}init(){this._initialized||(this._initialized=!0)}item(t){let{items:e}=this,i=e[t];if(!i||i.destroyed){e.splice(t,1);return}return i}async load(t){let e=t.id??t.element?.id??`tsparticles${Math.floor(1e4*J())}`,{index:i,url:s}=t,a=tI(s?await io({fallback:t.options,url:s,index:i}):t.options,i),{items:n}=this,o=n.findIndex(t=>t.id.description===e),r=new ii(this,e,a);if(o>=0){let t=this.item(o),e=t?1:0;t&&!t.destroyed&&t.destroy(!1),n.splice(o,e,r)}else n.push(r);let l=ir(il(e,t.element));return r.canvas.loadCanvas(l),await r.start(),r}loadOptions(t,e){this.plugins.forEach(i=>i.loadOptions?.(t,e))}loadParticlesOptions(t,e,...i){let s=this.updaters.get(t);s&&s.forEach(t=>t.loadOptions?.(e,...i))}async refresh(t=!0){t&&await Promise.all(this.items.map(t=>t.refresh()))}removeEventListener(t,e){this._eventDispatcher.removeEventListener(t,e)}setOnClickHandler(t){let{items:e}=this;if(!e.length)throw Error(`${S} can only set click handlers after calling tsParticles.load()`);e.forEach(e=>e.addClickHandler(t))}}class ic{constructor(t){this.type=_.external,this.container=t}}class id{constructor(t){this.type=_.particles,this.container=t}}!function(t){t.clockwise="clockwise",t.counterClockwise="counter-clockwise",t.random="random"}(x||(x={})),function(t){t.linear="linear",t.radial="radial",t.random="random"}(z||(z={})),function(t){t.easeInBack="ease-in-back",t.easeInCirc="ease-in-circ",t.easeInCubic="ease-in-cubic",t.easeInLinear="ease-in-linear",t.easeInQuad="ease-in-quad",t.easeInQuart="ease-in-quart",t.easeInQuint="ease-in-quint",t.easeInExpo="ease-in-expo",t.easeInSine="ease-in-sine",t.easeOutBack="ease-out-back",t.easeOutCirc="ease-out-circ",t.easeOutCubic="ease-out-cubic",t.easeOutLinear="ease-out-linear",t.easeOutQuad="ease-out-quad",t.easeOutQuart="ease-out-quart",t.easeOutQuint="ease-out-quint",t.easeOutExpo="ease-out-expo",t.easeOutSine="ease-out-sine",t.easeInOutBack="ease-in-out-back",t.easeInOutCirc="ease-in-out-circ",t.easeInOutCubic="ease-in-out-cubic",t.easeInOutLinear="ease-in-out-linear",t.easeInOutQuad="ease-in-out-quad",t.easeInOutQuart="ease-in-out-quart",t.easeInOutQuint="ease-in-out-quint",t.easeInOutExpo="ease-in-out-expo",t.easeInOutSine="ease-in-out-sine"}(M||(M={}));let iu=function(){let t=new ih;return t.init(),t}();tm()||(window.tsParticles=iu)}}]);