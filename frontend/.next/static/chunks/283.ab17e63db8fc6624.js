"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[283],{2283:(t,e,i)=>{i.d(e,{loadFull:()=>iF});var o,s,a,n,r,l,c,d,h,u,p,f,y=i(2665);class v{constructor(){this.radius=0,this.mass=0}load(t){(0,y.kZJ)(t)||(void 0!==t.mass&&(this.mass=t.mass),void 0===t.radius||(this.radius=t.radius))}}class g extends y.PVU{constructor(){super(),this.density=5,this.value=50,this.limit=new v}load(t){(0,y.kZJ)(t)||(super.load(t),void 0!==t.density&&(this.density=t.density),(0,y.EtT)(t.limit)?this.limit.radius=t.limit:this.limit.load(t.limit))}}class b{constructor(){this.color=new y.Oit,this.color.value="#000000",this.draggable=!1,this.opacity=1,this.destroy=!0,this.orbits=!1,this.size=new g}load(t){(0,y.kZJ)(t)||(void 0!==t.color&&(this.color=y.Oit.create(this.color,t.color)),void 0!==t.draggable&&(this.draggable=t.draggable),this.name=t.name,void 0!==t.opacity&&(this.opacity=t.opacity),void 0!==t.position&&(this.position={},void 0!==t.position.x&&(this.position.x=(0,y.DT4)(t.position.x)),void 0!==t.position.y&&(this.position.y=(0,y.DT4)(t.position.y))),void 0!==t.size&&this.size.load(t.size),void 0!==t.destroy&&(this.destroy=t.destroy),void 0===t.orbits||(this.orbits=t.orbits))}}(o||(o={})).absorber="absorber";let m=2*Math.PI;class w{constructor(t,e,i,o,s){this._calcPosition=()=>{let t=(0,y.l1q)({size:this._container.canvas.size,position:this.options.position});return y.Miz.create(t.x,t.y)},this._updateParticlePosition=(t,e)=>{if(t.destroyed)return;let i=this._container,o=i.canvas.size;if(t.needsNewPosition){let e=(0,y.MhH)({size:o});t.position.setTo(e),t.velocity.setTo(t.initialVelocity),t.absorberOrbit=void 0,t.needsNewPosition=!1}if(this.options.orbits){if(void 0===t.absorberOrbit&&(t.absorberOrbit=y.Miz.origin,t.absorberOrbit.length=(0,y.YfF)(t.getPosition(),this.position),t.absorberOrbit.angle=(0,y.G0i)()*m),t.absorberOrbit.length<=this.size&&!this.options.destroy){let e=Math.min(o.width,o.height);t.absorberOrbit.length=e*(1+(.2*(0,y.G0i)()-.1))}void 0===t.absorberOrbitDirection&&(t.absorberOrbitDirection=t.velocity.x>=0?y.pTR.clockwise:y.pTR.counterClockwise);let s=t.absorberOrbit.length,a=t.absorberOrbit.angle,n=t.absorberOrbitDirection;t.velocity.setTo(y.Miz.origin);let r={x:n===y.pTR.clockwise?Math.cos:Math.sin,y:n===y.pTR.clockwise?Math.sin:Math.cos};t.position.x=this.position.x+s*r.x(a),t.position.y=this.position.y+s*r.y(a),t.absorberOrbit.length-=e.length,t.absorberOrbit.angle+=(t.retina.moveSpeed??0)*i.retina.pixelRatio/y.a56*i.retina.reduceFactor}else{let i=y.Miz.origin;i.length=e.length,i.angle=e.angle,t.velocity.addTo(i)}},this._absorbers=t,this._container=e,this._engine=i,this.initialPosition=s?y.Miz.create(s.x,s.y):void 0,o instanceof b?this.options=o:(this.options=new b,this.options.load(o)),this.dragging=!1,this.name=this.options.name,this.opacity=this.options.opacity,this.size=(0,y.VGA)(this.options.size.value)*e.retina.pixelRatio,this.mass=this.size*this.options.size.density*e.retina.reduceFactor;let a=this.options.size.limit;this.limit={radius:a.radius*e.retina.pixelRatio*e.retina.reduceFactor,mass:a.mass},this.color=(0,y.BN0)(this._engine,this.options.color)??{b:0,g:0,r:0},this.position=this.initialPosition?.copy()??this._calcPosition()}attract(t){let e=this._container,i=this.options;if(i.draggable){let t=e.interactivity.mouse;t.clicking&&t.downPosition?(0,y.YfF)(this.position,t.downPosition)<=this.size&&(this.dragging=!0):this.dragging=!1,this.dragging&&t.position&&(this.position.x=t.position.x,this.position.y=t.position.y)}let o=t.getPosition(),{dx:s,dy:a,distance:n}=(0,y.vrU)(this.position,o),r=y.Miz.create(s,a);if(r.length=this.mass/Math.pow(n,2)*e.retina.reduceFactor,n<this.size+t.getRadius()){let o=.033*t.getRadius()*e.retina.pixelRatio;this.size>t.getRadius()&&n<this.size-t.getRadius()||void 0!==t.absorberOrbit&&t.absorberOrbit.length<0?i.destroy?t.destroy():(t.needsNewPosition=!0,this._updateParticlePosition(t,r)):(i.destroy&&(t.size.value-=o),this._updateParticlePosition(t,r)),(this.limit.radius<=0||this.size<this.limit.radius)&&(this.size+=o),(this.limit.mass<=0||this.mass<this.limit.mass)&&(this.mass+=o*this.options.size.density*e.retina.reduceFactor)}else this._updateParticlePosition(t,r)}draw(t){t.translate(this.position.x,this.position.y),t.beginPath(),t.arc(y.boI.x,y.boI.y,this.size,0,m,!1),t.closePath(),t.fillStyle=(0,y.xxz)(this.color,this.opacity),t.fill()}resize(){let t=this.initialPosition;this.position=t&&(0,y.Tj8)(t,this._container.canvas.size,y.Miz.origin)?t:this._calcPosition()}}class x{constructor(t,e){this._container=t,this._engine=e,this.array=[],this.absorbers=[],this.interactivityAbsorbers=[],t.getAbsorber=t=>void 0===t||(0,y.EtT)(t)?this.array[t??0]:this.array.find(e=>e.name===t),t.addAbsorber=async(t,e)=>this.addAbsorber(t,e)}async addAbsorber(t,e){let i=new w(this,this._container,this._engine,t,e);return this.array.push(i),Promise.resolve(i)}draw(t){for(let e of this.array)e.draw(t)}handleClickMode(t){let e=this.absorbers,i=this.interactivityAbsorbers;if(t===o.absorber){let t=(0,y.TA3)(i)??(0,y.TA3)(e),o=this._container.interactivity.mouse.clickPosition;this.addAbsorber(t,o)}}async init(){this.absorbers=this._container.actualOptions.absorbers,this.interactivityAbsorbers=this._container.actualOptions.interactivity.modes.absorbers;let t=(0,y.wJ2)(this.absorbers,async t=>{await this.addAbsorber(t)});t instanceof Array?await Promise.all(t):await t}particleUpdate(t){for(let e of this.array)if(e.attract(t),t.destroyed)break}removeAbsorber(t){let e=this.array.indexOf(t);e>=0&&this.array.splice(e,1)}resize(){for(let t of this.array)t.resize()}stop(){this.array=[]}}class k{constructor(t){this.id="absorbers",this._engine=t}async getPlugin(t){return Promise.resolve(new x(t,this._engine))}loadOptions(t,e){(this.needsPlugin(t)||this.needsPlugin(e))&&(e?.absorbers&&(t.absorbers=(0,y.wJ2)(e.absorbers,t=>{let e=new b;return e.load(t),e})),t.interactivity.modes.absorbers=(0,y.wJ2)(e?.interactivity?.modes?.absorbers,t=>{let e=new b;return e.load(t),e}))}needsPlugin(t){if(!t)return!1;let e=t.absorbers;return(0,y.cyL)(e)?!!e.length:!!(e||t.interactivity?.events?.onClick?.mode&&(0,y.hnD)(o.absorber,t.interactivity.events.onClick.mode))}}async function _(t,e=!0){t.checkVersion("3.8.1"),await t.addPlugin(new k(t),e)}class C{load(t){(0,y.kZJ)(t)||(void 0!==t.bottom&&(this.bottom=(0,y.DT4)(t.bottom)),void 0!==t.left&&(this.left=(0,y.DT4)(t.left)),void 0!==t.right&&(this.right=(0,y.DT4)(t.right)),void 0!==t.top&&(this.top=(0,y.DT4)(t.top)))}}!function(t){t.none="none",t.split="split"}(s||(s={}));class z extends y.PVU{constructor(){super(),this.value=3}}class P extends y.PVU{constructor(){super(),this.value={min:4,max:9}}}class T{constructor(){this.count=1,this.factor=new z,this.rate=new P,this.sizeOffset=!0}load(t){!(0,y.kZJ)(t)&&(void 0!==t.color&&(this.color=y.Oit.create(this.color,t.color)),void 0!==t.count&&(this.count=t.count),this.factor.load(t.factor),this.rate.load(t.rate),this.particles=(0,y.wJ2)(t.particles,t=>(0,y.zwS)({},t)),void 0!==t.sizeOffset&&(this.sizeOffset=t.sizeOffset),t.colorOffset&&(this.colorOffset=this.colorOffset??{},void 0!==t.colorOffset.h&&(this.colorOffset.h=t.colorOffset.h),void 0!==t.colorOffset.s&&(this.colorOffset.s=t.colorOffset.s),void 0!==t.colorOffset.l&&(this.colorOffset.l=t.colorOffset.l)))}}class D{constructor(){this.bounds=new C,this.mode=s.none,this.split=new T}load(t){(0,y.kZJ)(t)||(t.mode&&(this.mode=t.mode),t.bounds&&this.bounds.load(t.bounds),this.split.load(t.split))}}class M{constructor(t,e){this.container=e,this.engine=t}init(t){let e=this.container,i=t.options.destroy;if(!i)return;t.splitCount=0;let o=i.bounds;t.destroyBounds||(t.destroyBounds={});let{bottom:s,left:a,right:n,top:r}=o,{destroyBounds:l}=t,c=e.canvas.size;s&&(l.bottom=(0,y.VGA)(s)*c.height/y.a56),a&&(l.left=(0,y.VGA)(a)*c.width/y.a56),n&&(l.right=(0,y.VGA)(n)*c.width/y.a56),r&&(l.top=(0,y.VGA)(r)*c.height/y.a56)}isEnabled(t){return!t.destroyed}loadOptions(t,...e){for(let i of(t.destroy||(t.destroy=new D),e))t.destroy.load(i?.destroy)}particleDestroyed(t,e){if(e)return;let i=t.options.destroy;i&&i.mode===s.split&&function(t,e,i){let o=i.options.destroy;if(!o)return;let s=o.split;if(s.count>=0&&(void 0===i.splitCount||i.splitCount++>s.count))return;let a=(0,y.VGA)(s.rate.value),n=(0,y.TA3)(s.particles);for(let o=0;o<a;o++)!function(t,e,i,o){let s=i.options.destroy;if(!s)return;let a=s.split,n=(0,y.yVN)(t,e,i.options),r=(0,y.VGA)(a.factor.value),l=i.getFillColor();a.color?n.color.load(a.color):a.colorOffset&&l?n.color.load({value:{hsl:{h:l.h+(0,y.VGA)(a.colorOffset.h??0),s:l.s+(0,y.VGA)(a.colorOffset.s??0),l:l.l+(0,y.VGA)(a.colorOffset.l??0)}}}):n.color.load({value:{hsl:i.getFillColor()}}),n.move.load({center:{x:i.position.x,y:i.position.y,mode:y.qiC.precise}}),(0,y.EtT)(n.size.value)?n.size.value/=r:(n.size.value.min/=r,n.size.value.max/=r),n.load(o);let c=a.sizeOffset?(0,y.DT4)(-i.size.value,i.size.value):0,d={x:i.position.x+(0,y.U43)(c),y:i.position.y+(0,y.U43)(c)};e.particles.addParticle(d,n,i.group,t=>!(t.size.value<.5)&&(t.velocity.length=(0,y.U43)((0,y.DT4)(i.velocity.length,t.velocity.length)),t.splitCount=(i.splitCount??0)+1,t.unbreakable=!0,setTimeout(()=>{t.unbreakable=!1},500),!0))}(t,e,i,n)}(this.engine,this.container,t)}update(t){if(!this.isEnabled(t))return;let e=t.getPosition(),i=t.destroyBounds;i&&(void 0!==i.bottom&&e.y>=i.bottom||void 0!==i.left&&e.x<=i.left||void 0!==i.right&&e.x>=i.right||void 0!==i.top&&e.y<=i.top)&&t.destroy()}}async function E(t,e=!0){t.checkVersion("3.8.1"),await t.addParticleUpdater("destroy",e=>Promise.resolve(new M(t,e)),e)}class O{constructor(){this.wait=!1}load(t){(0,y.kZJ)(t)||(void 0!==t.count&&(this.count=t.count),void 0!==t.delay&&(this.delay=(0,y.DT4)(t.delay)),void 0!==t.duration&&(this.duration=(0,y.DT4)(t.duration)),void 0===t.wait||(this.wait=t.wait))}}class S{constructor(){this.quantity=1,this.delay=.1}load(t){(0,y.kZJ)(t)||(void 0!==t.quantity&&(this.quantity=(0,y.DT4)(t.quantity)),void 0!==t.delay&&(this.delay=(0,y.DT4)(t.delay)))}}class R{constructor(){this.color=!1,this.opacity=!1}load(t){(0,y.kZJ)(t)||(void 0!==t.color&&(this.color=t.color),void 0===t.opacity||(this.opacity=t.opacity))}}class V{constructor(){this.options={},this.replace=new R,this.type="square"}load(t){(0,y.kZJ)(t)||(void 0!==t.options&&(this.options=(0,y.zwS)({},t.options??{})),this.replace.load(t.replace),void 0===t.type||(this.type=t.type))}}class I{constructor(){this.mode=y.qiC.percent,this.height=0,this.width=0}load(t){(0,y.kZJ)(t)||(void 0!==t.mode&&(this.mode=t.mode),void 0!==t.height&&(this.height=t.height),void 0===t.width||(this.width=t.width))}}class A{constructor(){this.autoPlay=!0,this.fill=!0,this.life=new O,this.rate=new S,this.shape=new V,this.startCount=0}load(t){(0,y.kZJ)(t)||(void 0!==t.autoPlay&&(this.autoPlay=t.autoPlay),void 0!==t.size&&(this.size||(this.size=new I),this.size.load(t.size)),void 0!==t.direction&&(this.direction=t.direction),this.domId=t.domId,void 0!==t.fill&&(this.fill=t.fill),this.life.load(t.life),this.name=t.name,this.particles=(0,y.wJ2)(t.particles,t=>(0,y.zwS)({},t)),this.rate.load(t.rate),this.shape.load(t.shape),void 0!==t.position&&(this.position={},void 0!==t.position.x&&(this.position.x=(0,y.DT4)(t.position.x)),void 0!==t.position.y&&(this.position.y=(0,y.DT4)(t.position.y))),void 0!==t.spawnColor&&(void 0===this.spawnColor&&(this.spawnColor=new y.A9e),this.spawnColor.load(t.spawnColor)),void 0===t.startCount||(this.startCount=t.startCount))}}(a||(a={})).emitter="emitter";let G=1;function q(t,e){t.color?t.color.value=e:t.color={value:e}}class B{constructor(t,e,i,o,s){this.emitters=e,this.container=i,this._destroy=()=>{this._mutationObserver?.disconnect(),this._mutationObserver=void 0,this._resizeObserver?.disconnect(),this._resizeObserver=void 0,this.emitters.removeEmitter(this),this._engine.dispatchEvent("emitterDestroyed",{container:this.container,data:{emitter:this}})},this._prepareToDie=()=>{if(this._paused)return;let t=this.options.life?.duration!==void 0?(0,y.VGA)(this.options.life.duration):void 0;this.container.retina.reduceFactor&&(this._lifeCount>0||this._immortal)&&void 0!==t&&t>0&&(this._duration=t*y.XuV)},this._setColorAnimation=(t,e,i,o=G)=>{let s=this.container;if(!t.enable)return e;let a=(0,y.U43)(t.offset),n=(0,y.VGA)(this.options.rate.delay)*y.XuV/s.retina.reduceFactor;return(e+(0,y.VGA)(t.speed??0)*s.fpsLimit/n+a*o)%i},this._engine=t,this._currentDuration=0,this._currentEmitDelay=0,this._currentSpawnDelay=0,this._initialPosition=s,o instanceof A?this.options=o:(this.options=new A,this.options.load(o)),this._spawnDelay=(0,y.VGA)(this.options.life.delay??0)*y.XuV/this.container.retina.reduceFactor,this.position=this._initialPosition??this._calcPosition(),this.name=this.options.name,this.fill=this.options.fill,this._firstSpawn=!this.options.life.wait,this._startParticlesAdded=!1;let a=(0,y.zwS)({},this.options.particles);if(a??={},a.move??={},a.move.direction??=this.options.direction,this.options.spawnColor&&(this.spawnColor=(0,y.R5R)(this._engine,this.options.spawnColor)),this._paused=!this.options.autoPlay,this._particlesOptions=a,this._size=this._calcSize(),this.size=(0,y.YCE)(this._size,this.container.canvas.size),this._lifeCount=this.options.life.count??-1,this._immortal=this._lifeCount<=0,this.options.domId){let t=document.getElementById(this.options.domId);t&&(this._mutationObserver=new MutationObserver(()=>{this.resize()}),this._resizeObserver=new ResizeObserver(()=>{this.resize()}),this._mutationObserver.observe(t,{attributes:!0,attributeFilter:["style","width","height"]}),this._resizeObserver.observe(t))}let n=this.options.shape,r=this._engine.emitterShapeManager?.getShapeGenerator(n.type);r&&(this._shape=r.generate(this.position,this.size,this.fill,n.options)),this._engine.dispatchEvent("emitterCreated",{container:i,data:{emitter:this}}),this.play()}externalPause(){this._paused=!0,this.pause()}externalPlay(){this._paused=!1,this.play()}async init(){await this._shape?.init()}pause(){this._paused||delete this._emitDelay}play(){if(!this._paused&&this.container.retina.reduceFactor&&(this._lifeCount>0||this._immortal||!this.options.life.count)&&(this._firstSpawn||this._currentSpawnDelay>=(this._spawnDelay??0))){if(void 0===this._emitDelay){let t=(0,y.VGA)(this.options.rate.delay);this._emitDelay=t*y.XuV/this.container.retina.reduceFactor}(this._lifeCount>0||this._immortal)&&this._prepareToDie()}}resize(){let t=this._initialPosition;this.position=t&&(0,y.Tj8)(t,this.container.canvas.size,y.Miz.origin)?t:this._calcPosition(),this._size=this._calcSize(),this.size=(0,y.YCE)(this._size,this.container.canvas.size),this._shape?.resize(this.position,this.size)}update(t){!this._paused&&(this._firstSpawn&&(this._firstSpawn=!1,this._currentSpawnDelay=this._spawnDelay??0,this._currentEmitDelay=this._emitDelay??0),this._startParticlesAdded||(this._startParticlesAdded=!0,this._emitParticles(this.options.startCount)),void 0!==this._duration&&(this._currentDuration+=t.value,this._currentDuration>=this._duration&&(this.pause(),void 0!==this._spawnDelay&&delete this._spawnDelay,!this._immortal&&this._lifeCount--,this._lifeCount>0||this._immortal?(this.position=this._calcPosition(),this._shape?.resize(this.position,this.size),this._spawnDelay=(0,y.VGA)(this.options.life.delay??0)*y.XuV/this.container.retina.reduceFactor):this._destroy(),this._currentDuration-=this._duration,delete this._duration)),void 0!==this._spawnDelay&&(this._currentSpawnDelay+=t.value,this._currentSpawnDelay>=this._spawnDelay&&(this._engine.dispatchEvent("emitterPlay",{container:this.container}),this.play(),this._currentSpawnDelay-=this._currentSpawnDelay,delete this._spawnDelay)),void 0!==this._emitDelay&&(this._currentEmitDelay+=t.value,this._currentEmitDelay>=this._emitDelay&&(this._emit(),this._currentEmitDelay-=this._emitDelay)))}_calcPosition(){if(this.options.domId){let t=document.getElementById(this.options.domId);if(t){let e=t.getBoundingClientRect(),i=this.container.retina.pixelRatio;return{x:(e.x+.5*e.width)*i,y:(e.y+.5*e.height)*i}}}return(0,y.l1q)({size:this.container.canvas.size,position:this.options.position})}_calcSize(){let t=this.container;if(this.options.domId){let e=document.getElementById(this.options.domId);if(e){let i=e.getBoundingClientRect();return{width:i.width*t.retina.pixelRatio,height:i.height*t.retina.pixelRatio,mode:y.qiC.precise}}}return this.options.size??(()=>{let t=new I;return t.load({height:0,mode:y.qiC.percent,width:0}),t})()}_emit(){if(this._paused)return;let t=(0,y.VGA)(this.options.rate.quantity);this._emitParticles(t)}_emitParticles(t){let e=(0,y.TA3)(this._particlesOptions);for(let i=0;i<t;i++){let t=(0,y.zwS)({},e);if(this.spawnColor){let e=this.options.spawnColor?.animation;if(e){let t={h:360,s:100,l:100};this.spawnColor.h=this._setColorAnimation(e.h,this.spawnColor.h,t.h,3.6),this.spawnColor.s=this._setColorAnimation(e.s,this.spawnColor.s,t.s),this.spawnColor.l=this._setColorAnimation(e.l,this.spawnColor.l,t.l)}q(t,this.spawnColor)}let i=this.options.shape,o=this.position;if(this._shape){let e=this._shape.randomPosition();if(e){o=e.position;let s=i.replace;s.color&&e.color&&q(t,e.color),s.opacity&&(t.opacity?t.opacity.value=e.opacity:t.opacity={value:e.opacity})}else o=null}o&&this.container.particles.addParticle(o,t)}}}class U{constructor(t,e){this.container=e,this._engine=t,this.array=[],this.emitters=[],this.interactivityEmitters={random:{count:1,enable:!1},value:[]},e.getEmitter=t=>void 0===t||(0,y.EtT)(t)?this.array[t??0]:this.array.find(e=>e.name===t),e.addEmitter=async(t,e)=>this.addEmitter(t,e),e.removeEmitter=t=>{let i=e.getEmitter(t);i&&this.removeEmitter(i)},e.playEmitter=t=>{let i=e.getEmitter(t);i&&i.externalPlay()},e.pauseEmitter=t=>{let i=e.getEmitter(t);i&&i.externalPause()}}async addEmitter(t,e){let i=new A;i.load(t);let o=new B(this._engine,this,this.container,i,e);return await o.init(),this.array.push(o),o}handleClickMode(t){let e;let i=this.emitters,o=this.interactivityEmitters;if(t!==a.emitter)return;if(o&&(0,y.cyL)(o.value)){if(o.value.length>0&&o.random.enable){e=[];let t=[];for(let i=0;i<o.random.count;i++){let s=(0,y.n02)(o.value);if(t.includes(s)&&t.length<o.value.length){i--;continue}t.push(s),e.push((0,y.Vh1)(o.value,s))}}else e=o.value}else e=o?.value;let s=e??i,n=this.container.interactivity.mouse.clickPosition;(0,y.wJ2)(s,async t=>{await this.addEmitter(t,n)})}async init(){if(this.emitters=this.container.actualOptions.emitters,this.interactivityEmitters=this.container.actualOptions.interactivity.modes.emitters,this.emitters){if((0,y.cyL)(this.emitters))for(let t of this.emitters)await this.addEmitter(t);else await this.addEmitter(this.emitters)}}pause(){for(let t of this.array)t.pause()}play(){for(let t of this.array)t.play()}removeEmitter(t){let e=this.array.indexOf(t);e>=0&&this.array.splice(e,1)}resize(){for(let t of this.array)t.resize()}stop(){this.array=[]}update(t){for(let e of this.array)e.update(t)}}class F{constructor(t){this._engine=t,this.id="emitters"}getPlugin(t){return Promise.resolve(new U(this._engine,t))}loadOptions(t,e){if(!this.needsPlugin(t)&&!this.needsPlugin(e))return;e?.emitters&&(t.emitters=(0,y.wJ2)(e.emitters,t=>{let e=new A;return e.load(t),e}));let i=e?.interactivity?.modes?.emitters;if(i){if((0,y.cyL)(i))t.interactivity.modes.emitters={random:{count:1,enable:!0},value:i.map(t=>{let e=new A;return e.load(t),e})};else if(void 0!==i.value){if((0,y.cyL)(i.value))t.interactivity.modes.emitters={random:{count:i.random.count??1,enable:i.random.enable??!1},value:i.value.map(t=>{let e=new A;return e.load(t),e})};else{let e=new A;e.load(i.value),t.interactivity.modes.emitters={random:{count:i.random.count??1,enable:i.random.enable??!1},value:e}}}else(t.interactivity.modes.emitters={random:{count:1,enable:!1},value:new A}).value.load(i)}}needsPlugin(t){if(!t)return!1;let e=t.emitters;return(0,y.cyL)(e)&&!!e.length||void 0!==e||!!t.interactivity?.events?.onClick?.mode&&(0,y.hnD)(a.emitter,t.interactivity.events.onClick.mode)}}let J=new Map;class L{constructor(t){this._engine=t}addShapeGenerator(t,e){this.getShapeGenerator(t)||J.set(t,e)}getShapeGenerator(t){return J.get(t)}getSupportedShapeGenerators(){return J.keys()}}class Z{constructor(t,e,i,o){this.position=t,this.size=e,this.fill=i,this.options=o}resize(t,e){this.position=t,this.size=e}}async function j(t,e=!0){t.checkVersion("3.8.1"),t.emitterShapeManager||(t.emitterShapeManager=new L(t)),t.addEmitterShapeGenerator||(t.addEmitterShapeGenerator=(e,i)=>{t.emitterShapeManager?.addShapeGenerator(e,i)});let i=new F(t);await t.addPlugin(i,e)}let H=2*Math.PI;class Y extends Z{constructor(t,e,i,o){super(t,e,i,o)}async init(){}randomPosition(){let t=this.size,e=this.fill,i=this.position,[o,s]=[.5*t.width,.5*t.height],a=((t,e)=>{let i=Math.atan(e/t*Math.tan(H*(.25*(0,y.G0i)()))),o=(0,y.G0i)();return o<.25?i:o<.5?Math.PI-i:o<.75?Math.PI+i:-i})(o,s),n=o*s/Math.sqrt((s*Math.cos(a))**2+(o*Math.sin(a))**2),r=e?n*Math.sqrt((0,y.G0i)()):n;return{position:{x:i.x+r*Math.cos(a),y:i.y+r*Math.sin(a)}}}}class W{generate(t,e,i,o){return new Y(t,e,i,o)}}async function X(t,e=!0){t.checkVersion("3.8.1"),t.addEmitterShapeGenerator?.("circle",new W),await t.refresh(e)}function $(t,e){return t+e*((0,y.G0i)()-y.MXx)}!function(t){t[t.TopLeft=0]="TopLeft",t[t.TopRight=1]="TopRight",t[t.BottomRight=2]="BottomRight",t[t.BottomLeft=3]="BottomLeft"}(n||(n={}));class N extends Z{constructor(t,e,i,o){super(t,e,i,o)}async init(){}randomPosition(){let t=this.fill,e=this.position,i=this.size;if(t)return{position:{x:$(e.x,i.width),y:$(e.y,i.height)}};{let t=i.width*y.MXx,o=i.height*y.MXx,s=Math.floor(4*(0,y.G0i)()),a=((0,y.G0i)()-y.MXx)*y.gdl;switch(s){case n.TopLeft:return{position:{x:e.x+a*t,y:e.y-o}};case n.TopRight:return{position:{x:e.x-t,y:e.y+a*o}};case n.BottomRight:return{position:{x:e.x+a*t,y:e.y+o}};case n.BottomLeft:default:return{position:{x:e.x+t,y:e.y+a*o}}}}}}class K{generate(t,e,i,o){return new N(t,e,i,o)}}async function Q(t,e=!0){t.checkVersion("3.8.1"),t.addEmitterShapeGenerator?.("square",new K),await t.refresh(e)}class tt{constructor(){this.delay=1,this.pauseOnStop=!1,this.quantity=1}load(t){(0,y.kZJ)(t)||(void 0!==t.delay&&(this.delay=t.delay),void 0!==t.quantity&&(this.quantity=t.quantity),void 0!==t.particles&&(this.particles=(0,y.zwS)({},t.particles)),void 0===t.pauseOnStop||(this.pauseOnStop=t.pauseOnStop))}}let te="trail";class ti extends y.sJd{constructor(t){super(t),this._delay=0}clear(){}init(){}interact(t){let e=this.container,{interactivity:i}=e;if(!e.retina.reduceFactor)return;let o=e.actualOptions.interactivity.modes.trail;if(!o)return;let s=o.delay*y.XuV/this.container.retina.reduceFactor;if(this._delay<s&&(this._delay+=t.value),this._delay<s)return;let a=!(o.pauseOnStop&&(i.mouse.position===this._lastPosition||i.mouse.position?.x===this._lastPosition?.x&&i.mouse.position?.y===this._lastPosition?.y)),n=e.interactivity.mouse.position;n?this._lastPosition={...n}:delete this._lastPosition,a&&e.particles.push(o.quantity,e.interactivity.mouse,o.particles),this._delay-=s}isEnabled(t){let e=this.container,i=e.actualOptions,o=e.interactivity.mouse,s=(t?.interactivity??i.interactivity).events;return o.clicking&&o.inside&&!!o.position&&(0,y.hnD)(te,s.onClick.mode)||o.inside&&!!o.position&&(0,y.hnD)(te,s.onHover.mode)}loadModeOptions(t,...e){for(let i of(t.trail||(t.trail=new tt),e))t.trail.load(i?.trail)}reset(){}}async function to(t,e=!0){t.checkVersion("3.8.1"),await t.addInteractor("externalTrail",t=>Promise.resolve(new ti(t)),e)}!function(t){t.both="both",t.horizontal="horizontal",t.vertical="vertical"}(r||(r={}));let ts=2*Math.PI;class ta{constructor(){this.enable=!1,this.value=0}load(t){(0,y.kZJ)(t)||(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.value&&(this.value=(0,y.DT4)(t.value)))}}class tn{constructor(){this.darken=new ta,this.enable=!1,this.enlighten=new ta,this.mode=r.vertical,this.speed=25}load(t){(0,y.kZJ)(t)||(void 0!==t.backColor&&(this.backColor=y.Oit.create(this.backColor,t.backColor)),this.darken.load(t.darken),void 0!==t.enable&&(this.enable=t.enable),this.enlighten.load(t.enlighten),void 0!==t.mode&&(this.mode=t.mode),void 0!==t.speed&&(this.speed=(0,y.DT4)(t.speed)))}}class tr{constructor(t){this._engine=t}getTransformValues(t){let e=t.roll?.enable&&t.roll,i=e&&e.horizontal,o=e&&e.vertical;return{a:i?Math.cos(e.angle):void 0,d:o?Math.sin(e.angle):void 0}}init(t){!function(t,e){let i=e.options.roll;if(!i?.enable){e.roll={enable:!1,horizontal:!1,vertical:!1,angle:0,speed:0};return}if(e.roll={enable:i.enable,horizontal:i.mode===r.horizontal||i.mode===r.both,vertical:i.mode===r.vertical||i.mode===r.both,angle:(0,y.G0i)()*ts,speed:(0,y.VGA)(i.speed)/360},i.backColor)e.backColor=(0,y.R5R)(t,i.backColor);else if(i.darken.enable&&i.enlighten.enable){let t=(0,y.G0i)()>=y.MXx?y.HKZ.darken:y.HKZ.enlighten;e.roll.alter={type:t,value:(0,y.VGA)(t===y.HKZ.darken?i.darken.value:i.enlighten.value)}}else i.darken.enable?e.roll.alter={type:y.HKZ.darken,value:(0,y.VGA)(i.darken.value)}:i.enlighten.enable&&(e.roll.alter={type:y.HKZ.enlighten,value:(0,y.VGA)(i.enlighten.value)})}(this._engine,t)}isEnabled(t){let e=t.options.roll;return!t.destroyed&&!t.spawning&&!!e?.enable}loadOptions(t,...e){for(let i of(t.roll||(t.roll=new tn),e))t.roll.load(i?.roll)}update(t,e){this.isEnabled(t)&&function(t,e){let i=t.options.roll,o=t.roll;if(!o||!i?.enable)return;let s=o.speed*e.factor;o.angle+=s,o.angle>ts&&(o.angle-=ts)}(t,e)}}async function tl(t,e=!0){t.checkVersion("3.8.1"),await t.addParticleUpdater("roll",()=>Promise.resolve(new tr(t)),e)}let tc=2*Math.PI;class td{init(t){let e=t.options.move.gravity;t.gravity={enable:e.enable,acceleration:(0,y.VGA)(e.acceleration),inverse:e.inverse},function(t){let e=t.container,i=t.options.move.spin;if(!i.enable)return;let o=i.position??{x:50,y:50},s={x:.01*o.x*e.canvas.size.width,y:.01*o.y*e.canvas.size.height},a=t.getPosition(),n=(0,y.YfF)(a,s),r=(0,y.VGA)(i.acceleration);t.retina.spinAcceleration=r*e.retina.pixelRatio,t.spin={center:s,direction:t.velocity.x>=0?y.pTR.clockwise:y.pTR.counterClockwise,angle:(0,y.G0i)()*tc,radius:n,acceleration:t.retina.spinAcceleration}}(t)}isEnabled(t){return!t.destroyed&&t.options.move.enable}move(t,e){let i=t.options,o=i.move;if(!o.enable)return;let s=t.container,a=s.retina.pixelRatio;t.retina.moveSpeed??=(0,y.VGA)(o.speed)*a,t.retina.moveDrift??=(0,y.VGA)(t.options.move.drift)*a;let n=t.slow.inRange?t.slow.factor:1,r=t.retina.moveSpeed*s.retina.reduceFactor,l=t.retina.moveDrift,c=(0,y.W9e)(i.size.value)*a,d=r*(o.size?t.getRadius()/c:1)*n*(e.factor||1)/2,h=t.retina.maxSpeed??s.retina.maxSpeed;o.spin.enable?function(t,e){let i=t.container;if(!t.spin)return;let o=t.spin.direction===y.pTR.clockwise,s={x:o?Math.cos:Math.sin,y:o?Math.sin:Math.cos};t.position.x=t.spin.center.x+t.spin.radius*s.x(t.spin.angle),t.position.y=t.spin.center.y+t.spin.radius*s.y(t.spin.angle),t.spin.radius+=t.spin.acceleration;let a=Math.max(i.canvas.size.width,i.canvas.size.height),n=.5*a;t.spin.radius>n?(t.spin.radius=n,t.spin.acceleration*=-1):t.spin.radius<0&&(t.spin.radius=0,t.spin.acceleration*=-1),t.spin.angle+=.01*e*(1-t.spin.radius/a)}(t,d):function(t,e,i,o,s,a){(function(t,e){let i=t.options.move.path;if(!i.enable)return;if(t.lastPathTime<=t.pathDelay){t.lastPathTime+=e.value;return}let o=t.pathGenerator?.generate(t,e);o&&t.velocity.addTo(o),i.clamp&&(t.velocity.x=(0,y.qE8)(t.velocity.x,-1,1),t.velocity.y=(0,y.qE8)(t.velocity.y,-1,1)),t.lastPathTime-=t.pathDelay})(t,a);let n=t.gravity,r=n?.enable&&n.inverse?-1:1;s&&i&&(t.velocity.x+=s*a.factor/(60*i)),n?.enable&&i&&(t.velocity.y+=r*(n.acceleration*a.factor)/(60*i));let l=t.moveDecay;t.velocity.multTo(l);let c=t.velocity.mult(i);n?.enable&&o>0&&(!n.inverse&&c.y>=0&&c.y>=o||n.inverse&&c.y<=0&&c.y<=-o)&&(c.y=r*o,i&&(t.velocity.y=c.y/i));let d=t.options.zIndex,h=(1-t.zIndexFactor)**d.velocityRate;c.multTo(h);let{position:u}=t;u.addTo(c),e.vibrate&&(u.x+=Math.sin(u.x*Math.cos(u.y)),u.y+=Math.cos(u.y*Math.sin(u.x)))}(t,o,d,h,l,e),function(t){let e=t.initialPosition,{dx:i,dy:o}=(0,y.vrU)(e,t.position),s=Math.abs(i),a=Math.abs(o),{maxDistance:n}=t.retina,r=n.horizontal,l=n.vertical;if(r||l){if((r&&s>=r||l&&a>=l)&&!t.misplaced)t.misplaced=!!r&&s>r||!!l&&a>l,r&&(t.velocity.x=.5*t.velocity.y-t.velocity.x),l&&(t.velocity.y=.5*t.velocity.x-t.velocity.y);else if((!r||s<r)&&(!l||a<l)&&t.misplaced)t.misplaced=!1;else if(t.misplaced){let i=t.position,o=t.velocity;r&&(i.x<e.x&&o.x<0||i.x>e.x&&o.x>0)&&(o.x*=-(0,y.G0i)()),l&&(i.y<e.y&&o.y<0||i.y>e.y&&o.y>0)&&(o.y*=-(0,y.G0i)())}}}(t)}}async function th(t,e=!0){t.checkVersion("3.8.1"),await t.addMover("base",()=>Promise.resolve(new td),e)}let tu=2*Math.PI,tp={x:0,y:0};class tf{constructor(){this.validTypes=["circle"]}draw(t){!function(t){let{context:e,particle:i,radius:o}=t;i.circleRange||(i.circleRange={min:0,max:tu});let s=i.circleRange;e.arc(tp.x,tp.y,o,s.min,s.max,!1)}(t)}getSidesCount(){return 12}particleInit(t,e){let i=e.shapeData,o=i?.angle??{max:360,min:0};e.circleRange=(0,y.Gvm)(o)?{min:(0,y.puj)(o.min),max:(0,y.puj)(o.max)}:{min:0,max:(0,y.puj)(o)}}}async function ty(t,e=!0){t.checkVersion("3.8.1"),await t.addShape(new tf,e)}class tv{constructor(t,e){this._container=t,this._engine=e}init(t){let e=(0,y.R5R)(this._engine,t.options.color,t.id,t.options.reduceDuplicates);e&&(t.color=(0,y.pzy)(e,t.options.color.animation,this._container.retina.reduceFactor))}isEnabled(t){let{h:e,s:i,l:o}=t.options.color.animation,{color:s}=t;return!t.destroyed&&!t.spawning&&(s?.h.value!==void 0&&e.enable||s?.s.value!==void 0&&i.enable||s?.l.value!==void 0&&o.enable)}update(t,e){(0,y.JvX)(t.color,e)}}async function tg(t,e=!0){t.checkVersion("3.8.1"),await t.addParticleUpdater("color",e=>Promise.resolve(new tv(e,t)),e)}!function(t){t[t.r=1]="r",t[t.g=2]="g",t[t.b=3]="b",t[t.a=4]="a"}(l||(l={}));let tb=/^#?([a-f\d])([a-f\d])([a-f\d])([a-f\d])?$/i,tm=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})?$/i;class tw{constructor(){this.key="hex",this.stringPrefix="#"}handleColor(t){return this._parseString(t.value)}handleRangeColor(t){return this._parseString(t.value)}parseString(t){return this._parseString(t)}_parseString(t){if("string"!=typeof t||!t?.startsWith(this.stringPrefix))return;let e=t.replace(tb,(t,e,i,o,s)=>e+e+i+i+o+o+(void 0!==s?s+s:"")),i=tm.exec(e);return i?{a:void 0!==i[l.a]?parseInt(i[l.a],16)/255:1,b:parseInt(i[l.b],16),g:parseInt(i[l.g],16),r:parseInt(i[l.r],16)}:void 0}}async function tx(t,e=!0){t.checkVersion("3.8.1"),await t.addColorManager(new tw,e)}!function(t){t[t.h=1]="h",t[t.s=2]="s",t[t.l=3]="l",t[t.a=5]="a"}(c||(c={}));class tk{constructor(){this.key="hsl",this.stringPrefix="hsl"}handleColor(t){let e=t.value.hsl??t.value;if(void 0!==e.h&&void 0!==e.s&&void 0!==e.l)return(0,y.YLU)(e)}handleRangeColor(t){let e=t.value.hsl??t.value;if(void 0!==e.h&&void 0!==e.l)return(0,y.YLU)({h:(0,y.VGA)(e.h),l:(0,y.VGA)(e.l),s:(0,y.VGA)(e.s)})}parseString(t){if(!t.startsWith("hsl"))return;let e=/hsla?\(\s*(\d+)\s*[\s,]\s*(\d+)%\s*[\s,]\s*(\d+)%\s*([\s,]\s*(0|1|0?\.\d+|(\d{1,3})%)\s*)?\)/i.exec(t);return e?(0,y.ayx)({a:e.length>4?(0,y.M3Y)(e[c.a]):1,h:parseInt(e[c.h],10),l:parseInt(e[c.l],10),s:parseInt(e[c.s],10)}):void 0}}async function t_(t,e=!0){t.checkVersion("3.8.1"),await t.addColorManager(new tk,e)}class tC{constructor(t){this.container=t}init(t){let e=t.options.opacity;t.opacity=(0,y.XsS)(e,1);let i=e.animation;i.enable&&(t.opacity.velocity=(0,y.VGA)(i.speed)/y.a56*this.container.retina.reduceFactor,i.sync||(t.opacity.velocity*=(0,y.G0i)()))}isEnabled(t){return!t.destroyed&&!t.spawning&&!!t.opacity&&t.opacity.enable&&((t.opacity.maxLoops??0)<=0||(t.opacity.maxLoops??0)>0&&(t.opacity.loops??0)<(t.opacity.maxLoops??0))}reset(t){t.opacity&&(t.opacity.time=0,t.opacity.loops=0)}update(t,e){this.isEnabled(t)&&t.opacity&&(0,y.UC0)(t,t.opacity,!0,t.options.opacity.animation.destroy,e)}}async function tz(t,e=!0){t.checkVersion("3.8.1"),await t.addParticleUpdater("opacity",t=>Promise.resolve(new tC(t)),e)}class tP{constructor(t){this.container=t,this.modes=[y.Yzx.bounce,y.Yzx.split]}update(t,e,i,o){if(!this.modes.includes(o))return;let s=this.container,a=!1;for(let o of s.plugins.values())if(void 0!==o.particleBounce&&(a=o.particleBounce(t,i,e)),a)break;if(a)return;let n=t.getPosition(),r=t.offset,l=t.getRadius(),c=(0,y.AEc)(n,l),d=s.canvas.size;!function(t){if(t.outMode!==y.Yzx.bounce&&t.outMode!==y.Yzx.split||t.direction!==y.v5k.left&&t.direction!==y.v5k.right)return;t.bounds.right<0&&t.direction===y.v5k.left?t.particle.position.x=t.size+t.offset.x:t.bounds.left>t.canvasSize.width&&t.direction===y.v5k.right&&(t.particle.position.x=t.canvasSize.width-t.size-t.offset.x);let e=t.particle.velocity.x,i=!1;if(t.direction===y.v5k.right&&t.bounds.right>=t.canvasSize.width&&e>0||t.direction===y.v5k.left&&t.bounds.left<=0&&e<0){let e=(0,y.VGA)(t.particle.options.bounce.horizontal.value);t.particle.velocity.x*=-e,i=!0}if(!i)return;let o=t.offset.x+t.size;t.bounds.right>=t.canvasSize.width&&t.direction===y.v5k.right?t.particle.position.x=t.canvasSize.width-o:t.bounds.left<=0&&t.direction===y.v5k.left&&(t.particle.position.x=o),t.outMode===y.Yzx.split&&t.particle.destroy()}({particle:t,outMode:o,direction:e,bounds:c,canvasSize:d,offset:r,size:l}),function(t){if(t.outMode!==y.Yzx.bounce&&t.outMode!==y.Yzx.split||t.direction!==y.v5k.bottom&&t.direction!==y.v5k.top)return;t.bounds.bottom<0&&t.direction===y.v5k.top?t.particle.position.y=t.size+t.offset.y:t.bounds.top>t.canvasSize.height&&t.direction===y.v5k.bottom&&(t.particle.position.y=t.canvasSize.height-t.size-t.offset.y);let e=t.particle.velocity.y,i=!1;if(t.direction===y.v5k.bottom&&t.bounds.bottom>=t.canvasSize.height&&e>0||t.direction===y.v5k.top&&t.bounds.top<=0&&e<0){let e=(0,y.VGA)(t.particle.options.bounce.vertical.value);t.particle.velocity.y*=-e,i=!0}if(!i)return;let o=t.offset.y+t.size;t.bounds.bottom>=t.canvasSize.height&&t.direction===y.v5k.bottom?t.particle.position.y=t.canvasSize.height-o:t.bounds.top<=0&&t.direction===y.v5k.top&&(t.particle.position.y=o),t.outMode===y.Yzx.split&&t.particle.destroy()}({particle:t,outMode:o,direction:e,bounds:c,canvasSize:d,offset:r,size:l})}}class tT{constructor(t){this.container=t,this.modes=[y.Yzx.destroy]}update(t,e,i,o){if(!this.modes.includes(o))return;let s=this.container;switch(t.outType){case y.x_I.normal:case y.x_I.outside:if((0,y.Tj8)(t.position,s.canvas.size,y.Miz.origin,t.getRadius(),e))return;break;case y.x_I.inside:{let{dx:e,dy:i}=(0,y.vrU)(t.position,t.moveCenter),{x:o,y:s}=t.velocity;if(o<0&&e>t.moveCenter.radius||s<0&&i>t.moveCenter.radius||o>=0&&e<-t.moveCenter.radius||s>=0&&i<-t.moveCenter.radius)return}}s.particles.remove(t,t.group,!0)}}class tD{constructor(t){this.container=t,this.modes=[y.Yzx.none]}update(t,e,i,o){if(!this.modes.includes(o)||((t.options.move.distance.horizontal&&(e===y.v5k.left||e===y.v5k.right))??(t.options.move.distance.vertical&&(e===y.v5k.top||e===y.v5k.bottom))))return;let s=t.options.move.gravity,a=this.container,n=a.canvas.size,r=t.getRadius();if(s.enable){let i=t.position;(!s.inverse&&i.y>n.height+r&&e===y.v5k.bottom||s.inverse&&i.y<-r&&e===y.v5k.top)&&a.particles.remove(t)}else{if(t.velocity.y>0&&t.position.y<=n.height+r||t.velocity.y<0&&t.position.y>=-r||t.velocity.x>0&&t.position.x<=n.width+r||t.velocity.x<0&&t.position.x>=-r)return;(0,y.Tj8)(t.position,a.canvas.size,y.Miz.origin,r,e)||a.particles.remove(t)}}}class tM{constructor(t){this.container=t,this.modes=[y.Yzx.out]}update(t,e,i,o){if(!this.modes.includes(o))return;let s=this.container;if(t.outType===y.x_I.inside){let{x:e,y:i}=t.velocity,o=y.Miz.origin;o.length=t.moveCenter.radius,o.angle=t.velocity.angle+Math.PI,o.addTo(y.Miz.create(t.moveCenter));let{dx:a,dy:n}=(0,y.vrU)(t.position,o);if(e<=0&&a>=0||i<=0&&n>=0||e>=0&&a<=0||i>=0&&n<=0)return;t.position.x=Math.floor((0,y.U43)({min:0,max:s.canvas.size.width})),t.position.y=Math.floor((0,y.U43)({min:0,max:s.canvas.size.height}));let{dx:r,dy:l}=(0,y.vrU)(t.position,t.moveCenter);t.direction=Math.atan2(-l,-r),t.velocity.angle=t.direction}else{if((0,y.Tj8)(t.position,s.canvas.size,y.Miz.origin,t.getRadius(),e))return;switch(t.outType){case y.x_I.outside:{t.position.x=Math.floor((0,y.U43)({min:-t.moveCenter.radius,max:t.moveCenter.radius}))+t.moveCenter.x,t.position.y=Math.floor((0,y.U43)({min:-t.moveCenter.radius,max:t.moveCenter.radius}))+t.moveCenter.y;let{dx:e,dy:i}=(0,y.vrU)(t.position,t.moveCenter);t.moveCenter.radius&&(t.direction=Math.atan2(i,e),t.velocity.angle=t.direction);break}case y.x_I.normal:{let i=t.options.move.warp,o=s.canvas.size,a={bottom:o.height+t.getRadius()+t.offset.y,left:-t.getRadius()-t.offset.x,right:o.width+t.getRadius()+t.offset.x,top:-t.getRadius()-t.offset.y},n=t.getRadius(),r=(0,y.AEc)(t.position,n);e===y.v5k.right&&r.left>o.width+t.offset.x?(t.position.x=a.left,t.initialPosition.x=t.position.x,i||(t.position.y=(0,y.G0i)()*o.height,t.initialPosition.y=t.position.y)):e===y.v5k.left&&r.right<-t.offset.x&&(t.position.x=a.right,t.initialPosition.x=t.position.x,i||(t.position.y=(0,y.G0i)()*o.height,t.initialPosition.y=t.position.y)),e===y.v5k.bottom&&r.top>o.height+t.offset.y?(i||(t.position.x=(0,y.G0i)()*o.width,t.initialPosition.x=t.position.x),t.position.y=a.top,t.initialPosition.y=t.position.y):e===y.v5k.top&&r.bottom<-t.offset.y&&(i||(t.position.x=(0,y.G0i)()*o.width,t.initialPosition.x=t.position.x),t.position.y=a.bottom,t.initialPosition.y=t.position.y)}}}}}let tE=(t,e)=>t.default===e||t.bottom===e||t.left===e||t.right===e||t.top===e;class tO{constructor(t){this._addUpdaterIfMissing=(t,e,i)=>{let o=t.options.move.outModes;!this.updaters.has(e)&&tE(o,e)&&this.updaters.set(e,i(this.container))},this._updateOutMode=(t,e,i,o)=>{for(let s of this.updaters.values())s.update(t,o,e,i)},this.container=t,this.updaters=new Map}init(t){this._addUpdaterIfMissing(t,y.Yzx.bounce,t=>new tP(t)),this._addUpdaterIfMissing(t,y.Yzx.out,t=>new tM(t)),this._addUpdaterIfMissing(t,y.Yzx.destroy,t=>new tT(t)),this._addUpdaterIfMissing(t,y.Yzx.none,t=>new tD(t))}isEnabled(t){return!t.destroyed&&!t.spawning}update(t,e){let i=t.options.move.outModes;this._updateOutMode(t,e,i.bottom??i.default,y.v5k.bottom),this._updateOutMode(t,e,i.left??i.default,y.v5k.left),this._updateOutMode(t,e,i.right??i.default,y.v5k.right),this._updateOutMode(t,e,i.top??i.default,y.v5k.top)}}async function tS(t,e=!0){t.checkVersion("3.8.1"),await t.addParticleUpdater("outModes",t=>Promise.resolve(new tO(t)),e)}!function(t){t[t.r=1]="r",t[t.g=2]="g",t[t.b=3]="b",t[t.a=5]="a"}(d||(d={}));class tR{constructor(){this.key="rgb",this.stringPrefix="rgb"}handleColor(t){let e=t.value.rgb??t.value;if(void 0!==e.r)return e}handleRangeColor(t){let e=t.value.rgb??t.value;if(void 0!==e.r)return{r:(0,y.VGA)(e.r),g:(0,y.VGA)(e.g),b:(0,y.VGA)(e.b)}}parseString(t){if(!t.startsWith(this.stringPrefix))return;let e=/rgba?\(\s*(\d{1,3})\s*[\s,]\s*(\d{1,3})\s*[\s,]\s*(\d{1,3})\s*([\s,]\s*(0|1|0?\.\d+|(\d{1,3})%)\s*)?\)/i.exec(t);return e?{a:e.length>4?(0,y.M3Y)(e[d.a]):1,b:parseInt(e[d.b],10),g:parseInt(e[d.g],10),r:parseInt(e[d.r],10)}:void 0}}async function tV(t,e=!0){t.checkVersion("3.8.1"),await t.addColorManager(new tR,e)}class tI{init(t){let e=t.container,i=t.options.size.animation;i.enable&&(t.size.velocity=(t.retina.sizeAnimationSpeed??e.retina.sizeAnimationSpeed)/y.a56*e.retina.reduceFactor,i.sync||(t.size.velocity*=(0,y.G0i)()))}isEnabled(t){return!t.destroyed&&!t.spawning&&t.size.enable&&((t.size.maxLoops??0)<=0||(t.size.maxLoops??0)>0&&(t.size.loops??0)<(t.size.maxLoops??0))}reset(t){t.size.loops=0}update(t,e){this.isEnabled(t)&&(0,y.UC0)(t,t.size,!0,t.options.size.animation.destroy,e)}}async function tA(t,e=!0){t.checkVersion("3.8.1"),await t.addParticleUpdater("size",()=>Promise.resolve(new tI),e)}async function tG(t,e=!0){t.checkVersion("3.8.1"),await tx(t,!1),await t_(t,!1),await tV(t,!1),await th(t,!1),await ty(t,!1),await tg(t,!1),await tz(t,!1),await tS(t,!1),await tA(t,!1),await t.refresh(e)}async function tq(t,e=!0){t.checkVersion("3.8.1"),await t.addEasing(y.mgy.easeInQuad,t=>t**2,!1),await t.addEasing(y.mgy.easeOutQuad,t=>1-(1-t)**2,!1),await t.addEasing(y.mgy.easeInOutQuad,t=>t<.5?2*t**2:1-(-2*t+2)**2/2,!1),await t.refresh(e)}let tB='"Twemoji Mozilla", Apple Color Emoji, "Segoe UI Emoji", "Noto Color Emoji", "EmojiOne Color"';class tU{constructor(){this.validTypes=["emoji"],this._emojiShapeDict=new Map}destroy(){for(let[t,e]of this._emojiShapeDict)e instanceof ImageBitmap&&e?.close(),this._emojiShapeDict.delete(t)}draw(t){let e=t.particle.emojiDataKey;if(!e)return;let i=this._emojiShapeDict.get(e);i&&function(t,e){let{context:i,opacity:o}=t,s=i.globalAlpha;if(!e)return;let a=e.width,n=.5*a;i.globalAlpha=o,i.drawImage(e,-n,-n,a,a),i.globalAlpha=s}(t,i)}async init(t){let e=t.actualOptions,{validTypes:i}=this;if(!i.find(t=>(0,y.hnD)(t,e.particles.shape.type)))return;let o=[(0,y.AlN)(tB)],s=i.map(t=>e.particles.shape.options[t]).find(t=>!!t);s&&(0,y.wJ2)(s,t=>{t.font&&o.push((0,y.AlN)(t.font))}),await Promise.all(o)}particleDestroy(t){t.emojiDataKey=void 0}particleInit(t,e){let i;let o=e.shapeData;if(!o?.value)return;let s=(0,y.TA3)(o.value,e.randomIndexData);if(!s)return;let a="string"==typeof s?{font:o.font??tB,padding:o.padding??0,value:s}:{font:tB,padding:0,...o,...s},n=a.font,r=a.value,l=`${r}_${n}`;if(this._emojiShapeDict.has(l)){e.emojiDataKey=l;return}let c=2*a.padding,d=(0,y.W9e)(e.size.value),h=d+c,u=2*h;if("undefined"!=typeof OffscreenCanvas){let t=new OffscreenCanvas(u,u),e=t.getContext("2d");if(!e)return;e.font=`400 ${2*d}px ${n}`,e.textBaseline="middle",e.textAlign="center",e.fillText(r,h,h),i=t.transferToImageBitmap()}else{let t=document.createElement("canvas");t.width=u,t.height=u;let e=t.getContext("2d");if(!e)return;e.font=`400 ${2*d}px ${n}`,e.textBaseline="middle",e.textAlign="center",e.fillText(r,h,h),i=t}this._emojiShapeDict.set(l,i),e.emojiDataKey=l}}async function tF(t,e=!0){t.checkVersion("3.8.1"),await t.addShape(new tU,e)}function tJ(t,e,i,o,s,a){let n=e.actualOptions.interactivity.modes.attract;if(n)for(let r of e.particles.quadTree.query(s,a)){let{dx:e,dy:s,distance:a}=(0,y.vrU)(r.position,i),l=n.speed*n.factor,c=(0,y.qE8)(t.getEasing(n.easing)(1-a/o)*l,1,n.maxSpeed),d=y.Miz.create(a?e/a*c:l,a?s/a*c:l);r.position.subFrom(d)}}class tL{constructor(){this.distance=200,this.duration=.4,this.easing=y.mgy.easeOutQuad,this.factor=1,this.maxSpeed=50,this.speed=1}load(t){(0,y.kZJ)(t)||(void 0!==t.distance&&(this.distance=t.distance),void 0!==t.duration&&(this.duration=t.duration),void 0!==t.easing&&(this.easing=t.easing),void 0!==t.factor&&(this.factor=t.factor),void 0!==t.maxSpeed&&(this.maxSpeed=t.maxSpeed),void 0===t.speed||(this.speed=t.speed))}}let tZ="attract";class tj extends y.sJd{constructor(t,e){super(e),this._engine=t,e.attract||(e.attract={particles:[]}),this.handleClickMode=t=>{let i=this.container.actualOptions.interactivity.modes.attract;if(i&&t===tZ){for(let t of(e.attract||(e.attract={particles:[]}),e.attract.clicking=!0,e.attract.count=0,e.attract.particles))this.isEnabled(t)&&t.velocity.setTo(t.initialVelocity);e.attract.particles=[],e.attract.finish=!1,setTimeout(()=>{e.destroyed||(e.attract||(e.attract={particles:[]}),e.attract.clicking=!1)},i.duration*y.XuV)}}}clear(){}init(){let t=this.container,e=t.actualOptions.interactivity.modes.attract;e&&(t.retina.attractModeDistance=e.distance*t.retina.pixelRatio)}interact(){let t=this.container,e=t.actualOptions,i=t.interactivity.status===y.RbF,o=e.interactivity.events,{enable:s,mode:a}=o.onHover,{enable:n,mode:r}=o.onClick;i&&s&&(0,y.hnD)(tZ,a)?function(t,e,i){let o=e.interactivity.mouse.position,s=e.retina.attractModeDistance;s&&!(s<0)&&o&&tJ(t,e,o,s,new y.jlt(o.x,o.y,s),t=>i(t))}(this._engine,this.container,t=>this.isEnabled(t)):n&&(0,y.hnD)(tZ,r)&&function(t,e,i){e.attract||(e.attract={particles:[]});let{attract:o}=e;if(o.finish||(o.count||(o.count=0),o.count++,o.count!==e.particles.count||(o.finish=!0)),o.clicking){let o=e.interactivity.mouse.clickPosition,s=e.retina.attractModeDistance;if(!s||s<0||!o)return;tJ(t,e,o,s,new y.jlt(o.x,o.y,s),t=>i(t))}else!1===o.clicking&&(o.particles=[])}(this._engine,this.container,t=>this.isEnabled(t))}isEnabled(t){let e=this.container,i=e.actualOptions,o=e.interactivity.mouse,s=(t?.interactivity??i.interactivity).events;if((!o.position||!s.onHover.enable)&&(!o.clickPosition||!s.onClick.enable))return!1;let a=s.onHover.mode,n=s.onClick.mode;return(0,y.hnD)(tZ,a)||(0,y.hnD)(tZ,n)}loadModeOptions(t,...e){for(let i of(t.attract||(t.attract=new tL),e))t.attract.load(i?.attract)}reset(){}}async function tH(t,e=!0){t.checkVersion("3.8.1"),await t.addInteractor("externalAttract",e=>Promise.resolve(new tj(t,e)),e)}let tY=.5*Math.PI;function tW(t,e,i,o,s){for(let a of t.particles.quadTree.query(o,s))o instanceof y.jlt?(0,y.pEn)((0,y.Tgl)(a),{position:e,radius:i,mass:i**2*tY,velocity:y.Miz.origin,factor:y.Miz.origin}):o instanceof y.M_G&&(0,y.jon)(a,(0,y.AEc)(e,i))}class tX{constructor(){this.distance=200}load(t){(0,y.kZJ)(t)||void 0===t.distance||(this.distance=t.distance)}}let t$="bounce";class tN extends y.sJd{constructor(t){super(t)}clear(){}init(){let t=this.container,e=t.actualOptions.interactivity.modes.bounce;e&&(t.retina.bounceModeDistance=e.distance*t.retina.pixelRatio)}interact(){let t=this.container,e=t.actualOptions.interactivity.events,i=t.interactivity.status===y.RbF,o=e.onHover.enable,s=e.onHover.mode,a=e.onDiv;if(i&&o&&(0,y.hnD)(t$,s))!function(t,e){let i=t.retina.pixelRatio,o=t.interactivity.mouse.position,s=t.retina.bounceModeDistance;s&&!(s<0)&&o&&tW(t,o,s,new y.jlt(o.x,o.y,s+10*i),e)}(this.container,t=>this.isEnabled(t));else{var n,r;n=this.container,r=t=>this.isEnabled(t),(0,y.U6C)(t$,a,(t,e)=>(function(t,e,i,o){let s=document.querySelectorAll(e);s.length&&s.forEach(e=>{let s=t.retina.pixelRatio,a={x:(e.offsetLeft+.5*e.offsetWidth)*s,y:(e.offsetTop+.5*e.offsetHeight)*s},n=.5*e.offsetWidth*s,r=10*s,l=i.type===y.Q5V.circle?new y.jlt(a.x,a.y,n+r):new y.M_G(e.offsetLeft*s-r,e.offsetTop*s-r,e.offsetWidth*s+2*r,e.offsetHeight*s+2*r);o(a,n,l)})})(n,t,e,(t,e,i)=>tW(n,t,e,i,r)))}}isEnabled(t){let e=this.container,i=e.actualOptions,o=e.interactivity.mouse,s=(t?.interactivity??i.interactivity).events,a=s.onDiv;return!!o.position&&s.onHover.enable&&(0,y.hnD)(t$,s.onHover.mode)||(0,y.iKf)(t$,a)}loadModeOptions(t,...e){for(let i of(t.bounce||(t.bounce=new tX),e))t.bounce.load(i?.bounce)}reset(){}}async function tK(t,e=!0){t.checkVersion("3.8.1"),await t.addInteractor("externalBounce",t=>Promise.resolve(new tN(t)),e)}class tQ{constructor(){this.distance=200,this.duration=.4,this.mix=!1}load(t){if(!(0,y.kZJ)(t)){if(void 0!==t.distance&&(this.distance=t.distance),void 0!==t.duration&&(this.duration=t.duration),void 0!==t.mix&&(this.mix=t.mix),void 0!==t.opacity&&(this.opacity=t.opacity),void 0!==t.color){let e=(0,y.cyL)(this.color)?void 0:this.color;this.color=(0,y.wJ2)(t.color,t=>y.Oit.create(e,t))}void 0!==t.size&&(this.size=t.size)}}}class t0 extends tQ{constructor(){super(),this.selectors=[]}load(t){super.load(t),(0,y.kZJ)(t)||void 0===t.selectors||(this.selectors=t.selectors)}}class t1 extends tQ{load(t){super.load(t),(0,y.kZJ)(t)||(this.divs=(0,y.wJ2)(t.divs,t=>{let e=new t0;return e.load(t),e}))}}function t2(t,e,i,o){return e>=i?(0,y.qE8)(t+(e-i)*o,t,e):e<i?(0,y.qE8)(t-(i-e)*o,e,t):void 0}!function(t){t.color="color",t.opacity="opacity",t.size="size"}(h||(h={}));let t3="bubble";class t5 extends y.sJd{constructor(t,e){super(t),this._clickBubble=()=>{let t=this.container,e=t.actualOptions,i=t.interactivity.mouse.clickPosition,o=e.interactivity.modes.bubble;if(!o||!i)return;t.bubble||(t.bubble={});let s=t.retina.bubbleModeDistance;if(!s||s<0)return;let a=t.particles.quadTree.queryCircle(i,s,t=>this.isEnabled(t)),{bubble:n}=t;for(let e of a){if(!n.clicking)continue;e.bubble.inRange=!n.durationEnd;let a=e.getPosition(),r=(0,y.YfF)(a,i),l=(new Date().getTime()-(t.interactivity.mouse.clickTime??0))/y.XuV;l>o.duration&&(n.durationEnd=!0),l>2*o.duration&&(n.clicking=!1,n.durationEnd=!1);let c={bubbleObj:{optValue:t.retina.bubbleModeSize,value:e.bubble.radius},particlesObj:{optValue:(0,y.W9e)(e.options.size.value)*t.retina.pixelRatio,value:e.size.value},type:h.size};this._process(e,r,l,c);let d={bubbleObj:{optValue:o.opacity,value:e.bubble.opacity},particlesObj:{optValue:(0,y.W9e)(e.options.opacity.value),value:e.opacity?.value??1},type:h.opacity};this._process(e,r,l,d),!n.durationEnd&&r<=s?this._hoverBubbleColor(e,r):delete e.bubble.color}},this._hoverBubble=()=>{let t=this.container,e=t.interactivity.mouse.position,i=t.retina.bubbleModeDistance;if(i&&!(i<0)&&e)for(let o of t.particles.quadTree.queryCircle(e,i,t=>this.isEnabled(t))){o.bubble.inRange=!0;let s=o.getPosition(),a=(0,y.YfF)(s,e),n=1-a/i;a<=i?n>=0&&t.interactivity.status===y.RbF&&(this._hoverBubbleSize(o,n),this._hoverBubbleOpacity(o,n),this._hoverBubbleColor(o,n)):this.reset(o),t.interactivity.status===y.Z04&&this.reset(o)}},this._hoverBubbleColor=(t,e,i)=>{let o=this.container.actualOptions,s=i??o.interactivity.modes.bubble;if(s){if(!t.bubble.finalColor){let e=s.color;if(!e)return;let i=(0,y.TA3)(e);t.bubble.finalColor=(0,y.R5R)(this._engine,i)}if(t.bubble.finalColor){if(s.mix){t.bubble.color=void 0;let i=t.getFillColor();t.bubble.color=i?(0,y.K6u)((0,y.EYT)(i,t.bubble.finalColor,1-e,e)):t.bubble.finalColor}else t.bubble.color=t.bubble.finalColor}}},this._hoverBubbleOpacity=(t,e,i)=>{let o=this.container.actualOptions,s=i?.opacity??o.interactivity.modes.bubble?.opacity;if(!s)return;let a=t.options.opacity.value,n=t2(t.opacity?.value??1,s,(0,y.W9e)(a),e);void 0!==n&&(t.bubble.opacity=n)},this._hoverBubbleSize=(t,e,i)=>{let o=this.container,s=i?.size?i.size*o.retina.pixelRatio:o.retina.bubbleModeSize;if(void 0===s)return;let a=(0,y.W9e)(t.options.size.value)*o.retina.pixelRatio,n=t2(t.size.value,s,a,e);void 0!==n&&(t.bubble.radius=n)},this._process=(t,e,i,o)=>{let s=this.container,a=o.bubbleObj.optValue,n=s.actualOptions.interactivity.modes.bubble;if(!n||void 0===a)return;let r=n.duration,l=s.retina.bubbleModeDistance,c=o.particlesObj.optValue,d=o.bubbleObj.value,u=o.particlesObj.value??0,p=o.type;if(l&&!(l<0)&&a!==c){if(s.bubble||(s.bubble={}),s.bubble.durationEnd)d&&(p===h.size&&delete t.bubble.radius,p===h.opacity&&delete t.bubble.opacity);else if(e<=l){if((d??u)!==a){let e=u-i*(u-a)/r;p===h.size&&(t.bubble.radius=e),p===h.opacity&&(t.bubble.opacity=e)}}else p===h.size&&delete t.bubble.radius,p===h.opacity&&delete t.bubble.opacity}},this._singleSelectorHover=(t,e,i)=>{let o=this.container,s=document.querySelectorAll(e),a=o.actualOptions.interactivity.modes.bubble;a&&s.length&&s.forEach(e=>{let s=o.retina.pixelRatio,n={x:(e.offsetLeft+.5*e.offsetWidth)*s,y:(e.offsetTop+.5*e.offsetHeight)*s},r=.5*e.offsetWidth*s,l=i.type===y.Q5V.circle?new y.jlt(n.x,n.y,r):new y.M_G(e.offsetLeft*s,e.offsetTop*s,e.offsetWidth*s,e.offsetHeight*s);for(let i of o.particles.quadTree.query(l,t=>this.isEnabled(t))){if(!l.contains(i.getPosition()))continue;i.bubble.inRange=!0;let o=a.divs,s=(0,y.NVT)(o,e);i.bubble.div&&i.bubble.div===e||(this.clear(i,t,!0),i.bubble.div=e),this._hoverBubbleSize(i,1,s),this._hoverBubbleOpacity(i,1,s),this._hoverBubbleColor(i,1,s)}})},this._engine=e,t.bubble||(t.bubble={}),this.handleClickMode=e=>{e===t3&&(t.bubble||(t.bubble={}),t.bubble.clicking=!0)}}clear(t,e,i){(!t.bubble.inRange||i)&&(delete t.bubble.div,delete t.bubble.opacity,delete t.bubble.radius,delete t.bubble.color)}init(){let t=this.container,e=t.actualOptions.interactivity.modes.bubble;e&&(t.retina.bubbleModeDistance=e.distance*t.retina.pixelRatio,void 0!==e.size&&(t.retina.bubbleModeSize=e.size*t.retina.pixelRatio))}interact(t){let e=this.container.actualOptions.interactivity.events,i=e.onHover,o=e.onClick,s=i.enable,a=i.mode,n=o.enable,r=o.mode,l=e.onDiv;s&&(0,y.hnD)(t3,a)?this._hoverBubble():n&&(0,y.hnD)(t3,r)?this._clickBubble():(0,y.U6C)(t3,l,(e,i)=>this._singleSelectorHover(t,e,i))}isEnabled(t){let e=this.container,i=e.actualOptions,o=e.interactivity.mouse,{onClick:s,onDiv:a,onHover:n}=(t?.interactivity??i.interactivity).events,r=(0,y.iKf)(t3,a);return(!!r||!!n.enable&&!!o.position||!!s.enable&&!!o.clickPosition)&&((0,y.hnD)(t3,n.mode)||(0,y.hnD)(t3,s.mode)||r)}loadModeOptions(t,...e){for(let i of(t.bubble||(t.bubble=new t1),e))t.bubble.load(i?.bubble)}reset(t){t.bubble.inRange=!1}}async function t8(t,e=!0){t.checkVersion("3.8.1"),await t.addInteractor("externalBubble",e=>Promise.resolve(new t5(e,t)),e)}class t4{constructor(){this.opacity=.5}load(t){(0,y.kZJ)(t)||void 0===t.opacity||(this.opacity=t.opacity)}}class t6{constructor(){this.distance=80,this.links=new t4,this.radius=60}load(t){(0,y.kZJ)(t)||(void 0!==t.distance&&(this.distance=t.distance),this.links.load(t.links),void 0===t.radius||(this.radius=t.radius))}}class t9 extends y.sJd{constructor(t){super(t)}clear(){}init(){let t=this.container,e=t.actualOptions.interactivity.modes.connect;e&&(t.retina.connectModeDistance=e.distance*t.retina.pixelRatio,t.retina.connectModeRadius=e.radius*t.retina.pixelRatio)}interact(){let t=this.container;if(t.actualOptions.interactivity.events.onHover.enable&&"pointermove"===t.interactivity.status){let e=t.interactivity.mouse.position,{connectModeDistance:i,connectModeRadius:o}=t.retina;if(!i||i<0||!o||o<0||!e)return;let s=Math.abs(o),a=t.particles.quadTree.queryCircle(e,s,t=>this.isEnabled(t));a.forEach((e,o)=>{let s=e.getPosition();for(let n of a.slice(o+1)){let o=n.getPosition(),a=Math.abs(i),r=Math.abs(s.x-o.x),l=Math.abs(s.y-o.y);r<a&&l<a&&function(t,e,i){t.canvas.draw(o=>{var s;let a=function(t,e,i,o){let s=t.actualOptions.interactivity.modes.connect;if(s)return function(t,e,i,o){let s=Math.floor(i.getRadius()/e.getRadius()),a=e.getFillColor(),n=i.getFillColor();if(!a||!n)return;let r=e.getPosition(),l=i.getPosition(),c=(0,y.EYT)(a,n,e.getRadius(),i.getRadius()),d=t.createLinearGradient(r.x,r.y,l.x,l.y);return d.addColorStop(0,(0,y.LCf)(a,o)),d.addColorStop((0,y.qE8)(s,0,1),(0,y.xxz)(c,o)),d.addColorStop(1,(0,y.LCf)(n,o)),d}(e,i,o,s.links.opacity)}(t,o,e,i);if(!a)return;let n=e.getPosition(),r=i.getPosition();s=e.retina.linksWidth??0,(0,y.V6E)(o,n,r),o.lineWidth=s,o.strokeStyle=a,o.stroke()})}(t,e,n)}})}}isEnabled(t){let e=this.container,i=e.interactivity.mouse,o=(t?.interactivity??e.actualOptions.interactivity).events;return!!o.onHover.enable&&!!i.position&&(0,y.hnD)("connect",o.onHover.mode)}loadModeOptions(t,...e){for(let i of(t.connect||(t.connect=new t6),e))t.connect.load(i?.connect)}reset(){}}async function t7(t,e=!0){t.checkVersion("3.8.1"),await t.addInteractor("externalConnect",t=>Promise.resolve(new t9(t)),e)}class et{constructor(){this.blink=!1,this.consent=!1,this.opacity=1}load(t){(0,y.kZJ)(t)||(void 0!==t.blink&&(this.blink=t.blink),void 0!==t.color&&(this.color=y.Oit.create(this.color,t.color)),void 0!==t.consent&&(this.consent=t.consent),void 0===t.opacity||(this.opacity=t.opacity))}}class ee{constructor(){this.distance=100,this.links=new et}load(t){(0,y.kZJ)(t)||(void 0!==t.distance&&(this.distance=t.distance),this.links.load(t.links))}}class ei extends y.sJd{constructor(t,e){super(t),this._engine=e}clear(){}init(){let t=this.container,e=t.actualOptions.interactivity.modes.grab;e&&(t.retina.grabModeDistance=e.distance*t.retina.pixelRatio)}interact(){let t=this.container,e=t.actualOptions.interactivity;if(!e.modes.grab||!e.events.onHover.enable||t.interactivity.status!==y.RbF)return;let i=t.interactivity.mouse.position;if(!i)return;let o=t.retina.grabModeDistance;if(o&&!(o<0))for(let s of t.particles.quadTree.queryCircle(i,o,t=>this.isEnabled(t))){let a=s.getPosition(),n=(0,y.YfF)(a,i);if(n>o)continue;let r=e.modes.grab.links,l=r.opacity,c=l-n*l/o;if(c<=0)continue;let d=r.color??s.options.links?.color;if(!t.particles.grabLineColor&&d){let i=e.modes.grab.links;t.particles.grabLineColor=(0,y.PGG)(this._engine,d,i.blink,i.consent)}let h=(0,y._hh)(s,void 0,t.particles.grabLineColor);h&&function(t,e,i,o,s){t.canvas.draw(t=>{var a;let n=e.getPosition();a=e.retina.linksWidth??0,(0,y.V6E)(t,n,s),t.strokeStyle=(0,y.xxz)(i,o),t.lineWidth=a,t.stroke()})}(t,s,h,c,i)}}isEnabled(t){let e=this.container,i=e.interactivity.mouse,o=(t?.interactivity??e.actualOptions.interactivity).events;return o.onHover.enable&&!!i.position&&(0,y.hnD)("grab",o.onHover.mode)}loadModeOptions(t,...e){for(let i of(t.grab||(t.grab=new ee),e))t.grab.load(i?.grab)}reset(){}}async function eo(t,e=!0){t.checkVersion("3.8.1"),await t.addInteractor("externalGrab",e=>Promise.resolve(new ei(e,t)),e)}class es extends y.sJd{constructor(t){super(t),this.handleClickMode=t=>{if("pause"!==t)return;let e=this.container;e.animationStatus?e.pause():e.play()}}clear(){}init(){}interact(){}isEnabled(){return!0}reset(){}}async function ea(t,e=!0){t.checkVersion("3.8.1"),await t.addInteractor("externalPause",t=>Promise.resolve(new es(t)),e)}class en{constructor(){this.default=!0,this.groups=[],this.quantity=4}load(t){if((0,y.kZJ)(t))return;void 0!==t.default&&(this.default=t.default),void 0!==t.groups&&(this.groups=t.groups.map(t=>t)),this.groups.length||(this.default=!0);let e=t.quantity;void 0!==e&&(this.quantity=(0,y.DT4)(e))}}class er extends y.sJd{constructor(t){super(t),this.handleClickMode=t=>{if("push"!==t)return;let e=this.container,i=e.actualOptions.interactivity.modes.push;if(!i)return;let o=(0,y.VGA)(i.quantity);if(o<=0)return;let s=(0,y.Vh1)([void 0,...i.groups]),a=void 0!==s?e.actualOptions.particles.groups[s]:void 0;e.particles.push(o,e.interactivity.mouse,a,s)}}clear(){}init(){}interact(){}isEnabled(){return!0}loadModeOptions(t,...e){for(let i of(t.push||(t.push=new en),e))t.push.load(i?.push)}reset(){}}async function el(t,e=!0){t.checkVersion("3.8.1"),await t.addInteractor("externalPush",t=>Promise.resolve(new er(t)),e)}class ec{constructor(){this.quantity=2}load(t){if((0,y.kZJ)(t))return;let e=t.quantity;void 0!==e&&(this.quantity=(0,y.DT4)(e))}}class ed extends y.sJd{constructor(t){super(t),this.handleClickMode=t=>{let e=this.container,i=e.actualOptions;if(!i.interactivity.modes.remove||"remove"!==t)return;let o=(0,y.VGA)(i.interactivity.modes.remove.quantity);e.particles.removeQuantity(o)}}clear(){}init(){}interact(){}isEnabled(){return!0}loadModeOptions(t,...e){for(let i of(t.remove||(t.remove=new ec),e))t.remove.load(i?.remove)}reset(){}}async function eh(t,e=!0){t.checkVersion("3.8.1"),await t.addInteractor("externalRemove",t=>Promise.resolve(new ed(t)),e)}class eu{constructor(){this.distance=200,this.duration=.4,this.factor=100,this.speed=1,this.maxSpeed=50,this.easing=y.mgy.easeOutQuad}load(t){(0,y.kZJ)(t)||(void 0!==t.distance&&(this.distance=t.distance),void 0!==t.duration&&(this.duration=t.duration),void 0!==t.easing&&(this.easing=t.easing),void 0!==t.factor&&(this.factor=t.factor),void 0!==t.speed&&(this.speed=t.speed),void 0===t.maxSpeed||(this.maxSpeed=t.maxSpeed))}}class ep extends eu{constructor(){super(),this.selectors=[]}load(t){super.load(t),(0,y.kZJ)(t)||void 0===t.selectors||(this.selectors=t.selectors)}}class ef extends eu{load(t){super.load(t),(0,y.kZJ)(t)||(this.divs=(0,y.wJ2)(t.divs,t=>{let e=new ep;return e.load(t),e}))}}let ey="repulse";class ev extends y.sJd{constructor(t,e){super(e),this._clickRepulse=()=>{let t=this.container,e=t.actualOptions.interactivity.modes.repulse;if(!e)return;let i=t.repulse??{particles:[]};if(i.finish||(i.count||(i.count=0),i.count++,i.count!==t.particles.count||(i.finish=!0)),i.clicking){let o=t.retina.repulseModeDistance;if(!o||o<0)return;let s=Math.pow(o/6,3),a=t.interactivity.mouse.clickPosition;if(void 0===a)return;let n=new y.jlt(a.x,a.y,s);for(let o of t.particles.quadTree.query(n,t=>this.isEnabled(t))){let{dx:t,dy:n,distance:r}=(0,y.vrU)(a,o.position),l=r**2,c=-s*e.speed/l;if(l<=s){i.particles.push(o);let e=y.Miz.create(t,n);e.length=c,o.velocity.setTo(e)}}}else if(!1===i.clicking){for(let t of i.particles)t.velocity.setTo(t.initialVelocity);i.particles=[]}},this._hoverRepulse=()=>{let t=this.container,e=t.interactivity.mouse.position,i=t.retina.repulseModeDistance;i&&!(i<0)&&e&&this._processRepulse(e,i,new y.jlt(e.x,e.y,i))},this._processRepulse=(t,e,i,o)=>{let s=this.container,a=s.particles.quadTree.query(i,t=>this.isEnabled(t)),n=s.actualOptions.interactivity.modes.repulse;if(!n)return;let{easing:r,speed:l,factor:c,maxSpeed:d}=n,h=this._engine.getEasing(r),u=(o?.speed??l)*c;for(let i of a){let{dx:o,dy:s,distance:a}=(0,y.vrU)(i.position,t),n=(0,y.qE8)(h(1-a/e)*u,0,d),r=y.Miz.create(a?o/a*n:u,a?s/a*n:u);i.position.addTo(r)}},this._singleSelectorRepulse=(t,e)=>{let i=this.container,o=i.actualOptions.interactivity.modes.repulse;if(!o)return;let s=document.querySelectorAll(t);s.length&&s.forEach(t=>{let s=i.retina.pixelRatio,a={x:(t.offsetLeft+.5*t.offsetWidth)*s,y:(t.offsetTop+.5*t.offsetHeight)*s},n=.5*t.offsetWidth*s,r=e.type===y.Q5V.circle?new y.jlt(a.x,a.y,n):new y.M_G(t.offsetLeft*s,t.offsetTop*s,t.offsetWidth*s,t.offsetHeight*s),l=o.divs,c=(0,y.NVT)(l,t);this._processRepulse(a,n,r,c)})},this._engine=t,e.repulse||(e.repulse={particles:[]}),this.handleClickMode=t=>{let i=this.container.actualOptions.interactivity.modes.repulse;if(!i||t!==ey)return;e.repulse||(e.repulse={particles:[]});let o=e.repulse;for(let t of(o.clicking=!0,o.count=0,e.repulse.particles))this.isEnabled(t)&&t.velocity.setTo(t.initialVelocity);o.particles=[],o.finish=!1,setTimeout(()=>{e.destroyed||(o.clicking=!1)},i.duration*y.XuV)}}clear(){}init(){let t=this.container,e=t.actualOptions.interactivity.modes.repulse;e&&(t.retina.repulseModeDistance=e.distance*t.retina.pixelRatio)}interact(){let t=this.container,e=t.actualOptions,i=t.interactivity.status===y.RbF,o=e.interactivity.events,s=o.onHover,a=s.enable,n=s.mode,r=o.onClick,l=r.enable,c=r.mode,d=o.onDiv;i&&a&&(0,y.hnD)(ey,n)?this._hoverRepulse():l&&(0,y.hnD)(ey,c)?this._clickRepulse():(0,y.U6C)(ey,d,(t,e)=>this._singleSelectorRepulse(t,e))}isEnabled(t){let e=this.container,i=e.actualOptions,o=e.interactivity.mouse,s=(t?.interactivity??i.interactivity).events,a=s.onDiv,n=s.onHover,r=s.onClick,l=(0,y.iKf)(ey,a);if(!(l||n.enable&&o.position||r.enable&&o.clickPosition))return!1;let c=n.mode,d=r.mode;return(0,y.hnD)(ey,c)||(0,y.hnD)(ey,d)||l}loadModeOptions(t,...e){for(let i of(t.repulse||(t.repulse=new ef),e))t.repulse.load(i?.repulse)}reset(){}}async function eg(t,e=!0){t.checkVersion("3.8.1"),await t.addInteractor("externalRepulse",e=>Promise.resolve(new ev(t,e)),e)}class eb{constructor(){this.factor=3,this.radius=200}load(t){(0,y.kZJ)(t)||(void 0!==t.factor&&(this.factor=t.factor),void 0===t.radius||(this.radius=t.radius))}}class em extends y.sJd{constructor(t){super(t)}clear(t,e,i){(!t.slow.inRange||i)&&(t.slow.factor=1)}init(){let t=this.container,e=t.actualOptions.interactivity.modes.slow;e&&(t.retina.slowModeRadius=e.radius*t.retina.pixelRatio)}interact(){}isEnabled(t){let e=this.container,i=e.interactivity.mouse,o=(t?.interactivity??e.actualOptions.interactivity).events;return o.onHover.enable&&!!i.position&&(0,y.hnD)("slow",o.onHover.mode)}loadModeOptions(t,...e){for(let i of(t.slow||(t.slow=new eb),e))t.slow.load(i?.slow)}reset(t){t.slow.inRange=!1;let e=this.container,i=e.actualOptions,o=e.interactivity.mouse.position,s=e.retina.slowModeRadius,a=i.interactivity.modes.slow;if(!a||!s||s<0||!o)return;let n=t.getPosition(),r=(0,y.YfF)(o,n),l=a.factor,{slow:c}=t;r>s||(c.inRange=!0,c.factor=r/s/l)}}async function ew(t,e=!0){t.checkVersion("3.8.1"),await t.addInteractor("externalSlow",t=>Promise.resolve(new em(t)),e)}let ex=/(#(?:[0-9a-f]{2}){2,4}|(#[0-9a-f]{3})|(rgb|hsl)a?\((-?\d+%?[,\s]+){2,3}\s*[\d.]+%?\))|currentcolor/gi;async function ek(t){return new Promise(e=>{t.loading=!0;let i=new Image;t.element=i,i.addEventListener("load",()=>{t.loading=!1,e()}),i.addEventListener("error",()=>{t.element=void 0,t.error=!0,t.loading=!1,(0,y.tZQ)().error(`${y.dIx} loading image: ${t.source}`),e()}),i.src=t.source})}async function e_(t){if("svg"!==t.type){await ek(t);return}t.loading=!0;let e=await fetch(t.source);e.ok?t.svgData=await e.text():((0,y.tZQ)().error(`${y.dIx} Image not found`),t.error=!0),t.loading=!1}let eC=[0,4,2,1],ez=[8,8,4,2];class eP{constructor(t){this.pos=0,this.data=new Uint8ClampedArray(t)}getString(t){let e=this.data.slice(this.pos,this.pos+t);return this.pos+=e.length,e.reduce((t,e)=>t+String.fromCharCode(e),"")}nextByte(){return this.data[this.pos++]}nextTwoBytes(){return this.pos+=2,this.data[this.pos-2]+(this.data[this.pos-1]<<8)}readSubBlocks(){let t="",e=0;do{e=this.data[this.pos++];for(let i=e;--i>=0;t+=String.fromCharCode(this.data[this.pos++]));}while(0!==e);return t}readSubBlocksBin(){let t=this.data[this.pos],e=0;for(let i=0;0!==t;i+=t+1,t=this.data[this.pos+i])e+=t;let i=new Uint8Array(e);t=this.data[this.pos++];for(let e=0;0!==t;t=this.data[this.pos++])for(let o=t;--o>=0;i[e++]=this.data[this.pos++]);return i}skipSubBlocks(){for(;0!==this.data[this.pos];this.pos+=this.data[this.pos]+1);this.pos++}}!function(t){t[t.Replace=0]="Replace",t[t.Combine=1]="Combine",t[t.RestoreBackground=2]="RestoreBackground",t[t.RestorePrevious=3]="RestorePrevious",t[t.UndefinedA=4]="UndefinedA",t[t.UndefinedB=5]="UndefinedB",t[t.UndefinedC=6]="UndefinedC",t[t.UndefinedD=7]="UndefinedD"}(u||(u={})),function(t){t[t.Extension=33]="Extension",t[t.ApplicationExtension=255]="ApplicationExtension",t[t.GraphicsControlExtension=249]="GraphicsControlExtension",t[t.PlainTextExtension=1]="PlainTextExtension",t[t.CommentExtension=254]="CommentExtension",t[t.Image=44]="Image",t[t.EndOfFile=59]="EndOfFile"}(p||(p={}));let eT={x:0,y:0};function eD(t,e){let i=[];for(let o=0;o<e;o++)i.push({r:t.data[t.pos],g:t.data[t.pos+1],b:t.data[t.pos+2]}),t.pos+=3;return i}async function eM(t,e,i,o,s,a){let n=e.frames[o(!0)];n.left=t.nextTwoBytes(),n.top=t.nextTwoBytes(),n.width=t.nextTwoBytes(),n.height=t.nextTwoBytes();let r=t.nextByte(),l=(128&r)==128;n.sortFlag=(32&r)==32,n.reserved=(24&r)>>>3,l&&(n.localColorTable=eD(t,1<<(7&r)+1));let c=t=>{let{r:o,g:a,b:r}=(l?n.localColorTable:e.globalColorTable)[t];return t!==s(null)?{r:o,g:a,b:r,a:255}:{r:o,g:a,b:r,a:i?~~((o+a+r)/3):0}},d=(()=>{try{return new ImageData(n.width,n.height,{colorSpace:"srgb"})}catch(t){if(t instanceof DOMException&&"IndexSizeError"===t.name)return null;throw t}})();if(null==d)throw EvalError("GIF frame size is to large");let h=t.nextByte(),u=t.readSubBlocksBin(),p=1<<h,f=(t,e)=>{let i=t>>>3,o=7&t;return(u[i]+(u[i+1]<<8)+(u[i+2]<<16)&(1<<e)-1<<o)>>>o};if((64&r)==64){for(let i=0,s=h+1,r=0,l=[[0]],u=0;u<4;u++){if(eC[u]<n.height){let t=0,e=0,o=!1;for(;!o;){let a=i;if(i=f(r,s),r+=s+1,i===p){s=h+1,l.length=p+2;for(let t=0;t<l.length;t++)l[t]=t<p?[t]:[]}else{for(let o of(i>=l.length?l.push(l[a].concat(l[a][0])):a!==p&&l.push(l[a].concat(l[i][0])),l[i])){let{r:i,g:s,b:a,a:r}=c(o);d.data.set([i,s,a,r],eC[u]*n.width+ez[u]*e+t%(4*n.width)),t+=4}l.length===1<<s&&s<12&&s++}t===4*n.width*(e+1)&&(e++,eC[u]+ez[u]*e>=n.height&&(o=!0))}}a?.(t.pos/(t.data.length-1),o(!1)+1,d,{x:n.left,y:n.top},{width:e.width,height:e.height})}n.image=d,n.bitmap=await createImageBitmap(d)}else{let i=0,s=h+1,r=0,l=-4,u=!1,y=[[0]];for(;!u;){let t=i;if(i=f(r,s),r+=s,i===p){s=h+1,y.length=p+2;for(let t=0;t<y.length;t++)y[t]=t<p?[t]:[]}else{if(i===p+1){u=!0;break}for(let e of(i>=y.length?y.push(y[t].concat(y[t][0])):t!==p&&y.push(y[t].concat(y[i][0])),y[i])){let{r:t,g:i,b:o,a:s}=c(e);d.data.set([t,i,o,s],l+=4)}y.length>=1<<s&&s<12&&s++}}n.image=d,n.bitmap=await createImageBitmap(d),a?.((t.pos+1)/t.data.length,o(!1)+1,n.image,{x:n.left,y:n.top},{width:e.width,height:e.height})}}async function eE(t,e,i,o,s,a){switch(t.nextByte()){case p.EndOfFile:return!0;case p.Image:await eM(t,e,i,o,s,a);break;case p.Extension:!function(t,e,i,o){switch(t.nextByte()){case p.GraphicsControlExtension:{let s=e.frames[i(!1)];t.pos++;let a=t.nextByte();s.GCreserved=(224&a)>>>5,s.disposalMethod=(28&a)>>>2,s.userInputDelayFlag=(2&a)==2,s.delayTime=10*t.nextTwoBytes();let n=t.nextByte();(1&a)==1&&o(n),t.pos++;break}case p.ApplicationExtension:{t.pos++;let i={identifier:t.getString(8),authenticationCode:t.getString(3),data:t.readSubBlocksBin()};e.applicationExtensions.push(i);break}case p.CommentExtension:e.comments.push([i(!1),t.readSubBlocks()]);break;case p.PlainTextExtension:if(0===e.globalColorTable.length)throw EvalError("plain text extension without global color table");t.pos++,e.frames[i(!1)].plainTextData={left:t.nextTwoBytes(),top:t.nextTwoBytes(),width:t.nextTwoBytes(),height:t.nextTwoBytes(),charSize:{width:t.nextTwoBytes(),height:t.nextTwoBytes()},foregroundColor:t.nextByte(),backgroundColor:t.nextByte(),text:t.readSubBlocks()};break;default:t.skipSubBlocks()}}(t,e,o,s);break;default:throw EvalError("undefined block found")}return!1}async function eO(t,e,i){i||(i=!1);let o=await fetch(t);if(!o.ok&&404===o.status)throw EvalError("file not found");let s=await o.arrayBuffer(),a={width:0,height:0,totalTime:0,colorRes:0,pixelAspectRatio:0,frames:[],sortFlag:!1,globalColorTable:[],backgroundImage:new ImageData(1,1,{colorSpace:"srgb"}),comments:[],applicationExtensions:[]},n=new eP(new Uint8ClampedArray(s));if("GIF89a"!==n.getString(6))throw Error("not a supported GIF file");a.width=n.nextTwoBytes(),a.height=n.nextTwoBytes();let r=n.nextByte(),l=(128&r)==128;a.colorRes=(112&r)>>>4,a.sortFlag=(8&r)==8;let c=n.nextByte();a.pixelAspectRatio=n.nextByte(),0!==a.pixelAspectRatio&&(a.pixelAspectRatio=(a.pixelAspectRatio+15)/64),l&&(a.globalColorTable=eD(n,1<<(7&r)+1));let d=(()=>{try{return new ImageData(a.width,a.height,{colorSpace:"srgb"})}catch(t){if(t instanceof DOMException&&"IndexSizeError"===t.name)return null;throw t}})();if(null==d)throw Error("GIF frame size is to large");let{r:h,g:p,b:f}=a.globalColorTable[c];d.data.set(l?[h,p,f,255]:[0,0,0,0]);for(let t=4;t<d.data.length;t*=2)d.data.copyWithin(t,0,t);a.backgroundImage=d;let y=-1,v=!0,g=-1,b=t=>(t&&(v=!0),y),m=t=>(null!=t&&(g=t),g);try{do v&&(a.frames.push({left:0,top:0,width:0,height:0,disposalMethod:u.Replace,image:new ImageData(1,1,{colorSpace:"srgb"}),plainTextData:null,userInputDelayFlag:!1,delayTime:0,sortFlag:!1,localColorTable:[],reserved:0,GCreserved:0}),y++,g=-1,v=!1);while(!await eE(n,a,i,b,m,e));for(let t of(a.frames.length--,a.frames)){if(t.userInputDelayFlag&&0===t.delayTime){a.totalTime=1/0;break}a.totalTime+=t.delayTime}return a}catch(t){if(t instanceof EvalError)throw Error(`error while parsing frame ${y} "${t.message}"`);throw t}}async function eS(t){if("gif"!==t.type){await ek(t);return}t.loading=!0;try{t.gifData=await eO(t.source),t.gifLoopCount=function(t){for(let e of t.applicationExtensions)if(e.identifier+e.authenticationCode==="NETSCAPE2.0")return e.data[1]+(e.data[2]<<8);return NaN}(t.gifData)??0,t.gifLoopCount||(t.gifLoopCount=1/0)}catch{t.error=!0}t.loading=!1}class eR{constructor(t){this.validTypes=["image","images"],this.loadImageShape=async t=>{if(!this._engine.loadImage)throw Error(`${y.dIx} image shape not initialized`);await this._engine.loadImage({gif:t.gif,name:t.name,replaceColor:t.replaceColor??!1,src:t.src})},this._engine=t}addImage(t){this._engine.images||(this._engine.images=[]),this._engine.images.push(t)}draw(t){let{context:e,radius:i,particle:o,opacity:s}=t,a=o.image,n=a?.element;if(a){if(e.globalAlpha=s,a.gif&&a.gifData)!function(t){let{context:e,radius:i,particle:o,delta:s}=t,a=o.image;if(!a?.gifData||!a.gif)return;let n=new OffscreenCanvas(a.gifData.width,a.gifData.height),r=n.getContext("2d");if(!r)throw Error("could not create offscreen canvas context");r.imageSmoothingQuality="low",r.imageSmoothingEnabled=!1,r.clearRect(eT.x,eT.y,n.width,n.height),void 0===o.gifLoopCount&&(o.gifLoopCount=a.gifLoopCount??0);let l=o.gifFrame??0,c={x:-(.5*a.gifData.width),y:-(.5*a.gifData.height)},d=a.gifData.frames[l];if(void 0===o.gifTime&&(o.gifTime=0),d.bitmap){switch(e.scale(i/a.gifData.width,i/a.gifData.height),d.disposalMethod){case u.UndefinedA:case u.UndefinedB:case u.UndefinedC:case u.UndefinedD:case u.Replace:r.drawImage(d.bitmap,d.left,d.top),e.drawImage(n,c.x,c.y),r.clearRect(eT.x,eT.y,n.width,n.height);break;case u.Combine:r.drawImage(d.bitmap,d.left,d.top),e.drawImage(n,c.x,c.y);break;case u.RestoreBackground:r.drawImage(d.bitmap,d.left,d.top),e.drawImage(n,c.x,c.y),r.clearRect(eT.x,eT.y,n.width,n.height),a.gifData.globalColorTable.length?r.putImageData(a.gifData.backgroundImage,c.x,c.y):r.putImageData(a.gifData.frames[0].image,c.x+d.left,c.y+d.top);break;case u.RestorePrevious:{let t=r.getImageData(eT.x,eT.y,n.width,n.height);r.drawImage(d.bitmap,d.left,d.top),e.drawImage(n,c.x,c.y),r.clearRect(eT.x,eT.y,n.width,n.height),r.putImageData(t,eT.x,eT.y)}}if(o.gifTime+=s.value,o.gifTime>d.delayTime){if(o.gifTime-=d.delayTime,++l>=a.gifData.frames.length){if(--o.gifLoopCount<=0)return;l=0,r.clearRect(eT.x,eT.y,n.width,n.height)}o.gifFrame=l}e.scale(a.gifData.width/i,a.gifData.height/i)}}(t);else if(n){let t=a.ratio,o={x:-i,y:-i},s=2*i;e.drawImage(n,o.x,o.y,s,s/t)}e.globalAlpha=1}}getSidesCount(){return 12}async init(t){let e=t.actualOptions;if(e.preload&&this._engine.loadImage)for(let t of e.preload)await this._engine.loadImage(t)}loadShape(t){if("image"!==t.shape&&"images"!==t.shape)return;this._engine.images||(this._engine.images=[]);let e=t.shapeData;e&&(this._engine.images.find(t=>t.name===e.name||t.source===e.src)||this.loadImageShape(e).then(()=>{this.loadShape(t)}))}particleInit(t,e){if("image"!==e.shape&&"images"!==e.shape)return;this._engine.images||(this._engine.images=[]);let i=this._engine.images,o=e.shapeData;if(!o)return;let s=e.getFillColor(),a=i.find(t=>t.name===o.name||t.source===o.src);if(!a)return;let n=o.replaceColor??a.replaceColor;if(a.loading){setTimeout(()=>{this.particleInit(t,e)});return}(async()=>{let t;(t=a.svgData&&s?await function(t,e,i,o){let s=function(t,e,i){let{svgData:o}=t;if(!o)return"";let s=(0,y.LCf)(e,i);if(o.includes("fill"))return o.replace(ex,()=>s);let a=o.indexOf(">");return`${o.substring(0,a)} fill="${s}"${o.substring(a)}`}(t,i,o.opacity?.value??1),a={color:i,gif:e.gif,data:{...t,svgData:s},loaded:!1,ratio:e.width/e.height,replaceColor:e.replaceColor,source:e.src};return new Promise(e=>{let i=new Blob([s],{type:"image/svg+xml"}),o=URL||window.URL||window.webkitURL||window,n=o.createObjectURL(i),r=new Image;r.addEventListener("load",()=>{a.loaded=!0,a.element=r,e(a),o.revokeObjectURL(n)});let l=async()=>{o.revokeObjectURL(n);let i={...t,error:!1,loading:!0};await ek(i),a.loaded=!0,a.element=i.element,e(a)};r.addEventListener("error",()=>void l()),r.src=n})}(a,o,s,e):{color:s,data:a,element:a.element,gif:a.gif,gifData:a.gifData,gifLoopCount:a.gifLoopCount,loaded:!0,ratio:o.width&&o.height?o.width/o.height:a.ratio??1,replaceColor:n,source:o.src}).ratio||(t.ratio=1);let i=o.fill??e.shapeFill,r=o.close??e.shapeClose;e.image=t,e.shapeFill=i,e.shapeClose=r})()}}class eV{constructor(){this.src="",this.gif=!1}load(t){(0,y.kZJ)(t)||(void 0!==t.gif&&(this.gif=t.gif),void 0!==t.height&&(this.height=t.height),void 0!==t.name&&(this.name=t.name),void 0!==t.replaceColor&&(this.replaceColor=t.replaceColor),void 0!==t.src&&(this.src=t.src),void 0===t.width||(this.width=t.width))}}class eI{constructor(t){this.id="imagePreloader",this._engine=t}async getPlugin(){return await Promise.resolve(),{}}loadOptions(t,e){if(!e?.preload)return;t.preload||(t.preload=[]);let i=t.preload;for(let t of e.preload){let e=i.find(e=>e.name===t.name||e.src===t.src);if(e)e.load(t);else{let e=new eV;e.load(t),i.push(e)}}}needsPlugin(){return!0}}async function eA(t,e=!0){t.checkVersion("3.8.1"),t.loadImage||(t.loadImage=async e=>{if(!e.name&&!e.src)throw Error(`${y.dIx} no image source provided`);if(t.images||(t.images=[]),!t.images.find(t=>t.name===e.name||t.source===e.src))try{let i;let o={gif:e.gif??!1,name:e.name??e.src,source:e.src,type:e.src.substring(e.src.length-3),error:!1,loading:!0,replaceColor:e.replaceColor,ratio:e.width&&e.height?e.width/e.height:void 0};t.images.push(o),i=e.gif?eS:e.replaceColor?e_:ek,await i(o)}catch{throw Error(`${y.dIx} ${e.name??e.src} not found`)}});let i=new eI(t);await t.addPlugin(i,e),await t.addShape(new eR(t),e)}class eG extends y.PVU{constructor(){super(),this.sync=!1}load(t){(0,y.kZJ)(t)||(super.load(t),void 0===t.sync||(this.sync=t.sync))}}class eq extends y.PVU{constructor(){super(),this.sync=!1}load(t){(0,y.kZJ)(t)||(super.load(t),void 0===t.sync||(this.sync=t.sync))}}class eB{constructor(){this.count=0,this.delay=new eG,this.duration=new eq}load(t){(0,y.kZJ)(t)||(void 0!==t.count&&(this.count=t.count),this.delay.load(t.delay),this.duration.load(t.duration))}}class eU{constructor(t){this.container=t}init(t){let e=this.container,i=t.options.life;i&&(t.life={delay:e.retina.reduceFactor?(0,y.VGA)(i.delay.value)*(i.delay.sync?1:(0,y.G0i)())/e.retina.reduceFactor*y.XuV:0,delayTime:0,duration:e.retina.reduceFactor?(0,y.VGA)(i.duration.value)*(i.duration.sync?1:(0,y.G0i)())/e.retina.reduceFactor*y.XuV:0,time:0,count:i.count},t.life.duration<=0&&(t.life.duration=-1),t.life.count<=0&&(t.life.count=-1),t.life&&(t.spawning=t.life.delay>0))}isEnabled(t){return!t.destroyed}loadOptions(t,...e){for(let i of(t.life||(t.life=new eB),e))t.life.load(i?.life)}update(t,e){this.isEnabled(t)&&t.life&&function(t,e,i){if(!t.life)return;let o=t.life,s=!1;if(t.spawning){if(o.delayTime+=e.value,!(o.delayTime>=t.life.delay))return;s=!0,t.spawning=!1,o.delayTime=0,o.time=0}if(-1===o.duration||t.spawning||(s?o.time=0:o.time+=e.value,o.time<o.duration))return;if(o.time=0,t.life.count>0&&t.life.count--,0===t.life.count){t.destroy();return}let a=(0,y.DT4)(0,i.width),n=(0,y.DT4)(0,i.width);t.position.x=(0,y.U43)(a),t.position.y=(0,y.U43)(n),t.spawning=!0,o.delayTime=0,o.time=0,t.reset();let r=t.options.life;r&&(o.delay=(0,y.VGA)(r.delay.value)*y.XuV,o.duration=(0,y.VGA)(r.duration.value)*y.XuV)}(t,e,this.container.canvas.size)}}async function eF(t,e=!0){t.checkVersion("3.8.1"),await t.addParticleUpdater("life",async t=>Promise.resolve(new eU(t)),e)}class eJ{constructor(){this.validTypes=["line"]}draw(t){!function(t){let{context:e,particle:i,radius:o}=t,s=i.shapeData;e.moveTo(-o,0),e.lineTo(o,0),e.lineCap=s?.cap??"butt"}(t)}getSidesCount(){return 1}}async function eL(t,e=!0){t.checkVersion("3.8.1"),await t.addShape(new eJ,e)}class eZ{init(){}isEnabled(t){return!(0,y.B90)()&&!t.destroyed&&t.container.actualOptions.interactivity.events.onHover.parallax.enable}move(t){let e=t.container,i=e.actualOptions.interactivity.events.onHover.parallax;if((0,y.B90)()||!i.enable)return;let o=i.force,s=e.interactivity.mouse.position;if(!s)return;let a=e.canvas.size,n={x:.5*a.width,y:.5*a.height},r=i.smooth,l=t.getRadius()/o,c={x:(s.x-n.x)*l,y:(s.y-n.y)*l},{offset:d}=t;d.x+=(c.x-d.x)/r,d.y+=(c.y-d.y)/r}}async function ej(t,e=!0){t.checkVersion("3.8.1"),await t.addMover("parallax",()=>Promise.resolve(new eZ),e)}class eH extends y.U4p{constructor(t){super(t)}clear(){}init(){}interact(t){let e=this.container;void 0===t.attractDistance&&(t.attractDistance=(0,y.VGA)(t.options.move.attract.distance)*e.retina.pixelRatio);let i=t.attractDistance,o=t.getPosition();for(let s of e.particles.quadTree.queryCircle(o,i)){if(t===s||!s.options.move.attract.enable||s.destroyed||s.spawning)continue;let e=s.getPosition(),{dx:i,dy:a}=(0,y.vrU)(o,e),n=t.options.move.attract.rotate,r=i/(1e3*n.x),l=a/(1e3*n.y),c=s.size.value/t.size.value,d=1/c;t.velocity.x-=r*c,t.velocity.y-=l*c,s.velocity.x+=r*d,s.velocity.y+=l*d}}isEnabled(t){return t.options.move.attract.enable}reset(){}}async function eY(t,e=!0){t.checkVersion("3.8.1"),await t.addInteractor("particlesAttract",t=>Promise.resolve(new eH(t)),e)}function eW(t,e,i,o,s,a){let n=(0,y.qE8)(t.options.collisions.absorb.speed*s.factor/10,0,o);t.size.value+=.5*n,i.size.value-=n,o<=a&&(i.size.value=0,i.destroy())}let eX=t=>{void 0===t.collisionMaxSpeed&&(t.collisionMaxSpeed=(0,y.VGA)(t.options.collisions.maxSpeed)),t.velocity.length>t.collisionMaxSpeed&&(t.velocity.length=t.collisionMaxSpeed)};function e$(t,e){(0,y.pEn)((0,y.Tgl)(t),(0,y.Tgl)(e)),eX(t),eX(e)}class eN extends y.U4p{constructor(t){super(t)}clear(){}init(){}interact(t,e){if(t.destroyed||t.spawning)return;let i=this.container,o=t.getPosition(),s=t.getRadius();for(let a of i.particles.quadTree.queryCircle(o,2*s)){if(t===a||!a.options.collisions.enable||t.options.collisions.mode!==a.options.collisions.mode||a.destroyed||a.spawning)continue;let n=a.getPosition(),r=a.getRadius();!(Math.abs(Math.round(o.z)-Math.round(n.z))>s+r)&&((0,y.YfF)(o,n)>s+r||function(t,e,i,o){switch(t.options.collisions.mode){case y._or.absorb:!function(t,e,i,o){let s=t.getRadius(),a=e.getRadius();void 0===s&&void 0!==a?t.destroy():void 0!==s&&void 0===a?e.destroy():void 0!==s&&void 0!==a&&(s>=a?eW(t,s,e,a,i,o):eW(e,a,t,s,i,o))}(t,e,i,o);break;case y._or.bounce:e$(t,e);break;case y._or.destroy:t.unbreakable||e.unbreakable||e$(t,e),void 0===t.getRadius()&&void 0!==e.getRadius()?t.destroy():void 0!==t.getRadius()&&void 0===e.getRadius()?e.destroy():void 0!==t.getRadius()&&void 0!==e.getRadius()&&(t.getRadius()>=e.getRadius()?e:t).destroy()}}(t,a,e,i.retina.pixelRatio))}}isEnabled(t){return t.options.collisions.enable}reset(){}}async function eK(t,e=!0){t.checkVersion("3.8.1"),await t.addInteractor("particlesCollisions",t=>Promise.resolve(new eN(t)),e)}class eQ extends y.jlt{constructor(t,e,i,o){super(t,e,i),this.canvasSize=o,this.canvasSize={...o}}contains(t){let{width:e,height:i}=this.canvasSize,{x:o,y:s}=t;return super.contains(t)||super.contains({x:o-e,y:s})||super.contains({x:o-e,y:s-i})||super.contains({x:o,y:s-i})}intersects(t){if(super.intersects(t))return!0;let e={x:t.position.x-this.canvasSize.width,y:t.position.y-this.canvasSize.height};if(void 0!==t.radius){let i=new y.jlt(e.x,e.y,2*t.radius);return super.intersects(i)}if(void 0!==t.size){let i=new y.M_G(e.x,e.y,2*t.size.width,2*t.size.height);return super.intersects(i)}return!1}}class e0{constructor(){this.blur=5,this.color=new y.Oit,this.color.value="#000",this.enable=!1}load(t){(0,y.kZJ)(t)||(void 0!==t.blur&&(this.blur=t.blur),this.color=y.Oit.create(this.color,t.color),void 0===t.enable||(this.enable=t.enable))}}class e1{constructor(){this.enable=!1,this.frequency=1}load(t){(0,y.kZJ)(t)||(void 0!==t.color&&(this.color=y.Oit.create(this.color,t.color)),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.frequency&&(this.frequency=t.frequency),void 0===t.opacity||(this.opacity=t.opacity))}}class e2{constructor(){this.blink=!1,this.color=new y.Oit,this.color.value="#fff",this.consent=!1,this.distance=100,this.enable=!1,this.frequency=1,this.opacity=1,this.shadow=new e0,this.triangles=new e1,this.width=1,this.warp=!1}load(t){(0,y.kZJ)(t)||(void 0!==t.id&&(this.id=t.id),void 0!==t.blink&&(this.blink=t.blink),this.color=y.Oit.create(this.color,t.color),void 0!==t.consent&&(this.consent=t.consent),void 0!==t.distance&&(this.distance=t.distance),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.frequency&&(this.frequency=t.frequency),void 0!==t.opacity&&(this.opacity=t.opacity),this.shadow.load(t.shadow),this.triangles.load(t.triangles),void 0!==t.width&&(this.width=t.width),void 0===t.warp||(this.warp=t.warp))}}let e3={x:0,y:0};class e5 extends y.U4p{constructor(t,e){super(t),this._setColor=t=>{if(!t.options.links)return;let e=this._linkContainer,i=t.options.links,o=void 0===i.id?e.particles.linksColor:e.particles.linksColors.get(i.id);if(o)return;let s=i.color;o=(0,y.PGG)(this._engine,s,i.blink,i.consent),void 0===i.id?e.particles.linksColor=o:e.particles.linksColors.set(i.id,o)},this._linkContainer=t,this._engine=e}clear(){}init(){this._linkContainer.particles.linksColor=void 0,this._linkContainer.particles.linksColors=new Map}interact(t){let e;if(!t.options.links)return;t.links=[];let i=t.getPosition(),o=this.container,s=o.canvas.size;if(i.x<e3.x||i.y<e3.y||i.x>s.width||i.y>s.height)return;let a=t.options.links,n=a.opacity,r=t.retina.linksDistance??0,l=a.warp;for(let c of(e=l?new eQ(i.x,i.y,r,s):new y.jlt(i.x,i.y,r),o.particles.quadTree.query(e))){let e=c.options.links;if(t===c||!e?.enable||a.id!==e.id||c.spawning||c.destroyed||!c.links||t.links.some(t=>t.destination===c)||c.links.some(e=>e.destination===t))continue;let o=c.getPosition();if(o.x<e3.x||o.y<e3.y||o.x>s.width||o.y>s.height)continue;let d=function(t,e,i,o,s){let{dx:a,dy:n,distance:r}=(0,y.vrU)(t,e);if(!s||r<=i)return r;let l={x:Math.abs(a),y:Math.abs(n)},c={x:Math.min(l.x,o.width-l.x),y:Math.min(l.y,o.height-l.y)};return Math.sqrt(c.x**2+c.y**2)}(i,o,r,s,l&&e.warp);if(d>r)continue;let h=(1-d/r)*n;this._setColor(t),t.links.push({destination:c,opacity:h})}}isEnabled(t){return!!t.options.links?.enable}loadParticlesOptions(t,...e){for(let i of(t.links||(t.links=new e2),e))t.links.load(i?.links)}reset(){}}async function e8(t,e=!0){await t.addInteractor("particlesLinks",async e=>Promise.resolve(new e5(e,t)),e)}function e4(t,e){var i;let o=((i=t.map(t=>t.id)).sort((t,e)=>t-e),i.join("_")),s=e.get(o);return void 0===s&&(s=(0,y.G0i)(),e.set(o,s)),s}class e6{constructor(t,e){this._drawLinkLine=(t,e)=>{let i=t.options.links;if(!i?.enable)return;let o=this._container,s=o.actualOptions,a=e.destination,n=t.getPosition(),r=a.getPosition(),l=e.opacity;o.canvas.draw(e=>{let c;let d=t.options.twinkle?.lines;if(d?.enable){let t=d.frequency,e=(0,y.BN0)(this._engine,d.color);(0,y.G0i)()<t&&e&&(c=e,l=(0,y.VGA)(d.opacity))}if(!c){let e=void 0!==i.id?o.particles.linksColors.get(i.id):o.particles.linksColor;c=(0,y._hh)(t,a,e)}if(!c)return;let h=t.retina.linksWidth??0,u=t.retina.linksDistance??0,{backgroundMask:p}=s;!function(t){let e=!1,{begin:i,end:o,engine:s,maxDistance:a,context:n,canvasSize:r,width:l,backgroundMask:c,colorLine:d,opacity:h,links:u}=t;if((0,y.YfF)(i,o)<=a)(0,y.V6E)(n,i,o),e=!0;else if(u.warp){let t,s;let l={x:o.x-r.width,y:o.y},c=(0,y.vrU)(i,l);if(c.distance<=a){let e=i.y-c.dy/c.dx*i.x;t={x:0,y:e},s={x:r.width,y:e}}else{let e={x:o.x,y:o.y-r.height},n=(0,y.vrU)(i,e);if(n.distance<=a){let e=-(i.y-n.dy/n.dx*i.x)/(n.dy/n.dx);t={x:e,y:0},s={x:e,y:r.height}}else{let e={x:o.x-r.width,y:o.y-r.height},n=(0,y.vrU)(i,e);if(n.distance<=a){let e=i.y-n.dy/n.dx*i.x;s={x:(t={x:-e/(n.dy/n.dx),y:e}).x+r.width,y:t.y+r.height}}}}t&&s&&((0,y.V6E)(n,i,t),(0,y.V6E)(n,o,s),e=!0)}if(!e)return;n.lineWidth=l,c.enable&&(n.globalCompositeOperation=c.composite),n.strokeStyle=(0,y.xxz)(d,h);let{shadow:p}=u;if(p.enable){let t=(0,y.BN0)(s,p.color);t&&(n.shadowBlur=p.blur,n.shadowColor=(0,y.xxz)(t))}n.stroke()}({context:e,width:h,begin:n,end:r,engine:this._engine,maxDistance:u,canvasSize:o.canvas.size,links:i,backgroundMask:p,colorLine:c,opacity:l})})},this._drawLinkTriangle=(t,e,i)=>{let o=t.options.links;if(!o?.enable)return;let s=o.triangles;if(!s.enable)return;let a=this._container,n=a.actualOptions,r=e.destination,l=i.destination,c=s.opacity??(e.opacity+i.opacity)*.5;c<=0||a.canvas.draw(e=>{let i=t.getPosition(),d=r.getPosition(),h=l.getPosition(),u=t.retina.linksDistance??0;if((0,y.YfF)(i,d)>u||(0,y.YfF)(h,d)>u||(0,y.YfF)(h,i)>u)return;let p=(0,y.BN0)(this._engine,s.color);if(!p){let e=void 0!==o.id?a.particles.linksColors.get(o.id):a.particles.linksColor;p=(0,y._hh)(t,r,e)}p&&function(t){let{context:e,pos1:i,pos2:o,pos3:s,backgroundMask:a,colorTriangle:n,opacityTriangle:r}=t;e.beginPath(),e.moveTo(i.x,i.y),e.lineTo(o.x,o.y),e.lineTo(s.x,s.y),e.closePath(),a.enable&&(e.globalCompositeOperation=a.composite),e.fillStyle=(0,y.xxz)(n,r),e.fill()}({context:e,pos1:i,pos2:d,pos3:h,backgroundMask:n.backgroundMask,colorTriangle:p,opacityTriangle:c})})},this._drawTriangles=(t,e,i,o)=>{let s=i.destination;if(!(t.links?.triangles.enable&&s.options.links?.triangles.enable))return;let a=s.links?.filter(t=>{let e=this._getLinkFrequency(s,t.destination);return s.options.links&&e<=s.options.links.frequency&&o.findIndex(e=>e.destination===t.destination)>=0});if(a?.length)for(let o of a){let a=o.destination;this._getTriangleFrequency(e,s,a)>t.links.triangles.frequency||this._drawLinkTriangle(e,i,o)}},this._getLinkFrequency=(t,e)=>e4([t,e],this._freqs.links),this._getTriangleFrequency=(t,e,i)=>e4([t,e,i],this._freqs.triangles),this._container=t,this._engine=e,this._freqs={links:new Map,triangles:new Map}}drawParticle(t,e){let{links:i,options:o}=e;if(!i?.length)return;let s=i.filter(t=>o.links&&(o.links.frequency>=1||this._getLinkFrequency(e,t.destination)<=o.links.frequency));for(let t of s)this._drawTriangles(o,e,t,s),t.opacity>0&&(e.retina.linksWidth??0)>0&&this._drawLinkLine(e,t)}async init(){this._freqs.links=new Map,this._freqs.triangles=new Map,await Promise.resolve()}particleCreated(t){if(t.links=[],!t.options.links)return;let e=this._container.retina.pixelRatio,{retina:i}=t,{distance:o,width:s}=t.options.links;i.linksDistance=o*e,i.linksWidth=s*e}particleDestroyed(t){t.links=[]}}class e9{constructor(t){this.id="links",this._engine=t}getPlugin(t){return Promise.resolve(new e6(t,this._engine))}loadOptions(){}needsPlugin(){return!0}}async function e7(t,e=!0){let i=new e9(t);await t.addPlugin(i,e)}async function it(t,e=!0){t.checkVersion("3.8.1"),await e8(t,e),await e7(t,e)}let ie={x:0,y:0};class ii{draw(t){let{particle:e,radius:i}=t;!function(t,e,i){let{context:o}=t,s=i.count.numerator*i.count.denominator,a=i.count.numerator/i.count.denominator,n=Math.PI-(0,y.puj)(180*(a-2)/a);if(o){o.beginPath(),o.translate(e.x,e.y),o.moveTo(ie.x,ie.y);for(let t=0;t<s;t++)o.lineTo(i.length,ie.y),o.translate(i.length,ie.y),o.rotate(n)}}(t,this.getCenter(e,i),this.getSidesData(e,i))}getSidesCount(t){let e=t.shapeData;return Math.round((0,y.VGA)(e?.sides??5))}}class io extends ii{constructor(){super(...arguments),this.validTypes=["polygon"]}getCenter(t,e){return{x:-e/(t.sides/3.5),y:-e/.76}}getSidesData(t,e){let i=t.sides;return{count:{denominator:1,numerator:i},length:2.66*e/(i/3)}}}class is extends ii{constructor(){super(...arguments),this.validTypes=["triangle"]}getCenter(t,e){return{x:-e,y:e/1.66}}getSidesCount(){return 3}getSidesData(t,e){return{count:{denominator:2,numerator:3},length:2*e}}}async function ia(t,e=!0){t.checkVersion("3.8.1"),await t.addShape(new io,e)}async function ir(t,e=!0){t.checkVersion("3.8.1"),await t.addShape(new is,e)}async function il(t,e=!0){t.checkVersion("3.8.1"),await ia(t,e),await ir(t,e)}class ic{constructor(){this.enable=!1,this.speed=0,this.decay=0,this.sync=!1}load(t){(0,y.kZJ)(t)||(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.speed&&(this.speed=(0,y.DT4)(t.speed)),void 0!==t.decay&&(this.decay=(0,y.DT4)(t.decay)),void 0===t.sync||(this.sync=t.sync))}}class id extends y.PVU{constructor(){super(),this.animation=new ic,this.direction=y.pTR.clockwise,this.path=!1,this.value=0}load(t){(0,y.kZJ)(t)||(super.load(t),void 0!==t.direction&&(this.direction=t.direction),this.animation.load(t.animation),void 0===t.path||(this.path=t.path))}}let ih=2*Math.PI;class iu{constructor(t){this.container=t}init(t){let e=t.options.rotate;if(!e)return;t.rotate={enable:e.animation.enable,value:(0,y.puj)((0,y.VGA)(e.value)),min:0,max:ih},t.pathRotation=e.path;let i=e.direction;switch(i===y.pTR.random&&(i=Math.floor(2*(0,y.G0i)())>0?y.pTR.counterClockwise:y.pTR.clockwise),i){case y.pTR.counterClockwise:case"counterClockwise":t.rotate.status=y.H_P.decreasing;break;case y.pTR.clockwise:t.rotate.status=y.H_P.increasing}let o=e.animation;o.enable&&(t.rotate.decay=1-(0,y.VGA)(o.decay),t.rotate.velocity=(0,y.VGA)(o.speed)/360*this.container.retina.reduceFactor,o.sync||(t.rotate.velocity*=(0,y.G0i)())),t.rotation=t.rotate.value}isEnabled(t){let e=t.options.rotate;return!!e&&!t.destroyed&&!t.spawning&&(!!e.value||e.animation.enable||e.path)}loadOptions(t,...e){for(let i of(t.rotate||(t.rotate=new id),e))t.rotate.load(i?.rotate)}update(t,e){this.isEnabled(t)&&(t.isRotating=!!t.rotate,t.rotate&&((0,y.UC0)(t,t.rotate,!1,y.VKE.none,e),t.rotation=t.rotate.value))}}async function ip(t,e=!0){t.checkVersion("3.8.1"),await t.addParticleUpdater("rotate",t=>Promise.resolve(new iu(t)),e)}let iy=Math.sqrt(2);class iv{constructor(){this.validTypes=["edge","square"]}draw(t){!function(t){let{context:e,radius:i}=t,o=i/iy,s=2*o;e.rect(-o,-o,s,s)}(t)}getSidesCount(){return 4}}async function ig(t,e=!0){t.checkVersion("3.8.1"),await t.addShape(new iv,e)}let ib={x:0,y:0};class im{constructor(){this.validTypes=["star"]}draw(t){!function(t){let{context:e,particle:i,radius:o}=t,s=i.sides,a=i.starInset??2;e.moveTo(ib.x,ib.y-o);for(let t=0;t<s;t++)e.rotate(Math.PI/s),e.lineTo(ib.x,ib.y-o*a),e.rotate(Math.PI/s),e.lineTo(ib.x,ib.y-o)}(t)}getSidesCount(t){let e=t.shapeData;return Math.round((0,y.VGA)(e?.sides??5))}particleInit(t,e){let i=e.shapeData;e.starInset=(0,y.VGA)(i?.inset??2)}}async function iw(t,e=!0){t.checkVersion("3.8.1"),await t.addShape(new im,e)}class ix{constructor(t,e){this._container=t,this._engine=e}init(t){let e=this._container,i=t.options,o=(0,y.TA3)(i.stroke,t.id,i.reduceDuplicates);t.strokeWidth=(0,y.VGA)(o.width)*e.retina.pixelRatio,t.strokeOpacity=(0,y.VGA)(o.opacity??1),t.strokeAnimation=o.color?.animation;let s=(0,y.R5R)(this._engine,o.color)??t.getFillColor();s&&(t.strokeColor=(0,y.pzy)(s,t.strokeAnimation,e.retina.reduceFactor))}isEnabled(t){let e=t.strokeAnimation,{strokeColor:i}=t;return!t.destroyed&&!t.spawning&&!!e&&(i?.h.value!==void 0&&i.h.enable||i?.s.value!==void 0&&i.s.enable||i?.l.value!==void 0&&i.l.enable)}update(t,e){this.isEnabled(t)&&(0,y.JvX)(t.strokeColor,e)}}async function ik(t,e=!0){t.checkVersion("3.8.1"),await t.addParticleUpdater("strokeColor",e=>Promise.resolve(new ix(e,t)),e)}async function i_(t,e=!0){t.checkVersion("3.8.1"),await ej(t,!1),await tH(t,!1),await tK(t,!1),await t8(t,!1),await t7(t,!1),await eo(t,!1),await ea(t,!1),await el(t,!1),await eh(t,!1),await eg(t,!1),await ew(t,!1),await eY(t,!1),await eK(t,!1),await it(t,!1),await tq(t,!1),await tF(t,!1),await eA(t,!1),await eL(t,!1),await il(t,!1),await ig(t,!1),await iw(t,!1),await eF(t,!1),await ip(t,!1),await ik(t,!1),await tG(t,e)}class iC{constructor(){this.validTypes=["text","character","char","multiline-text"]}draw(t){!function(t){let{context:e,particle:i,radius:o,opacity:s}=t,a=i.shapeData;if(!a)return;let n=a.value;if(void 0===n)return;void 0===i.text&&(i.text=(0,y.TA3)(n,i.randomIndexData));let r=i.text,l=a.style??"",c=a.weight??"400",d=2*Math.round(o),h=a.font??"Verdana",u=i.shapeFill,p=r?.split("\n");if(p){e.font=`${l} ${c} ${d}px "${h}"`,e.globalAlpha=s;for(let t=0;t<p.length;t++)!function(t,e,i,o,s,a){let n={x:-(e.length*i*.5),y:.5*i},r=2*i;a?t.fillText(e,n.x,n.y+r*s):t.strokeText(e,n.x,n.y+r*s)}(e,p[t],o,0,t,u);e.globalAlpha=1}}(t)}async init(t){let e=t.actualOptions,{validTypes:i}=this;if(i.find(t=>(0,y.hnD)(t,e.particles.shape.type))){let t=i.map(t=>e.particles.shape.options[t]).find(t=>!!t),o=[];(0,y.wJ2)(t,t=>{o.push((0,y.AlN)(t.font,t.weight))}),await Promise.all(o)}}particleInit(t,e){if(!e.shape||!this.validTypes.includes(e.shape))return;let i=e.shapeData;if(void 0===i)return;let o=i.value;void 0!==o&&(e.text=(0,y.TA3)(o,e.randomIndexData))}}async function iz(t,e=!0){t.checkVersion("3.8.1"),await t.addShape(new iC,e)}!function(t){t.clockwise="clockwise",t.counterClockwise="counter-clockwise",t.random="random"}(f||(f={}));class iP{constructor(){this.enable=!1,this.speed=0,this.decay=0,this.sync=!1}load(t){(0,y.kZJ)(t)||(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.speed&&(this.speed=(0,y.DT4)(t.speed)),void 0!==t.decay&&(this.decay=(0,y.DT4)(t.decay)),void 0===t.sync||(this.sync=t.sync))}}class iT extends y.PVU{constructor(){super(),this.animation=new iP,this.direction=f.clockwise,this.enable=!1,this.value=0}load(t){super.load(t),(0,y.kZJ)(t)||(this.animation.load(t.animation),void 0!==t.direction&&(this.direction=t.direction),void 0===t.enable||(this.enable=t.enable))}}let iD=2*Math.PI;class iM{constructor(t){this.container=t}getTransformValues(t){let e=t.tilt?.enable&&t.tilt;return{b:e?Math.cos(e.value)*e.cosDirection:void 0,c:e?Math.sin(e.value)*e.sinDirection:void 0}}init(t){let e=t.options.tilt;if(!e)return;t.tilt={enable:e.enable,value:(0,y.puj)((0,y.VGA)(e.value)),sinDirection:(0,y.G0i)()>=y.MXx?1:-1,cosDirection:(0,y.G0i)()>=y.MXx?1:-1,min:0,max:iD};let i=e.direction;switch(i===f.random&&(i=Math.floor(2*(0,y.G0i)())>0?f.counterClockwise:f.clockwise),i){case f.counterClockwise:case"counterClockwise":t.tilt.status=y.H_P.decreasing;break;case f.clockwise:t.tilt.status=y.H_P.increasing}let o=t.options.tilt?.animation;o?.enable&&(t.tilt.decay=1-(0,y.VGA)(o.decay),t.tilt.velocity=(0,y.VGA)(o.speed)/360*this.container.retina.reduceFactor,o.sync||(t.tilt.velocity*=(0,y.G0i)()))}isEnabled(t){let e=t.options.tilt?.animation;return!t.destroyed&&!t.spawning&&!!e?.enable}loadOptions(t,...e){for(let i of(t.tilt||(t.tilt=new iT),e))t.tilt.load(i?.tilt)}async update(t,e){this.isEnabled(t)&&t.tilt&&((0,y.UC0)(t,t.tilt,!1,y.VKE.none,e),await Promise.resolve())}}async function iE(t,e=!0){t.checkVersion("3.8.1"),await t.addParticleUpdater("tilt",t=>Promise.resolve(new iM(t)),e)}class iO{constructor(){this.enable=!1,this.frequency=.05,this.opacity=1}load(t){(0,y.kZJ)(t)||(void 0!==t.color&&(this.color=y.Oit.create(this.color,t.color)),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.frequency&&(this.frequency=t.frequency),void 0!==t.opacity&&(this.opacity=(0,y.DT4)(t.opacity)))}}class iS{constructor(){this.lines=new iO,this.particles=new iO}load(t){(0,y.kZJ)(t)||(this.lines.load(t.lines),this.particles.load(t.particles))}}class iR{constructor(t){this._engine=t}getColorStyles(t,e,i,o){let s=t.options.twinkle;if(!s)return{};let a=s.particles,n=a.enable&&(0,y.G0i)()<a.frequency,r=t.options.zIndex,l=(1-t.zIndexFactor)**r.opacityRate,c=n?(0,y.VGA)(a.opacity)*l:o,d=(0,y.R5R)(this._engine,a.color),h=d?(0,y.LCf)(d,c):void 0,u={},p=n&&h;return u.fill=p?h:void 0,u.stroke=p?h:void 0,u}async init(){await Promise.resolve()}isEnabled(t){let e=t.options.twinkle;return!!e&&e.particles.enable}loadOptions(t,...e){for(let i of(t.twinkle||(t.twinkle=new iS),e))t.twinkle.load(i?.twinkle)}async update(){await Promise.resolve()}}async function iV(t,e=!0){t.checkVersion("3.8.1"),await t.addParticleUpdater("twinkle",()=>Promise.resolve(new iR(t)),e)}class iI{constructor(){this.angle=50,this.move=10}load(t){(0,y.kZJ)(t)||(void 0!==t.angle&&(this.angle=(0,y.DT4)(t.angle)),void 0!==t.move&&(this.move=(0,y.DT4)(t.move)))}}class iA{constructor(){this.distance=5,this.enable=!1,this.speed=new iI}load(t){if(!(0,y.kZJ)(t)&&(void 0!==t.distance&&(this.distance=(0,y.DT4)(t.distance)),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.speed)){if((0,y.EtT)(t.speed))this.speed.load({angle:t.speed});else{let e=t.speed;void 0!==e.min?this.speed.load({angle:e}):this.speed.load(t.speed)}}}}let iG=2*Math.PI,iq=2*Math.PI;class iB{constructor(t){this.container=t}init(t){let e=t.options.wobble;e?.enable?t.wobble={angle:(0,y.G0i)()*iq,angleSpeed:(0,y.VGA)(e.speed.angle)/360,moveSpeed:(0,y.VGA)(e.speed.move)/10}:t.wobble={angle:0,angleSpeed:0,moveSpeed:0},t.retina.wobbleDistance=(0,y.VGA)(e?.distance??0)*this.container.retina.pixelRatio}isEnabled(t){return!t.destroyed&&!t.spawning&&!!t.options.wobble?.enable}loadOptions(t,...e){for(let i of(t.wobble||(t.wobble=new iA),e))t.wobble.load(i?.wobble)}update(t,e){this.isEnabled(t)&&function(t,e){let{wobble:i}=t.options,{wobble:o}=t;if(!i?.enable||!o)return;let s=o.angleSpeed*e.factor,a=o.moveSpeed*e.factor*((t.retina.wobbleDistance??0)*e.factor)/(y.XuV/60),{position:n}=t;o.angle+=s,o.angle>iG&&(o.angle-=iG),n.x+=a*Math.cos(o.angle),n.y+=a*Math.abs(Math.sin(o.angle))}(t,e)}}async function iU(t,e=!0){t.checkVersion("3.8.1"),await t.addParticleUpdater("wobble",t=>Promise.resolve(new iB(t)),e)}async function iF(t,e=!0){t.checkVersion("3.8.1"),await E(t,!1),await tl(t,!1),await iE(t,!1),await iV(t,!1),await iU(t,!1),await iz(t,!1),await to(t,!1),await _(t,!1),await j(t,!1),await X(t,!1),await Q(t,!1),await i_(t,e)}}}]);