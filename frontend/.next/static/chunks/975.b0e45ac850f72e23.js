"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[975],{8975:(e,l,t)=>{t.r(l),t.d(l,{Particles:()=>n,default:()=>n,initParticlesEngine:()=>r});var s=t(2665),a=t(5155),i=t(2115);let n=e=>{let l=e.id??"tsparticles";return(0,i.useEffect)(()=>{let t;return s.$k3.load({id:l,url:e.url,options:e.options}).then(l=>{var s;t=l,null==(s=e.particlesLoaded)||s.call(e,l)}),()=>{null==t||t.destroy()}},[l,e,e.url,e.options]),(0,a.jsx)("div",{id:l,className:e.className})};async function r(e){await e(s.$k3)}}}]);