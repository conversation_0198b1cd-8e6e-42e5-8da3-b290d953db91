(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/node_modules_ab0279._.js", {

"[project]/node_modules/@tsparticles/react/dist/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__((__turbopack_import__) => {
    return Promise.all([
  "static/chunks/node_modules_@tsparticles_189b99._.js",
  "static/chunks/node_modules_@tsparticles_react_dist_index_d96e79.js"
].map((chunk) => __turbopack_load__(chunk))).then(() => {
        return __turbopack_import__("[project]/node_modules/@tsparticles/react/dist/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/tsparticles/browser/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__((__turbopack_import__) => {
    return Promise.all([
  "static/chunks/node_modules_a7b87d._.js",
  "static/chunks/node_modules_tsparticles_browser_index_7dc93b.js"
].map((chunk) => __turbopack_load__(chunk))).then(() => {
        return __turbopack_import__("[project]/node_modules/tsparticles/browser/index.js [app-client] (ecmascript)");
    });
});
}}),
}]);