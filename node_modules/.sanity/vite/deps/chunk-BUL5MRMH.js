import {
  defineLocaleResourceBundle,
  definePlugin,
  route
} from "./chunk-OLTKT4RK.js";
import {
  EyeOpenIcon
} from "./chunk-7WLEJDEH.js";
import {
  require_react
} from "./chunk-U3RAP3IQ.js";
import {
  __toESM
} from "./chunk-5IKWDFCZ.js";

// node_modules/@sanity/vision/lib/_chunks-es/index.mjs
var import_react = __toESM(require_react(), 1);
var visionLocaleNamespace = "vision";
var visionUsEnglishLocaleBundle = defineLocaleResourceBundle({
  locale: "en-US",
  namespace: visionLocaleNamespace,
  resources: () => import("./resources-AW2FRHCT.js")
});
var visionTool = definePlugin((options) => {
  const {
    name,
    title,
    icon,
    ...config
  } = options || {};
  return {
    name: "@sanity/vision",
    tools: [{
      name: name || "vision",
      title: title || "Vision",
      icon: icon || EyeOpenIcon,
      component: (0, import_react.lazy)(() => import("./SanityVision-DF4P5R7E.js")),
      options: config,
      router: route.create("/*"),
      __internalApplicationType: "sanity/vision"
    }],
    i18n: {
      bundles: [visionUsEnglishLocaleBundle]
    }
  };
});

export {
  visionLocaleNamespace,
  visionTool
};
//# sourceMappingURL=chunk-BUL5MRMH.js.map
