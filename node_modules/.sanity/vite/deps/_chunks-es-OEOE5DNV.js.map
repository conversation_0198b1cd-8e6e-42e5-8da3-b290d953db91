{"version": 3, "sources": ["../../../sanity/src/structure/panes/userComponent/UserComponentPaneContent.tsx", "../../../sanity/src/structure/panes/userComponent/UserComponentPaneHeader.tsx", "../../../sanity/src/structure/panes/userComponent/UserComponentPane.tsx"], "sourcesContent": ["import {Box} from '@sanity/ui'\nimport {type ReactNode} from 'react'\nimport {styled} from 'styled-components'\n\nimport {usePane} from '../../components'\n\ninterface UserComponentPaneContentProps {\n  children: ReactNode\n}\n\nconst Root = styled(Box)`\n  position: relative;\n`\n\nexport function UserComponentPaneContent(props: UserComponentPaneContentProps) {\n  const {children} = props\n  const {collapsed} = usePane()\n\n  return (\n    <Root hidden={collapsed} height=\"fill\" overflow=\"auto\">\n      {children}\n    </Root>\n  )\n}\n", "import {ArrowLeftIcon} from '@sanity/icons'\n\nimport {Button} from '../../../ui-components'\nimport {BackLink, PaneHeader, PaneHeaderActions} from '../../components'\nimport {\n  type PaneMenuItem,\n  type PaneMenuItemGroup,\n  type StructureToolPaneActionHandler,\n} from '../../types'\nimport {useStructureTool} from '../../useStructureTool'\n\ninterface UserComponentPaneHeaderProps {\n  actionHandlers?: Record<string, StructureToolPaneActionHandler>\n  index: number\n  menuItems?: PaneMenuItem[]\n  menuItemGroups?: PaneMenuItemGroup[]\n  title: string\n}\n\nexport function UserComponentPaneHeader(props: UserComponentPaneHeaderProps) {\n  const {actionHandlers, index, menuItems, menuItemGroups, title} = props\n  const {features} = useStructureTool()\n\n  if (!menuItems?.length && !title) {\n    return null\n  }\n\n  return (\n    <PaneHeader\n      actions={\n        <PaneHeaderActions\n          menuItems={menuItems}\n          menuItemGroups={menuItemGroups}\n          actionHandlers={actionHandlers}\n        />\n      }\n      backButton={\n        features.backButton &&\n        index > 0 && (\n          <Button\n            as={BackLink}\n            data-as=\"a\"\n            icon={ArrowLeftIcon}\n            mode=\"bleed\"\n            tooltipProps={{content: 'Back'}}\n          />\n        )\n      }\n      title={title}\n    />\n  )\n}\n", "import {isValidElement, useState} from 'react'\nimport {isValidElementType} from 'react-is'\nimport {useI18nText} from 'sanity'\n\nimport {Pane} from '../../components'\nimport {type StructureToolPaneActionHandler} from '../../types'\nimport {type BaseStructureToolPaneProps} from '../types'\nimport {UserComponentPaneContent} from './UserComponentPaneContent'\nimport {UserComponentPaneHeader} from './UserComponentPaneHeader'\n\ntype UserComponentPaneProps = BaseStructureToolPaneProps<'component'>\n\n/**\n * @internal\n */\nexport function UserComponentPane(props: UserComponentPaneProps) {\n  const {index, pane, paneKey, ...restProps} = props\n  const {\n    child,\n    component: UserComponent,\n    menuItems,\n    menuItemGroups,\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    type: _unused,\n    ...restPane\n  } = pane\n  const [ref, setRef] = useState<{\n    actionHandlers?: Record<string, StructureToolPaneActionHandler>\n  } | null>(null)\n  const {title = ''} = useI18nText(pane)\n\n  const {key, ...componentProps} = {...restProps, ...restPane}\n\n  return (\n    <Pane id={paneKey} minWidth={320} selected={restProps.isSelected}>\n      <UserComponentPaneHeader\n        actionHandlers={ref?.actionHandlers}\n        index={index}\n        menuItems={menuItems}\n        menuItemGroups={menuItemGroups}\n        title={title}\n      />\n\n      <UserComponentPaneContent>\n        {isValidElementType(UserComponent) && (\n          <UserComponent\n            key={key}\n            {...componentProps}\n            // NOTE: here we're utilizing the function form of refs so setting\n            // the ref causes a re-render for `UserComponentPaneHeader`\n            ref={setRef as any}\n            child={child}\n            paneKey={paneKey}\n          />\n        )}\n        {isValidElement(UserComponent) && UserComponent}\n      </UserComponentPaneContent>\n    </Pane>\n  )\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,IAAMA,OAAOC,GAAOC,GAAG;;;AAIhB,SAAAC,yBAAAC,OAAA;AAAAC,QAAAA,QAAAC,iCAAA,CAAA,GACL;IAAAC;EAAAA,IAAmBH,OACnB;IAAAI;EAAAA,IAAoBC,QAAQ;AAACC,MAAAA;AAAA,SAAAL,EAAAE,CAAAA,MAAAA,YAAAF,EAAAA,CAAAA,MAAAG,aAG3BE,SAAAA,wBAAC,MAAaF,EAAAA,QAAQ,WAAU,QAAA,QAAgB,UAAA,QAAA,SAEhD,CAAA,GAAOH,EAAAA,CAAAA,IAAAE,UAAAF,EAAAA,CAAAA,IAAAG,WAAAH,EAAAA,CAAAA,IAAAK,MAAAA,KAAAL,EAAA,CAAA,GAFPK;AAEO;ACFJ,SAAAC,wBAAAP,OAAA;AAAAC,QAAAA,QAAAC,iCAAA,EAAA,GACL;IAAAM;IAAAC;IAAAC;IAAAC;IAAAC;EAAAA,IAAkEZ,OAClE;IAAAa;EAAAA,IAAmBC,iBAAiB;AAEhC,MAAA,EAACJ,uCAASK,WAAAA,CAAaH;AAAK,WAAA;AAAAN,MAAAA;AAAAL,IAAAO,CAAAA,MAAAA,kBAAAP,EAAAA,CAAAA,MAAAU,kBAAAV,EAAA,CAAA,MAAAS,aAO1BJ,SAAC,wBAAA,mBAAA,EACYI,WACKC,gBACAH,eAAAA,CAChB,GAAAP,EAAAA,CAAAA,IAAAO,gBAAAP,EAAAA,CAAAA,IAAAU,gBAAAV,EAAAA,CAAAA,IAAAS,WAAAT,EAAAA,CAAAA,IAAAK,MAAAA,KAAAL,EAAA,CAAA;AAAAe,MAAAA;AAAAf,IAAAA,CAAAA,MAAAY,SAAAI,cAAAhB,EAAAA,CAAAA,MAAAQ,SAGFO,KAAAH,SAAQI,cACRR,QAAS,SACP,wBAAC,QACKS,EAAAA,IAAOA,UACH,WAAA,KACFC,MAAAA,eACD,MAAA,SACS,cAAA;IAAAC,SAAU;EAAA,EAE3B,CAAA,GAAAnB,EAAA,CAAA,IAAAY,SAAAI,YAAAhB,EAAAA,CAAAA,IAAAQ,OAAAR,EAAAA,CAAAA,IAAAe,MAAAA,KAAAf,EAAA,CAAA;AAAAoB,MAAAA;AAAA,SAAApB,EAAAK,CAAAA,MAAAA,MAAAL,EAAAA,CAAAA,MAAAe,MAAAf,EAAA,CAAA,MAAAW,SAlBLS,SAAAA,wBAAC,cAAA,EAEG,SAAAf,IAOA,YAAAU,IAWKJ,MACP,CAAA,GAAAX,EAAAA,CAAAA,IAAAK,IAAAL,EAAAA,CAAAA,IAAAe,IAAAf,EAAAA,CAAAA,IAAAW,OAAAX,EAAAA,EAAAA,IAAAoB,MAAAA,KAAApB,EAAA,EAAA,GArBFoB;AAqBE;AClCC,SAAAC,kBAAAtB,OAAA;AAAAC,QAAAA,QAAAC,iCAAA,EAAA;AAAAO,MAAAA,OAAAc,MAAAC,SAAAC;AAAAxB,IAAAA,CAAAA,MAAAD,SACL;IAAAS;IAAAc;IAAAC;IAAA,GAAAC;EAAAA,IAA6CzB,OAAKC,EAAAA,CAAAA,IAAAD,OAAAC,EAAAA,CAAAA,IAAAQ,OAAAR,EAAAA,CAAAA,IAAAsB,MAAAtB,EAAAA,CAAAA,IAAAuB,SAAAvB,EAAAA,CAAAA,IAAAwB,cAAAhB,QAAAR,EAAA,CAAA,GAAAsB,OAAAtB,EAAA,CAAA,GAAAuB,UAAAvB,EAAA,CAAA,GAAAwB,YAAAxB,EAAA,CAAA;AAAAyB,MAAAA,eAAAC,OAAAhB,gBAAAD,WAAAkB;AAAA3B,MAAAA,EAAAA,CAAAA,MAAAsB,MAAA;AAClD,UAAA;MAAAI,OAAArB;MAAAuB,WAAAb;MAAAN,WAAAW;MAAAV,gBAAAmB;MAAAC,MAAAC;MAAA,GAAAC;IAAAA,IAQIV;AARJI,YAAArB,KAAAoB,gBAAAV,KAAAN,YAAAW,KAAAV,iBAAAmB,KAAAF,WAAAK,KAQQhC,EAAAA,CAAAA,IAAAsB,MAAAtB,EAAAA,CAAAA,IAAAyB,eAAAzB,EAAAA,CAAAA,IAAA0B,OAAA1B,EAAAA,CAAAA,IAAAU,gBAAAV,EAAAA,CAAAA,IAAAS,WAAAT,EAAAA,EAAAA,IAAA2B;EAAA;AAAAF,oBAAAzB,EAAA,CAAA,GAAA0B,QAAA1B,EAAA,CAAA,GAAAU,iBAAAV,EAAA,CAAA,GAAAS,YAAAT,EAAA,CAAA,GAAA2B,WAAA3B,EAAA,EAAA;AACR,QAAA,CAAAiC,KAAAC,MAAA,QAAsBC,uBAAAA,IAER,GACd;IAAAxB,OAAAN;EAAAA,IAAqB+B,YAAYd,IAAI,GAA9BX,QAAAN,OAAUgC,SAAF,KAARhC;AAAU,MAAAiC,gBAAAC;AAAAvC,IAAA2B,EAAAA,MAAAA,YAAA3B,EAAAA,EAAAA,MAAAwB,aAEjB;IAAAe;IAAA,GAAAD;EAAAA,IAAA;IAAA,GAAqCd;IAAS,GAAKG;EAAAA,GAAS3B,EAAAA,EAAAA,IAAA2B,UAAA3B,EAAAA,EAAAA,IAAAwB,WAAAxB,EAAAA,EAAAA,IAAAsC,gBAAAtC,EAAAA,EAAAA,IAAAuC,QAAAD,iBAAAtC,EAAA,EAAA,GAAAuC,MAAAvC,EAAA,EAAA;AAKtC,QAAAe,KAAAkB,2BAAG1B;AAAgBa,MAAAA;AAAApB,IAAAQ,EAAAA,MAAAA,SAAAR,EAAA,EAAA,MAAAU,kBAAAV,EAAAS,EAAAA,MAAAA,aAAAT,EAAA,EAAA,MAAAe,MAAAf,EAAAA,EAAAA,MAAAW,SADrCS,SAAC,wBAAA,yBACiB,EAAA,gBAAAL,IACTP,OACIC,WACKC,gBACTC,MAAAA,CACP,GAAAX,EAAAA,EAAAA,IAAAQ,OAAAR,EAAAA,EAAAA,IAAAU,gBAAAV,EAAAA,EAAAA,IAAAS,WAAAT,EAAAA,EAAAA,IAAAe,IAAAf,EAAAA,EAAAA,IAAAW,OAAAX,EAAAA,EAAAA,IAAAoB,MAAAA,KAAApB,EAAA,EAAA;AAAA6B,MAAAA;AAAA7B,IAAAyB,EAAAA,MAAAA,iBAAAzB,EAAA,EAAA,MAAA0B,SAAA1B,EAAAsC,EAAAA,MAAAA,kBAAAtC,EAAA,EAAA,MAAAuC,OAAAvC,EAAAA,EAAAA,MAAAuB,WAGCM,SAAAW,oCAAmBf,aAAa,SAC9B,wBAAA,eAAA,EAEKa,GAAAA,gBAGC,KAAAJ,QACER,OACEH,QAAAA,GANJgB,GAQR,GAAAvC,EAAAA,EAAAA,IAAAyB,eAAAzB,EAAAA,EAAAA,IAAA0B,OAAA1B,EAAAA,EAAAA,IAAAsC,gBAAAtC,EAAAA,EAAAA,IAAAuC,KAAAvC,EAAAA,EAAAA,IAAAuB,SAAAvB,EAAAA,EAAAA,IAAA6B,MAAAA,KAAA7B,EAAA,EAAA;AAAAgC,MAAAA;AAAAhC,IAAAA,EAAAA,MAAAyB,iBACAO,SAAAS,6BAAehB,aAAa,KAAKA,eAAazB,EAAAA,EAAAA,IAAAyB,eAAAzB,EAAAA,EAAAA,IAAAgC,MAAAA,KAAAhC,EAAA,EAAA;AAAA0C,MAAAA;AAAA1C,IAAA6B,EAAAA,MAAAA,MAAA7B,EAAAA,EAAAA,MAAAgC,MAZjDU,SAAA,yBAAC,0BACEb,EAAAA,UAAAA;IAAAA;IAWAG;EAAAA,EACH,CAAA,GAA2BhC,EAAAA,EAAAA,IAAA6B,IAAA7B,EAAAA,EAAAA,IAAAgC,IAAAhC,EAAAA,EAAAA,IAAA0C,MAAAA,KAAA1C,EAAA,EAAA;AAAA2C,MAAAA;AAAA3C,SAAAA,EAAAuB,EAAAA,MAAAA,WAAAvB,EAAAA,EAAAA,MAAAwB,UAAAoB,cAAA5C,EAAAoB,EAAAA,MAAAA,MAAApB,EAAAA,EAAAA,MAAA0C,MAtB7BC,SAAC,yBAAA,MAAA,EAASpB,IAAAA,SAAmB,UAAE,KAAa,UAAAC,UAASoB,YACnDxB,UAAAA;IAAAA;IAQAsB;EAcF,EAAA,CAAA,GAAO1C,EAAAA,EAAAA,IAAAuB,SAAAvB,EAAA,EAAA,IAAAwB,UAAAoB,YAAA5C,EAAAA,EAAAA,IAAAoB,IAAApB,EAAAA,EAAAA,IAAA0C,IAAA1C,EAAAA,EAAAA,IAAA2C,MAAAA,KAAA3C,EAAA,EAAA,GAvBP2C;AAuBO;", "names": ["Root", "styled", "Box", "UserComponentPaneContent", "props", "$", "_c", "children", "collapsed", "usePane", "t0", "UserComponentPaneHeader", "actionHandlers", "index", "menuItems", "menuItemGroups", "title", "features", "useStructureTool", "length", "t1", "backButton", "BackLink", "ArrowLeftIcon", "content", "t2", "UserComponentPane", "pane", "paneKey", "restProps", "UserComponent", "child", "restPane", "component", "t3", "type", "_unused", "t4", "ref", "setRef", "useState", "useI18nText", "undefined", "componentProps", "key", "isValidElementType", "isValidElement", "t5", "t6", "isSelected"]}