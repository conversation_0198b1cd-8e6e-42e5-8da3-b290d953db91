{"version": 3, "sources": ["../../../sanity/src/structure/i18n/resources.ts"], "sourcesContent": ["/* eslint sort-keys: \"error\" */\nimport {defineLocalesResources} from 'sanity'\n\n/**\n * Defined locale strings for the structure tool, in US English.\n *\n * @internal\n */\nconst structureLocaleStrings = defineLocalesResources('structure', {\n  /** Label for the \"Copy Document URL\" document action */\n  'action.copy-document-url.label': 'Copy Document URL',\n  /** Tooltip when action button is disabled because the operation is not ready   */\n  'action.delete.disabled.not-ready': 'Operation not ready',\n  /** Tooltip when action button is disabled because the document does not exist */\n  'action.delete.disabled.nothing-to-delete':\n    \"This document doesn't yet exist or is already deleted\",\n  /** Label for the \"Delete\" document action button */\n  'action.delete.label': 'Delete',\n  /** Label for the \"Delete\" document action while the document is being deleted */\n  'action.delete.running.label': 'Deleting…',\n  /** Tooltip when action is disabled because the document is linked to Canvas */\n  'action.disabled-by-canvas.tooltip':\n    'Some document actions are disabled for documents linked to Canvas',\n  /** Message prompting the user to confirm discarding changes */\n  'action.discard-changes.confirm-dialog.confirm-discard-changes':\n    'Are you sure you want to discard all changes since last published?',\n  /** <PERSON>ltip when action is disabled because the document has no unpublished changes */\n  'action.discard-changes.disabled.no-change': 'This document has no unpublished changes',\n  /** Tooltip when action is disabled because the document is not published */\n  'action.discard-changes.disabled.not-published': 'This document is not published',\n  /** Tooltip when action button is disabled because the operation is not ready   */\n  'action.discard-changes.disabled.not-ready': 'Operation not ready',\n  /** Label for the \"Discard changes\" document action */\n  'action.discard-changes.label': 'Discard changes',\n\n  /** Tooltip when action is disabled because the operation is not ready   */\n  'action.duplicate.disabled.not-ready': 'Operation not ready',\n  /** Tooltip when action is disabled because the document doesn't exist */\n  'action.duplicate.disabled.nothing-to-duplicate':\n    \"This document doesn't yet exist so there's nothing to duplicate\",\n  /** Label for the \"Duplicate\" document action */\n  'action.duplicate.label': 'Duplicate',\n  /** Label for the \"Duplicate\" document action while the document is being duplicated */\n  'action.duplicate.running.label': 'Duplicating…',\n  /** Tooltip when publish button is disabled because the document is already published, and published time is unavailable.*/\n  'action.publish.already-published.no-time-ago.tooltip': 'Already published',\n  /** Tooltip when publish button is disabled because the document is already published.*/\n  'action.publish.already-published.tooltip': 'Published {{timeSincePublished}}',\n\n  /** Tooltip when action is disabled because the studio is not ready.*/\n  'action.publish.disabled.not-ready': 'Operation not ready',\n  /** Label for action when there are pending changes.*/\n  'action.publish.draft.label': 'Publish',\n  /** Label for the \"Publish\" document action */\n  'action.publish.label': 'Publish',\n  /** Label for the \"Publish\" document action when the document has live edit enabled.*/\n  'action.publish.live-edit.label': 'Publish',\n  /** Fallback tooltip for the \"Publish\" document action when publish is invoked for a document with live edit enabled.*/\n  'action.publish.live-edit.publish-disabled':\n    'Cannot publish since Live Edit is enabled for this document type',\n  /** Tooltip for the \"Publish\" document action when the document has live edit enabled.*/\n  'action.publish.live-edit.tooltip':\n    'Live Edit is enabled for this content type and publishing happens automatically as you make changes',\n  /** Tooltip when publish button is disabled because there are no changes.*/\n  'action.publish.no-changes.tooltip': 'No unpublished changes',\n  /** Label for the \"Publish\" document action when there are no changes.*/\n  'action.publish.published.label': 'Published',\n  /** Label for the \"Publish\" document action while publish is being executed.*/\n  'action.publish.running.label': 'Publishing…',\n  /** Tooltip when the \"Publish\" document action is disabled due to validation issues */\n  'action.publish.validation-issues.tooltip':\n    'There are validation errors that need to be fixed before this document can be published',\n  /** Tooltip when publish button is waiting for validation and async tasks to complete.*/\n  'action.publish.waiting': 'Waiting for tasks to finish before publishing',\n\n  /** Message prompting the user to confirm that they want to restore to an earlier revision*/\n  'action.restore.confirm.message': 'Are you sure you want to restore this document?',\n  /** Fallback tooltip for when user is looking at the initial revision */\n  'action.restore.disabled.cannot-restore-initial': \"You can't restore to the initial revision\",\n\n  /** Label for the \"Restore\" document action */\n  'action.restore.label': 'Revert to revision',\n  /** Default tooltip for the action */\n  'action.restore.tooltip': 'Restore to this revision',\n\n  /** Tooltip when action is disabled because the document is not already published */\n  'action.unpublish.disabled.not-published': 'This document is not published',\n  /** Tooltip when action is disabled because the operation is not ready   */\n  'action.unpublish.disabled.not-ready': 'Operation not ready',\n  /** Label for the \"Unpublish\" document action */\n  'action.unpublish.label': 'Unpublish',\n  /** Fallback tooltip for the Unpublish document action when publish is invoked for a document with live edit enabled.*/\n  'action.unpublish.live-edit.disabled':\n    'This document has live edit enabled and cannot be unpublished',\n  /** Description for the archived release banner, rendered when viewing the history of a version document from the publihed view */\n  'banners.archived-release.description':\n    'This document version belongs to the archived <VersionBadge>{{title}}</VersionBadge> release',\n  /** The text for the restore button on the deleted document banner */\n  'banners.deleted-document-banner.restore-button.text': 'Restore most recent revision',\n  /** The text content for the deleted document banner */\n  'banners.deleted-document-banner.text': 'This document has been deleted.',\n  /** The text content for the deprecated document type banner */\n  'banners.deprecated-document-type-banner.text': 'This document type has been deprecated.',\n  /** The text for publish action for discarding the version */\n  'banners.live-edit-draft-banner.discard.tooltip': 'Discard draft',\n  /** The text for publish action for the draft banner */\n  'banners.live-edit-draft-banner.publish.tooltip': 'Publish to continue editing',\n\n  /** The text content for the live edit document when it's a draft */\n  'banners.live-edit-draft-banner.text':\n    'The type <strong>{{schemaType}}</strong> has <code>liveEdit</code> enabled, but a draft version of this document exists. Publish or discard the draft in order to continue live editing it.',\n  /** The text for the permission check banner if the user only has one role, and it does not allow publishing this document */\n  'banners.permission-check-banner.missing-permission_create_one':\n    'Your role <Roles/> does not have permission to publish this document.',\n  /** The text for the permission check banner if the user only has multiple roles, but they do not allow publishing this document */\n  'banners.permission-check-banner.missing-permission_create_other':\n    'Your roles <Roles/> do not have permission to publish this document.',\n  /** The text for the permission check banner if the user only has one role, and it does not allow editing this document */\n  'banners.permission-check-banner.missing-permission_update_one':\n    'Your role <Roles/> does not have permission to edit this document.',\n  /** The text for the permission check banner if the user only has multiple roles, but they do not allow editing this document */\n  'banners.permission-check-banner.missing-permission_update_other':\n    'Your roles <Roles/> do not have permission to edit this document.',\n  /** The pending text for the request permission button that appears for viewer roles */\n  'banners.permission-check-banner.request-permission-button.sent': 'Editor request sent',\n  /** The text for the request permission button that appears for viewer roles */\n  'banners.permission-check-banner.request-permission-button.text': 'Ask to edit',\n  /** Description for the archived release banner, rendered when viewing the history of a version document from the published view */\n  'banners.published-release.description':\n    \"You are viewing a read-only document that was published as part of <VersionBadge>{{title}}</VersionBadge>. It can't be edited\",\n  /** The text for the reload button */\n  'banners.reference-changed-banner.reason-changed.reload-button.text': 'Reload reference',\n  /** The text for the reference change banner if the reason is that the reference has been changed */\n  'banners.reference-changed-banner.reason-changed.text':\n    'This reference has changed since you opened it.',\n  /** The text for the close button */\n  'banners.reference-changed-banner.reason-removed.close-button.text': 'Close reference',\n  /** The text for the reference change banner if the reason is that the reference has been deleted */\n  'banners.reference-changed-banner.reason-removed.text':\n    'This reference has been removed since you opened it.',\n  /** The text that appears for the action button to add the current document to the global release */\n  'banners.release.action.add-to-release': 'Add to release',\n  /** The text that appears for the action button to add the current document to the global release */\n  'banners.release.action.open-to-edit': 'Open release to edit',\n  /** Toast description in case an error occurs when adding a document to a release  */\n  'banners.release.error.description':\n    'An error occurred when adding document to the release: {{message}}',\n  /** Toast title in case an error occurs when adding a document to a release  */\n  'banners.release.error.title': 'Error adding document to release',\n  /** The text for the banner that appears when a document only has versions but is in a draft or published pinned release */\n  'banners.release.navigate-to-edit-description': 'The document only exists in the',\n  /** The text for the banner that appears when a document only has versions but is in a draft or published pinned release */\n  'banners.release.navigate-to-edit-description-end_one': 'release',\n  /** The text for the banner that appears when a document only has versions but is in a draft or published pinned release */\n  'banners.release.navigate-to-edit-description-end_other': 'releases',\n  /** The text for the banner that appears when there are multiple versions but no drafts or published, only one extra releases */\n  'banners.release.navigate-to-edit-description-multiple_one':\n    'This document is part of the <VersionBadge/> release and {{count}} more release.',\n  /** The text for the banner that appears when there are multiple versions but no drafts or published, more than one extra releases */\n  'banners.release.navigate-to-edit-description-multiple_other':\n    'This document is part of the <VersionBadge/> release and {{count}} more releases',\n  /** The text for the banner that appears when a document only has one version but is in a draft or published pinned release */\n  'banners.release.navigate-to-edit-description-single':\n    'This document is part of the <VersionBadge/> release',\n  /** The text for the banner that appears when a document is not in the current global release */\n  'banners.release.not-in-release': 'Not in the <VersionBadge>{{title}}</VersionBadge> release.',\n  /** Description of toast that will appear in case of latency between the user adding a document to a release and the UI reflecting it */\n  'banners.release.waiting.description':\n    'Please hold tight while the document is added to the release. It should not take longer than a few seconds.',\n  /** Title of toast that will appear in case of latency between the user adding a document to a release and the UI reflecting it */\n  'banners.release.waiting.title': 'Adding document to release…',\n  /** The text content for the unpublished document banner when is part of a release */\n  'banners.unpublished-release-banner.text':\n    'This document will be unpublished as part of the <VersionBadge>{{title}}</VersionBadge> release',\n\n  /** Browser/tab title when creating a new document of a given type */\n  'browser-document-title.new-document': 'New {{schemaType}}',\n  /** Browser/tab title when editing a document where the title cannot be resolved from preview configuration */\n  'browser-document-title.untitled-document': 'Untitled',\n\n  /** The action menu button aria-label */\n  'buttons.action-menu-button.aria-label': 'Open document actions',\n  /** The action menu button tooltip */\n  'buttons.action-menu-button.tooltip': 'Document actions',\n\n  /** The aria-label for the split pane button on the document panel header */\n  'buttons.split-pane-button.aria-label': 'Split pane right',\n  /** The tool tip for the split pane button on the document panel header */\n  'buttons.split-pane-button.tooltip': 'Split pane right',\n  /** The title for the close button on the split pane on the document panel header */\n  'buttons.split-pane-close-button.title': 'Close split pane',\n  /** The title for the close group button on the split pane on the document panel header */\n  'buttons.split-pane-close-group-button.title': 'Close pane group',\n\n  /** The text for the canvas linked banner action button */\n  'canvas.banner.edit-in-canvas-action': 'Edit in Canvas',\n  /** The text for the canvas linked banner when the document is a draft */\n  'canvas.banner.linked-text.draft': 'This draft document is linked to Canvas',\n  /** The text for the canvas linked banner when the document is a live document */\n  'canvas.banner.linked-text.published': 'This live document is linked to Canvas',\n  /** The text for the canvas linked banner when the document is a version document */\n  'canvas.banner.linked-text.version': 'This version document is linked to Canvas',\n  /** The text for the canvas linked banner popover button */\n  'canvas.banner.popover-button-text': 'Learn more',\n  /** The description for the canvas linked banner popover */\n  'canvas.banner.popover-description':\n    'Canvas lets you author in a free-form editor that automatically maps back to the Studio as structured content - as you type.',\n  /** The heading for the canvas linked banner popover */\n  'canvas.banner.popover-heading': 'Idea first authoring',\n\n  /** The label used in the changes inspector for the from selector */\n  'changes.from.label': 'From',\n  /* The label for the history tab in the changes inspector*/\n  'changes.tab.history': 'History',\n  /* The label for the review tab in the changes inspector*/\n  'changes.tab.review-changes': 'Review changes',\n  /** The label used in the changes inspector for the to selector */\n  'changes.to.label': 'To',\n\n  /** The error message shown when the specified document comparison mode is not supported */\n  'compare-version.error.invalidModeParam':\n    '\"{{input}}\" is not a supported document comparison mode.',\n  /** The error message shown when the next document for comparison could not be extracted from the URL */\n  'compare-version.error.invalidNextDocumentParam': 'The next document parameter is invalid.',\n  /** The error message shown when the document comparison URL could not be parsed */\n  'compare-version.error.invalidParams.title': 'Unable to compare documents',\n  /** The error message shown when the previous document for comparison could not be extracted from the URL */\n  'compare-version.error.invalidPreviousDocumentParam':\n    'The previous document parameter is invalid.',\n  /** The text for the tooltip when the \"Compare versions\" action for a document is disabled */\n  'compare-versions.menu-item.disabled-reason':\n    'There are no other versions of this document to compare.',\n  /** The text for the \"Compare versions\" action for a document */\n  'compare-versions.menu-item.title': 'Compare versions',\n  /** The string used to label draft documents */\n  'compare-versions.status.draft': 'Draft',\n  /** The string used to label published documents */\n  'compare-versions.status.published': 'Published',\n  /** The title used when comparing versions of a document */\n  'compare-versions.title': 'Compare versions',\n\n  /** The text in the \"Cancel\" button in the confirm delete dialog that cancels the action and closes the dialog */\n  'confirm-delete-dialog.cancel-button.text': 'Cancel',\n  /** Used in `confirm-delete-dialog.cdr-summary.title` */\n  'confirm-delete-dialog.cdr-summary.document-count_one': '1 document',\n  /** Used in `confirm-delete-dialog.cdr-summary.title` */\n  'confirm-delete-dialog.cdr-summary.document-count_other': '{{count}} documents',\n  /** The text that appears in the subtitle `<summary>` that lists the datasets below the title */\n  'confirm-delete-dialog.cdr-summary.subtitle_one': 'Dataset: {{datasets}}',\n  /** The text that appears in the subtitle `<summary>` that lists the datasets below the title */\n  'confirm-delete-dialog.cdr-summary.subtitle_other': 'Datasets: {{datasets}}',\n  /** The text that appears in the subtitle `<summary>` that lists the datasets below the title */\n  'confirm-delete-dialog.cdr-summary.subtitle_unavailable_one': 'Unavailable dataset',\n  /** The text that appears in the subtitle `<summary>` that lists the datasets below the title */\n  'confirm-delete-dialog.cdr-summary.subtitle_unavailable_other': 'Unavailable datasets',\n  /** The text that appears in the title `<summary>` that includes the list of CDRs (singular) */\n  'confirm-delete-dialog.cdr-summary.title_one': '{{documentCount}} in another dataset',\n  /** The text that appears in the title `<summary>` that includes the list of CDRs (plural) */\n  'confirm-delete-dialog.cdr-summary.title_other': '{{documentCount}} in {{count}} datasets',\n  /** Appears when hovering over the copy button to copy */\n  'confirm-delete-dialog.cdr-table.copy-id-button.tooltip': 'Copy ID to clipboard',\n  /** The header for the dataset column in the list of cross-dataset references found */\n  'confirm-delete-dialog.cdr-table.dataset.label': 'Dataset',\n  /** The header for the document ID column in the list of cross-dataset references found */\n  'confirm-delete-dialog.cdr-table.document-id.label': 'Document ID',\n  /** The toast title when the copy button has been clicked but copying failed */\n  'confirm-delete-dialog.cdr-table.id-copied-toast.title-failed': 'Failed to copy document ID',\n  /** The header for the project ID column in the list of cross-dataset references found */\n  'confirm-delete-dialog.cdr-table.project-id.label': 'Project ID',\n  /** The text in the \"Delete anyway\" button in the confirm delete dialog that confirms the action */\n  'confirm-delete-dialog.confirm-anyway-button.text_delete': 'Delete anyway',\n  /** The text in the \"Unpublish anyway\" button in the confirm delete dialog that confirms the action */\n  'confirm-delete-dialog.confirm-anyway-button.text_unpublish': 'Unpublish anyway',\n  /** The text in the \"Delete now\" button in the confirm delete dialog that confirms the action */\n  'confirm-delete-dialog.confirm-button.text_delete': 'Delete now',\n  /** The text in the \"Unpublish now\" button in the confirm delete dialog that confirms the action */\n  'confirm-delete-dialog.confirm-button.text_unpublish': 'Unpublish now',\n  /** If no referring documents are found, this text appears above the cancel and confirmation buttons */\n  'confirm-delete-dialog.confirmation.text_delete':\n    'Are you sure you want to delete “<DocumentTitle/>”?',\n  /** If no referring documents are found, this text appears above the cancel and confirmation buttons */\n  'confirm-delete-dialog.confirmation.text_unpublish':\n    'Are you sure you want to unpublish “<DocumentTitle/>”?',\n  /** The text body of the error dialog. */\n  'confirm-delete-dialog.error.message.text':\n    'An error occurred while loading referencing documents.',\n  /** The text in the retry button of the confirm delete dialog if an error occurred. */\n  'confirm-delete-dialog.error.retry-button.text': 'Retry',\n  /** The header of the confirm delete dialog if an error occurred while the confirm delete dialog was open. */\n  'confirm-delete-dialog.error.title.text': 'Error',\n  /** The header of the confirm delete dialog */\n  'confirm-delete-dialog.header.text_delete': 'Delete document?',\n  /** The header of the confirm delete dialog */\n  'confirm-delete-dialog.header.text_unpublish': 'Unpublish document?',\n  /** The text that appears while the referring documents are queried */\n  'confirm-delete-dialog.loading.text': 'Looking for referring documents…',\n  /** Shown if there are references to other documents but the user does not have the permission to see the relevant document IDs */\n  'confirm-delete-dialog.other-reference-count.title_one': '1 other reference not show',\n  /** Shown if there are references to other documents but the user does not have the permission to see the relevant document IDs */\n  'confirm-delete-dialog.other-reference-count.title_other': '{{count}} other references not shown',\n  /** Text in the tooltip of this component if hovering over the info icon */\n  'confirm-delete-dialog.other-reference-count.tooltip':\n    \"We can't display metadata for these references due to a missing access token for the related datasets.\",\n  /** Appears when unable to render a document preview in the referring document list */\n  'confirm-delete-dialog.preview-item.preview-unavailable.subtitle': 'ID: {{documentId}}',\n  /** Appears when unable to render a document preview in the referring document list */\n  'confirm-delete-dialog.preview-item.preview-unavailable.title': 'Preview unavailable',\n  /** Warns the user of affects to other documents if the action is confirmed (delete) */\n  'confirm-delete-dialog.referential-integrity-disclaimer.text_delete':\n    'If you delete this document, documents that refer to it will no longer be able to access it.',\n  /** Warns the user of affects to other documents if the action is confirmed (unpublish) */\n  'confirm-delete-dialog.referential-integrity-disclaimer.text_unpublish':\n    'If you unpublish this document, documents that refer to it will no longer be able to access it.',\n  /** Tells the user the count of how many other referring documents there are before listing them. (singular) */\n  'confirm-delete-dialog.referring-document-count.text_one':\n    '1 document refers to “<DocumentTitle/>”',\n  /** Tells the user the count of how many other referring documents there are before listing them. (plural) */\n  'confirm-delete-dialog.referring-document-count.text_other':\n    '{{count}} documents refer to “<DocumentTitle/>”',\n  /** Describes the list of documents that refer to the one trying to be deleted (delete) */\n  'confirm-delete-dialog.referring-documents-descriptor.text_delete':\n    'You may not be able to delete “<DocumentTitle/>” because the following documents refer to it:',\n  /** Describes the list of documents that refer to the one trying to be deleted (unpublish) */\n  'confirm-delete-dialog.referring-documents-descriptor.text_unpublish':\n    'You may not be able to unpublish “<DocumentTitle/>” because the following documents refer to it:',\n\n  /** The text for the cancel button in the confirm dialog used in document action shortcuts if none is provided */\n  'confirm-dialog.cancel-button.fallback-text': 'Cancel',\n  /** The text for the confirm button in the confirm dialog used in document action shortcuts if none is provided */\n  'confirm-dialog.confirm-button.fallback-text': 'Confirm',\n\n  /** For the default structure definition, the title for the \"Content\" pane */\n  'default-definition.content-title': 'Content',\n\n  /** The text shown if there was an error while getting the document's title via a preview value */\n  'doc-title.error.text': 'Error: {{errorMessage}}',\n  /** The text shown if the preview value for a document is non-existent or empty */\n  'doc-title.fallback.text': 'Untitled',\n  /** The text shown if a document's title via a preview value cannot be determined due to an unknown schema type */\n  'doc-title.unknown-schema-type.text': 'Unknown schema type: {{schemaType}}',\n\n  /** Tooltip text shown for the close button of the document inspector */\n  'document-inspector.close-button.tooltip': 'Close',\n  /** The title shown in the dialog header, when inspecting a valid document */\n  'document-inspector.dialog.title': 'Inspecting <DocumentTitle/>',\n  /** The title shown in the dialog header, when the document being inspected is not created yet/has no value */\n  'document-inspector.dialog.title-no-value': 'No value',\n  /** Title shown for menu item that opens the \"Inspect\" dialog */\n  'document-inspector.menu-item.title': 'Inspect',\n  /** the placeholder text for the search input on the inspect dialog */\n  'document-inspector.search.placeholder': 'Search',\n  /** The \"parsed\" view mode, meaning the JSON is searchable, collapsible etc */\n  'document-inspector.view-mode.parsed': 'Parsed',\n  /** The \"raw\" view mode, meaning the JSON is presented syntax-highlighted, but with no other features - optimal for copying */\n  'document-inspector.view-mode.raw-json': 'Raw JSON',\n\n  /** The text for when a form is hidden */\n  'document-view.form-view.form-hidden': 'This form is hidden',\n  /** Fallback title shown when a form title is not provided */\n  'document-view.form-view.form-title-fallback': 'Untitled',\n  /** The text for when the form view is loading a document */\n  'document-view.form-view.loading': 'Loading document…',\n  /** The description of the sync lock toast on the form view */\n  'document-view.form-view.sync-lock-toast.description':\n    'Please hold tight while the document is synced. This usually happens right after the document has been published, and it should not take more than a few seconds',\n  /** The title of the sync lock toast on the form view */\n  'document-view.form-view.sync-lock-toast.title': 'Syncing document…',\n\n  /** The description for the document favorite action */\n  'document.favorites.add-to-favorites': 'Add to favorites',\n  /** The description for the document unfavorite action */\n  'document.favorites.remove-from-favorites': 'Remove from favorites',\n\n  /**The title for the menu items that will be shown when expanding a publish release event to inspect the document */\n  'events.inspect.release': 'Inspect <VersionBadge>{{releaseTitle}}</VersionBadge> document',\n  /**The title for the menu items that will be shown when expanding a publish draft event to inspect the draft document*/\n  'events.open.draft': 'Open <VersionBadge>draft</VersionBadge> document',\n  /**The title for the menu items that will be shown when expanding a publish release event to inspect the release*/\n  'events.open.release': 'Open <VersionBadge>{{releaseTitle}}</VersionBadge> release',\n  /** The loading messaging for when the tooltip is still loading permission info */\n  'insufficient-permissions-message-tooltip.loading-text': 'Loading…',\n\n  /** --- Menu items --- */\n  /** The menu item group title to use for the Action menu items */\n  'menu-item-groups.actions-group': 'Actions',\n  /** The menu item group title to use for the Layout menu items */\n  'menu-item-groups.layout-group': 'Layout',\n  /** The menu item group title to use for the Sort menu items */\n  'menu-item-groups.sorting-group': 'Sort',\n\n  /** The menu item title to use the compact view */\n  'menu-items.layout.compact-view': 'Compact view',\n  /** The menu item title to use the detailed view */\n  'menu-items.layout.detailed-view': 'Detailed view',\n  /** The menu item title to Sort by Created */\n  'menu-items.sort-by.created': 'Sort by Created',\n  /** The menu item title to Sort by Last Edited */\n  'menu-items.sort-by.last-edited': 'Sort by Last Edited',\n\n  /** The link text of the no document type screen that appears directly below the subtitle */\n  'no-document-types-screen.link-text': 'Learn how to add a document type →',\n  /** The subtitle of the no document type screen that appears directly below the title */\n  'no-document-types-screen.subtitle': 'Please define at least one document type in your schema.',\n\n  /** The title of the no document type screen */\n  'no-document-types-screen.title': 'No document types',\n\n  /** Text shown on back button visible on smaller breakpoints */\n  'pane-header.back-button.text': 'Back',\n  /** tooltip text (via `title` attribute) for the menu button */\n  'pane-header.context-menu-button.tooltip': 'Show menu',\n  /** Appears in a document list pane header if there are more than one option for create. This is the label for that menu */\n  'pane-header.create-menu.label': 'Create',\n  /** Tooltip displayed on the create new button in document lists */\n  'pane-header.create-new-button.tooltip': 'Create new document',\n  /** The `aria-label` for the disabled button in the pane header if create permissions are granted */\n  'pane-header.disabled-created-button.aria-label': 'Insufficient permissions',\n  /** The text shown in the tooltip of pane item previews of documents if there are unpublished edits */\n  'pane-item.draft-status.has-draft.tooltip': 'Edited <RelativeTime/>',\n  /** The text shown in the tooltip of pane item previews of documents if there are no unpublished edits */\n  'pane-item.draft-status.no-draft.tooltip': 'No unpublished edits',\n  /** The subtitle tor pane item previews if there isn't a matching schema type found */\n  'pane-item.missing-schema-type.subtitle': 'Document: <Code>{{documentId}}</Code>',\n  /** The title tor pane item previews if there isn't a matching schema type found */\n  'pane-item.missing-schema-type.title': 'No schema found for type <Code>{{documentType}}</Code>',\n  /** The text shown in the tooltip of pane item previews of documents if there are unpublished edits */\n  'pane-item.published-status.has-published.tooltip': 'Published <RelativeTime/>',\n  /** The text shown in the tooltip of pane item previews of documents if there are no unpublished edits */\n  'pane-item.published-status.no-published.tooltip': 'No unpublished edits',\n  /** The text used in the document header title if there is an error */\n  'panes.document-header-title.error.text': 'Error: {{error}}',\n  /** The text used in the document header title if creating a new item */\n  'panes.document-header-title.new.text': 'New {{schemaType}}',\n  /** The text used in the document header title if no other title can be determined */\n  'panes.document-header-title.untitled.text': 'Untitled',\n  /** The help text saying that we have given up on automatic retry */\n  'panes.document-list-pane.error.max-retries-attempted':\n    'Not automatically retrying after {{count}} unsuccessful attempts.',\n  /** The help text saying that we'll retry fetching the document list */\n  'panes.document-list-pane.error.retrying': 'Retrying…',\n  /** The error text on the document list pane */\n  'panes.document-list-pane.error.text': 'Encountered an error while fetching documents.',\n  /** The error text on the document list pane */\n  'panes.document-list-pane.error.text.dev': 'Error: <Code>{{error}}</Code>',\n  /** The error text on the document list pane if the browser appears to be offlline */\n  'panes.document-list-pane.error.text.offline': 'The Internet connection appears to be offline.',\n  /** The error title on the document list pane */\n  'panes.document-list-pane.error.title': 'Could not fetch list items',\n  /** The help text saying that we'll retry fetching the document list */\n  'panes.document-list-pane.error.will-retry-automatically_one': 'Retrying…',\n  'panes.document-list-pane.error.will-retry-automatically_other': 'Retrying… (#{{count}}).',\n  /** The text of the document list pane if more than a maximum number of documents are returned */\n  'panes.document-list-pane.max-items.text': 'Displaying a maximum of {{limit}} documents',\n  /** The text of the document list pane if no documents are found for a specified type */\n  'panes.document-list-pane.no-documents-of-type.text': 'No documents of this type',\n  /** The text of the document list pane if no documents are found */\n  'panes.document-list-pane.no-documents.text': 'No results found',\n  /** The text of the document list pane if no documents are found matching specified criteria */\n  'panes.document-list-pane.no-matching-documents.text': 'No matching documents',\n  /** The search input for the search input on the document list pane */\n  'panes.document-list-pane.reconnecting': 'Trying to connect…',\n  /** The aria-label for the search input on the document list pane */\n  'panes.document-list-pane.search-input.aria-label': 'Search list',\n  /** The search input for the search input on the document list pane */\n  'panes.document-list-pane.search-input.placeholder': 'Search list',\n  /** The summary title when displaying an error for a document operation result */\n  'panes.document-operation-results.error.summary.title': 'Details',\n  /** The text when a generic operation failed (fallback, generally not shown)  */\n  'panes.document-operation-results.operation-error': 'An error occurred during {{context}}',\n  /** The text when a delete operation failed  */\n  'panes.document-operation-results.operation-error_delete':\n    'An error occurred while attempting to delete this document. This usually means that there are other documents that refers to it.',\n  /** The text when an unpublish operation failed  */\n  'panes.document-operation-results.operation-error_unpublish':\n    'An error occurred while attempting to unpublish this document. This usually means that there are other documents that refers to it.',\n  /** The text when a generic operation succeeded (fallback, generally not shown)  */\n  'panes.document-operation-results.operation-success': 'Successfully performed {{op}} on document',\n  /** The text when copy URL operation succeeded  */\n  'panes.document-operation-results.operation-success_copy-url': 'Document URL copied to clipboard',\n  /**  */\n  'panes.document-operation-results.operation-success_createVersion':\n    '<Strong>{{title}}</Strong> was added to the release',\n  /** The text when a delete operation succeeded  */\n  'panes.document-operation-results.operation-success_delete':\n    'The document was successfully deleted',\n  /** The text when a discard changes operation succeeded  */\n  'panes.document-operation-results.operation-success_discardChanges':\n    'All changes since last publish has now been discarded. The discarded draft can still be recovered from history',\n  /** The text when a duplicate operation succeeded  */\n  'panes.document-operation-results.operation-success_duplicate':\n    'The document was successfully duplicated',\n  /** The text when a publish operation succeeded  */\n  'panes.document-operation-results.operation-success_publish':\n    '<Strong>{{title}}</Strong> was published',\n  /** The text when a restore operation succeeded  */\n  'panes.document-operation-results.operation-success_restore':\n    '<Strong>{{title}}</Strong> was restored',\n  /** The text when an unpublish operation succeeded  */\n  'panes.document-operation-results.operation-success_unpublish':\n    '<Strong>{{title}}</Strong> was unpublished. A draft has been created from the latest published revision.',\n  /** The document title shown when document title is \"undefined\" in operation message */\n  'panes.document-operation-results.operation-undefined-title': 'Untitled',\n  /** The loading message for the document not found pane */\n  'panes.document-pane.document-not-found.loading': 'Loading document…',\n  /** The text of the document not found pane if the schema is known */\n  'panes.document-pane.document-not-found.text':\n    'The document type is not defined, and a document with the <Code>{{id}}</Code> identifier could not be found.',\n  /** The title of the document not found pane if the schema is known */\n  'panes.document-pane.document-not-found.title': 'The document was not found',\n  /** The text of the document not found pane if the schema is not found */\n  'panes.document-pane.document-unknown-type.text':\n    'This document has the schema type <Code>{{documentType}}</Code>, which is not defined as a type in the local content studio schema.',\n  /** The title of the document not found pane if the schema is not found or unknown */\n  'panes.document-pane.document-unknown-type.title':\n    'Unknown document type: <Code>{{documentType}}</Code>',\n  /** The title of the document not found pane if the schema is unknown */\n  'panes.document-pane.document-unknown-type.without-schema.text':\n    'This document does not exist, and no schema type was specified for it.',\n  /** Default message shown while resolving the structure definition for an asynchronous node */\n  'panes.resolving.default-message': 'Loading…',\n  /** Message shown while resolving the structure definition for an asynchronous node and it is taking a while (more than 5s) */\n  'panes.resolving.slow-resolve-message': 'Still loading…',\n  /** The text to display when type is missing */\n  'panes.unknown-pane-type.missing-type.text':\n    'Structure item is missing required <Code>type</Code> property.',\n  /** The title of the unknown pane */\n  'panes.unknown-pane-type.title': 'Unknown pane type',\n  /** The text to display when type is unknown */\n  'panes.unknown-pane-type.unknown-type.text':\n    'Structure item of type <Code>{{type}}</Code> is not a known entity.',\n\n  /** The text for the \"Open preview\" action for a document */\n  'production-preview.menu-item.title': 'Open preview',\n\n  /** The text for the confirm button in the request permission dialog used in the permissions banner */\n  'request-permission-dialog.confirm-button.text': 'Send request',\n  /** The description text for the request permission dialog used in the permissions banner */\n  'request-permission-dialog.description.text':\n    \"Your request will be sent to the project administrator(s). If you'd like, you can also include a note\",\n  /** The header/title for the request permission dialog used in the permissions banner */\n  'request-permission-dialog.header.text': 'Ask for edit access',\n  /** The text describing the note input for the request permission dialog used in the permissions banner */\n  'request-permission-dialog.note-input.description.text': \"If you'd like, you can add a note\",\n  /** The placeholder for the note input in the request permission dialog used in the permissions banner */\n  'request-permission-dialog.note-input.placeholder.text': 'Add note...',\n  /** The error/warning text in the request permission dialog when the user's request has been declined */\n  'request-permission-dialog.warning.denied.text':\n    'Your request to access this project has been declined.',\n  /** The error/warning text in the request permission dialog when the user's request has been denied due to too many outstanding requests */\n  'request-permission-dialog.warning.limit-reached.text':\n    \"You've reached the limit for role requests across all projects. Please wait before submitting more requests or contact an administrator for assistance.\",\n\n  /** Label for button when status is saved */\n  'status-bar.document-status-pulse.status.saved.text': 'Saved',\n  /** Label for button when status is syncing */\n  'status-bar.document-status-pulse.status.syncing.text': 'Saving...',\n  /** Accessibility label indicating when the document was last published, in relative time, eg \"3 weeks ago\" */\n  'status-bar.publish-status-button.last-published-time.aria-label':\n    'Last published {{relativeTime}}',\n  /** Text for tooltip showing explanation of timestamp/relative time, eg \"Last published <RelativeTime/>\" */\n  'status-bar.publish-status-button.last-published-time.tooltip': 'Last published <RelativeTime/>',\n  /** Accessibility label indicating when the document was last updated, in relative time, eg \"2 hours ago\" */\n  'status-bar.publish-status-button.last-updated-time.aria-label': 'Last updated {{relativeTime}}',\n  /** Text for tooltip showing explanation of timestamp/relative time, eg \"Last updated <RelativeTime/>\" */\n  'status-bar.publish-status-button.last-updated-time.tooltip': 'Last updated <RelativeTime/>',\n  /** Aria label for the button */\n  'status-bar.review-changes-button.aria-label': 'Review changes',\n  /** Label for button when status is saved */\n  'status-bar.review-changes-button.status.saved.text': 'Saved!',\n  /** Label for button when status is syncing */\n  'status-bar.review-changes-button.status.syncing.text': 'Saving...',\n  /** Text for the secondary text for tooltip for the button */\n  'status-bar.review-changes-button.tooltip.changes-saved': 'Changes saved',\n  /** Primary text for tooltip for the button */\n  'status-bar.review-changes-button.tooltip.text': 'Review changes',\n\n  /** The text that appears in side the documentation link */\n  'structure-error.docs-link.text': 'View documentation',\n  /** Labels the error message or error stack of the structure error screen */\n  'structure-error.error.label': 'Error',\n  /** The header that appears at the top of the error screen */\n  'structure-error.header.text': 'Encountered an error while reading structure',\n  /** The text in the reload button to retry rendering the structure */\n  'structure-error.reload-button.text': 'Reload',\n  /** Labels the structure path of the structure error screen */\n  'structure-error.structure-path.label': 'Structure path',\n\n  /** The aria label for the menu button in the timeline item */\n  'timeline-item.menu-button.aria-label': 'Open action menu',\n  /** The text for the tooltip in menu button the timeline item */\n  'timeline-item.menu-button.tooltip': 'Actions',\n  /** The text for the collapse action in the timeline item menu */\n  'timeline-item.menu.action-collapse': 'Collapse',\n  /** The text for the expand action in the timeline item menu */\n  'timeline-item.menu.action-expand': 'Expand',\n})\n\n/**\n * @alpha\n */\nexport type StructureLocaleResourceKeys = keyof typeof structureLocaleStrings\n\nexport default structureLocaleStrings\n"], "mappings": ";;;;;;;;;;;;;;AAQMA,IAAAA,yBAAyBC,uBAAuB,aAAa;;EAEjE,kCAAkC;;EAElC,oCAAoC;;EAEpC,4CACE;;EAEF,uBAAuB;;EAEvB,+BAA+B;;EAE/B,qCACE;;EAEF,iEACE;;EAEF,6CAA6C;;EAE7C,iDAAiD;;EAEjD,6CAA6C;;EAE7C,gCAAgC;;EAGhC,uCAAuC;;EAEvC,kDACE;;EAEF,0BAA0B;;EAE1B,kCAAkC;;EAElC,wDAAwD;;EAExD,4CAA4C;;EAG5C,qCAAqC;;EAErC,8BAA8B;;EAE9B,wBAAwB;;EAExB,kCAAkC;;EAElC,6CACE;;EAEF,oCACE;;EAEF,qCAAqC;;EAErC,kCAAkC;;EAElC,gCAAgC;;EAEhC,4CACE;;EAEF,0BAA0B;;EAG1B,kCAAkC;;EAElC,kDAAkD;;EAGlD,wBAAwB;;EAExB,0BAA0B;;EAG1B,2CAA2C;;EAE3C,uCAAuC;;EAEvC,0BAA0B;;EAE1B,uCACE;;EAEF,wCACE;;EAEF,uDAAuD;;EAEvD,wCAAwC;;EAExC,gDAAgD;;EAEhD,kDAAkD;;EAElD,kDAAkD;;EAGlD,uCACE;;EAEF,iEACE;;EAEF,mEACE;;EAEF,iEACE;;EAEF,mEACE;;EAEF,kEAAkE;;EAElE,kEAAkE;;EAElE,yCACE;;EAEF,sEAAsE;;EAEtE,wDACE;;EAEF,qEAAqE;;EAErE,wDACE;;EAEF,yCAAyC;;EAEzC,uCAAuC;;EAEvC,qCACE;;EAEF,+BAA+B;;EAE/B,gDAAgD;;EAEhD,wDAAwD;;EAExD,0DAA0D;;EAE1D,6DACE;;EAEF,+DACE;;EAEF,uDACE;;EAEF,kCAAkC;;EAElC,uCACE;;EAEF,iCAAiC;;EAEjC,2CACE;;EAGF,uCAAuC;;EAEvC,4CAA4C;;EAG5C,yCAAyC;;EAEzC,sCAAsC;;EAGtC,wCAAwC;;EAExC,qCAAqC;;EAErC,yCAAyC;;EAEzC,+CAA+C;;EAG/C,uCAAuC;;EAEvC,mCAAmC;;EAEnC,uCAAuC;;EAEvC,qCAAqC;;EAErC,qCAAqC;;EAErC,qCACE;;EAEF,iCAAiC;;EAGjC,sBAAsB;;EAEtB,uBAAuB;;EAEvB,8BAA8B;;EAE9B,oBAAoB;;EAGpB,0CACE;;EAEF,kDAAkD;;EAElD,6CAA6C;;EAE7C,sDACE;;EAEF,8CACE;;EAEF,oCAAoC;;EAEpC,iCAAiC;;EAEjC,qCAAqC;;EAErC,0BAA0B;;EAG1B,4CAA4C;;EAE5C,wDAAwD;;EAExD,0DAA0D;;EAE1D,kDAAkD;;EAElD,oDAAoD;;EAEpD,8DAA8D;;EAE9D,gEAAgE;;EAEhE,+CAA+C;;EAE/C,iDAAiD;;EAEjD,0DAA0D;;EAE1D,iDAAiD;;EAEjD,qDAAqD;;EAErD,gEAAgE;;EAEhE,oDAAoD;;EAEpD,2DAA2D;;EAE3D,8DAA8D;;EAE9D,oDAAoD;;EAEpD,uDAAuD;;EAEvD,kDACE;;EAEF,qDACE;;EAEF,4CACE;;EAEF,iDAAiD;;EAEjD,0CAA0C;;EAE1C,4CAA4C;;EAE5C,+CAA+C;;EAE/C,sCAAsC;;EAEtC,yDAAyD;;EAEzD,2DAA2D;;EAE3D,uDACE;;EAEF,mEAAmE;;EAEnE,gEAAgE;;EAEhE,sEACE;;EAEF,yEACE;;EAEF,2DACE;;EAEF,6DACE;;EAEF,oEACE;;EAEF,uEACE;;EAGF,8CAA8C;;EAE9C,+CAA+C;;EAG/C,oCAAoC;;EAGpC,wBAAwB;;EAExB,2BAA2B;;EAE3B,sCAAsC;;EAGtC,2CAA2C;;EAE3C,mCAAmC;;EAEnC,4CAA4C;;EAE5C,sCAAsC;;EAEtC,yCAAyC;;EAEzC,uCAAuC;;EAEvC,yCAAyC;;EAGzC,uCAAuC;;EAEvC,+CAA+C;;EAE/C,mCAAmC;;EAEnC,uDACE;;EAEF,iDAAiD;;EAGjD,uCAAuC;;EAEvC,4CAA4C;;EAG5C,0BAA0B;;EAE1B,qBAAqB;;EAErB,uBAAuB;;EAEvB,yDAAyD;;;EAIzD,kCAAkC;;EAElC,iCAAiC;;EAEjC,kCAAkC;;EAGlC,kCAAkC;;EAElC,mCAAmC;;EAEnC,8BAA8B;;EAE9B,kCAAkC;;EAGlC,sCAAsC;;EAEtC,qCAAqC;;EAGrC,kCAAkC;;EAGlC,gCAAgC;;EAEhC,2CAA2C;;EAE3C,iCAAiC;;EAEjC,yCAAyC;;EAEzC,kDAAkD;;EAElD,4CAA4C;;EAE5C,2CAA2C;;EAE3C,0CAA0C;;EAE1C,uCAAuC;;EAEvC,oDAAoD;;EAEpD,mDAAmD;;EAEnD,0CAA0C;;EAE1C,wCAAwC;;EAExC,6CAA6C;;EAE7C,wDACE;;EAEF,2CAA2C;;EAE3C,uCAAuC;;EAEvC,2CAA2C;;EAE3C,+CAA+C;;EAE/C,wCAAwC;;EAExC,+DAA+D;EAC/D,iEAAiE;;EAEjE,2CAA2C;;EAE3C,sDAAsD;;EAEtD,8CAA8C;;EAE9C,uDAAuD;;EAEvD,yCAAyC;;EAEzC,oDAAoD;;EAEpD,qDAAqD;;EAErD,wDAAwD;;EAExD,oDAAoD;;EAEpD,2DACE;;EAEF,8DACE;;EAEF,sDAAsD;;EAEtD,+DAA+D;;EAE/D,oEACE;;EAEF,6DACE;;EAEF,qEACE;;EAEF,gEACE;;EAEF,8DACE;;EAEF,8DACE;;EAEF,gEACE;;EAEF,8DAA8D;;EAE9D,kDAAkD;;EAElD,+CACE;;EAEF,gDAAgD;;EAEhD,kDACE;;EAEF,mDACE;;EAEF,iEACE;;EAEF,mCAAmC;;EAEnC,wCAAwC;;EAExC,6CACE;;EAEF,iCAAiC;;EAEjC,6CACE;;EAGF,sCAAsC;;EAGtC,iDAAiD;;EAEjD,8CACE;;EAEF,yCAAyC;;EAEzC,yDAAyD;;EAEzD,yDAAyD;;EAEzD,iDACE;;EAEF,wDACE;;EAGF,sDAAsD;;EAEtD,wDAAwD;;EAExD,mEACE;;EAEF,gEAAgE;;EAEhE,iEAAiE;;EAEjE,8DAA8D;;EAE9D,+CAA+C;;EAE/C,sDAAsD;;EAEtD,wDAAwD;;EAExD,0DAA0D;;EAE1D,iDAAiD;;EAGjD,kCAAkC;;EAElC,+BAA+B;;EAE/B,+BAA+B;;EAE/B,sCAAsC;;EAEtC,wCAAwC;;EAGxC,wCAAwC;;EAExC,qCAAqC;;EAErC,sCAAsC;;EAEtC,oCAAoC;AACtC,CAAC;", "names": ["structureLocaleStrings", "defineLocalesResources"]}