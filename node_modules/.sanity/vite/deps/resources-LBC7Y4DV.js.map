{"version": 3, "sources": ["../../../sanity/src/core/comments/i18n/resources.ts"], "sourcesContent": ["/* eslint sort-keys: \"error\" */\nimport {defineLocalesResources} from '../../i18n'\n\n/**\n * Defined locale strings for the comments feature, in US English.\n *\n * @internal\n */\nconst commentsLocaleStrings = defineLocalesResources('comments', {\n  /** The close comments button text */\n  'close-pane-button-text': 'Close comments',\n  /** The aria label for the close comments button */\n  'close-pane-button-text-aria-label': 'Close comments',\n\n  /** When composing a comment, the placeholder text shown when adding a comment to a field with no current comments */\n  'compose.add-comment-input-placeholder': 'Add comment to <strong>{{field}}</strong>',\n  /** When composing a comment, the placeholder text shown when adding a comment to a field with no current comments and the mode is upsell */\n  'compose.add-comment-input-placeholder-upsell': 'Upgrade to add comment',\n  /** When composing a comment, the placeholder text shown when the input is empty */\n  'compose.create-comment-placeholder': 'Create a new comment',\n  /** When composing a comment, the aria label for the button to mention a user */\n  'compose.mention-user-aria-label': 'Mention user',\n  /** When composing a comment, the tooltip text for the button to mention a user */\n  'compose.mention-user-tooltip': 'Mention user',\n  /** When composing a reply, the placeholder text shown when the input is empty */\n  'compose.reply-placeholder': 'Reply',\n  /** When composing a reply, the placeholder text shown when the input is empty and the mode is upsell */\n  'compose.reply-placeholder-upsell': 'Upgrade to reply',\n  /** When composing a comment, the aria label for the button to send a comment */\n  'compose.send-comment-aria-label': 'Send comment',\n  /** When composing a comment, the tooltip text for the button to send a comment */\n  'compose.send-comment-tooltip': 'Send comment',\n\n  /** The inspector text when error copying link */\n  'copy-link-error-message': 'Unable to copy link to clipboard',\n\n  /** The delete dialog body for a comment */\n  'delete-comment.body': 'Once deleted, a comment cannot be recovered.',\n  /** The delete dialog confirm button text for a comment */\n  'delete-comment.confirm': 'Delete comment',\n  /** The delete dialog title for a comment */\n  'delete-comment.title': 'Delete this comment?',\n  /** The delete dialog error */\n  'delete-dialog.error': 'An error occurred while deleting the comment. Please try again.',\n  /** The delete dialog body for a thread */\n  'delete-thread.body':\n    'This comment and its replies will be deleted, and once deleted cannot be recovered.',\n  /** The delete dialog conform button text for a thread */\n  'delete-thread.confirm': 'Delete thread',\n  /** The delete dialog title for a thread */\n  'delete-thread.title': 'Delete this comment thread?',\n\n  /** The button text for confirming discard */\n  'discard.button-confirm': 'Discard',\n  /** The header for discard comment dialog */\n  'discard.header': 'Discard comment?',\n  /** The text for discard comment dialog */\n  'discard.text': 'Do you want to discard the comment?',\n\n  /** Sharing feedback on the comments feature: The link title */\n  'feature-feedback.link': 'Share your feedback',\n  /** Sharing feedback on the comments feature: The form title  */\n  'feature-feedback.title': 'Help improve ',\n\n  /** The name of the comments feature, for use in header. Capitalized, eg \"Comments\". */\n  'feature-name': 'Comments',\n\n  /** Aria label for button above fields to add a comment, when the field currently do not have any comments */\n  'field-button.aria-label-add': 'Add comment',\n  /** Aria label for button above fields that opens the comments for this field, when there are existing comments */\n  'field-button.aria-label-open': 'Open comments',\n  /** Text shown in popover when hovering the button above fields that opens the comments panel, when there is a single comment present */\n  'field-button.content_one': 'View comment',\n  /** Text shown in popover when hovering the button above fields that opens the comments panel, when there are more than one comment present */\n  'field-button.content_other': 'View comments',\n  /** Text shown in popover when hovering the button above fields to add a comment, when the field currently do not have any comments */\n  'field-button.title': 'Add comment',\n\n  /* The text shown in the inline comment button when the button is disabled due to overlap */\n  'inline-add-comment-button.disabled-overlap-title': 'Comments cannot overlap',\n  /** The text shown in the inline comment button */\n  'inline-add-comment-button.title': 'Add comment',\n\n  /** Aria label for the breadcrumb button showing the field path. `{{field}}` is the last (most specific) field. */\n  'list-item.breadcrumb-button-go-to-field-aria-label': 'Go to {{field}} field',\n  /** The button tooltip content for the add reaction button */\n  'list-item.context-menu-add-reaction': 'Add reaction',\n  /** The button tooltip aria label for adding a reaction */\n  'list-item.context-menu-add-reaction-aria-label': 'Add reaction',\n  /** The button tooltip content for the add reaction button and mode is upsell */\n  'list-item.context-menu-add-reaction-upsell': 'Upgrade to add reaction',\n  /** The action menu item for copying a comment link */\n  'list-item.copy-link': 'Copy link to comment',\n  /** The action menu item for deleting a comment */\n  'list-item.delete-comment': 'Delete comment',\n  /** The action menu item for editing a comment */\n  'list-item.edit-comment': 'Edit comment',\n  /** The action menu item for editing a comment and the mode is upsell */\n  'list-item.edit-comment-upsell': 'Upgrade to edit comment',\n  /** Aria label for the button that takes you to the field, which wraps a thread/comment */\n  'list-item.go-to-field-button.aria-label': 'Go to field',\n  /**\n   * The text shown below the author and timestamp of a comment including a link back to the context in which the comment was made.\n   * Consists of a document title wrapped in a link, and a word or phrase to indicate that the link refers to a location:\n   * eg \"on Home\", \"on Coffee Machine | Products\", \"on Pricing – Sanity\"\n   */\n  'list-item.layout-context': 'on <IntentLink>{{title}}</IntentLink>',\n  /** The marker to indicate that a comment has been edited in brackets */\n  'list-item.layout-edited': 'edited',\n  /** The error text when sending a comment has failed */\n  'list-item.layout-failed-sent': 'Failed to send.',\n  /** The loading message when posting a comment is in progress */\n  'list-item.layout-posting': 'Posting...',\n  /** The text for retrying posting a comment */\n  'list-item.layout-retry': 'Retry',\n  /** The text shown when the value a comment references has been deleted */\n  'list-item.missing-referenced-value-tooltip-content': 'The commented text has been deleted',\n  /** The aria label for the comments menu button to open the actions menu */\n  'list-item.open-menu-aria-label': 'Open comment actions menu',\n  /** The button text to re-open a resolved comment  */\n  'list-item.re-open-resolved': 'Re-open',\n  /** The button aria label to re-open a comment that is resolved */\n  'list-item.re-open-resolved-aria-label': 'Re-open',\n  /** The button aria label to mark a comment as resolved */\n  'list-item.resolved-tooltip-aria-label': 'Mark comment as resolved',\n  /** The button text to mark a comment as resolved */\n  'list-item.resolved-tooltip-content': 'Mark as resolved',\n\n  /** The empty state text for open comments */\n  'list-status.empty-state-open-text': 'Open comments on this document will be shown here.',\n  /** The empty state title for open comments */\n  'list-status.empty-state-open-title': 'No open comments yet',\n  /** The empty state text for resolved comments */\n  'list-status.empty-state-resolved-text': 'Resolved comments on this document will be shown here.',\n  /** The empty state title for resolved comments */\n  'list-status.empty-state-resolved-title': 'No resolved comments yet',\n  /** The list status message for error */\n  'list-status.error': 'Something went wrong',\n  /** The list status message for loading status */\n  'list-status.loading': 'Loading comments',\n\n  /** Text shown when no users can be found to mention */\n  'mentions.no-users-found': 'No users found',\n  /** Label/badge shown for users that are not authorized to see the document, and thus cannot be mentioned */\n  'mentions.unauthorized-user': 'Unauthorized',\n  /** Aria label for the command list for users to mention */\n  'mentions.user-list-aria-label': 'List of users to mention',\n\n  /** The comments onboarding popover text */\n  'onboarding.body':\n    \"You can add comments to any field in a document. They'll show up here, grouped by field.\",\n  /** The comments onboarding dismiss text */\n  'onboarding.dismiss': 'Got it',\n  /** The comments onboarding popover header text */\n  'onboarding.header': 'Document fields now have comments',\n\n  /** Tooltip for the button to add a reaction to a comment */\n  'reactions.add-reaction-tooltip': 'Add reaction',\n  /** Aria label for the individual reactions you can choose from when reacting to a comment */\n  'reactions.react-with-aria-label': 'React with {{reactionName}}',\n  /** When a users' name cannot be resolved, fall back to this name */\n  'reactions.user-list.unknown-user-fallback-name': 'Unknown user',\n  /**\n   * When showing list of users who have reacted, replaces your own name with \"you\", eg\n   * \"Donna, you, James, and Tyler reacted with 👍\". A different key (`_leading` suffix)\n   * is used when you are the first to react, eg \"You, Donna and Tyler reacted with 👍\".\n   * Use `{{name}}` if you want to instead use the current users' actual name.\n   */\n  'reactions.user-list.you': 'you',\n  /**\n   * When showing list of users who have reacted, replaces your own name with \"You\", eg\n   * \"You, Donna, James, and Tyler reacted with 👍\". A different key (`_leading` suffix)\n   * is used when you are not the first to react, eg \"Donna, you, James and Tyler reacted with 👍\".\n   * Use `{{name}}` if you want to instead use the current users' actual name.\n   */\n  'reactions.user-list.you_leading': 'You',\n  /**\n   * The text shown for the tooltip that appears when hovering over the reaction count, eg\n   * \"Donna, James, and Tyler Reacted with 👍\". Three components are available for use:\n   * - `<UserList/>` - the list of names of users who have reacted, using the locales list format\n   * - `<Text>` - should be wrapped around the text describing the action (\"reacted with\")\n   * - `<ReactionName/>` - the name of the reaction emoji, eg \":heavy_plus_sign:\"\n   */\n  'reactions.users-reacted-with-reaction': '<UserList/> <Text>reacted with</Text> <ReactionName/>',\n\n  /** Status filter: The short title describing filtering on open (non-resolved) comments */\n  'status-filter.status-open': 'Open',\n  /** Status filter: The full text for describing filtering on open (non-resolved) comments */\n  'status-filter.status-open-full': 'Open comments',\n  /** Status filter: The short title describing filtering on resolved comments */\n  'status-filter.status-resolved': 'Resolved',\n  /** Status filter: The full text for describing filtering on resolved comments */\n  'status-filter.status-resolved-full': 'Resolved comments',\n  /** Status filter: The full text for describing filtering on resolved comments and is upsell mode */\n  'status-filter.status-resolved-full-upsell': 'Upgrade to see resolved comments',\n})\n\n/**\n * @alpha\n */\nexport type CommentsLocaleResourceKeys = keyof typeof commentsLocaleStrings\n\nexport default commentsLocaleStrings\n"], "mappings": ";;;;;;;;;;;;;;AAQMA,IAAAA,wBAAwBC,uBAAuB,YAAY;;EAE/D,0BAA0B;;EAE1B,qCAAqC;;EAGrC,yCAAyC;;EAEzC,gDAAgD;;EAEhD,sCAAsC;;EAEtC,mCAAmC;;EAEnC,gCAAgC;;EAEhC,6BAA6B;;EAE7B,oCAAoC;;EAEpC,mCAAmC;;EAEnC,gCAAgC;;EAGhC,2BAA2B;;EAG3B,uBAAuB;;EAEvB,0BAA0B;;EAE1B,wBAAwB;;EAExB,uBAAuB;;EAEvB,sBACE;;EAEF,yBAAyB;;EAEzB,uBAAuB;;EAGvB,0BAA0B;;EAE1B,kBAAkB;;EAElB,gBAAgB;;EAGhB,yBAAyB;;EAEzB,0BAA0B;;EAG1B,gBAAgB;;EAGhB,+BAA+B;;EAE/B,gCAAgC;;EAEhC,4BAA4B;;EAE5B,8BAA8B;;EAE9B,sBAAsB;;EAGtB,oDAAoD;;EAEpD,mCAAmC;;EAGnC,sDAAsD;;EAEtD,uCAAuC;;EAEvC,kDAAkD;;EAElD,8CAA8C;;EAE9C,uBAAuB;;EAEvB,4BAA4B;;EAE5B,0BAA0B;;EAE1B,iCAAiC;;EAEjC,2CAA2C;;;;;;EAM3C,4BAA4B;;EAE5B,2BAA2B;;EAE3B,gCAAgC;;EAEhC,4BAA4B;;EAE5B,0BAA0B;;EAE1B,sDAAsD;;EAEtD,kCAAkC;;EAElC,8BAA8B;;EAE9B,yCAAyC;;EAEzC,yCAAyC;;EAEzC,sCAAsC;;EAGtC,qCAAqC;;EAErC,sCAAsC;;EAEtC,yCAAyC;;EAEzC,0CAA0C;;EAE1C,qBAAqB;;EAErB,uBAAuB;;EAGvB,2BAA2B;;EAE3B,8BAA8B;;EAE9B,iCAAiC;;EAGjC,mBACE;;EAEF,sBAAsB;;EAEtB,qBAAqB;;EAGrB,kCAAkC;;EAElC,mCAAmC;;EAEnC,kDAAkD;;;;;;;EAOlD,2BAA2B;;;;;;;EAO3B,mCAAmC;;;;;;;;EAQnC,yCAAyC;;EAGzC,6BAA6B;;EAE7B,kCAAkC;;EAElC,iCAAiC;;EAEjC,sCAAsC;;EAEtC,6CAA6C;AAC/C,CAAC;", "names": ["commentsLocaleStrings", "defineLocalesResources"]}